#!/usr/bin/env python3
"""
Quick test per verificare che il backend funzioni
"""
import requests
import json

BASE_URL = "https://c00cf951-a5fc-49b4-9d11-e40befa8b953-00-34azgt7zrw3wu.picard.replit.dev"

def test_health():
    """Test endpoint di health"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Health check: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_docs():
    """Test documentazione API"""
    try:
        response = requests.get(f"{BASE_URL}/docs")
        print(f"✅ API Docs: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ API Docs failed: {e}")
        return False

def test_workflow_templates():
    """Test endpoint workflow templates (pubblico)"""
    try:
        response = requests.get(f"{BASE_URL}/api/workflow-templates/")
        print(f"✅ Workflow Templates: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📋 Templates trovati: {len(data)}")
            if data:
                print(f"   📄 Primo template: {data[0].get('name', 'N/A')}")
        return True
    except Exception as e:
        print(f"❌ Workflow Templates failed: {e}")
        return False

def test_search_endpoints():
    """Test endpoint search (richiedono auth)"""
    endpoints = [
        "/api/search/deadlines/check",
        "/api/search/procedures/status", 
        "/api/search/projects/health"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 401:
                print(f"✅ {endpoint}: Richiede autenticazione (corretto)")
            elif response.status_code == 403:
                print(f"✅ {endpoint}: Accesso negato (corretto)")
            else:
                print(f"⚠️ {endpoint}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def main():
    print("🧪 Test rapido backend ExProject")
    print("=" * 50)
    
    # Test base
    if not test_health():
        print("❌ Backend non raggiungibile")
        return
    
    test_docs()
    test_workflow_templates()
    test_search_endpoints()
    
    print("\n" + "=" * 50)
    print("🎉 Test completati!")
    print("\n💡 Prossimi passi:")
    print("1. Testare login e ottenere token")
    print("2. Testare endpoint search con autenticazione")
    print("3. Verificare wizard creazione progetti")

if __name__ == "__main__":
    main()
