Metadata-Version: 2.4
Name: opentelemetry-api
Version: 1.34.1
Summary: OpenTelemetry Python API
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/opentelemetry-api
Project-URL: Repository, https://github.com/open-telemetry/opentelemetry-python
Author-email: OpenTelemetry Authors <<EMAIL>>
License: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: OpenTelemetry
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Typing :: Typed
Requires-Python: >=3.9
Requires-Dist: importlib-metadata<8.8.0,>=6.0
Requires-Dist: typing-extensions>=4.5.0
Description-Content-Type: text/x-rst

OpenTelemetry Python API
============================================================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-api.svg
   :target: https://pypi.org/project/opentelemetry-api/

Installation
------------

::

    pip install opentelemetry-api

References
----------

* `OpenTelemetry Project <https://opentelemetry.io/>`_
