Metadata-Version: 2.1
Name: langchain-core
Version: 0.0.13
Summary: Building applications with LLMs through composability
Home-page: https://github.com/langchain-ai/langchain
License: MIT
Requires-Python: >=3.8.1,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Provides-Extra: extended-testing
Requires-Dist: PyYAML (>=5.3)
Requires-Dist: anyio (>=3,<5)
Requires-Dist: jinja2 (>=3,<4) ; extra == "extended-testing"
Requires-Dist: jsonpatch (>=1.33,<2.0)
Requires-Dist: langsmith (>=0.0.63,<0.1.0)
Requires-Dist: packaging (>=23.2,<24.0)
Requires-Dist: pydantic (>=1,<3)
Requires-Dist: requests (>=2,<3)
Requires-Dist: tenacity (>=8.1.0,<9.0.0)
Project-URL: Repository, https://github.com/langchain-ai/langchain
Description-Content-Type: text/markdown

# langchain-core

