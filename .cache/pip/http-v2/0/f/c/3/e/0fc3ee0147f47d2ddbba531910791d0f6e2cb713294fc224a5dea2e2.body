Metadata-Version: 2.4
Name: pulsar-client
Version: 3.7.0
Summary: Apache Pulsar Python client library
Home-page: https://pulsar.apache.org/
Author: Pulsar Devs
Author-email: <EMAIL>
License: Apache License v2.0
License-File: LICENSE
License-File: NOTICE
Requires-Dist: certifi
Provides-Extra: functions
Requires-Dist: apache-bookkeeper-client>=4.16.1; extra == "functions"
Requires-Dist: grpcio>=1.59.3; extra == "functions"
Requires-Dist: prometheus_client; extra == "functions"
Requires-Dist: protobuf<=3.20.3,>=3.6.1; extra == "functions"
Requires-Dist: ratelimit; extra == "functions"
Provides-Extra: avro
Requires-Dist: fastavro>=1.9.2; extra == "avro"
Provides-Extra: all
Requires-Dist: apache-bookkeeper-client>=4.16.1; extra == "all"
Requires-Dist: fastavro>=1.9.2; extra == "all"
Requires-Dist: grpcio>=1.59.3; extra == "all"
Requires-Dist: prometheus_client; extra == "all"
Requires-Dist: protobuf<=3.20.3,>=3.6.1; extra == "all"
Requires-Dist: ratelimit; extra == "all"
Dynamic: author
Dynamic: author-email
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: summary
