# Installation
> `npm install --save @types/selenium-webdriver`

# Summary
This package contains type definitions for selenium-webdriver (https://github.com/SeleniumHQ/selenium).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/selenium-webdriver.

### Additional Details
 * Last updated: <PERSON><PERSON>, 07 Jan 2025 06:02:23 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [@types/ws](https://npmjs.com/package/@types/ws)

# Credits
These definitions were written by [<PERSON>](https://github.com/BillArmstrong), [<PERSON><PERSON>](https://github.com/Kuniwak), [<PERSON>](https://github.com/cnishina), [<PERSON>](https://github.com/SupernaviX), [<PERSON>](https://github.com/bendxn), [<PERSON><PERSON><PERSON>](https://github.com/oddui), [<PERSON>](https://github.com/beta-vulgaris), [<PERSON><PERSON><PERSON>](https://github.com/Dzenly), [<PERSON>](https://github.com/AutomatedTester), [Pirasis Leelatanon](https://github.com/1pete), [Harmandeep Singh](https://github.com/SinghHrmn), [Shubh Sheth](https://github.com/shubhsheth), and [Sri Harsha](https://github.com/harsha509).
