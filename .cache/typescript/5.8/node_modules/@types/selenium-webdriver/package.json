{"name": "@types/selenium-webdriver", "version": "4.1.28", "description": "TypeScript definitions for selenium-webdriver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/selenium-webdriver", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Bill<PERSON><PERSON>trong", "url": "https://github.com/BillArmstrong"}, {"name": "<PERSON><PERSON>", "githubUsername": "Kuniwak", "url": "https://github.com/Kuniwak"}, {"name": "<PERSON>", "githubUsername": "cnishina", "url": "https://github.com/cnishina"}, {"name": "<PERSON>", "githubUsername": "SupernaviX", "url": "https://github.com/SupernaviX"}, {"name": "<PERSON>", "githubUsername": "bendxn", "url": "https://github.com/bendxn"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "oddui", "url": "https://github.com/oddui"}, {"name": "<PERSON>", "githubUsername": "beta-vulgaris", "url": "https://github.com/beta-vulgaris"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "Dzenly", "url": "https://github.com/Dzenly"}, {"name": "<PERSON>", "githubUsername": "AutomatedTester", "url": "https://github.com/AutomatedTester"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "1pete", "url": "https://github.com/1pete"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "SinghHrmn", "url": "https://github.com/SinghHrmn"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/shubhsheth"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "harsha509", "url": "https://github.com/harsha509"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/selenium-webdriver"}, "scripts": {}, "dependencies": {"@types/ws": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "96892901f543b1c9cd06909e5f89265bbfc0a5c9a18f2fa5fe2754ef898396c8", "typeScriptVersion": "5.0"}