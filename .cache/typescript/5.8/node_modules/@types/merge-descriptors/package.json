{"name": "@types/merge-descriptors", "version": "1.0.3", "description": "TypeScript definitions for merge-descriptors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/merge-descriptors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "danny8002", "url": "https://github.com/danny8002"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/merge-descriptors"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "b808c6f5927aace71a88e58e0aaf0d89b3ca3deefb45318a871de783da86d0d8", "typeScriptVersion": "4.5"}