# Installation
> `npm install --save @types/merge-descriptors`

# Summary
This package contains type definitions for merge-descriptors (https://github.com/component/merge-descriptors).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/merge-descriptors.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/merge-descriptors/index.d.ts)
````ts
declare function merge(destination: Object, source: Object, redefine?: boolean): Object;
export = merge;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/danny8002).
