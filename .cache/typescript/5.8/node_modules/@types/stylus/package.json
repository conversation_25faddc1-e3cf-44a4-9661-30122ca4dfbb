{"name": "@types/stylus", "version": "0.48.43", "description": "TypeScript definitions for stylus", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stylus", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "githubUsername": "SomaticIT", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON>", "githubUsername": "STRd6", "url": "https://github.com/STRd6"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/stylus"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "233166a6d02b0229cd72fc3b4294b19934da7eb9eafe1d117827d8ce621b8473", "typeScriptVersion": "4.8"}