<template>
  <div class="project-header">
    <div class="project-header-main">
      <div class="project-info">
        <div class="project-title-section">
          <h1 class="project-title">{{ project.name }}</h1>
          <div class="project-badges">
            <span class="status-badge" :class="getStatusClass(project.status)">
              {{ getStatusLabel(project.status) }}
            </span>
            <span class="phase-badge" :class="getPhaseClass(project.phase)">
              {{ getPhaseLabel(project.phase) }}
            </span>
          </div>
        </div>
        <p class="project-description">{{ project.description }}</p>
        
        <div class="project-meta">
          <div class="meta-item">
            <i class="pi pi-calendar"></i>
            <span>Creato: {{ formatDate(project.created_at) }}</span>
          </div>
          <div class="meta-item" v-if="project.municipality">
            <i class="pi pi-map-marker"></i>
            <span>{{ project.municipality }}</span>
          </div>
          <div class="meta-item" v-if="project.budget">
            <i class="pi pi-euro"></i>
            <span>{{ formatCurrency(project.budget) }}</span>
          </div>
        </div>
      </div>
      
      <!-- FIXED: Single action button with dropdown -->
      <div class="project-actions">
        <Button 
          v-if="canEdit"
          @click="$emit('edit')"
          icon="pi pi-pencil"
          label="Modifica"
          class="p-button-primary"
          size="small"
        />
        
        <Button 
          icon="pi pi-ellipsis-v"
          class="p-button-text"
          @click="toggleActionsMenu"
          aria-haspopup="true"
          aria-controls="project-actions-menu"
        />
        
        <Menu 
          ref="actionsMenu"
          id="project-actions-menu"
          :model="actionMenuItems"
          :popup="true"
        />
      </div>
    </div>
    
    <!-- Progress indicator if project has phases -->
    <div class="project-progress" v-if="project.phases && project.phases.length > 0">
      <div class="progress-header">
        <span class="progress-label">Avanzamento Procedimento</span>
        <span class="progress-percentage">{{ progressPercentage }}%</span>
      </div>
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
      <div class="progress-phases">
        <div 
          v-for="(phase, index) in project.phases" 
          :key="phase.id"
          class="phase-indicator"
          :class="getPhaseIndicatorClass(phase, index)"
        >
          <div class="phase-dot"></div>
          <span class="phase-name">{{ phase.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Button from 'primevue/button'
import Menu from 'primevue/menu'

interface Props {
  project: {
    id: number
    name: string
    description?: string
    status: string
    phase?: string
    created_at: string
    municipality?: string
    budget?: number
    phases?: Array<{
      id: number
      name: string
      status: string
      completed: boolean
    }>
  }
  canEdit?: boolean
  canDelete?: boolean
  canArchive?: boolean
}

interface Emits {
  (e: 'edit'): void
  (e: 'delete'): void
  (e: 'archive'): void
  (e: 'duplicate'): void
  (e: 'export'): void
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: true,
  canDelete: false,
  canArchive: false
})

const emit = defineEmits<Emits>()

const actionsMenu = ref()

const actionMenuItems = computed(() => {
  const items = []
  
  if (props.canEdit) {
    items.push({
      label: 'Duplica Progetto',
      icon: 'pi pi-copy',
      command: () => emit('duplicate')
    })
  }
  
  items.push({
    label: 'Esporta Dati',
    icon: 'pi pi-download',
    command: () => emit('export')
  })
  
  if (props.canArchive) {
    items.push({
      separator: true
    })
    items.push({
      label: 'Archivia',
      icon: 'pi pi-archive',
      command: () => emit('archive')
    })
  }
  
  if (props.canDelete) {
    items.push({
      separator: true
    })
    items.push({
      label: 'Elimina',
      icon: 'pi pi-trash',
      class: 'text-red-500',
      command: () => emit('delete')
    })
  }
  
  return items
})

const progressPercentage = computed(() => {
  if (!props.project.phases || props.project.phases.length === 0) return 0
  
  const completedPhases = props.project.phases.filter(phase => phase.completed).length
  return Math.round((completedPhases / props.project.phases.length) * 100)
})

const toggleActionsMenu = (event: Event) => {
  actionsMenu.value.toggle(event)
}

const getStatusClass = (status: string) => {
  const classes = {
    'draft': 'status-draft',
    'active': 'status-active', 
    'completed': 'status-completed',
    'archived': 'status-archived',
    'suspended': 'status-suspended'
  }
  return classes[status as keyof typeof classes] || 'status-default'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'draft': 'Bozza',
    'active': 'Attivo',
    'completed': 'Completato', 
    'archived': 'Archiviato',
    'suspended': 'Sospeso'
  }
  return labels[status as keyof typeof labels] || status
}

const getPhaseClass = (phase?: string) => {
  if (!phase) return 'phase-default'
  
  const classes = {
    'preparatorie': 'phase-preparatory',
    'dichiarazione': 'phase-declaration',
    'decreto': 'phase-decree',
    'indennizzo': 'phase-compensation'
  }
  return classes[phase as keyof typeof classes] || 'phase-default'
}

const getPhaseLabel = (phase?: string) => {
  if (!phase) return 'Non definita'
  
  const labels = {
    'preparatorie': 'Attività Preparatorie',
    'dichiarazione': 'Dichiarazione P.U.',
    'decreto': 'Decreto Esproprio',
    'indennizzo': 'Indennizzo'
  }
  return labels[phase as keyof typeof labels] || phase
}

const getPhaseIndicatorClass = (phase: any, index: number) => {
  return {
    'phase-completed': phase.completed,
    'phase-current': !phase.completed && index === 0, // Simplified logic
    'phase-pending': !phase.completed
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('it-IT')
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}
</script>

<style scoped>
.project-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.project-header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 2rem;
}

.project-info {
  flex: 1;
}

.project-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.project-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.project-badges {
  display: flex;
  gap: 0.5rem;
}

.status-badge, .phase-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active { background: var(--color-success-light); color: var(--color-success); }
.status-draft { background: var(--color-warning-light); color: var(--color-warning); }
.status-completed { background: var(--color-primary-light); color: var(--color-primary); }

.phase-preparatory { background: #fef3c7; color: #92400e; }
.phase-declaration { background: #dbeafe; color: #1e40af; }

.project-description {
  color: var(--color-gray-600);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.project-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-gray-600);
  font-size: 0.875rem;
}

.meta-item i {
  color: var(--color-primary);
}

.project-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.project-progress {
  border-top: 1px solid var(--color-gray-200);
  padding-top: 1.5rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.progress-label {
  font-weight: 500;
  color: var(--color-gray-700);
}

.progress-percentage {
  font-weight: 600;
  color: var(--color-primary);
}

.progress-bar {
  height: 8px;
  background: var(--color-gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
  transition: width 0.3s ease;
}

.progress-phases {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.phase-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.phase-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-gray-300);
  transition: background 0.2s;
}

.phase-completed .phase-dot {
  background: var(--color-success);
}

.phase-current .phase-dot {
  background: var(--color-primary);
}

.phase-name {
  font-size: 0.75rem;
  color: var(--color-gray-600);
  text-align: center;
}

@media (max-width: 768px) {
  .project-header-main {
    flex-direction: column;
    gap: 1rem;
  }
  
  .project-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .project-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .progress-phases {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .phase-indicator {
    flex-direction: row;
    justify-content: flex-start;
  }
}
</style>
