import{d as D,z as M,g as p,p as x,c,a as k,b as t,B as h,h as m,j as w,k as T,e as I,f as _,F as j,q as F,t as r,n as g,u as R,o as s,_ as U}from"./index-Bibk5VaL.js";import{A as q}from"./AdminNavbar-jBevcaIl.js";import{P as H}from"./PageHeader-B4SpV5zp.js";import{L as G}from"./LoadingState-9ob6nEfb.js";import{E as O}from"./ErrorState-DJld4rs8.js";import{E as J}from"./EmptyState-rMfszj3C.js";import"./BaseNavbar-BSvAFYsY.js";const K={class:"admin-layout"},Q={class:"admin-content"},W={class:"main-content"},X={key:2,class:"notifications-content"},Y={class:"filters-section"},Z={class:"filters-row"},tt={class:"filter-group"},et={class:"filter-group"},ot={class:"notifications-grid"},it={class:"notification-header"},st={class:"notification-icon"},at={class:"notification-meta"},nt={class:"notification-type"},rt={class:"notification-time"},lt={class:"notification-body"},ct={class:"notification-title"},dt={class:"notification-description"},ut={class:"notification-project"},pt={class:"notification-actions"},vt=["onClick"],ft=["onClick"],mt=["onClick"],ht={key:1,class:"pagination-section"},_t={class:"pagination-info"},gt={class:"pagination-actions"},yt=D({__name:"NotificationList",setup(bt){const y=R(),d=M(),l=p([]),v=p(!1),f=p(""),C=p(!1),a=p({status:"",priority:""}),N=[{label:"Dashboard",to:"/admin/dashboard"},{label:"Notifiche",to:"/admin/notifications"}],E=o=>[`notification-${o.type}`,`priority-${o.priority}`,`status-${o.status}`],$=o=>({pec_pending:"PEC in attesa",document_required:"Documento richiesto",approval_needed:"Approvazione necessaria",deadline_warning:"Scadenza imminente",system_alert:"Avviso sistema"})[o]||o,S=o=>({high:"Alta",medium:"Media",low:"Bassa",urgent:"Urgente"})[o]||o,z=o=>{const i=new Date().getTime()-new Date(o).getTime(),n=Math.floor(i/(1e3*60*60)),b=Math.floor(n/24);return b>0?`${b} giorni fa`:n>0?`${n} ore fa`:"Ora"},u=async()=>{v.value=!0,f.value="";try{const o=new URLSearchParams;a.value.status&&o.append("status",a.value.status),a.value.priority&&o.append("priority",a.value.priority);const e=await fetch(`/api/notifications?${o.toString()}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const i=await e.json();l.value=i}catch(o){f.value="Errore nel caricamento delle notifiche",console.error("Error loading notifications:",o),d.showToast("Errore nel caricamento delle notifiche","error")}finally{v.value=!1}},P=()=>{a.value={status:"",priority:""},u()},A=o=>{o.action_route?y.push(o.action_route):d.showToast("Azione non ancora implementata","info")},B=o=>{y.push(`/admin/notifications/${o.id}`)},L=async o=>{if(confirm("Sei sicuro di voler eliminare questa notifica?"))try{const e=await fetch(`/api/notifications/${o}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const i=l.value.findIndex(n=>n.id===o);i>-1&&l.value.splice(i,1),d.showToast("Notifica eliminata con successo","success")}catch(e){console.error("Error deleting notification:",e),d.showToast("Errore nell'eliminazione della notifica","error")}},V=()=>{d.showToast("Paginazione non ancora implementata","info")};return x(()=>{u()}),(o,e)=>(s(),c("div",K,[k(q),t("div",Q,[t("main",W,[k(H,{title:"Notifiche e Task",subtitle:"Gestione notifiche e attività per D.P.R. 327/2001",breadcrumbs:N}),v.value?(s(),h(G,{key:0,message:"Caricamento notifiche..."})):f.value?(s(),h(O,{key:1,error:f.value,onRetry:u},null,8,["error"])):(s(),c("div",X,[t("div",Y,[t("div",Z,[t("div",tt,[e[3]||(e[3]=t("label",null,"Stato:",-1)),w(t("select",{"onUpdate:modelValue":e[0]||(e[0]=i=>a.value.status=i),onChange:u},e[2]||(e[2]=[I('<option value="" data-v-82c36899>Tutti</option><option value="pending" data-v-82c36899>In attesa</option><option value="sent" data-v-82c36899>Inviate</option><option value="delivered" data-v-82c36899>Consegnate</option><option value="failed" data-v-82c36899>Fallite</option>',5)]),544),[[T,a.value.status]])]),t("div",et,[e[5]||(e[5]=t("label",null,"Priorità:",-1)),w(t("select",{"onUpdate:modelValue":e[1]||(e[1]=i=>a.value.priority=i),onChange:u},e[4]||(e[4]=[t("option",{value:""},"Tutte",-1),t("option",{value:"high"},"Alta",-1),t("option",{value:"medium"},"Media",-1),t("option",{value:"low"},"Bassa",-1)]),544),[[T,a.value.priority]])]),t("div",{class:"filter-group"},[t("button",{onClick:P,class:"btn btn-secondary"},e[6]||(e[6]=[t("i",{class:"pi pi-times"},null,-1),_(" Pulisci filtri ")]))])])]),t("div",ot,[(s(!0),c(j,null,F(l.value,i=>(s(),c("div",{key:i.id,class:g(["notification-card",E(i)])},[t("div",it,[t("div",st,[t("i",{class:g(i.icon)},null,2)]),t("div",at,[t("span",nt,r($(i.type)),1),t("span",rt,r(z(i.created_at)),1)]),t("div",{class:g(["notification-priority",`priority-${i.priority}`])},r(S(i.priority)),3)]),t("div",lt,[t("h3",ct,r(i.title),1),t("p",dt,r(i.description),1),t("div",ut,[e[7]||(e[7]=t("i",{class:"pi pi-folder"},null,-1)),_(" "+r(i.project_name),1)])]),t("div",pt,[i.actionable?(s(),c("button",{key:0,onClick:n=>A(i),class:"btn btn-primary"},r(i.action_label),9,vt)):m("",!0),t("button",{onClick:n=>B(i),class:"btn btn-secondary"},e[8]||(e[8]=[t("i",{class:"pi pi-eye"},null,-1),_(" Visualizza ")]),8,ft),t("button",{onClick:n=>L(i.id),class:"btn btn-danger btn-sm"},e[9]||(e[9]=[t("i",{class:"pi pi-trash"},null,-1)]),8,mt)])],2))),128))]),l.value.length===0&&!v.value?(s(),h(J,{key:0,title:"Nessuna notifica",message:"Non ci sono notifiche che corrispondono ai filtri selezionati",icon:"pi pi-bell-slash"})):m("",!0),l.value.length>0?(s(),c("div",ht,[t("div",_t," Visualizzando "+r(l.value.length)+" notifiche ",1),t("div",gt,[C.value?(s(),c("button",{key:0,onClick:V,class:"btn btn-secondary"}," Carica altre ")):m("",!0)])])):m("",!0)]))])])]))}}),St=U(yt,[["__scopeId","data-v-82c36899"]]);export{St as default};
