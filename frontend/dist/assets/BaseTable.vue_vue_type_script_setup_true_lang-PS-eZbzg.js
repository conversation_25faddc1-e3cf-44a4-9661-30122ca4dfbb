import{_ as $}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{d as J,g as y,m as d,c as n,b as s,h,F as k,q as b,n as C,t as g,H as O,f as Q,a as z,w as K,o}from"./index-Bibk5VaL.js";const U={class:"bg-white shadow rounded-lg overflow-hidden"},W={class:"overflow-x-auto"},X={class:"min-w-full divide-y divide-gray-200"},Y={class:"bg-gray-50"},Z={key:0,class:"px-6 py-3 text-left"},j=["checked","indeterminate"],ee=["onClick"],te={class:"flex items-center space-x-1"},se={class:"bg-white divide-y divide-gray-200"},ae={key:0},le=["colspan"],ne={key:1},oe=["colspan"],ie={key:0,class:"px-6 py-4"},re=["checked","onChange"],ce={key:0,class:"px-6 py-3 border-t border-gray-200 flex items-center justify-between"},de={class:"text-sm text-gray-700"},ue={class:"flex items-center space-x-2"},pe={class:"text-sm text-gray-700"},ye=J({__name:"BaseTable",props:{columns:{},data:{},loading:{type:Boolean,default:!1},pagination:{type:Boolean,default:!1},selectable:{type:Boolean,default:!1},pageSize:{default:10},rowKey:{default:"id"}},emits:["sort","select"],setup(B,{emit:I}){const l=B,f=I,r=y(1),x=y(""),v=y("asc"),i=y([]),w=d(()=>l.columns.length+(l.selectable?1:0)),m=d(()=>l.data.length),_=d(()=>Math.ceil(m.value/l.pageSize)),N=d(()=>(r.value-1)*l.pageSize+1),P=d(()=>Math.min(r.value*l.pageSize,m.value)),V=d(()=>{if(!l.pagination)return l.data;const e=(r.value-1)*l.pageSize,a=e+l.pageSize;return l.data.slice(e,a)}),S=d(()=>l.data.length>0&&i.value.length===l.data.length),R=d(()=>i.value.length>0&&i.value.length<l.data.length),D=e=>{const a="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",t={left:"text-left",center:"text-center",right:"text-right"},c=e.sortable?"cursor-pointer hover:text-gray-700":"";return`${a} ${t[e.align||"left"]} ${c}`},M=e=>`px-6 py-4 whitespace-nowrap text-sm ${{left:"text-left",center:"text-center",right:"text-right"}[e.align||"left"]}`,u=(e,a)=>a.split(".").reduce((t,c)=>t==null?void 0:t[c],e),F=(e,a)=>u(e,l.rowKey)||a,H=e=>x.value!==e?"pi pi-sort-alt":v.value==="asc"?"pi pi-sort-amount-up":"pi pi-sort-amount-down",T=e=>{e.sortable&&(x.value===e.key?v.value=v.value==="asc"?"desc":"asc":(x.value=e.key,v.value="asc"),f("sort",e.key,v.value))},q=e=>{const a=u(e,l.rowKey);return i.value.some(t=>u(t,l.rowKey)===a)},A=e=>{const a=u(e,l.rowKey),t=i.value.findIndex(c=>u(c,l.rowKey)===a);t===-1?i.value.push(e):i.value.splice(t,1),f("select",i.value)},E=()=>{S.value?i.value=[]:i.value=[...l.data],f("select",i.value)},L=()=>{r.value>1&&r.value--},G=()=>{r.value<_.value&&r.value++};return(e,a)=>(o(),n("div",U,[s("div",W,[s("table",X,[s("thead",Y,[s("tr",null,[e.selectable?(o(),n("th",Z,[s("input",{type:"checkbox",checked:S.value,indeterminate:R.value,onChange:E,class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,40,j)])):h("",!0),(o(!0),n(k,null,b(e.columns,t=>(o(),n("th",{key:t.key,class:C(D(t)),onClick:c=>T(t)},[s("div",te,[s("span",null,g(t.title),1),t.sortable?(o(),n("i",{key:0,class:C([H(t.key),"text-gray-400"])},null,2)):h("",!0)])],10,ee))),128))])]),s("tbody",se,[e.loading?(o(),n("tr",ae,[s("td",{colspan:w.value,class:"px-6 py-12 text-center"},a[0]||(a[0]=[s("div",{class:"flex items-center justify-center space-x-2"},[s("i",{class:"pi pi-spin pi-spinner text-primary-600"}),s("span",{class:"text-gray-500"},"Caricamento...")],-1)]),8,le)])):e.data.length===0?(o(),n("tr",ne,[s("td",{colspan:w.value,class:"px-6 py-12 text-center"},a[1]||(a[1]=[s("div",{class:"text-gray-500"},[s("i",{class:"pi pi-inbox text-4xl mb-4 block"}),s("p",null,"Nessun dato disponibile")],-1)]),8,oe)])):(o(!0),n(k,{key:2},b(V.value,(t,c)=>(o(),n("tr",{key:F(t,c),class:"hover:bg-gray-50 transition-colors"},[e.selectable?(o(),n("td",ie,[s("input",{type:"checkbox",checked:q(t),onChange:p=>A(t),class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,40,re)])):h("",!0),(o(!0),n(k,null,b(e.columns,p=>(o(),n("td",{key:p.key,class:C(M(p))},[O(e.$slots,`cell-${p.key}`,{row:t,column:p,value:u(t,p.key)},()=>[Q(g(u(t,p.key)),1)])],2))),128))]))),128))])])]),e.pagination&&!e.loading&&e.data.length>0?(o(),n("div",ce,[s("div",de," Mostrando "+g(N.value)+" - "+g(P.value)+" di "+g(m.value)+" risultati ",1),s("div",ue,[z($,{variant:"outline",size:"sm",disabled:r.value===1,onClick:L},{default:K(()=>a[2]||(a[2]=[s("i",{class:"pi pi-chevron-left"},null,-1)])),_:1,__:[2]},8,["disabled"]),s("span",pe," Pagina "+g(r.value)+" di "+g(_.value),1),z($,{variant:"outline",size:"sm",disabled:r.value===_.value,onClick:G},{default:K(()=>a[3]||(a[3]=[s("i",{class:"pi pi-chevron-right"},null,-1)])),_:1,__:[3]},8,["disabled"])])])):h("",!0)]))}});export{ye as _};
