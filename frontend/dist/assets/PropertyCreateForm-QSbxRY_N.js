import{d as N,g as p,m as z,c as m,b as o,i as P,h as f,f as u,a as i,j as E,k as F,e as T,t as b,w as _,o as c,_ as k}from"./index-Bibk5VaL.js";import{_ as s}from"./BaseInput.vue_vue_type_script_setup_true_lang-DWl8EspK.js";import{_ as V}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";const O={class:"property-create-form"},x={class:"form-section"},M={class:"form-grid"},j={class:"form-section"},R={class:"form-grid"},$={class:"form-group full-width"},Z={class:"form-section"},q={class:"form-grid"},B={class:"form-group"},D=["disabled"],L={key:0,class:"error-message"},h={key:0,class:"error-section"},G={class:"error-alert"},H={class:"form-actions"},J=N({__name:"PropertyCreateForm",props:{projectId:{}},emits:["close","success"],setup(y,{emit:g}){const w=y,A=g,t=p(!1),d=p(""),r=p({}),e=p({comune:"",sezione:"",foglio:"",particella:"",subalterno:"",owner_name:"",owner_fiscal_code:"",owner_address:"",property_type:"",surface_area:"",cadastral_income:"",estimated_value:""}),C=z(()=>e.value.comune&&e.value.foglio&&e.value.particella&&e.value.owner_name&&e.value.property_type),I=()=>(r.value={},e.value.comune||(r.value.comune="Il comune è obbligatorio"),e.value.foglio||(r.value.foglio="Il foglio è obbligatorio"),e.value.particella||(r.value.particella="La particella è obbligatoria"),e.value.owner_name||(r.value.owner_name="Il nome del proprietario è obbligatorio"),e.value.property_type||(r.value.property_type="Il tipo di immobile è obbligatorio"),e.value.owner_fiscal_code&&(/^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/.test(e.value.owner_fiscal_code.toUpperCase())||(r.value.owner_fiscal_code="Formato codice fiscale non valido")),Object.keys(r.value).length===0),S=async()=>{if(I()){t.value=!0,d.value="";try{const n=localStorage.getItem("token"),a={comune:e.value.comune,sezione:e.value.sezione||null,foglio:e.value.foglio,particella:e.value.particella,subalterno:e.value.subalterno||null,owner_name:e.value.owner_name,owner_fiscal_code:e.value.owner_fiscal_code||null,owner_address:e.value.owner_address||null,property_type:e.value.property_type,surface_area:e.value.surface_area?parseFloat(e.value.surface_area):null,cadastral_income:e.value.cadastral_income?parseFloat(e.value.cadastral_income):null,estimated_value:e.value.estimated_value?parseFloat(e.value.estimated_value):null,project_id:w.projectId},l=await fetch("/api/properties/",{method:"POST",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!l.ok){if(l.status===400){const v=await l.json();v.detail&&v.detail.includes("already exists")?d.value="Una particella con gli stessi dati catastali esiste già nel sistema":d.value=v.detail||"Errore nella validazione dei dati"}else throw new Error(`HTTP error! status: ${l.status}`);return}const U=await l.json();A("success",U)}catch(n){console.error("Create property error:",n),d.value="Errore durante la creazione della particella"}finally{t.value=!1}}};return(n,a)=>(c(),m("div",O,[o("form",{onSubmit:P(S,["prevent"]),class:"create-form"},[o("div",x,[a[13]||(a[13]=o("h3",{class:"section-title"},[o("i",{class:"pi pi-map"}),u(" Dati Catastali ")],-1)),o("div",M,[i(s,{modelValue:e.value.comune,"onUpdate:modelValue":a[0]||(a[0]=l=>e.value.comune=l),label:"Comune *",placeholder:"es. Milano",required:"",disabled:t.value,error:r.value.comune},null,8,["modelValue","disabled","error"]),i(s,{modelValue:e.value.sezione,"onUpdate:modelValue":a[1]||(a[1]=l=>e.value.sezione=l),label:"Sezione",placeholder:"es. A",disabled:t.value},null,8,["modelValue","disabled"]),i(s,{modelValue:e.value.foglio,"onUpdate:modelValue":a[2]||(a[2]=l=>e.value.foglio=l),label:"Foglio *",placeholder:"es. 42",required:"",disabled:t.value,error:r.value.foglio},null,8,["modelValue","disabled","error"]),i(s,{modelValue:e.value.particella,"onUpdate:modelValue":a[3]||(a[3]=l=>e.value.particella=l),label:"Particella *",placeholder:"es. 156",required:"",disabled:t.value,error:r.value.particella},null,8,["modelValue","disabled","error"]),i(s,{modelValue:e.value.subalterno,"onUpdate:modelValue":a[4]||(a[4]=l=>e.value.subalterno=l),label:"Subalterno",placeholder:"es. 1",disabled:t.value},null,8,["modelValue","disabled"])])]),o("div",j,[a[14]||(a[14]=o("h3",{class:"section-title"},[o("i",{class:"pi pi-user"}),u(" Dati Proprietario ")],-1)),o("div",R,[i(s,{modelValue:e.value.owner_name,"onUpdate:modelValue":a[5]||(a[5]=l=>e.value.owner_name=l),label:"Nome Proprietario *",placeholder:"es. Rossi Mario",required:"",disabled:t.value,error:r.value.owner_name},null,8,["modelValue","disabled","error"]),i(s,{modelValue:e.value.owner_fiscal_code,"onUpdate:modelValue":a[6]||(a[6]=l=>e.value.owner_fiscal_code=l),label:"Codice Fiscale",placeholder:"es. ****************",maxlength:"16",disabled:t.value,error:r.value.owner_fiscal_code},null,8,["modelValue","disabled","error"]),o("div",$,[i(s,{modelValue:e.value.owner_address,"onUpdate:modelValue":a[7]||(a[7]=l=>e.value.owner_address=l),label:"Indirizzo",placeholder:"es. Via Roma 123, Milano (MI)",disabled:t.value},null,8,["modelValue","disabled"])])])]),o("div",Z,[a[17]||(a[17]=o("h3",{class:"section-title"},[o("i",{class:"pi pi-cog"}),u(" Caratteristiche Immobile ")],-1)),o("div",q,[o("div",B,[a[16]||(a[16]=o("label",null,"Tipo Immobile *",-1)),E(o("select",{"onUpdate:modelValue":a[8]||(a[8]=l=>e.value.property_type=l),class:"form-select",required:"",disabled:t.value},a[15]||(a[15]=[T('<option value="" data-v-7b1d45fe>Seleziona tipo</option><option value="SEMINATIVO" data-v-7b1d45fe>Seminativo</option><option value="ABITAZIONE" data-v-7b1d45fe>Abitazione</option><option value="NEGOZIO" data-v-7b1d45fe>Negozio</option><option value="CAPANNONE" data-v-7b1d45fe>Capannone</option><option value="VILLA" data-v-7b1d45fe>Villa</option><option value="ULIVETO" data-v-7b1d45fe>Uliveto</option><option value="APPARTAMENTO" data-v-7b1d45fe>Appartamento</option>',8)]),8,D),[[F,e.value.property_type]]),r.value.property_type?(c(),m("span",L,b(r.value.property_type),1)):f("",!0)]),i(s,{modelValue:e.value.surface_area,"onUpdate:modelValue":a[9]||(a[9]=l=>e.value.surface_area=l),label:"Superficie (m²)",placeholder:"es. 1500.50",type:"number",step:"0.01",min:"0",disabled:t.value},null,8,["modelValue","disabled"]),i(s,{modelValue:e.value.cadastral_income,"onUpdate:modelValue":a[10]||(a[10]=l=>e.value.cadastral_income=l),label:"Reddito Catastale (€)",placeholder:"es. 850.00",type:"number",step:"0.01",min:"0",disabled:t.value},null,8,["modelValue","disabled"]),i(s,{modelValue:e.value.estimated_value,"onUpdate:modelValue":a[11]||(a[11]=l=>e.value.estimated_value=l),label:"Valore Stimato (€)",placeholder:"es. 250000.00",type:"number",step:"0.01",min:"0",disabled:t.value},null,8,["modelValue","disabled"])])]),d.value?(c(),m("div",h,[o("div",G,[a[18]||(a[18]=o("i",{class:"pi pi-exclamation-triangle"},null,-1)),o("span",null,b(d.value),1)])])):f("",!0),o("div",H,[i(V,{type:"button",variant:"secondary",onClick:a[12]||(a[12]=l=>n.$emit("close")),disabled:t.value,icon:"pi pi-times"},{default:_(()=>a[19]||(a[19]=[u(" Annulla ")])),_:1,__:[19]},8,["disabled"]),i(V,{type:"submit",variant:"primary",loading:t.value,disabled:!C.value,icon:"pi pi-save"},{default:_(()=>a[20]||(a[20]=[u(" Crea Particella ")])),_:1,__:[20]},8,["loading","disabled"])])],32)]))}}),W=k(J,[["__scopeId","data-v-7b1d45fe"]]);export{W as P};
