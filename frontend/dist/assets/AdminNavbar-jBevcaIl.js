import{d as A,y as B,g as h,m as E,p as y,I,B as j,w as r,b as e,c as g,h as k,t as n,G as l,a as w,f as d,r as M,i as U,u as V,o as c,_ as P}from"./index-Bibk5VaL.js";import{B as S}from"./BaseNavbar-BSvAFYsY.js";const z={class:"action-button notification-button","aria-label":"Notifiche"},L={key:0,class:"notification-badge"},D={class:"user-menu-container"},G=["aria-expanded"],R={class:"user-avatar"},O={class:"user-name"},T={class:"dropdown-section"},W={class:"dropdown-header"},$={class:"user-info"},q={class:"user-avatar-large"},F={class:"user-full-name"},H={class:"user-email"},J={class:"dropdown-section"},K=A({__name:"AdminNavbar",setup(Q){const C=V(),a=B(),s=h(!1),u=h(3),x=[{name:"Dashboard",href:"/admin/dashboard",icon:"pi pi-home"},{name:"Progetti",href:"/admin/projects",icon:"pi pi-folder"},{name:"Utenti",href:"/admin/users",icon:"pi pi-users"},{name:"Workflow",href:"/admin/workflows",icon:"pi pi-sitemap"},{name:"Integrazioni",href:"/admin/integrations",icon:"pi pi-link"},{name:"Report",href:"/admin/reports",icon:"pi pi-chart-bar"}],m=E(()=>{var o;return(((o=a.user)==null?void 0:o.full_name)||"Admin").split(" ").map(i=>i[0]).join("").toUpperCase()}),p=()=>{s.value=!1},N=async()=>{try{await a.logout(),C.push("/admin/login")}catch(t){console.error("Logout error:",t)}},v=t=>{t.target.closest(".user-menu-container")||(s.value=!1)};return y(()=>{document.addEventListener("click",v)}),I(()=>{document.removeEventListener("click",v)}),(t,o)=>{const i=M("router-link");return c(),j(S,{variant:"admin","brand-title":"ExProject","brand-subtitle":"Sistema Gestione Espropri","brand-link":"/admin/dashboard","brand-aria-label":"ExProject Admin - Vai alla dashboard","navigation-items":x,"aria-label":"Navigazione amministratori"},{actions:r(()=>{var _,f,b;return[e("button",z,[o[2]||(o[2]=e("i",{class:"pi pi-bell"},null,-1)),u.value?(c(),g("span",L,n(u.value),1)):k("",!0)]),e("div",D,[e("button",{onClick:o[0]||(o[0]=X=>s.value=!s.value),class:"user-menu-button","aria-expanded":s.value,"aria-label":"Menu utente"},[e("div",R,[e("span",null,n(m.value),1)]),e("span",O,n(((_=l(a).user)==null?void 0:_.full_name)||"Admin"),1),o[3]||(o[3]=e("i",{class:"pi pi-chevron-down dropdown-icon"},null,-1))],8,G),s.value?(c(),g("div",{key:0,class:"dropdown-menu",onClick:o[1]||(o[1]=U(()=>{},["stop"]))},[e("div",T,[e("div",W,[e("div",$,[e("div",q,[e("span",null,n(m.value),1)]),e("div",null,[e("div",F,n(((f=l(a).user)==null?void 0:f.full_name)||"Amministratore"),1),e("div",H,n(((b=l(a).user)==null?void 0:b.email)||"<EMAIL>"),1)])])])]),e("div",J,[w(i,{to:"/admin/profile",class:"dropdown-item",onClick:p},{default:r(()=>o[4]||(o[4]=[e("i",{class:"pi pi-user"},null,-1),d(" Profilo ")])),_:1,__:[4]}),w(i,{to:"/admin/settings",class:"dropdown-item",onClick:p},{default:r(()=>o[5]||(o[5]=[e("i",{class:"pi pi-cog"},null,-1),d(" Impostazioni ")])),_:1,__:[5]})]),e("div",{class:"dropdown-section"},[e("button",{onClick:N,class:"dropdown-item danger"},o[6]||(o[6]=[e("i",{class:"pi pi-sign-out"},null,-1),d(" Esci ")]))])])):k("",!0)])]}),_:1})}}}),ee=P(K,[["__scopeId","data-v-3145164c"]]);export{ee as A};
