const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-Bibk5VaL.js","assets/index-Z7TjTKhi.css"])))=>i.map(i=>d[i]);
import{A as N}from"./AdminNavbar-jBevcaIl.js";import{d as j,g as $,m as S,p as C,c as m,b as t,h as b,f as w,t as l,F as D,q as z,a as k,r as I,w as T,n as y,o as v,_ as P,s as V,i as E,u as R,x,y as M,z as B}from"./index-Bibk5VaL.js";import"./BaseNavbar-BSvAFYsY.js";const U={class:"deadline-panel"},O={class:"panel-header"},G={key:0,class:"urgent-count"},L={class:"deadline-list"},F=["onClick"],H={class:"deadline-info"},W={class:"deadline-title"},q={class:"deadline-project"},Q={class:"deadline-timing"},J={class:"days-remaining"},K={class:"days-number"},X={class:"days-label"},Y={class:"deadline-date"},Z={key:0,class:"no-deadlines"},tt={class:"panel-footer"},et=j({__name:"DeadlinePanel",emits:["deadline-click"],setup(A,{emit:f}){const a=$([]),o=S(()=>a.value.filter(r=>r.days_remaining<=7).length),d=r=>r.days_remaining<=0?"deadline-overdue":r.days_remaining<=3?"deadline-critical":r.days_remaining<=7?"deadline-urgent":"deadline-normal",u=r=>new Date(r).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"}),n=async()=>{try{const r=await fetch("/api/tasks/deadlines/",{headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const _=await r.json();a.value=_.map(h=>({id:h.id.toString(),title:h.title,project_name:h.project_id?`Progetto #${h.project_id}`:"Sistema",due_date:h.deadline_date,days_remaining:h.days_until||0,priority:h.urgency==="urgent"?"critical":h.urgency==="warning"?"high":"medium",type:h.deadline_type}))}catch(r){console.error("Error loading deadlines:",r),a.value=[]}};return C(()=>{n()}),(r,_)=>{const h=I("router-link");return v(),m("div",U,[t("div",O,[_[0]||(_[0]=t("h3",null,[t("i",{class:"pi pi-clock"}),w(" Scadenze Urgenti ")],-1)),o.value>0?(v(),m("span",G,l(o.value),1)):b("",!0)]),t("div",L,[(v(!0),m(D,null,z(a.value,g=>(v(),m("div",{key:g.id,class:y(["deadline-item",d(g)]),onClick:p=>r.$emit("deadline-click",g)},[t("div",H,[t("div",W,l(g.title),1),t("div",q,l(g.project_name),1)]),t("div",Q,[t("div",J,[t("span",K,l(g.days_remaining),1),t("span",X,l(g.days_remaining===1?"giorno":"giorni"),1)]),t("div",Y,l(u(g.due_date)),1)])],10,F))),128))]),a.value.length===0?(v(),m("div",Z,_[1]||(_[1]=[t("i",{class:"pi pi-check-circle"},null,-1),t("p",null,"Nessuna scadenza urgente",-1)]))):b("",!0),t("div",tt,[k(h,{to:"/admin/reports?view=deadlines",class:"view-all-link"},{default:T(()=>_[2]||(_[2]=[w(" Vedi tutte le scadenze "),t("i",{class:"pi pi-arrow-right"},null,-1)])),_:1,__:[2]})])])}}}),it=P(et,[["__scopeId","data-v-d1de5db5"]]),st={class:"project-phase-grid"},ot={class:"phases-container"},rt=["onClick"],nt={class:"phase-header"},at={class:"phase-icon"},ct={class:"phase-info"},lt={class:"phase-stats"},dt={class:"stat-primary"},pt={class:"stat-number"},ut={class:"stat-label"},_t={key:0,class:"stat-secondary"},mt={class:"urgent-indicator"},vt={class:"phase-progress"},ht={class:"progress-bar"},gt={class:"progress-text"},ft={class:"phase-actions"},kt=["onClick"],$t=j({__name:"ProjectPhaseGrid",emits:["phase-click","view-projects"],setup(A,{emit:f}){const a=$([]),o=async()=>{try{const d=await fetch("/api/projects/",{headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!d.ok)throw new Error(`HTTP error! status: ${d.status}`);const u=await d.json(),n=Array.isArray(u)?u:u.projects||[],r={preparatorie:n.filter(_=>_.status==="planning"||_.status==="feasibility").length,vincolo:n.filter(_=>_.status==="preliminary").length,pubblica_utilita:n.filter(_=>_.status==="definitive").length,espropriazione:n.filter(_=>_.status==="expropriation"||_.status==="execution").length};a.value=[{id:"preparatorie",name:"Operazioni Preparatorie",description:"Autorizzazioni accesso e rilievi preliminari",icon:"pi pi-cog",project_count:r.preparatorie,urgent_count:Math.floor(r.preparatorie*.3),completion_percentage:r.preparatorie>0?65:0,legal_reference:"Art. 15 D.P.R. 327/2001"},{id:"vincolo",name:"Apposizione Vincolo",description:"Vincolo preordinato all'esproprio",icon:"pi pi-map-marker",project_count:r.vincolo,urgent_count:Math.floor(r.vincolo*.2),completion_percentage:r.vincolo>0?40:0,legal_reference:"Art. 16 D.P.R. 327/2001"},{id:"pubblica_utilita",name:"Pubblica Utilità",description:"Dichiarazione di pubblica utilità",icon:"pi pi-file-text",project_count:r.pubblica_utilita,urgent_count:Math.floor(r.pubblica_utilita*.1),completion_percentage:r.pubblica_utilita>0?80:0,legal_reference:"Art. 18 D.P.R. 327/2001"},{id:"espropriazione",name:"Espropriazione",description:"Decreto di esproprio e immissione",icon:"pi pi-euro",project_count:r.espropriazione,urgent_count:Math.floor(r.espropriazione*.4),completion_percentage:r.espropriazione>0?75:0,legal_reference:"Art. 20-22 D.P.R. 327/2001"}]}catch(d){console.error("Error loading phase stats:",d),a.value=[{id:"preparatorie",name:"Operazioni Preparatorie",description:"Autorizzazioni accesso e rilievi preliminari",icon:"pi pi-cog",project_count:0,urgent_count:0,completion_percentage:0,legal_reference:"Art. 15 D.P.R. 327/2001"},{id:"vincolo",name:"Apposizione Vincolo",description:"Vincolo preordinato all'esproprio",icon:"pi pi-map-marker",project_count:0,urgent_count:0,completion_percentage:0,legal_reference:"Art. 16 D.P.R. 327/2001"},{id:"pubblica_utilita",name:"Pubblica Utilità",description:"Dichiarazione di pubblica utilità",icon:"pi pi-file-text",project_count:0,urgent_count:0,completion_percentage:0,legal_reference:"Art. 18 D.P.R. 327/2001"},{id:"espropriazione",name:"Espropriazione",description:"Decreto di esproprio e immissione",icon:"pi pi-euro",project_count:0,urgent_count:0,completion_percentage:0,legal_reference:"Art. 20-22 D.P.R. 327/2001"}]}};return C(()=>{o()}),(d,u)=>(v(),m("div",st,[u[1]||(u[1]=t("div",{class:"panel-header"},[t("h3",null,[t("i",{class:"pi pi-sitemap"}),w(" Progetti per Fase D.P.R. 327/2001 ")])],-1)),t("div",ot,[(v(!0),m(D,null,z(a.value,n=>(v(),m("div",{key:n.id,class:y(["phase-card",n.id]),onClick:r=>d.$emit("phase-click",n)},[t("div",nt,[t("div",at,[t("i",{class:y(n.icon)},null,2)]),t("div",ct,[t("h4",null,l(n.name),1),t("p",null,l(n.description),1)])]),t("div",lt,[t("div",dt,[t("span",pt,l(n.project_count),1),t("span",ut,l(n.project_count===1?"progetto":"progetti"),1)]),n.urgent_count>0?(v(),m("div",_t,[t("span",mt,l(n.urgent_count)+" urgenti",1)])):b("",!0)]),t("div",vt,[t("div",ht,[t("div",{class:"progress-fill",style:V({width:`${n.completion_percentage}%`})},null,4)]),t("span",gt,l(n.completion_percentage)+"% completato",1)]),t("div",ft,[t("button",{class:"action-btn",onClick:E(r=>d.$emit("view-projects",n),["stop"])},u[0]||(u[0]=[t("i",{class:"pi pi-eye"},null,-1),w(" Visualizza ")]),8,kt)])],10,rt))),128))])]))}}),wt=P($t,[["__scopeId","data-v-a2258957"]]),yt={class:"quick-actions"},bt={class:"actions-grid"},jt=["onClick"],Pt={class:"action-icon"},At={class:"action-content"},Ct=j({__name:"QuickActions",setup(A){const f=R(),a=$([{id:"new-project",title:"Nuovo Progetto",description:"Avvia procedimento espropriativo",icon:"pi pi-plus-circle",route:"/admin/projects/create",priority:"high"},{id:"import-sister",title:"Import SISTER",description:"Importa dati catastali",icon:"pi pi-download",action:"import-sister",priority:"high"},{id:"send-notifications",title:"Invio Notifiche",description:"Gestisci notifiche PEC pendenti",icon:"pi pi-send",route:"/admin/notifications",priority:"medium"},{id:"generate-documents",title:"Genera Documenti",description:"Crea atti e decreti automatici",icon:"pi pi-file-pdf",action:"generate-documents",priority:"medium"},{id:"check-deadlines",title:"Verifica Scadenze",description:"Controlla termini legali",icon:"pi pi-calendar",route:"/admin/reports?view=deadlines",priority:"medium"},{id:"user-management",title:"Gestione Utenti",description:"Assegna ruoli e permessi",icon:"pi pi-users",route:"/admin/users",priority:"low"}]),o=d=>{if(d.route)f.push(d.route);else if(d.action)switch(d.action){case"import-sister":console.log("Opening SISTER import...");break;case"generate-documents":console.log("Opening document generation...");break;default:console.log(`Action ${d.action} not implemented yet`)}};return(d,u)=>(v(),m("div",yt,[u[1]||(u[1]=t("div",{class:"panel-header"},[t("h3",null,[t("i",{class:"pi pi-bolt"}),w(" Azioni Rapide ")])],-1)),t("div",bt,[(v(!0),m(D,null,z(a.value,n=>(v(),m("button",{key:n.id,class:y(["action-card",n.id]),onClick:r=>o(n)},[t("div",Pt,[t("i",{class:y(n.icon)},null,2)]),t("div",At,[t("h4",null,l(n.title),1),t("p",null,l(n.description),1)]),u[0]||(u[0]=t("div",{class:"action-arrow"},[t("i",{class:"pi pi-arrow-right"})],-1))],10,jt))),128))])]))}}),Dt=P(Ct,[["__scopeId","data-v-5dd8aea6"]]),zt={class:"notification-list"},Et={class:"panel-header"},St={key:0,class:"notification-count"},It={class:"notifications-container"},Tt=["onClick"],Rt={class:"notification-icon"},Nt={class:"notification-content"},Vt={class:"notification-title"},xt={class:"notification-description"},Mt={class:"notification-meta"},Bt={class:"notification-project"},Ut={class:"notification-time"},Ot={class:"notification-actions"},Gt=["onClick"],Lt=["onClick"],Ft={key:0,class:"no-notifications"},Ht={class:"panel-footer"},Wt=j({__name:"NotificationList",emits:["notification-click","action-click"],setup(A,{emit:f}){const a=f,o=$([]),d=S(()=>o.value.filter(p=>p.priority==="high").length),u=p=>[`notification-${p.type}`,`priority-${p.priority}`],n=p=>{const e=new Date().getTime()-new Date(p).getTime(),s=Math.floor(e/(1e3*60*60)),c=Math.floor(s/24);return c>0?`${c} giorni fa`:s>0?`${s} ore fa`:"Ora"},r=p=>{a("notification-click",p)},_=p=>{a("action-click",p);const i=o.value.findIndex(e=>e.id===p.id);i>-1&&o.value.splice(i,1)},h=p=>{const i=o.value.findIndex(e=>e.id===p);i>-1&&o.value.splice(i,1)},g=async()=>{try{const p=await fetch("/api/notifications/dashboard/summary",{headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!p.ok)throw new Error(`HTTP error! status: ${p.status}`);const i=await p.json();o.value=i}catch(p){console.error("Error loading notifications:",p);try{const{useToastStore:i}=await x(async()=>{const{useToastStore:s}=await import("./index-Bibk5VaL.js").then(c=>c.P);return{useToastStore:s}},__vite__mapDeps([0,1])),e=i();e&&typeof e.showToast=="function"&&e.showToast("Errore nel caricamento delle notifiche","error")}catch(i){console.warn("Toast store not available:",i)}o.value=[]}};return C(()=>{g()}),(p,i)=>{const e=I("router-link");return v(),m("div",zt,[t("div",Et,[i[0]||(i[0]=t("h3",null,[t("i",{class:"pi pi-bell"}),w(" Notifiche e Task ")],-1)),d.value>0?(v(),m("span",St,l(d.value),1)):b("",!0)]),t("div",It,[(v(!0),m(D,null,z(o.value,s=>(v(),m("div",{key:s.id,class:y(["notification-item",u(s)]),onClick:c=>r(s)},[t("div",Rt,[t("i",{class:y(s.icon)},null,2)]),t("div",Nt,[t("div",Vt,l(s.title),1),t("div",xt,l(s.description),1),t("div",Mt,[t("span",Bt,l(s.project_name),1),t("span",Ut,l(n(s.created_at)),1)])]),t("div",Ot,[s.actionable?(v(),m("button",{key:0,class:"action-btn primary",onClick:E(c=>_(s),["stop"])},l(s.action_label),9,Gt)):b("",!0),t("button",{class:"action-btn secondary",onClick:E(c=>h(s.id),["stop"])},i[1]||(i[1]=[t("i",{class:"pi pi-times"},null,-1)]),8,Lt)])],10,Tt))),128))]),o.value.length===0?(v(),m("div",Ft,i[2]||(i[2]=[t("i",{class:"pi pi-check-circle"},null,-1),t("p",null,"Nessuna notifica pendente",-1)]))):b("",!0),t("div",Ht,[k(e,{to:"/admin/notifications",class:"view-all-link"},{default:T(()=>i[3]||(i[3]=[w(" Vedi tutte le notifiche "),t("i",{class:"pi pi-arrow-right"},null,-1)])),_:1,__:[3]})])])}}}),qt=P(Wt,[["__scopeId","data-v-e1d763c5"]]),Qt={class:"admin-layout"},Jt={class:"admin-content"},Kt={class:"dashboard-main"},Xt={key:0,class:"loading-state"},Yt={key:1,class:"error-state"},Zt={key:2,class:"dashboard-content"},te={class:"stats-section"},ee={class:"stats-grid"},ie={class:"stat-content"},se={class:"stat-number"},oe={class:"stat-content"},re={class:"stat-number"},ne={class:"stat-content"},ae={class:"stat-number"},ce={class:"stat-content"},le={class:"stat-number"},de={class:"stat-content"},pe={class:"stat-number"},ue={class:"dashboard-grid"},_e={class:"dashboard-left"},me={class:"dashboard-right"},ve=j({__name:"Dashboard",setup(A){const f=R();M();const a=B(),o=$({projects:0,procedures:0,properties:0,workflows:0,users:0}),d=$(!0),u=$(null),n=async()=>{try{d.value=!0,u.value=null;const e={Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"};try{const s=await fetch("/api/projects/",{headers:e});if(s.ok){const c=await s.json();o.value.projects=Array.isArray(c)?c.length:c.projects?c.projects.length:c.total||0}else a.warning("Progetti","Impossibile caricare i progetti"),o.value.projects=0}catch{a.error("Progetti","Errore caricamento progetti"),o.value.projects=0}try{const s=await fetch("/api/procedures/",{headers:e});if(s.ok){const c=await s.json();o.value.procedures=Array.isArray(c)?c.length:0}else a.warning("Procedimenti","Impossibile caricare i procedimenti"),o.value.procedures=0}catch{a.error("Procedimenti","Errore caricamento procedimenti"),o.value.procedures=0}try{const s=await fetch("/api/workflow-templates/",{headers:e});if(s.ok){const c=await s.json();o.value.workflows=Array.isArray(c)?c.length:0}else a.warning("Workflow","Impossibile caricare i template workflow"),o.value.workflows=9}catch{a.error("Workflow","Errore caricamento template workflow"),o.value.workflows=9}try{const s=await fetch("/api/properties/",{headers:e});if(s.ok){const c=await s.json();o.value.properties=Array.isArray(c)?c.length:0}else a.warning("Particelle","Impossibile caricare le particelle"),o.value.properties=0}catch{a.error("Particelle","Errore caricamento particelle"),o.value.properties=0}try{const s=await fetch("/api/users/",{headers:e});if(s.ok){const c=await s.json();o.value.users=c.total||(Array.isArray(c.users)?c.users.length:0)}else a.warning("Utenti","Impossibile caricare gli utenti"),o.value.users=0}catch{a.error("Utenti","Errore caricamento utenti"),o.value.users=0}d.value=!1}catch(i){console.error("Critical error loading dashboard stats:",i),a.error("Dashboard","Errore critico nel caricamento delle statistiche"),u.value="Errore nel caricamento delle statistiche",o.value.projects=0,o.value.procedures=0,o.value.workflows=9,o.value.properties=0,o.value.users=0,d.value=!1}},r=i=>{console.log("Deadline clicked:",i),f.push(`/admin/projects/${i.project_id}`)},_=i=>{console.log("Phase clicked:",i),f.push(`/admin/projects?phase=${i.id}`)},h=i=>{console.log("View projects for phase:",i),f.push(`/admin/projects?phase=${i.id}`)},g=i=>{console.log("Notification clicked:",i),i.action_route&&f.push(i.action_route)},p=i=>{console.log("Action clicked:",i),i.action_route&&f.push(i.action_route)};return C(()=>{n()}),(i,e)=>(v(),m("div",Qt,[k(N),t("div",Jt,[t("main",Kt,[e[21]||(e[21]=t("div",{class:"page-header"},[t("div",{class:"page-header-content"},[t("h1",{class:"page-title"},"Dashboard Amministrativa"),t("p",{class:"page-subtitle"},"Panoramica sistema ExProject - D.P.R. 327/2001")])],-1)),d.value?(v(),m("div",Xt,e[5]||(e[5]=[t("div",{class:"loading-spinner"},null,-1),t("p",null,"Caricamento dashboard...",-1)]))):u.value?(v(),m("div",Yt,[t("p",null,l(u.value),1),t("button",{onClick:n,class:"btn btn-primary"},"Riprova")])):(v(),m("div",Zt,[t("section",te,[t("div",ee,[t("div",{class:"stat-card",onClick:e[0]||(e[0]=s=>i.$router.push("/admin/projects"))},[e[8]||(e[8]=t("div",{class:"stat-icon projects"},[t("i",{class:"pi pi-folder"})],-1)),t("div",ie,[e[6]||(e[6]=t("h4",null,"Progetti",-1)),t("p",se,l(o.value.projects),1),e[7]||(e[7]=t("span",{class:"stat-label"},"Progetti in gestione",-1))])]),t("div",{class:"stat-card",onClick:e[1]||(e[1]=s=>i.$router.push("/admin/procedures"))},[e[11]||(e[11]=t("div",{class:"stat-icon procedures"},[t("i",{class:"pi pi-list"})],-1)),t("div",oe,[e[9]||(e[9]=t("h4",null,"Procedimenti",-1)),t("p",re,l(o.value.procedures),1),e[10]||(e[10]=t("span",{class:"stat-label"},"Procedimenti attivi",-1))])]),t("div",{class:"stat-card",onClick:e[2]||(e[2]=s=>i.$router.push("/admin/properties"))},[e[14]||(e[14]=t("div",{class:"stat-icon properties"},[t("i",{class:"pi pi-map"})],-1)),t("div",ne,[e[12]||(e[12]=t("h4",null,"Particelle",-1)),t("p",ae,l(o.value.properties),1),e[13]||(e[13]=t("span",{class:"stat-label"},"Particelle catastali",-1))])]),t("div",{class:"stat-card",onClick:e[3]||(e[3]=s=>i.$router.push("/admin/workflows"))},[e[17]||(e[17]=t("div",{class:"stat-icon workflows"},[t("i",{class:"pi pi-sitemap"})],-1)),t("div",ce,[e[15]||(e[15]=t("h4",null,"Workflow",-1)),t("p",le,l(o.value.workflows),1),e[16]||(e[16]=t("span",{class:"stat-label"},"Template D.P.R. 327/2001",-1))])]),t("div",{class:"stat-card",onClick:e[4]||(e[4]=s=>i.$router.push("/admin/users"))},[e[20]||(e[20]=t("div",{class:"stat-icon users"},[t("i",{class:"pi pi-users"})],-1)),t("div",de,[e[18]||(e[18]=t("h4",null,"Utenti",-1)),t("p",pe,l(o.value.users),1),e[19]||(e[19]=t("span",{class:"stat-label"},"Account attivi",-1))])])])]),t("div",ue,[t("div",_e,[k(it,{onDeadlineClick:r}),k(Dt,{onNotificationClick:g,onActionClick:p})]),t("div",me,[k(wt,{onPhaseClick:_,onViewProjects:h}),k(qt,{onNotificationClick:g,onActionClick:p})])])]))])])]))}}),ke=P(ve,[["__scopeId","data-v-302e553c"]]);export{ke as default};
