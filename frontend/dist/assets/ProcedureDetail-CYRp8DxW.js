import{d as R,g,p as q,c as r,a as d,b as s,B as k,h as m,w as l,E as C,D as U,f as p,r as H,t as n,n as v,s as O,F as J,q as K,u as Q,o as i,_ as X}from"./index-Bibk5VaL.js";import{A as Y}from"./AdminNavbar-jBevcaIl.js";import{_ as S}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as f}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{P as Z}from"./PageHeader-B4SpV5zp.js";import{L as z}from"./LoadingState-9ob6nEfb.js";import{E as ee}from"./ErrorState-DJld4rs8.js";import{E as se}from"./EmptyState-rMfszj3C.js";import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const te={class:"admin-layout"},ae={class:"admin-content"},oe={class:"procedure-detail-main"},ie={key:2,class:"procedure-content"},ne={class:"overview-section"},re={class:"overview-grid"},le={class:"overview-item"},ce={class:"overview-item"},de={class:"overview-item"},pe={class:"overview-item"},ue={class:"progress-container"},me={class:"progress-bar"},ve={class:"progress-text"},_e={class:"overview-item"},ge={class:"overview-item"},fe={key:0,class:"description-section"},ke={class:"workflow-section"},we={key:1,class:"workflow-timeline"},ye={class:"step-marker"},be={key:0,class:"pi pi-check"},he={key:1,class:"pi pi-clock"},Ce={key:2,class:"step-number"},Se={class:"step-content"},Pe={class:"step-header"},De={class:"step-description"},$e={class:"step-meta"},xe={key:0,class:"meta-item"},Ee={key:1,class:"meta-item legal"},ze={key:2,class:"meta-item"},Le={key:0,class:"step-actions"},Ie={class:"documents-section"},Ae=R({__name:"ProcedureDetail",setup(Ne){const u=U(),w=Q(),o=g(null),y=g([]),b=g(!0),h=g(!1),_=g(null),P=async()=>{var t,e;try{b.value=!0,_.value=null;const c=await C.get(`/procedures/${u.params.id}`);o.value=c.data,await D()}catch(c){console.error("Error loading procedure:",c),_.value=((e=(t=c.response)==null?void 0:t.data)==null?void 0:e.message)||"Errore caricamento procedimento"}finally{b.value=!1}},D=async()=>{try{h.value=!0;const t=await C.get(`/procedures/${u.params.id}/workflow-steps`);y.value=t.data}catch(t){console.error("Error loading workflow steps:",t)}finally{h.value=!1}},L=()=>{w.push(`/admin/procedures/${u.params.id}/edit`)},I=()=>{w.push(`/admin/procedures/${u.params.id}/workflow`)},A=()=>{w.push(`/admin/procedures/${u.params.id}/workflow/configure`)},N=async t=>{var e,c;try{await C.post(`/procedures/${u.params.id}/workflow-steps/${t.id}/complete`),await D()}catch(a){console.error("Error completing step:",a),_.value=((c=(e=a.response)==null?void 0:e.data)==null?void 0:c.message)||"Errore completamento step"}},B=t=>({preparatorie:"phase-preparatorie",vincolo:"phase-vincolo",pubblica_utilita:"phase-pubblica-utilita",indennita:"phase-indennita"})[t]||"phase-default",$=t=>({preparatorie:"Operazioni Preparatorie",vincolo:"Apposizione Vincolo",pubblica_utilita:"Pubblica Utilità",indennita:"Determinazione Indennità"})[t]||t,T=t=>({active:"status-active",pending:"status-pending",completed:"status-completed",suspended:"status-suspended",expired:"status-expired"})[t]||"status-default",x=t=>({active:"Attivo",pending:"In Attesa",completed:"Completato",suspended:"Sospeso",expired:"Scaduto"})[t]||t,V=t=>({low:"priority-low",medium:"priority-medium",high:"priority-high",urgent:"priority-urgent"})[t]||"priority-default",W=t=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[t]||t,M=t=>({pending:"step-status-pending",in_progress:"step-status-active",completed:"step-status-completed",skipped:"step-status-skipped"})[t]||"step-status-default",j=t=>({pending:"In Attesa",in_progress:"In Corso",completed:"Completato",skipped:"Saltato"})[t]||t,F=t=>{if(!t)return"";const e=new Date,c=new Date(t),a=Math.ceil((c.getTime()-e.getTime())/(1e3*60*60*24));return a<0?"deadline-expired":a<=7?"deadline-urgent":a<=30?"deadline-warning":"deadline-normal"},E=t=>t?new Date(t).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"Non specificata";return q(()=>{P()}),(t,e)=>{const c=H("router-link");return i(),r("div",te,[d(Y),s("div",ae,[s("main",oe,[b.value?(i(),k(z,{key:0,message:"Caricamento procedimento..."})):_.value?(i(),k(ee,{key:1,message:_.value},{actions:l(()=>[d(f,{onClick:P,variant:"primary"},{default:l(()=>e[0]||(e[0]=[p(" Riprova ")])),_:1,__:[0]})]),_:1},8,["message"])):o.value?(i(),r("div",ie,[d(Z,{title:o.value.title,subtitle:`${$(o.value.phase)} - ${x(o.value.status)}`},{actions:l(()=>[d(f,{onClick:L,variant:"outline"},{default:l(()=>e[1]||(e[1]=[s("i",{class:"pi pi-pencil"},null,-1),p(" Modifica ")])),_:1,__:[1]}),d(f,{onClick:I,variant:"primary"},{default:l(()=>e[2]||(e[2]=[s("i",{class:"pi pi-cog"},null,-1),p(" Gestisci Workflow ")])),_:1,__:[2]})]),_:1},8,["title","subtitle"]),s("section",ne,[d(S,{title:"Informazioni Generali"},{default:l(()=>[s("div",re,[s("div",le,[e[3]||(e[3]=s("span",{class:"item-label"},"Progetto",-1)),d(c,{to:`/admin/projects/${o.value.project_id}`,class:"item-value link"},{default:l(()=>[p(n(o.value.project_name),1)]),_:1},8,["to"])]),s("div",ce,[e[4]||(e[4]=s("span",{class:"item-label"},"Fase",-1)),s("span",{class:v([B(o.value.phase),"phase-badge"])},n($(o.value.phase)),3)]),s("div",de,[e[5]||(e[5]=s("span",{class:"item-label"},"Stato",-1)),s("span",{class:v([T(o.value.status),"status-badge"])},n(x(o.value.status)),3)]),s("div",pe,[e[6]||(e[6]=s("span",{class:"item-label"},"Progresso",-1)),s("div",ue,[s("div",me,[s("div",{class:"progress-fill",style:O({width:`${o.value.progress||0}%`})},null,4)]),s("span",ve,n(o.value.progress||0)+"%",1)])]),s("div",_e,[e[7]||(e[7]=s("span",{class:"item-label"},"Scadenza",-1)),s("span",{class:v([F(o.value.deadline),"deadline-text"])},n(E(o.value.deadline)),3)]),s("div",ge,[e[8]||(e[8]=s("span",{class:"item-label"},"Priorità",-1)),s("span",{class:v([V(o.value.priority),"priority-badge"])},n(W(o.value.priority)),3)])]),o.value.description?(i(),r("div",fe,[e[9]||(e[9]=s("h4",null,"Descrizione",-1)),s("p",null,n(o.value.description),1)])):m("",!0)]),_:1})]),s("section",ke,[d(S,{title:"Timeline Workflow"},{default:l(()=>[h.value?(i(),k(z,{key:0,message:"Caricamento workflow..."})):y.value.length>0?(i(),r("div",we,[(i(!0),r(J,null,K(y.value,(a,G)=>(i(),r("div",{key:a.id,class:v(["timeline-step",{completed:a.status==="completed",active:a.status==="in_progress",pending:a.status==="pending"}])},[s("div",ye,[a.status==="completed"?(i(),r("i",be)):a.status==="in_progress"?(i(),r("i",he)):(i(),r("span",Ce,n(G+1),1))]),s("div",Se,[s("div",Pe,[s("h5",null,n(a.name),1),s("span",{class:v([M(a.status),"step-status"])},n(j(a.status)),3)]),s("p",De,n(a.description),1),s("div",$e,[a.estimated_days?(i(),r("span",xe,[e[10]||(e[10]=s("i",{class:"pi pi-clock"},null,-1)),p(" "+n(a.estimated_days)+" giorni stimati ",1)])):m("",!0),a.legal_deadline_days?(i(),r("span",Ee,[e[11]||(e[11]=s("i",{class:"pi pi-exclamation-triangle"},null,-1)),p(" Scadenza legale: "+n(a.legal_deadline_days)+" giorni ",1)])):m("",!0),a.completed_at?(i(),r("span",ze,[e[12]||(e[12]=s("i",{class:"pi pi-calendar"},null,-1)),p(" Completato: "+n(E(a.completed_at)),1)])):m("",!0)]),a.status==="in_progress"?(i(),r("div",Le,[d(f,{onClick:Be=>N(a),variant:"primary",size:"sm"},{default:l(()=>e[13]||(e[13]=[s("i",{class:"pi pi-check"},null,-1),p(" Completa Step ")])),_:2,__:[13]},1032,["onClick"])])):m("",!0)])],2))),128))])):(i(),k(se,{key:2,icon:"pi pi-list",title:"Nessun step definito",description:"Il workflow per questo procedimento non è ancora stato configurato."},{actions:l(()=>[d(f,{onClick:A,variant:"primary"},{default:l(()=>e[14]||(e[14]=[s("i",{class:"pi pi-cog"},null,-1),p(" Configura Workflow ")])),_:1,__:[14]})]),_:1}))]),_:1})]),s("section",Ie,[d(S,{title:"Documenti"},{default:l(()=>e[15]||(e[15]=[s("div",{class:"documents-grid"},[s("div",{class:"document-placeholder"},[s("i",{class:"pi pi-file"}),s("span",null,"Gestione documenti in sviluppo")])],-1)])),_:1,__:[15]})])])):m("",!0)])])])}}}),He=X(Ae,[["__scopeId","data-v-52dcccca"]]);export{He as default};
