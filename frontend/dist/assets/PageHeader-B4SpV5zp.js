import{d as n,c as s,b as a,h as o,t as i,H as r,o as t,_ as c}from"./index-Bibk5VaL.js";const d={class:"page-header"},l={class:"page-header-content"},p={class:"page-title"},_={key:0,class:"page-subtitle"},h={key:0,class:"page-header-actions"},u=n({__name:"PageHeader",props:{title:{},subtitle:{}},setup(g){return(e,m)=>(t(),s("div",d,[a("div",l,[a("h1",p,i(e.title),1),e.subtitle?(t(),s("p",_,i(e.subtitle),1)):o("",!0)]),e.$slots.actions?(t(),s("div",h,[r(e.$slots,"actions",{},void 0,!0)])):o("",!0)]))}}),f=c(u,[["__scopeId","data-v-bfdb6e72"]]);export{f as P};
