import{d as V,g as p,m as Q,p as j,I as W,B as C,w as l,b as t,c as d,h as I,t as o,a,f as u,r as Y,i as R,u as F,o as c,_ as T,E as w,F as x,q as B,n as S,s as Z}from"./index-Bibk5VaL.js";import{B as tt}from"./BaseNavbar-BSvAFYsY.js";import{_ as h}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as k}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{P as it}from"./PageHeader-B4SpV5zp.js";import{L}from"./LoadingState-9ob6nEfb.js";import{E as U}from"./EmptyState-rMfszj3C.js";import"./design-system-BvlP813R.js";const st={class:"action-button notification-button","aria-label":"Notifiche"},et={key:0,class:"notification-badge"},nt={class:"user-menu-container"},ot=["aria-expanded"],at={class:"user-avatar"},lt={class:"user-name"},ct={class:"dropdown-section"},rt={class:"dropdown-header"},dt={class:"user-info"},ut={class:"user-avatar-large"},pt={class:"user-full-name"},mt={class:"user-cf"},vt={class:"dropdown-section"},_t={class:"dropdown-section"},ft=V({__name:"CitizenNavbar",setup(H){const m=F(),r=p(!1),v=p(2),_=p({name:"Mario Rossi",fiscal_code:"****************",email:"<EMAIL>"}),z=[{name:"Dashboard",href:"/citizen/dashboard",icon:"pi pi-home"},{name:"Le Mie Procedure",href:"/citizen/procedures",icon:"pi pi-file-o"},{name:"Documenti",href:"/citizen/documents",icon:"pi pi-file"},{name:"Comunicazioni",href:"/citizen/communications",icon:"pi pi-envelope"},{name:"Pagamenti",href:"/citizen/payments",icon:"pi pi-credit-card"}],b=Q(()=>{var e;return(((e=_.value)==null?void 0:e.name)||"Cittadino").split(" ").map(g=>g[0]).join("").toUpperCase()}),f=()=>{r.value=!1},A=()=>{localStorage.removeItem("citizen_token"),m.push("/citizen/login")},P=y=>{y.target.closest(".user-menu-container")||(r.value=!1)};return j(()=>{document.addEventListener("click",P)}),W(()=>{document.removeEventListener("click",P)}),(y,e)=>{const g=Y("router-link");return c(),C(tt,{variant:"citizen","brand-title":"ExProject","brand-subtitle":"Area Cittadini","brand-link":"/citizen/dashboard","brand-aria-label":"ExProject Cittadini - Vai alla dashboard","navigation-items":z,"aria-label":"Navigazione cittadini"},{actions:l(()=>{var $,D,N;return[t("button",st,[e[2]||(e[2]=t("i",{class:"pi pi-bell"},null,-1)),v.value?(c(),d("span",et,o(v.value),1)):I("",!0)]),t("div",nt,[t("button",{onClick:e[0]||(e[0]=M=>r.value=!r.value),class:"user-menu-button","aria-expanded":r.value,"aria-label":"Menu utente"},[t("div",at,[t("span",null,o(b.value),1)]),t("span",lt,o((($=_.value)==null?void 0:$.name)||"Cittadino"),1),e[3]||(e[3]=t("i",{class:"pi pi-chevron-down dropdown-icon"},null,-1))],8,ot),r.value?(c(),d("div",{key:0,class:"dropdown-menu",onClick:e[1]||(e[1]=R(()=>{},["stop"]))},[t("div",ct,[t("div",rt,[t("div",dt,[t("div",ut,[t("span",null,o(b.value),1)]),t("div",null,[t("div",pt,o(((D=_.value)==null?void 0:D.name)||"Mario Rossi"),1),t("div",mt,"CF: "+o(((N=_.value)==null?void 0:N.fiscal_code)||"****************"),1)])])])]),t("div",vt,[a(g,{to:"/citizen/profile",class:"dropdown-item",onClick:f},{default:l(()=>e[4]||(e[4]=[t("i",{class:"pi pi-user"},null,-1),u(" Il Mio Profilo ")])),_:1,__:[4]}),a(g,{to:"/citizen/preferences",class:"dropdown-item",onClick:f},{default:l(()=>e[5]||(e[5]=[t("i",{class:"pi pi-cog"},null,-1),u(" Preferenze ")])),_:1,__:[5]}),a(g,{to:"/citizen/help",class:"dropdown-item",onClick:f},{default:l(()=>e[6]||(e[6]=[t("i",{class:"pi pi-question-circle"},null,-1),u(" Aiuto ")])),_:1,__:[6]})]),t("div",_t,[a(g,{to:"/",class:"dropdown-item",onClick:f},{default:l(()=>e[7]||(e[7]=[t("i",{class:"pi pi-home"},null,-1),u(" Sito Pubblico ")])),_:1,__:[7]}),t("button",{onClick:A,class:"dropdown-item danger"},e[8]||(e[8]=[t("i",{class:"pi pi-sign-out"},null,-1),u(" Esci ")]))])])):I("",!0)])]}),_:1})}}}),gt=T(ft,[["__scopeId","data-v-2f563ea7"]]),ht={class:"citizen-layout"},bt={class:"citizen-content"},kt={class:"citizen-main"},Ct={class:"welcome-header"},zt={class:"citizen-info"},yt={class:"citizen-details"},wt={class:"citizen-data"},Pt={class:"citizen-id"},$t={class:"stats-section"},Dt={class:"stats-grid"},Nt={class:"stat-item"},St={class:"stat-content"},It={class:"stat-item"},At={class:"stat-content"},Et={class:"stat-item"},Rt={class:"stat-content"},Mt={class:"stat-item"},xt={class:"stat-content"},Bt={class:"procedures-section"},Lt={key:2,class:"procedures-list"},Ut=["onClick"],Vt={class:"procedure-header"},jt={class:"procedure-details"},Ft={class:"procedure-info"},Tt={class:"procedure-progress"},Ht={class:"progress-bar"},qt={class:"progress-text"},Xt={class:"procedure-actions"},Ot={class:"notifications-section"},Gt={key:2,class:"notifications-list"},Jt={class:"notification-content"},Kt={class:"notification-date"},Qt=["onClick"],Wt={class:"actions-section"},Yt={class:"actions-grid"},Zt=V({__name:"CitizenDashboard",setup(H){const m=F(),r=p({id:null,name:"",email:""}),v=p({activeProcesses:0,pendingDocuments:0,completedProcesses:0,affectedProperties:0}),_=p([]),z=p([]),b=p(!0),f=p(!0),A=async()=>{try{const s=await w.get("/citizen/profile");r.value=s.data;const i=await w.get("/citizen/stats");v.value=i.data}catch(s){console.error("Error loading dashboard data:",s)}},P=async()=>{try{b.value=!0;const s=await w.get("/citizen/procedures/active");_.value=s.data}catch(s){console.error("Error loading procedures:",s)}finally{b.value=!1}},y=async()=>{try{f.value=!0;const s=await w.get("/citizen/notifications?limit=5");z.value=s.data}catch(s){console.error("Error loading notifications:",s)}finally{f.value=!1}},e=s=>{m.push(`/citizen/procedures/${s.id}`)},g=s=>{m.push(`/citizen/procedures/${s.id}/documents`)},$=async s=>{try{await w.patch(`/citizen/notifications/${s.id}/read`),s.read=!0}catch(i){console.error("Error marking notification as read:",i)}},D=()=>{m.push("/citizen/documents")},N=()=>{m.push("/citizen/support")},M=()=>{m.push("/citizen/procedures")},q=()=>{m.push("/citizen/properties")},X=s=>({in_progress:"status-active",pending_documents:"status-pending",completed:"status-completed",suspended:"status-suspended"})[s]||"status-default",O=s=>({in_progress:"In Corso",pending_documents:"Documenti Richiesti",completed:"Completato",suspended:"Sospeso"})[s]||s,G=s=>({document:"notification-document",deadline:"notification-deadline",info:"notification-info",warning:"notification-warning"})[s]||"notification-default",J=s=>({document:"pi pi-file",deadline:"pi pi-clock",info:"pi pi-info-circle",warning:"pi pi-exclamation-triangle"})[s]||"pi pi-bell",K=s=>new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return j(()=>{A(),P(),y()}),(s,i)=>(c(),d("div",ht,[a(gt),t("div",bt,[t("main",kt,[t("div",Ct,[a(it,{title:"Benvenuto nel portale cittadini",subtitle:"Monitora lo stato dei tuoi procedimenti espropriativi"}),t("div",zt,[a(h,null,{default:l(()=>[t("div",yt,[i[0]||(i[0]=t("div",{class:"citizen-avatar"},[t("i",{class:"pi pi-user"})],-1)),t("div",wt,[t("h3",null,o(r.value.name||"Cittadino"),1),t("p",null,o(r.value.email||"Non disponibile"),1),t("span",Pt,"ID: "+o(r.value.id||"N/A"),1)])])]),_:1})])]),t("section",$t,[t("div",Dt,[a(h,null,{default:l(()=>[t("div",Nt,[i[2]||(i[2]=t("div",{class:"stat-icon stat-active"},[t("i",{class:"pi pi-clock"})],-1)),t("div",St,[t("h3",null,o(v.value.activeProcesses||0),1),i[1]||(i[1]=t("p",null,"Procedimenti Attivi",-1))])])]),_:1}),a(h,null,{default:l(()=>[t("div",It,[i[4]||(i[4]=t("div",{class:"stat-icon stat-pending"},[t("i",{class:"pi pi-file"})],-1)),t("div",At,[t("h3",null,o(v.value.pendingDocuments||0),1),i[3]||(i[3]=t("p",null,"Documenti Richiesti",-1))])])]),_:1}),a(h,null,{default:l(()=>[t("div",Et,[i[6]||(i[6]=t("div",{class:"stat-icon stat-completed"},[t("i",{class:"pi pi-check-circle"})],-1)),t("div",Rt,[t("h3",null,o(v.value.completedProcesses||0),1),i[5]||(i[5]=t("p",null,"Procedimenti Completati",-1))])])]),_:1}),a(h,null,{default:l(()=>[t("div",Mt,[i[8]||(i[8]=t("div",{class:"stat-icon stat-properties"},[t("i",{class:"pi pi-home"})],-1)),t("div",xt,[t("h3",null,o(v.value.affectedProperties||0),1),i[7]||(i[7]=t("p",null,"Immobili Interessati",-1))])])]),_:1})])]),t("section",Bt,[a(h,{title:"Procedimenti Attivi"},{default:l(()=>[b.value?(c(),C(L,{key:0,message:"Caricamento procedimenti..."})):_.value.length===0?(c(),C(U,{key:1,icon:"pi pi-folder-open",title:"Nessun procedimento attivo",description:"Al momento non hai procedimenti espropriativi in corso."})):(c(),d("div",Lt,[(c(!0),d(x,null,B(_.value,n=>(c(),d("div",{key:n.id,class:"procedure-card",onClick:E=>e(n)},[t("div",Vt,[t("h4",null,o(n.title),1),t("span",{class:S([X(n.status),"procedure-status"])},o(O(n.status)),3)]),t("div",jt,[t("div",Ft,[t("p",null,[i[9]||(i[9]=t("strong",null,"Progetto:",-1)),u(" "+o(n.project_name),1)]),t("p",null,[i[10]||(i[10]=t("strong",null,"Ente:",-1)),u(" "+o(n.contracting_authority),1)]),t("p",null,[i[11]||(i[11]=t("strong",null,"Fase:",-1)),u(" "+o(n.phase),1)])]),t("div",Tt,[t("div",Ht,[t("div",{class:"progress-fill",style:Z({width:`${n.progress||0}%`})},null,4)]),t("span",qt,o(n.progress||0)+"%",1)])]),t("div",Xt,[a(k,{variant:"outline",size:"sm",onClick:R(E=>e(n),["stop"])},{default:l(()=>i[12]||(i[12]=[t("i",{class:"pi pi-eye"},null,-1),u(" Dettagli ")])),_:2,__:[12]},1032,["onClick"]),n.has_pending_documents?(c(),C(k,{key:0,variant:"primary",size:"sm",onClick:R(E=>g(n),["stop"])},{default:l(()=>i[13]||(i[13]=[t("i",{class:"pi pi-upload"},null,-1),u(" Carica Documenti ")])),_:2,__:[13]},1032,["onClick"])):I("",!0)])],8,Ut))),128))]))]),_:1})]),t("section",Ot,[a(h,{title:"Notifiche Recenti"},{default:l(()=>[f.value?(c(),C(L,{key:0,message:"Caricamento notifiche..."})):z.value.length===0?(c(),C(U,{key:1,icon:"pi pi-bell",title:"Nessuna notifica",description:"Non hai notifiche recenti."})):(c(),d("div",Gt,[(c(!0),d(x,null,B(z.value,n=>(c(),d("div",{key:n.id,class:S(["notification-item",{unread:!n.read}])},[t("div",{class:S(["notification-icon",G(n.type)])},[t("i",{class:S(J(n.type))},null,2)],2),t("div",Jt,[t("h4",null,o(n.title),1),t("p",null,o(n.message),1),t("span",Kt,o(K(n.created_at)),1)]),n.read?I("",!0):(c(),d("button",{key:0,onClick:E=>$(n),class:"btn btn-sm btn-ghost",title:"Segna come letto"},i[14]||(i[14]=[t("i",{class:"pi pi-check"},null,-1)]),8,Qt))],2))),128))]))]),_:1})]),t("section",Wt,[a(h,{title:"Azioni Rapide"},{default:l(()=>[t("div",Yt,[a(k,{variant:"outline",class:"action-button",onClick:D},{default:l(()=>i[15]||(i[15]=[t("i",{class:"pi pi-download"},null,-1),t("span",null,"Scarica Documenti",-1)])),_:1,__:[15]}),a(k,{variant:"outline",class:"action-button",onClick:N},{default:l(()=>i[16]||(i[16]=[t("i",{class:"pi pi-phone"},null,-1),t("span",null,"Contatta Supporto",-1)])),_:1,__:[16]}),a(k,{variant:"outline",class:"action-button",onClick:M},{default:l(()=>i[17]||(i[17]=[t("i",{class:"pi pi-list"},null,-1),t("span",null,"Tutti i Procedimenti",-1)])),_:1,__:[17]}),a(k,{variant:"outline",class:"action-button",onClick:q},{default:l(()=>i[18]||(i[18]=[t("i",{class:"pi pi-home"},null,-1),t("span",null,"I Miei Immobili",-1)])),_:1,__:[18]})])]),_:1})])])])]))}}),ci=T(Zt,[["__scopeId","data-v-e7b1a558"]]);export{ci as default};
