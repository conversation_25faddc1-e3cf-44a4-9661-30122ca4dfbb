import{A as a}from"./AdminNavbar-jBevcaIl.js";import{_ as o}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{P as s}from"./PageHeader-B4SpV5zp.js";import{E as r}from"./EmptyState-rMfszj3C.js";import{d as i,c as p,a as t,b as e,w as n,o as c,_ as d}from"./index-Bibk5VaL.js";import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const _={class:"admin-layout"},m={class:"admin-content"},l={class:"reports-main"},h={class:"reports-section"},f=i({__name:"ReportDashboard",setup(b){return(u,v)=>(c(),p("div",_,[t(a),e("div",m,[e("main",l,[t(s,{title:"Report e Statistiche",subtitle:"Analytics e reporting per procedimenti espropriativi"}),e("section",h,[t(o,{title:"Dashboard Report"},{default:n(()=>[t(r,{icon:"pi pi-chart-bar",title:"Dashboard Report",description:"Sezione in sviluppo per analytics e report avanzati."})]),_:1})])])])]))}}),N=d(f,[["__scopeId","data-v-b0d77578"]]);export{N as default};
