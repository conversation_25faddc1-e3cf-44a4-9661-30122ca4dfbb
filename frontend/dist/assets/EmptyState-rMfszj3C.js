import{d as n,c as e,b as o,h as a,n as c,t as i,H as p,o as s,_ as r}from"./index-Bibk5VaL.js";const l={class:"empty-state"},d={class:"empty-title"},m={key:0,class:"empty-description"},_={key:1,class:"empty-actions"},y=n({__name:"EmptyState",props:{icon:{},title:{},description:{}},setup(h){return(t,u)=>(s(),e("div",l,[o("i",{class:c([t.icon,"empty-icon"])},null,2),o("h3",d,i(t.title),1),t.description?(s(),e("p",m,i(t.description),1)):a("",!0),t.$slots.actions?(s(),e("div",_,[p(t.$slots,"actions",{},void 0,!0)])):a("",!0)]))}}),k=r(y,[["__scopeId","data-v-34e637f5"]]);export{k as E};
