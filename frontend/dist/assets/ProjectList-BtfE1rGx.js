import{d as st,g as u,m as _,A as ot,p as at,c as C,a as d,b as e,B as g,h as P,w as l,j as S,v as lt,k as I,e as B,t as a,f as n,F as nt,q as rt,n as R,u as ut,o as r,_ as dt}from"./index-Bibk5VaL.js";import{A as pt}from"./AdminNavbar-jBevcaIl.js";import{P as vt}from"./PageHeader-B4SpV5zp.js";import{_ as D}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as y}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{_ as F}from"./BaseModal.vue_vue_type_script_setup_true_lang-CIvlL0EO.js";import{L as ct}from"./LoadingState-9ob6nEfb.js";import{E as mt}from"./ErrorState-DJld4rs8.js";import{E as ft}from"./EmptyState-rMfszj3C.js";/* empty css                        */import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const gt={class:"admin-layout"},yt={class:"admin-content"},bt={class:"projects-main"},Ct={class:"stats-section"},Pt={class:"stats-grid"},kt={class:"stat-card"},Et={class:"stat-content"},wt={class:"stat-card"},zt={class:"stat-content"},$t={class:"stat-card"},xt={class:"stat-content"},Tt={class:"stat-card"},_t={class:"stat-content"},St={class:"filters-section"},Dt={class:"filters-grid"},jt={class:"filter-group"},Nt={class:"search-input-group"},Vt={class:"filter-group"},At={class:"filter-group"},Lt={class:"filter-group"},Mt={class:"table-section"},It={class:"table-header"},Bt={class:"table-title"},Ft={key:3,class:"projects-table-container"},Ut={class:"projects-table"},Rt={class:"project-cell"},Ht={class:"project-cell-content"},ht={class:"project-info-main"},qt={class:"project-name"},Gt={key:0,class:"project-description"},Ot={class:"type-badge"},Qt={class:"date-cell"},Jt={class:"table-actions"},Kt=["onClick"],Wt=["onClick"],Xt=["onClick"],Yt={key:0,class:"pagination"},Zt={class:"pagination-info"},te={class:"modal-placeholder"},ee={class:"modal-actions"},ie={class:"modal-placeholder"},se={class:"modal-actions"},oe={class:"delete-confirmation"},ae={class:"modal-actions"},U=10,le=st({__name:"ProjectList",setup(ne){const H=ut(),j=u(!1),k=u(""),N=u(!1),c=u([]),p=u(null),V=u(!1),$=u(!1),E=u(!1),o=u({search:"",status:"",type:"",entity:""}),m=u(1),w=u("created_at"),x=u("desc"),T=_(()=>({planning:c.value.filter(i=>i.status==="planning").length,active:c.value.filter(i=>["feasibility","preliminary","definitive","executive","expropriation","execution"].includes(i.status)).length,completed:c.value.filter(i=>i.status==="completed").length,total:c.value.length})),z=_(()=>{let i=[...c.value];if(o.value.search){const t=o.value.search.toLowerCase();i=i.filter(s=>s.name.toLowerCase().includes(t)||s.entity.toLowerCase().includes(t)||s.municipality.toLowerCase().includes(t)||s.description&&s.description.toLowerCase().includes(t))}return o.value.status&&(i=i.filter(t=>t.status===o.value.status)),o.value.type&&(i=i.filter(t=>t.type===o.value.type)),o.value.entity&&(i=i.filter(t=>t.entity===o.value.entity)),i.sort((t,s)=>{let v=t[w.value],b=s[w.value];return w.value==="created_at"&&(v=new Date(v),b=new Date(b)),x.value==="asc"?v<b?-1:v>b?1:0:v>b?-1:v<b?1:0}),i}),A=_(()=>Math.ceil(z.value.length/U)),h=_(()=>{const i=(m.value-1)*U;return z.value.slice(i,i+U)});ot(o,()=>{m.value=1},{deep:!0});const f=async()=>{j.value=!0,k.value="";try{const i=localStorage.getItem("token"),t=await fetch("/api/projects/",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const s=await t.json();c.value=Array.isArray(s)?s:s.projects||[]}catch(i){k.value="Errore nel caricamento dei progetti",console.error("Error loading projects:",i)}finally{j.value=!1}},q=()=>{f()},L=i=>{w.value===i?x.value=x.value==="asc"?"desc":"asc":(w.value=i,x.value="asc")},G=i=>{H.push(`/admin/projects/${i.id}`)},O=i=>{p.value=i,$.value=!0},Q=i=>{p.value=i,E.value=!0},J=async()=>{if(p.value){N.value=!0;try{await new Promise(i=>setTimeout(i,1e3)),c.value=c.value.filter(i=>i.id!==p.value.id),E.value=!1,p.value=null}catch{k.value="Errore nell'eliminazione del progetto"}finally{N.value=!1}}};let M=null;const K=()=>{M&&clearTimeout(M),M=setTimeout(()=>{f()},300)},W=(i,t)=>i?i.length>t?i.substring(0,t)+"...":i:"",X=i=>new Date(i).toLocaleDateString("it-IT"),Y=i=>({comune:"Comune",provincia:"Provincia",regione:"Regione",stato:"Stato"})[i]||i,Z=i=>({planning:"Pianificazione",feasibility:"Studio Fattibilità",preliminary:"Progetto Preliminare",definitive:"Progetto Definitivo",executive:"Progetto Esecutivo",expropriation:"Espropriazione",execution:"Esecuzione",completed:"Completato"})[i]||i,tt=i=>({infrastructure:"Infrastrutture",urban:"Urbanistica",transport:"Trasporti",environment:"Ambiente",housing:"Edilizia"})[i]||i,et=i=>`entity-${i}`,it=i=>({planning:"status-planning",feasibility:"status-feasibility",preliminary:"status-preliminary",definitive:"status-definitive",executive:"status-executive",expropriation:"status-expropriation",execution:"status-execution",completed:"status-completed"})[i]||"status-default";return at(()=>{f()}),(i,t)=>(r(),C("div",gt,[d(pt),e("div",yt,[e("main",bt,[d(vt,{title:"Gestione Progetti",subtitle:"Crea e gestisci progetti espropriativi secondo D.P.R. 327/2001"},{actions:l(()=>[e("button",{onClick:t[0]||(t[0]=s=>i.$router.push("/admin/projects/create")),class:"btn btn-primary"},t[17]||(t[17]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),n(" Nuovo Progetto ")]))]),_:1}),e("section",Ct,[e("div",Pt,[d(D,null,{default:l(()=>[e("div",kt,[t[19]||(t[19]=e("div",{class:"stat-icon planning"},[e("i",{class:"pi pi-file-o"})],-1)),e("div",Et,[e("h3",null,a(T.value.planning),1),t[18]||(t[18]=e("p",null,"In Pianificazione",-1))])])]),_:1}),d(D,null,{default:l(()=>[e("div",wt,[t[21]||(t[21]=e("div",{class:"stat-icon active"},[e("i",{class:"pi pi-cog"})],-1)),e("div",zt,[e("h3",null,a(T.value.active),1),t[20]||(t[20]=e("p",null,"In Corso",-1))])])]),_:1}),d(D,null,{default:l(()=>[e("div",$t,[t[23]||(t[23]=e("div",{class:"stat-icon completed"},[e("i",{class:"pi pi-check-circle"})],-1)),e("div",xt,[e("h3",null,a(T.value.completed),1),t[22]||(t[22]=e("p",null,"Completati",-1))])])]),_:1}),d(D,null,{default:l(()=>[e("div",Tt,[t[25]||(t[25]=e("div",{class:"stat-icon total"},[e("i",{class:"pi pi-folder"})],-1)),e("div",_t,[e("h3",null,a(T.value.total),1),t[24]||(t[24]=e("p",null,"Totale Progetti",-1))])])]),_:1})])]),e("div",St,[e("div",Dt,[e("div",jt,[t[27]||(t[27]=e("label",{class:"filter-label"},"Cerca",-1)),e("div",Nt,[t[26]||(t[26]=e("i",{class:"pi pi-search search-icon"},null,-1)),S(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.value.search=s),onInput:K,type:"text",class:"search-input",placeholder:"Cerca per nome progetto, ente, comune..."},null,544),[[lt,o.value.search]])])]),e("div",Vt,[t[29]||(t[29]=e("label",{class:"filter-label"},"Stato",-1)),S(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>o.value.status=s),onChange:f,class:"filter-select"},t[28]||(t[28]=[B('<option value="" data-v-00575637>Tutti gli stati</option><option value="planning" data-v-00575637>Pianificazione</option><option value="feasibility" data-v-00575637>Studio Fattibilità</option><option value="preliminary" data-v-00575637>Progetto Preliminare</option><option value="definitive" data-v-00575637>Progetto Definitivo</option><option value="executive" data-v-00575637>Progetto Esecutivo</option><option value="expropriation" data-v-00575637>Espropriazione</option><option value="execution" data-v-00575637>Esecuzione</option><option value="completed" data-v-00575637>Completato</option>',9)]),544),[[I,o.value.status]])]),e("div",At,[t[31]||(t[31]=e("label",{class:"filter-label"},"Tipo Progetto",-1)),S(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>o.value.type=s),onChange:f,class:"filter-select"},t[30]||(t[30]=[B('<option value="" data-v-00575637>Tutti i tipi</option><option value="infrastructure" data-v-00575637>Infrastrutture</option><option value="urban" data-v-00575637>Urbanistica</option><option value="transport" data-v-00575637>Trasporti</option><option value="environment" data-v-00575637>Ambiente</option><option value="housing" data-v-00575637>Edilizia</option>',6)]),544),[[I,o.value.type]])]),e("div",Lt,[t[33]||(t[33]=e("label",{class:"filter-label"},"Ente",-1)),S(e("select",{"onUpdate:modelValue":t[4]||(t[4]=s=>o.value.entity=s),onChange:f,class:"filter-select"},t[32]||(t[32]=[B('<option value="" data-v-00575637>Tutti gli enti</option><option value="comune" data-v-00575637>Comune</option><option value="provincia" data-v-00575637>Provincia</option><option value="regione" data-v-00575637>Regione</option><option value="stato" data-v-00575637>Stato</option>',5)]),544),[[I,o.value.entity]])])])]),e("div",Mt,[e("div",It,[e("div",Bt,[e("h3",null,"Progetti ("+a(z.value.length)+")",1)]),e("div",{class:"table-actions"},[e("button",{onClick:q,class:"btn btn-secondary btn-sm"},t[34]||(t[34]=[e("i",{class:"pi pi-refresh btn-icon"},null,-1),n(" Aggiorna ")]))])]),j.value?(r(),g(ct,{key:0,message:"Caricamento progetti..."})):k.value?(r(),g(mt,{key:1,message:k.value,onRetry:f},null,8,["message"])):z.value.length===0?(r(),g(ft,{key:2,icon:"pi pi-folder-open",title:"Nessun progetto trovato",description:o.value.search||o.value.status||o.value.type?"Nessun progetto corrisponde ai filtri selezionati.":"Non ci sono ancora progetti nel sistema."},{default:l(()=>[!o.value.search&&!o.value.status&&!o.value.type?(r(),g(y,{key:0,variant:"primary",onClick:t[5]||(t[5]=s=>i.$router.push("/admin/projects/create")),icon:"pi pi-plus"},{default:l(()=>t[35]||(t[35]=[n(" Crea il primo progetto ")])),_:1,__:[35]})):P("",!0)]),_:1},8,["description"])):(r(),C("div",Ft,[e("table",Ut,[e("thead",null,[e("tr",null,[e("th",{onClick:t[6]||(t[6]=s=>L("name")),class:"sortable"},t[36]||(t[36]=[n(" Nome Progetto "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[39]||(t[39]=e("th",null,"Ente",-1)),t[40]||(t[40]=e("th",null,"Comune",-1)),e("th",{onClick:t[7]||(t[7]=s=>L("status")),class:"sortable"},t[37]||(t[37]=[n(" Stato "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[41]||(t[41]=e("th",null,"Tipo",-1)),e("th",{onClick:t[8]||(t[8]=s=>L("created_at")),class:"sortable"},t[38]||(t[38]=[n(" Data Creazione "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[42]||(t[42]=e("th",null,"Azioni",-1))])]),e("tbody",null,[(r(!0),C(nt,null,rt(h.value,s=>(r(),C("tr",{key:s.id,class:"project-row"},[e("td",Rt,[e("div",Ht,[e("div",ht,[e("div",qt,a(s.name),1),s.description?(r(),C("div",Gt,a(W(s.description,60)),1)):P("",!0)])])]),e("td",null,[e("span",{class:R(["entity-badge",et(s.entity)])},a(Y(s.entity)),3)]),e("td",null,a(s.municipality),1),e("td",null,[e("span",{class:R(["status-badge",it(s.status)])},a(Z(s.status)),3)]),e("td",null,[e("span",Ot,a(tt(s.type)),1)]),e("td",null,[e("span",Qt,a(X(s.created_at)),1)]),e("td",null,[e("div",Jt,[e("button",{onClick:v=>G(s),class:"action-btn",title:"Visualizza dettagli"},t[43]||(t[43]=[e("i",{class:"pi pi-eye action-icon"},null,-1)]),8,Kt),e("button",{onClick:v=>O(s),class:"action-btn",title:"Modifica progetto"},t[44]||(t[44]=[e("i",{class:"pi pi-pencil action-icon"},null,-1)]),8,Wt),e("button",{onClick:v=>Q(s),class:"action-btn danger",title:"Elimina progetto"},t[45]||(t[45]=[e("i",{class:"pi pi-trash action-icon"},null,-1)]),8,Xt)])])]))),128))])]),A.value>1?(r(),C("div",Yt,[d(y,{variant:"secondary",size:"sm",disabled:m.value===1,onClick:t[9]||(t[9]=s=>m.value--),icon:"pi pi-chevron-left"},null,8,["disabled"]),e("span",Zt," Pagina "+a(m.value)+" di "+a(A.value)+" ("+a(z.value.length)+" progetti) ",1),d(y,{variant:"secondary",size:"sm",disabled:m.value===A.value,onClick:t[10]||(t[10]=s=>m.value++),icon:"pi pi-chevron-right"},null,8,["disabled"])])):P("",!0)]))]),V.value?(r(),g(F,{key:0,onClose:t[12]||(t[12]=s=>V.value=!1),title:"Nuovo Progetto",size:"large"},{default:l(()=>[e("div",te,[t[47]||(t[47]=e("p",null,"Form creazione progetto in sviluppo",-1)),e("div",ee,[d(y,{variant:"secondary",onClick:t[11]||(t[11]=s=>V.value=!1)},{default:l(()=>t[46]||(t[46]=[n(" Chiudi ")])),_:1,__:[46]})])])]),_:1})):P("",!0),$.value&&p.value?(r(),g(F,{key:1,onClose:t[14]||(t[14]=s=>$.value=!1),title:"Modifica Progetto",size:"large"},{default:l(()=>[e("div",ie,[t[50]||(t[50]=e("p",null,"Form modifica progetto in sviluppo",-1)),e("p",null,[t[48]||(t[48]=e("strong",null,"Progetto:",-1)),n(" "+a(p.value.name),1)]),e("div",se,[d(y,{variant:"secondary",onClick:t[13]||(t[13]=s=>$.value=!1)},{default:l(()=>t[49]||(t[49]=[n(" Chiudi ")])),_:1,__:[49]})])])]),_:1})):P("",!0),E.value&&p.value?(r(),g(F,{key:2,onClose:t[16]||(t[16]=s=>E.value=!1),title:"Conferma Eliminazione",size:"small"},{default:l(()=>[e("div",oe,[t[55]||(t[55]=e("div",{class:"warning-icon"},[e("i",{class:"pi pi-exclamation-triangle"})],-1)),t[56]||(t[56]=e("h3",null,"Elimina Progetto",-1)),e("p",null,[t[51]||(t[51]=n(" Sei sicuro di voler eliminare il progetto ")),e("strong",null,'"'+a(p.value.name)+'"',1),t[52]||(t[52]=n("? "))]),t[57]||(t[57]=e("p",{class:"warning-text"}," Questa azione eliminerà anche tutte le particelle, i procedimenti e i documenti associati al progetto. ",-1)),e("div",ae,[d(y,{variant:"secondary",onClick:t[15]||(t[15]=s=>E.value=!1)},{default:l(()=>t[53]||(t[53]=[n(" Annulla ")])),_:1,__:[53]}),d(y,{variant:"danger",onClick:J,loading:N.value},{default:l(()=>t[54]||(t[54]=[n(" Elimina Progetto ")])),_:1,__:[54]},8,["loading"])])])]),_:1})):P("",!0)])])]))}}),Pe=dt(le,[["__scopeId","data-v-00575637"]]);export{Pe as default};
