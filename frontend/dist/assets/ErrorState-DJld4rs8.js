import{d as a,c as o,b as t,h as n,t as c,H as i,o as r,_ as d}from"./index-Bibk5VaL.js";const l={class:"error-state"},p={class:"error-text"},_={key:0,class:"error-actions"},m=a({__name:"ErrorState",props:{message:{}},setup(u){return(s,e)=>(r(),o("div",l,[e[0]||(e[0]=t("i",{class:"pi pi-exclamation-triangle error-icon"},null,-1)),t("p",p,c(s.message),1),s.$slots.actions?(r(),o("div",_,[i(s.$slots,"actions",{},void 0,!0)])):n("",!0)]))}}),g=d(m,[["__scopeId","data-v-8c7ddb98"]]);export{g as E};
