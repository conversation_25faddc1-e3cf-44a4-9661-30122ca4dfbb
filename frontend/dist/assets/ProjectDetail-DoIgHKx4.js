import{d as k,g as _,c as l,b as e,a as $,r as J,w as U,t as o,h as C,n as g,f,o as c,_ as y,m as j,F as I,q as T,e as S,s as M,p as W,B as L,j as A,C as R,D as X,u as Y}from"./index-Bibk5VaL.js";import{A as ee}from"./AdminNavbar-jBevcaIl.js";import{L as te}from"./LoadingState-9ob6nEfb.js";import{E as se}from"./ErrorState-DJld4rs8.js";import"./BaseNavbar-BSvAFYsY.js";const ae={class:"project-header"},ie={class:"breadcrumb"},oe={class:"breadcrumb-current"},ne={class:"header-content"},ce={class:"header-left"},le={class:"project-title-section"},de={class:"project-title"},re={class:"project-meta"},pe={class:"project-code"},ue={class:"project-phase"},ve={key:0,class:"project-description"},me={class:"header-actions"},be={class:"action-dropdown"},ge={key:0,class:"dropdown-menu"},_e={class:"key-info-bar"},he={class:"info-item"},fe={class:"info-content"},$e={class:"info-value"},ke={class:"info-item"},ye={class:"info-content"},we={class:"info-value"},De={class:"info-item"},Pe={class:"info-content"},je={class:"info-value"},ze={class:"info-item"},Ce={class:"info-content"},Ie={class:"info-value"},Te={class:"info-item"},Se={class:"info-content"},Ae={class:"info-value"},Re=k({__name:"ProjectHeader",props:{project:{}},emits:["refresh","edit","export"],setup(h,{emit:u}){const p=_(!1),d=n=>{const a="status-badge";switch(n){case"active":return`${a} status-active`;case"draft":return`${a} status-draft`;case"completed":return`${a} status-completed`;case"suspended":return`${a} status-suspended`;default:return`${a} status-unknown`}},b=n=>{switch(n){case"active":return"pi pi-play-circle";case"draft":return"pi pi-file";case"completed":return"pi pi-check-circle";case"suspended":return"pi pi-pause-circle";default:return"pi pi-question-circle"}},m=n=>{switch(n){case"active":return"Attivo";case"draft":return"Bozza";case"completed":return"Completato";case"suspended":return"Sospeso";default:return"Sconosciuto"}},v=n=>{switch(n){case"setup":return"Setup Iniziale";case"preparatorie":return"Operazioni Preparatorie";case"vincolo":return"Apposizione Vincolo";case"pubblica_utilita":return"Dichiarazione Pubblica Utilità";case"indennita":return"Determinazione Indennità";case"conclusione":return"Conclusione";default:return"Non definita"}},r=n=>n?new Date(n).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"}):"Non definita",s=()=>{console.log("Duplicate project")},t=()=>{console.log("Archive project")},i=()=>{console.log("Delete project")};return document.addEventListener("click",n=>{var a;(a=n.target)!=null&&a.closest(".action-dropdown")||(p.value=!1)}),(n,a)=>{const z=J("router-link");return c(),l("div",ae,[e("nav",ie,[$(z,{to:"/admin/projects",class:"breadcrumb-link"},{default:U(()=>a[5]||(a[5]=[e("i",{class:"pi pi-home"},null,-1),f(" Progetti ")])),_:1,__:[5]}),a[6]||(a[6]=e("i",{class:"pi pi-angle-right breadcrumb-separator"},null,-1)),e("span",oe,o(n.project.title),1)]),e("div",ne,[e("div",ce,[e("div",le,[e("h1",de,o(n.project.title),1),e("div",re,[e("span",{class:g([d(n.project.status),"status-badge"])},[e("i",{class:g(b(n.project.status))},null,2),f(" "+o(m(n.project.status)),1)],2),e("span",pe,"Codice: "+o(n.project.code||`PRJ-${n.project.id}`),1),e("span",ue,[a[7]||(a[7]=e("i",{class:"pi pi-flag"},null,-1)),f(" Fase: "+o(v(n.project.phase)),1)])])]),n.project.description?(c(),l("div",ve,[e("p",null,o(n.project.description),1)])):C("",!0)]),e("div",me,[e("button",{onClick:a[0]||(a[0]=E=>n.$emit("refresh")),class:"btn btn-secondary",title:"Aggiorna dati"},a[8]||(a[8]=[e("i",{class:"pi pi-refresh"},null,-1)])),e("div",be,[e("button",{onClick:a[1]||(a[1]=E=>p.value=!p.value),class:"btn btn-secondary dropdown-toggle"},a[9]||(a[9]=[e("i",{class:"pi pi-ellipsis-v"},null,-1),f(" Azioni ")])),p.value?(c(),l("div",ge,[e("button",{onClick:a[2]||(a[2]=E=>n.$emit("edit")),class:"dropdown-item"},a[10]||(a[10]=[e("i",{class:"pi pi-pencil"},null,-1),f(" Modifica Progetto ")])),e("button",{onClick:a[3]||(a[3]=E=>n.$emit("export")),class:"dropdown-item"},a[11]||(a[11]=[e("i",{class:"pi pi-download"},null,-1),f(" Esporta Dati ")])),e("button",{onClick:s,class:"dropdown-item"},a[12]||(a[12]=[e("i",{class:"pi pi-copy"},null,-1),f(" Duplica Progetto ")])),a[15]||(a[15]=e("div",{class:"dropdown-separator"},null,-1)),e("button",{onClick:t,class:"dropdown-item text-orange"},a[13]||(a[13]=[e("i",{class:"pi pi-archive"},null,-1),f(" Archivia ")])),e("button",{onClick:i,class:"dropdown-item text-danger"},a[14]||(a[14]=[e("i",{class:"pi pi-trash"},null,-1),f(" Elimina ")]))])):C("",!0)]),e("button",{onClick:a[4]||(a[4]=E=>n.$emit("edit")),class:"btn btn-primary"},a[16]||(a[16]=[e("i",{class:"pi pi-pencil"},null,-1),f(" Modifica ")]))])]),e("div",_e,[e("div",he,[a[18]||(a[18]=e("i",{class:"pi pi-user info-icon"},null,-1)),e("div",fe,[a[17]||(a[17]=e("span",{class:"info-label"},"RUP",-1)),e("span",$e,o(n.project.rup_name||"Non assegnato"),1)])]),e("div",ke,[a[20]||(a[20]=e("i",{class:"pi pi-users info-icon"},null,-1)),e("div",ye,[a[19]||(a[19]=e("span",{class:"info-label"},"RPE",-1)),e("span",we,o(n.project.rpe_name||"Non assegnato"),1)])]),e("div",De,[a[22]||(a[22]=e("i",{class:"pi pi-calendar info-icon"},null,-1)),e("div",Pe,[a[21]||(a[21]=e("span",{class:"info-label"},"Inizio",-1)),e("span",je,o(r(n.project.start_date)),1)])]),e("div",ze,[a[24]||(a[24]=e("i",{class:"pi pi-clock info-icon"},null,-1)),e("div",Ce,[a[23]||(a[23]=e("span",{class:"info-label"},"Scadenza",-1)),e("span",Ie,o(r(n.project.expected_end_date)),1)])]),e("div",Te,[a[26]||(a[26]=e("i",{class:"pi pi-map-marker info-icon"},null,-1)),e("div",Se,[a[25]||(a[25]=e("span",{class:"info-label"},"Località",-1)),e("span",Ae,o(n.project.municipality)+", "+o(n.project.region),1)])])])])}}}),Me=y(Re,[["__scopeId","data-v-b429b89d"]]),Ee={class:"overview-card"},Ne={class:"card-header"},Fe={class:"card-subtitle"},Le={class:"card-content"},Ue={class:"progress-section"},Ve={class:"progress-circle"},qe={class:"progress-ring",width:"100",height:"100"},xe=["stroke-dasharray","stroke-dashoffset"],Ge={class:"progress-text"},Be={class:"progress-percentage"},Oe={class:"progress-details"},Qe={class:"progress-item"},Ke={class:"progress-item-value"},Ze={class:"progress-item"},He={class:"progress-item-value"},Je={class:"phase-timeline"},We={class:"timeline-items"},Xe={class:"timeline-marker"},Ye={class:"timeline-content"},et={class:"timeline-phase"},tt={class:"timeline-status"},st=k({__name:"ProjectOverviewCard",props:{project:{},progress:{}},setup(h){const u=h,p=[{id:1,name:"Operazioni Preparatorie",status:"completed"},{id:2,name:"Apposizione Vincolo",status:"in_progress"},{id:3,name:"Pubblica Utilità",status:"pending"},{id:4,name:"Determinazione Indennità",status:"pending"}],d=j(()=>2*Math.PI*45),b=j(()=>d.value-u.progress.percentage/100*d.value),m=t=>{switch(t){case"preparatorie":return"Operazioni Preparatorie";case"vincolo":return"Apposizione Vincolo";case"pubblica_utilita":return"Dichiarazione Pubblica Utilità";case"indennita":return"Determinazione Indennità";default:return"Fase sconosciuta"}},v=t=>t<u.progress.completedPhases?"completed":t===u.progress.completedPhases?"in_progress":"pending",r=t=>{switch(v(t)){case"completed":return"pi pi-check-circle";case"in_progress":return"pi pi-clock";case"pending":return"pi pi-circle";default:return"pi pi-circle"}},s=t=>{switch(v(t)){case"completed":return"Completata";case"in_progress":return"In corso";case"pending":return"In attesa";default:return"Sconosciuto"}};return(t,i)=>(c(),l("div",Ee,[e("div",Ne,[i[0]||(i[0]=e("div",{class:"card-title"},[e("i",{class:"pi pi-chart-bar card-icon"}),e("h3",null,"Avanzamento Progetto")],-1)),e("div",Fe," Fase "+o(t.progress.currentPhase)+" di "+o(t.progress.totalPhases),1)]),e("div",Le,[e("div",Ue,[e("div",Ve,[(c(),l("svg",qe,[i[1]||(i[1]=e("circle",{class:"progress-ring-bg",cx:"50",cy:"50",r:"45"},null,-1)),e("circle",{class:"progress-ring-fill",cx:"50",cy:"50",r:"45","stroke-dasharray":d.value,"stroke-dashoffset":b.value},null,8,xe)])),e("div",Ge,[e("span",Be,o(t.progress.percentage)+"%",1),i[2]||(i[2]=e("span",{class:"progress-label"},"Completato",-1))])]),e("div",Oe,[e("div",Qe,[i[3]||(i[3]=e("span",{class:"progress-item-label"},"Fasi Completate",-1)),e("span",Ke,o(t.progress.completedPhases)+"/"+o(t.progress.totalPhases),1)]),e("div",Ze,[i[4]||(i[4]=e("span",{class:"progress-item-label"},"Fase Corrente",-1)),e("span",He,o(m(t.progress.currentPhase)),1)])])]),e("div",Je,[i[5]||(i[5]=e("h4",{class:"timeline-title"},"Timeline Fasi",-1)),e("div",We,[(c(),l(I,null,T(p,(n,a)=>e("div",{key:n.id,class:g(["timeline-item",v(a)])},[e("div",Xe,[e("i",{class:g(r(a))},null,2)]),e("div",Ye,[e("div",et,o(n.name),1),e("div",tt,o(s(a)),1)])],2)),64))])])])]))}}),at=y(st,[["__scopeId","data-v-4ee308f9"]]),it={class:"overview-card"},ot={class:"card-content"},nt={class:"score-section"},ct={class:"score-circle"},lt={class:"score-details"},dt={class:"score-item"},rt={class:"score-indicator critical"},pt={class:"score-number"},ut={class:"score-item"},vt={class:"score-indicator warning"},mt={class:"score-number"},bt={class:"deadline-section"},gt={class:"deadline-content"},_t={class:"deadline-date"},ht={class:"deadline-countdown"},ft={class:"checklist-section"},$t={class:"checklist-items"},kt={class:"checklist-icon"},yt={class:"checklist-content"},wt={class:"checklist-text"},Dt={key:0,class:"checklist-deadline"},Pt={key:0,class:"checklist-badge warning"},jt={key:1,class:"checklist-badge error"},zt=k({__name:"ComplianceCard",props:{project:{},compliance:{}},setup(h){const u=h,p=[{id:1,requirement:"Avviso avvio procedimento (Art. 11)",status:"completed",deadline:null},{id:2,requirement:"Autorizzazione accesso (Art. 15)",status:"completed",deadline:null},{id:3,requirement:"Gestione osservazioni (30gg)",status:"warning",deadline:"2025-01-15"},{id:4,requirement:"Dichiarazione pubblica utilità",status:"pending",deadline:"2025-03-01"}],d=j(()=>{if(!u.compliance.nextDeadline)return 0;const s=new Date(u.compliance.nextDeadline),t=new Date,i=s.getTime()-t.getTime();return Math.ceil(i/(1e3*60*60*24))}),b=s=>s>=90?"excellent":s>=80?"good":s>=70?"warning":"critical",m=s=>s<=7?"critical":s<=14?"warning":"normal",v=s=>{switch(s){case"completed":return"pi pi-check-circle";case"warning":return"pi pi-exclamation-triangle";case"error":return"pi pi-times-circle";case"pending":return"pi pi-clock";default:return"pi pi-circle"}},r=s=>s?new Date(s).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"}):"Non definita";return(s,t)=>(c(),l("div",it,[t[8]||(t[8]=e("div",{class:"card-header"},[e("div",{class:"card-title"},[e("i",{class:"pi pi-shield card-icon"}),e("h3",null,"Compliance Normativa")]),e("div",{class:"card-subtitle"}," Conformità D.P.R. 327/2001 ")],-1)),e("div",ot,[e("div",nt,[e("div",ct,[e("div",{class:g(["score-value",b(s.compliance.overallScore)])},o(s.compliance.overallScore),3),t[0]||(t[0]=e("div",{class:"score-label"},"Score",-1))]),e("div",lt,[e("div",dt,[e("div",rt,[t[1]||(t[1]=e("i",{class:"pi pi-exclamation-triangle"},null,-1)),e("span",pt,o(s.compliance.criticalIssues),1)]),t[2]||(t[2]=e("span",{class:"score-text"},"Problemi Critici",-1))]),e("div",ut,[e("div",vt,[t[3]||(t[3]=e("i",{class:"pi pi-exclamation-circle"},null,-1)),e("span",mt,o(s.compliance.warnings),1)]),t[4]||(t[4]=e("span",{class:"score-text"},"Avvisi",-1))])])]),e("div",bt,[t[6]||(t[6]=e("div",{class:"deadline-header"},[e("i",{class:"pi pi-clock deadline-icon"}),e("span",{class:"deadline-title"},"Prossima Scadenza")],-1)),e("div",gt,[e("div",_t,o(r(s.compliance.nextDeadline)),1),e("div",ht,[e("span",{class:g(["countdown-value",m(d.value)])},o(d.value),3),t[5]||(t[5]=e("span",{class:"countdown-label"},"giorni rimanenti",-1))])])]),e("div",ft,[t[7]||(t[7]=e("h4",{class:"checklist-title"},"Requisiti Normativi",-1)),e("div",$t,[(c(),l(I,null,T(p,i=>e("div",{key:i.id,class:g(["checklist-item",i.status])},[e("div",kt,[e("i",{class:g(v(i.status))},null,2)]),e("div",yt,[e("span",wt,o(i.requirement),1),i.deadline?(c(),l("span",Dt," Scadenza: "+o(r(i.deadline)),1)):C("",!0)]),i.status==="warning"?(c(),l("div",Pt," Urgente ")):C("",!0),i.status==="error"?(c(),l("div",jt," Scaduto ")):C("",!0)],2)),64))])])])]))}}),Ct=y(zt,[["__scopeId","data-v-6031dba8"]]),It={class:"overview-card"},Tt={class:"card-content"},St={class:"budget-summary"},At={class:"budget-total"},Rt={class:"budget-amount"},Mt={class:"budget-progress"},Et={class:"progress-bar"},Nt={class:"budget-breakdown"},Ft={class:"breakdown-item spent"},Lt={class:"breakdown-content"},Ut={class:"breakdown-amount"},Vt={class:"breakdown-percentage"},qt={class:"breakdown-item committed"},xt={class:"breakdown-content"},Gt={class:"breakdown-amount"},Bt={class:"breakdown-percentage"},Ot={class:"breakdown-item remaining"},Qt={class:"breakdown-content"},Kt={class:"breakdown-amount"},Zt={class:"breakdown-percentage"},Ht={class:"budget-categories"},Jt={class:"categories-list"},Wt={class:"category-info"},Xt={class:"category-name"},Yt={class:"category-amount"},es={class:"category-progress"},ts=k({__name:"BudgetCard",props:{project:{},budget:{}},setup(h){const u=h,p=[{id:1,name:"Indennità di esproprio",amount:15e5},{id:2,name:"Spese tecniche e legali",amount:35e4},{id:3,name:"Spese catastali e registrazione",amount:15e4},{id:4,name:"Consulenze specialistiche",amount:2e5},{id:5,name:"Pubblicazioni e notifiche",amount:5e4}],d=j(()=>u.budget.spent/u.budget.total*100),b=j(()=>u.budget.committed/u.budget.total*100),m=j(()=>u.budget.remaining/u.budget.total*100),v=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR",minimumFractionDigits:0,maximumFractionDigits:0}).format(r);return(r,s)=>(c(),l("div",It,[s[9]||(s[9]=e("div",{class:"card-header"},[e("div",{class:"card-title"},[e("i",{class:"pi pi-euro card-icon"}),e("h3",null,"Budget Progetto")]),e("div",{class:"card-subtitle"}," Gestione economica e indennità ")],-1)),e("div",Tt,[e("div",St,[e("div",At,[s[0]||(s[0]=e("span",{class:"budget-label"},"Budget Totale",-1)),e("span",Rt,o(v(r.budget.total)),1)]),e("div",Mt,[e("div",Et,[e("div",{class:"progress-fill",style:M({width:`${d.value}%`})},null,4),e("div",{class:"progress-fill committed",style:M({width:`${b.value}%`,marginLeft:`${d.value}%`})},null,4)]),s[1]||(s[1]=S('<div class="progress-legend" data-v-92e9d042><div class="legend-item" data-v-92e9d042><div class="legend-color spent" data-v-92e9d042></div><span data-v-92e9d042>Speso</span></div><div class="legend-item" data-v-92e9d042><div class="legend-color committed" data-v-92e9d042></div><span data-v-92e9d042>Impegnato</span></div><div class="legend-item" data-v-92e9d042><div class="legend-color remaining" data-v-92e9d042></div><span data-v-92e9d042>Disponibile</span></div></div>',1))])]),e("div",Nt,[e("div",Ft,[s[3]||(s[3]=e("div",{class:"breakdown-icon"},[e("i",{class:"pi pi-credit-card"})],-1)),e("div",Lt,[s[2]||(s[2]=e("span",{class:"breakdown-label"},"Speso",-1)),e("span",Ut,o(v(r.budget.spent)),1),e("span",Vt,o(d.value.toFixed(1))+"%",1)])]),e("div",qt,[s[5]||(s[5]=e("div",{class:"breakdown-icon"},[e("i",{class:"pi pi-bookmark"})],-1)),e("div",xt,[s[4]||(s[4]=e("span",{class:"breakdown-label"},"Impegnato",-1)),e("span",Gt,o(v(r.budget.committed)),1),e("span",Bt,o(b.value.toFixed(1))+"%",1)])]),e("div",Ot,[s[7]||(s[7]=e("div",{class:"breakdown-icon"},[e("i",{class:"pi pi-wallet"})],-1)),e("div",Qt,[s[6]||(s[6]=e("span",{class:"breakdown-label"},"Disponibile",-1)),e("span",Kt,o(v(r.budget.remaining)),1),e("span",Zt,o(m.value.toFixed(1))+"%",1)])])]),e("div",Ht,[s[8]||(s[8]=e("h4",{class:"categories-title"},"Principali Voci di Spesa",-1)),e("div",Jt,[(c(),l(I,null,T(p,t=>e("div",{key:t.id,class:"category-item"},[e("div",Wt,[e("span",Xt,o(t.name),1),e("span",Yt,o(v(t.amount)),1)]),e("div",es,[e("div",{class:"category-bar",style:M({width:`${t.amount/r.budget.total*100}%`})},null,4)])])),64))])])])]))}}),ss=y(ts,[["__scopeId","data-v-92e9d042"]]),as={class:"overview-card"},is={class:"card-content"},os={class:"status-section"},ns={class:"status-current"},cs={class:"status-content"},ls={class:"status-value"},ds={class:"status-next"},rs={class:"status-content"},ps={class:"status-value"},us={class:"status-countdown"},vs={class:"milestones-section"},ms={class:"milestones-list"},bs={class:"milestone-date"},gs={class:"milestone-day"},_s={class:"milestone-month"},hs={class:"milestone-content"},fs={class:"milestone-name"},$s={class:"milestone-type"},ks={class:"milestone-countdown"},ys={class:"progress-section"},ws={class:"progress-header"},Ds={class:"progress-percentage"},Ps={class:"progress-bar"},js={class:"progress-info"},zs={class:"progress-elapsed"},Cs={class:"progress-remaining"},Is=k({__name:"TimelineCard",props:{project:{},timeline:{}},setup(h){const u=h,p=[{id:1,name:"Scadenza osservazioni",type:"Normativa obbligatoria",date:"2025-01-15",daysRemaining:22},{id:2,name:"Dichiarazione pubblica utilità",type:"Milestone progetto",date:"2025-03-01",daysRemaining:67},{id:3,name:"Notifica decreti esproprio",type:"Attività procedurale",date:"2025-04-15",daysRemaining:112}],d=j(()=>{const t=new Date(u.timeline.startDate),i=new Date(u.timeline.endDate),n=new Date,a=i.getTime()-t.getTime(),z=n.getTime()-t.getTime();return Math.max(0,Math.min(100,z/a*100))}),b=j(()=>{const t=new Date(u.timeline.startDate),n=new Date().getTime()-t.getTime();return Math.max(0,Math.ceil(n/(1e3*60*60*24)))}),m=j(()=>{const t=new Date(u.timeline.endDate),i=new Date,n=t.getTime()-i.getTime();return Math.max(0,Math.ceil(n/(1e3*60*60*24)))}),v=t=>new Date(t).toLocaleDateString("it-IT",{day:"2-digit"}),r=t=>new Date(t).toLocaleDateString("it-IT",{month:"short"}).toUpperCase(),s=t=>t<=7?"critical":t<=30?"warning":"normal";return(t,i)=>(c(),l("div",as,[i[7]||(i[7]=e("div",{class:"card-header"},[e("div",{class:"card-title"},[e("i",{class:"pi pi-calendar card-icon"}),e("h3",null,"Timeline Progetto")]),e("div",{class:"card-subtitle"}," Scadenze e milestone ")],-1)),e("div",is,[e("div",os,[e("div",ns,[i[1]||(i[1]=e("div",{class:"status-icon current"},[e("i",{class:"pi pi-play-circle"})],-1)),e("div",cs,[i[0]||(i[0]=e("span",{class:"status-label"},"Milestone Corrente",-1)),e("span",ls,o(t.timeline.currentMilestone),1)])]),e("div",ds,[i[3]||(i[3]=e("div",{class:"status-icon next"},[e("i",{class:"pi pi-arrow-right"})],-1)),e("div",rs,[i[2]||(i[2]=e("span",{class:"status-label"},"Prossima Milestone",-1)),e("span",ps,o(t.timeline.nextMilestone),1),e("span",us,o(t.timeline.daysToNext)+" giorni",1)])])]),e("div",vs,[i[5]||(i[5]=e("h4",{class:"milestones-title"},"Prossime Scadenze",-1)),e("div",ms,[(c(),l(I,null,T(p,n=>e("div",{key:n.id,class:g(["milestone-item",s(n.daysRemaining)])},[e("div",bs,[e("span",gs,o(v(n.date)),1),e("span",_s,o(r(n.date)),1)]),e("div",hs,[e("span",fs,o(n.name),1),e("span",$s,o(n.type),1)]),e("div",ks,[e("span",{class:g(["countdown-days",s(n.daysRemaining)])},o(n.daysRemaining),3),i[4]||(i[4]=e("span",{class:"countdown-label"},"gg",-1))])],2)),64))])]),e("div",ys,[e("div",ws,[i[6]||(i[6]=e("span",{class:"progress-label"},"Avanzamento Temporale",-1)),e("span",Ds,o(d.value.toFixed(1))+"%",1)]),e("div",Ps,[e("div",{class:"progress-fill",style:M({width:`${d.value}%`})},null,4)]),e("div",js,[e("span",zs,o(b.value)+" giorni trascorsi",1),e("span",Cs,o(m.value)+" giorni rimanenti",1)])])])]))}}),Ts=y(Is,[["__scopeId","data-v-9419a18f"]]),Ss={class:"project-dashboard"},As={class:"kpi-section"},Rs={class:"kpi-grid"},Ms={class:"kpi-card"},Es={class:"kpi-content"},Ns={class:"kpi-value"},Fs={class:"kpi-card"},Ls={class:"kpi-content"},Us={class:"kpi-value"},Vs={class:"kpi-card"},qs={class:"kpi-content"},xs={class:"kpi-value"},Gs={class:"kpi-card"},Bs={class:"kpi-content"},Os={class:"kpi-value"},Qs={class:"charts-section"},Ks={class:"charts-grid"},Zs={class:"chart-card"},Hs={class:"chart-content"},Js={class:"phase-bars"},Ws={class:"phase-info"},Xs={class:"phase-name"},Ys={class:"phase-percentage"},ea={class:"phase-bar"},ta={class:"chart-card"},sa={class:"chart-content"},aa={class:"budget-chart"},ia={class:"budget-items"},oa={class:"budget-info"},na={class:"budget-category"},ca={class:"budget-amount"},la={class:"budget-percent"},da={class:"activities-section"},ra={class:"activities-timeline"},pa={class:"activity-content"},ua={class:"activity-header"},va={class:"activity-title"},ma={class:"activity-time"},ba={class:"activity-description"},ga={key:0,class:"activity-user"},_a=k({__name:"ProjectDashboard",props:{project:{},kpis:{}},setup(h){const u=[{id:1,name:"Operazioni Preparatorie",completion:100,status:"completed"},{id:2,name:"Apposizione Vincolo",completion:65,status:"in-progress"},{id:3,name:"Pubblica Utilità",completion:0,status:"pending"},{id:4,name:"Determinazione Indennità",completion:0,status:"pending"}],p=[{category:"Indennità Esproprio",amount:15e5,percentage:60,color:"#3b82f6"},{category:"Spese Tecniche",amount:35e4,percentage:14,color:"#10b981"},{category:"Spese Legali",amount:3e5,percentage:12,color:"#f59e0b"},{category:"Spese Catastali",amount:15e4,percentage:6,color:"#ef4444"},{category:"Altre Spese",amount:2e5,percentage:8,color:"#8b5cf6"}],d=[{id:1,type:"notification",title:"Notifiche inviate",description:"Inviate 45 notifiche di avvio procedimento ai proprietari interessati",user:"Mario Rossi",timestamp:"2025-01-10T14:30:00Z"},{id:2,type:"document",title:"Documento generato",description:"Creato avviso di avvio del procedimento espropriativo",user:"Laura Bianchi",timestamp:"2025-01-09T16:15:00Z"},{id:3,type:"deadline",title:"Scadenza imminente",description:"Termine per osservazioni scade tra 7 giorni",user:null,timestamp:"2025-01-08T09:00:00Z"},{id:4,type:"update",title:"Aggiornamento catastale",description:"Importati 12 nuovi dati catastali dal sistema SISTER",user:"Giuseppe Verdi",timestamp:"2025-01-07T11:45:00Z"}],b=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR",minimumFractionDigits:0,maximumFractionDigits:0}).format(r),m=r=>{const s=new Date(r),i=Math.floor((new Date().getTime()-s.getTime())/(1e3*60*60));if(i<1)return"Ora";if(i<24)return`${i}h fa`;const n=Math.floor(i/24);return n<7?`${n}g fa`:s.toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"})},v=r=>{switch(r){case"notification":return"pi pi-bell";case"document":return"pi pi-file";case"deadline":return"pi pi-clock";case"update":return"pi pi-refresh";default:return"pi pi-info-circle"}};return(r,s)=>(c(),l("div",Ss,[e("div",As,[s[8]||(s[8]=e("h3",{class:"section-title"},"Key Performance Indicators",-1)),e("div",Rs,[e("div",Ms,[s[1]||(s[1]=e("div",{class:"kpi-icon properties"},[e("i",{class:"pi pi-map"})],-1)),e("div",Es,[e("span",Ns,o(r.kpis.totalProperties),1),s[0]||(s[0]=e("span",{class:"kpi-label"},"Particelle Totali",-1))])]),e("div",Fs,[s[3]||(s[3]=e("div",{class:"kpi-icon notifications"},[e("i",{class:"pi pi-bell"})],-1)),e("div",Ls,[e("span",Us,o(r.kpis.notifiedOwners),1),s[2]||(s[2]=e("span",{class:"kpi-label"},"Proprietari Notificati",-1))])]),e("div",Vs,[s[5]||(s[5]=e("div",{class:"kpi-icon pending"},[e("i",{class:"pi pi-clock"})],-1)),e("div",qs,[e("span",xs,o(r.kpis.pendingResponses),1),s[4]||(s[4]=e("span",{class:"kpi-label"},"Risposte Pendenti",-1))])]),e("div",Gs,[s[7]||(s[7]=e("div",{class:"kpi-icon deadlines"},[e("i",{class:"pi pi-exclamation-triangle"})],-1)),e("div",Bs,[e("span",Os,o(r.kpis.legalDeadlines),1),s[6]||(s[6]=e("span",{class:"kpi-label"},"Scadenze Legali",-1))])])])]),e("div",Qs,[e("div",Ks,[e("div",Zs,[s[9]||(s[9]=S('<div class="chart-header" data-v-edcc90f1><h4 class="chart-title" data-v-edcc90f1>Avanzamento Fasi</h4><div class="chart-legend" data-v-edcc90f1><div class="legend-item" data-v-edcc90f1><div class="legend-dot completed" data-v-edcc90f1></div><span data-v-edcc90f1>Completate</span></div><div class="legend-item" data-v-edcc90f1><div class="legend-dot in-progress" data-v-edcc90f1></div><span data-v-edcc90f1>In corso</span></div><div class="legend-item" data-v-edcc90f1><div class="legend-dot pending" data-v-edcc90f1></div><span data-v-edcc90f1>Pianificate</span></div></div></div>',1)),e("div",Hs,[e("div",Js,[(c(),l(I,null,T(u,t=>e("div",{key:t.id,class:"phase-bar-item"},[e("div",Ws,[e("span",Xs,o(t.name),1),e("span",Ys,o(t.completion)+"%",1)]),e("div",ea,[e("div",{class:g(["phase-fill",t.status]),style:M({width:`${t.completion}%`})},null,6)])])),64))])])]),e("div",ta,[s[10]||(s[10]=e("div",{class:"chart-header"},[e("h4",{class:"chart-title"},"Distribuzione Budget")],-1)),e("div",sa,[e("div",aa,[e("div",ia,[(c(),l(I,null,T(p,t=>e("div",{key:t.category,class:"budget-item"},[e("div",{class:"budget-dot",style:M({backgroundColor:t.color})},null,4),e("div",oa,[e("span",na,o(t.category),1),e("span",ca,o(b(t.amount)),1),e("span",la,o(t.percentage)+"%",1)])])),64))])])])])])]),e("div",da,[s[12]||(s[12]=e("div",{class:"section-header"},[e("h3",{class:"section-title"},"Attività Recenti"),e("button",{class:"btn btn-secondary btn-sm"},[e("i",{class:"pi pi-external-link"}),f(" Vedi Tutte ")])],-1)),e("div",ra,[(c(),l(I,null,T(d,t=>e("div",{key:t.id,class:"activity-item"},[e("div",{class:g(["activity-marker",t.type])},[e("i",{class:g(v(t.type))},null,2)],2),e("div",pa,[e("div",ua,[e("span",va,o(t.title),1),e("span",ma,o(m(t.timestamp)),1)]),e("p",ba,o(t.description),1),t.user?(c(),l("div",ga,[s[11]||(s[11]=e("i",{class:"pi pi-user"},null,-1)),f(" "+o(t.user),1)])):C("",!0)])])),64))])]),s[13]||(s[13]=S('<div class="quick-actions-section" data-v-edcc90f1><h3 class="section-title" data-v-edcc90f1>Azioni Rapide</h3><div class="quick-actions-grid" data-v-edcc90f1><button class="quick-action-card" data-v-edcc90f1><div class="action-icon" data-v-edcc90f1><i class="pi pi-plus" data-v-edcc90f1></i></div><div class="action-content" data-v-edcc90f1><span class="action-title" data-v-edcc90f1>Aggiungi Particella</span><span class="action-subtitle" data-v-edcc90f1>Inserisci nuova particella catastale</span></div></button><button class="quick-action-card" data-v-edcc90f1><div class="action-icon" data-v-edcc90f1><i class="pi pi-send" data-v-edcc90f1></i></div><div class="action-content" data-v-edcc90f1><span class="action-title" data-v-edcc90f1>Invia Notifica</span><span class="action-subtitle" data-v-edcc90f1>Comunica con i proprietari</span></div></button><button class="quick-action-card" data-v-edcc90f1><div class="action-icon" data-v-edcc90f1><i class="pi pi-file" data-v-edcc90f1></i></div><div class="action-content" data-v-edcc90f1><span class="action-title" data-v-edcc90f1>Genera Documento</span><span class="action-subtitle" data-v-edcc90f1>Crea atto procedurale</span></div></button><button class="quick-action-card" data-v-edcc90f1><div class="action-icon" data-v-edcc90f1><i class="pi pi-chart-line" data-v-edcc90f1></i></div><div class="action-content" data-v-edcc90f1><span class="action-title" data-v-edcc90f1>Report Avanzamento</span><span class="action-subtitle" data-v-edcc90f1>Esporta stato progetto</span></div></button></div></div>',1))]))}}),ha=y(_a,[["__scopeId","data-v-edcc90f1"]]),fa={class:"procedure-phases"},$a={class:"phases-timeline"},ka={class:"phase-marker"},ya={class:"phase-number"},wa={class:"phase-content"},Da={class:"phase-header"},Pa={class:"phase-title"},ja={class:"phase-description"},za={class:"phase-meta"},Ca={class:"phase-dates"},Ia={class:"phase-date"},Ta={class:"phase-progress"},Sa={class:"progress-bar"},Aa={class:"progress-text"},Ra={key:0,class:"phase-tasks"},Ma={class:"tasks-list"},Ea={class:"task-name"},Na={class:"task-deadline"},Fa={class:"phase-actions"},La={key:0,class:"btn btn-secondary btn-sm"},Ua=k({__name:"ProcedurePhases",props:{project:{},phases:{}},emits:["phase-update"],setup(h,{emit:u}){const p=[{id:1,name:"Operazioni Preparatorie",description:"Autorizzazioni accesso (Art. 15), notifiche proprietari, documentazione stato luoghi",status:"completed",startDate:"2024-06-01",endDate:"2024-08-15",completion:100,tasks:[]},{id:2,name:"Apposizione Vincolo/Variante",description:"Avvio procedimento VPE, gestione osservazioni 30gg, pubblicazioni legali",status:"in_progress",startDate:"2024-08-16",endDate:"2025-02-28",completion:65,tasks:[{id:1,name:"Gestione osservazioni",status:"pending",deadline:"2025-01-15"},{id:2,name:"Pubblicazioni legali",status:"completed",deadline:"2024-12-30"}]},{id:3,name:"Dichiarazione Pubblica Utilità",description:"Avvio procedimento, periodo osservazioni 30gg, lista definitiva beni",status:"pending",startDate:"2025-03-01",endDate:"2025-06-30",completion:0,tasks:[]},{id:4,name:"Determinazione Indennità",description:"Procedure Art. 20/22bis, comunicazione offerte 20gg, decreto esproprio finale",status:"pending",startDate:"2025-07-01",endDate:"2025-12-31",completion:0,tasks:[]}],d=t=>p[t].status,b=t=>{switch(d(t)){case"completed":return"Completata";case"in_progress":return"In Corso";case"pending":return"In Attesa";default:return"Sconosciuto"}},m=t=>{switch(d(t)){case"completed":return"pi pi-check-circle";case"in_progress":return"pi pi-clock";case"pending":return"pi pi-circle";default:return"pi pi-question-circle"}},v=t=>{switch(t){case"completed":return"pi pi-check text-green-600";case"in_progress":return"pi pi-clock text-blue-600";case"pending":return"pi pi-circle text-gray-400";default:return"pi pi-question-circle text-gray-400"}},r=t=>{const i=d(t);return i==="in_progress"||i==="pending"},s=t=>t?new Date(t).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"}):"Non definita";return(t,i)=>(c(),l("div",fa,[i[4]||(i[4]=e("div",{class:"phases-header"},[e("h3",{class:"section-title"},"Fasi Procedimentali D.P.R. 327/2001"),e("p",{class:"section-subtitle"}," Gestione completa delle 4 fasi obbligatorie del procedimento espropriativo ")],-1)),e("div",$a,[(c(),l(I,null,T(p,(n,a)=>e("div",{key:n.id,class:g(["phase-card",d(a)])},[e("div",ka,[e("span",ya,o(a+1),1),e("i",{class:g([m(a),"phase-icon"])},null,2)]),e("div",wa,[e("div",Da,[e("h4",Pa,o(n.name),1),e("span",{class:g(["phase-status-badge",d(a)])},o(b(a)),3)]),e("p",ja,o(n.description),1),e("div",za,[e("div",Ca,[e("span",Ia,[i[0]||(i[0]=e("i",{class:"pi pi-calendar"},null,-1)),f(" "+o(s(n.startDate))+" - "+o(s(n.endDate)),1)])]),e("div",Ta,[e("div",Sa,[e("div",{class:"progress-fill",style:M({width:`${n.completion||0}%`})},null,4)]),e("span",Aa,o(n.completion||0)+"%",1)])]),n.tasks&&n.tasks.length>0?(c(),l("div",Ra,[i[1]||(i[1]=e("h5",{class:"tasks-title"},"Task Attivi",-1)),e("div",Ma,[(c(!0),l(I,null,T(n.tasks.slice(0,3),z=>(c(),l("div",{key:z.id,class:"task-item"},[e("i",{class:g([v(z.status),"task-icon"])},null,2),e("span",Ea,o(z.name),1),e("span",Na,o(s(z.deadline)),1)]))),128))])])):C("",!0),e("div",Fa,[i[3]||(i[3]=e("button",{class:"btn btn-primary btn-sm"},[e("i",{class:"pi pi-eye"}),f(" Dettagli Fase ")],-1)),r(a)?(c(),l("button",La,i[2]||(i[2]=[e("i",{class:"pi pi-pencil"},null,-1),f(" Gestisci ")]))):C("",!0)])])],2)),64))])]))}}),Va=y(Ua,[["__scopeId","data-v-1c72dce3"]]),qa={class:"property-manager"},xa=k({__name:"PropertyManager",props:{project:{},properties:{}},emits:["property-update"],setup(h,{emit:u}){return(p,d)=>(c(),l("div",qa,d[0]||(d[0]=[S('<div class="manager-header" data-v-214b7b16><div data-v-214b7b16><h3 class="section-title" data-v-214b7b16>Gestione Particelle</h3><p class="section-subtitle" data-v-214b7b16>Anagrafe catastale e gestione stakeholder</p></div><div class="header-actions" data-v-214b7b16><button class="btn btn-secondary" data-v-214b7b16><i class="pi pi-upload" data-v-214b7b16></i> Import SISTER </button><button class="btn btn-primary" data-v-214b7b16><i class="pi pi-plus" data-v-214b7b16></i> Aggiungi Particella </button></div></div><div class="placeholder-content" data-v-214b7b16><div class="placeholder-icon" data-v-214b7b16><i class="pi pi-map" data-v-214b7b16></i></div><h4 data-v-214b7b16>Gestione Particelle</h4><p data-v-214b7b16>Qui verrà implementata la gestione completa delle particelle catastali, compresi:</p><ul data-v-214b7b16><li data-v-214b7b16>Import automatico dati SISTER</li><li data-v-214b7b16>Anagrafe proprietari</li><li data-v-214b7b16>Stati particella workflow</li><li data-v-214b7b16>Comunicazioni PEC</li><li data-v-214b7b16>Gestione osservazioni</li></ul></div>',2)])))}}),Ga=y(xa,[["__scopeId","data-v-214b7b16"]]),Ba={class:"task-manager"},Oa=k({__name:"TaskManager",props:{project:{},tasks:{},notifications:{}},emits:["task-update"],setup(h,{emit:u}){return(p,d)=>(c(),l("div",Ba,d[0]||(d[0]=[S('<div class="manager-header" data-v-7e98e4ae><div data-v-7e98e4ae><h3 class="section-title" data-v-7e98e4ae>Task &amp; Notifiche</h3><p class="section-subtitle" data-v-7e98e4ae>Workflow procedurale e comunicazioni</p></div><div class="header-actions" data-v-7e98e4ae><button class="btn btn-secondary" data-v-7e98e4ae><i class="pi pi-bell" data-v-7e98e4ae></i> Centro Notifiche </button><button class="btn btn-primary" data-v-7e98e4ae><i class="pi pi-plus" data-v-7e98e4ae></i> Nuovo Task </button></div></div><div class="placeholder-content" data-v-7e98e4ae><div class="placeholder-icon" data-v-7e98e4ae><i class="pi pi-list-check" data-v-7e98e4ae></i></div><h4 data-v-7e98e4ae>Task &amp; Notifiche</h4><p data-v-7e98e4ae>Qui verrà implementato il sistema completo di task e notifiche, compresi:</p><ul data-v-7e98e4ae><li data-v-7e98e4ae>Task automatici da workflow JSON</li><li data-v-7e98e4ae>Notifiche normative D.P.R. 327/2001</li><li data-v-7e98e4ae>Scadenze legali obbligatorie</li><li data-v-7e98e4ae>Centro comunicazioni PEC</li><li data-v-7e98e4ae>Kanban procedurale</li><li data-v-7e98e4ae>Alert escalation automatica</li></ul></div>',2)])))}}),Qa=y(Oa,[["__scopeId","data-v-7e98e4ae"]]),Ka={class:"economic-manager"},Za=k({__name:"EconomicManager",props:{project:{},budget:{},expenses:{}},setup(h){return(u,p)=>(c(),l("div",Ka,p[0]||(p[0]=[S('<div class="manager-header" data-v-59f92914><div data-v-59f92914><h3 class="section-title" data-v-59f92914>Gestione Economica</h3><p class="section-subtitle" data-v-59f92914>Budget, indennità e controllo costi</p></div><div class="header-actions" data-v-59f92914><button class="btn btn-secondary" data-v-59f92914><i class="pi pi-chart-line" data-v-59f92914></i> Report Costi </button><button class="btn btn-primary" data-v-59f92914><i class="pi pi-euro" data-v-59f92914></i> Nuova Voce </button></div></div><div class="placeholder-content" data-v-59f92914><div class="placeholder-icon" data-v-59f92914><i class="pi pi-wallet" data-v-59f92914></i></div><h4 data-v-59f92914>Gestione Economica</h4><p data-v-59f92914>Qui verrà implementata la gestione completa dell&#39;aspetto economico, compresi:</p><ul data-v-59f92914><li data-v-59f92914>Quadro economico generale</li><li data-v-59f92914>Calcolo indennità automatico</li><li data-v-59f92914>Gestione offerte e accettazioni</li><li data-v-59f92914>Monitoraggio budget vs speso</li><li data-v-59f92914>Previsioni costi per fase</li><li data-v-59f92914>Integrazione con sistemi contabili</li></ul></div>',2)])))}}),Ha=y(Za,[["__scopeId","data-v-59f92914"]]),Ja={class:"document-manager"},Wa=k({__name:"DocumentManager",props:{project:{},documents:{}},setup(h){return(u,p)=>(c(),l("div",Ja,p[0]||(p[0]=[S('<div class="manager-header" data-v-eb01e793><div data-v-eb01e793><h3 class="section-title" data-v-eb01e793>Gestione Documenti</h3><p class="section-subtitle" data-v-eb01e793>Atti procedurali e documentazione ufficiale</p></div><div class="header-actions" data-v-eb01e793><button class="btn btn-secondary" data-v-eb01e793><i class="pi pi-folder-open" data-v-eb01e793></i> Archivio </button><button class="btn btn-primary" data-v-eb01e793><i class="pi pi-file-plus" data-v-eb01e793></i> Nuovo Documento </button></div></div><div class="placeholder-content" data-v-eb01e793><div class="placeholder-icon" data-v-eb01e793><i class="pi pi-files" data-v-eb01e793></i></div><h4 data-v-eb01e793>Gestione Documenti</h4><p data-v-eb01e793>Qui verrà implementata la gestione completa dei documenti, compresi:</p><ul data-v-eb01e793><li data-v-eb01e793>Template atti procedurali</li><li data-v-eb01e793>Generazione automatica documenti</li><li data-v-eb01e793>Workflow approvazione</li><li data-v-eb01e793>Archivio documentale</li><li data-v-eb01e793>Firme digitali</li><li data-v-eb01e793>Protocollo informatico</li></ul></div>',2)])))}}),Xa=y(Wa,[["__scopeId","data-v-eb01e793"]]),Ya={class:"report-center"},ei=k({__name:"ReportCenter",props:{project:{},analytics:{}},setup(h){return(u,p)=>(c(),l("div",Ya,p[0]||(p[0]=[S('<div class="manager-header" data-v-8dd6bb5a><div data-v-8dd6bb5a><h3 class="section-title" data-v-8dd6bb5a>Centro Report</h3><p class="section-subtitle" data-v-8dd6bb5a>Analytics, audit trail e reportistica avanzata</p></div><div class="header-actions" data-v-8dd6bb5a><button class="btn btn-secondary" data-v-8dd6bb5a><i class="pi pi-download" data-v-8dd6bb5a></i> Esporta Report </button><button class="btn btn-primary" data-v-8dd6bb5a><i class="pi pi-chart-bar" data-v-8dd6bb5a></i> Nuovo Report </button></div></div><div class="placeholder-content" data-v-8dd6bb5a><div class="placeholder-icon" data-v-8dd6bb5a><i class="pi pi-chart-pie" data-v-8dd6bb5a></i></div><h4 data-v-8dd6bb5a>Centro Report &amp; Analytics</h4><p data-v-8dd6bb5a>Qui verrà implementato il sistema completo di reportistica, compresi:</p><ul data-v-8dd6bb5a><li data-v-8dd6bb5a>Dashboard analytics avanzate</li><li data-v-8dd6bb5a>Report compliance normativa</li><li data-v-8dd6bb5a>Audit trail completo</li><li data-v-8dd6bb5a>KPI real-time</li><li data-v-8dd6bb5a>Export multi-formato</li><li data-v-8dd6bb5a>Monitoring performance</li></ul></div>',2)])))}}),ti=y(ei,[["__scopeId","data-v-8dd6bb5a"]]),si={class:"admin-layout"},ai={class:"admin-content"},ii={class:"project-detail-main"},oi={key:2,class:"project-content"},ni={class:"overview-cards"},ci={class:"tabs-container"},li={class:"tabs-nav"},di=["onClick"],ri={key:0,class:"tab-badge"},pi={class:"tab-content"},ui={class:"tab-panel"},vi={class:"tab-panel"},mi={class:"tab-panel"},bi={class:"tab-panel"},gi={class:"tab-panel"},_i={class:"tab-panel"},hi={class:"tab-panel"},fi=k({__name:"ProjectDetail",setup(h){const u=X(),p=Y(),d=_(!0),b=_(null),m=_(null),v=_("dashboard"),r=_({currentPhase:"preparatorie",completedPhases:1,totalPhases:4,percentage:25}),s=_({overallScore:92,criticalIssues:1,warnings:3,nextDeadline:"2025-01-15"}),t=_({total:25e5,spent:75e4,committed:5e5,remaining:125e4}),i=_({startDate:"2024-06-01",endDate:"2026-12-31",currentMilestone:"Apposizione Vincolo",nextMilestone:"Dichiarazione Pubblica Utilità",daysToNext:45}),n=_({totalProperties:156,notifiedOwners:142,pendingResponses:14,legalDeadlines:3}),a=_([{id:1,name:"Operazioni Preparatorie",status:"completed",startDate:"2024-06-01",endDate:"2024-08-15",tasks:[]},{id:2,name:"Apposizione Vincolo/Variante",status:"in_progress",startDate:"2024-08-16",endDate:"2025-02-28",tasks:[]}]),z=_([]),E=_([]),V=_([]),q=_([]),x=_([]),G=_({}),w=j(()=>m.value?{...m.value,title:m.value.name,phase:m.value.status,rup_name:"Da assegnare",rpe_name:"Da assegnare"}:null),B=j(()=>[{id:"dashboard",label:"Dashboard",icon:"pi pi-chart-bar",badge:null},{id:"fasi",label:"Fasi Procedimentali",icon:"pi pi-sitemap",badge:a.value.filter(D=>D.status==="in_progress").length||null},{id:"particelle",label:"Particelle",icon:"pi pi-map",badge:n.value.totalProperties},{id:"tasks",label:"Task & Notifiche",icon:"pi pi-bell",badge:n.value.pendingResponses||null},{id:"economico",label:"Economico",icon:"pi pi-euro",badge:null},{id:"documenti",label:"Documenti",icon:"pi pi-file",badge:null},{id:"report",label:"Report",icon:"pi pi-chart-line",badge:null}]),N=async()=>{d.value=!0,b.value=null;try{const D=u.params.id,F=localStorage.getItem("token"),P=await fetch(`/api/projects/${D}`,{headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});if(!P.ok)throw new Error(`Errore ${P.status}: ${P.statusText}`);m.value=await P.json()}catch(D){b.value=D.message||"Errore nel caricamento del progetto"}finally{d.value=!1}},O=()=>{p.push(`/admin/projects/${m.value.id}/edit`)},Q=()=>{console.log("Export project data")},K=D=>{console.log("Phase updated:",D)},Z=D=>{console.log("Property updated:",D)},H=D=>{console.log("Task updated:",D)};return W(()=>{N()}),(D,F)=>(c(),l("div",si,[$(ee),e("div",ai,[e("main",ii,[d.value?(c(),L(te,{key:0,message:"Caricamento progetto..."})):b.value?(c(),L(se,{key:1,message:b.value},{actions:U(()=>[e("button",{onClick:N,class:"btn btn-primary"}," Riprova ")]),_:1},8,["message"])):m.value?(c(),l("div",oi,[$(Me,{project:w.value,onRefresh:N,onEdit:O,onExport:Q},null,8,["project"]),e("div",ni,[$(at,{project:w.value,progress:r.value},null,8,["project","progress"]),$(Ct,{project:w.value,compliance:s.value},null,8,["project","compliance"]),$(ss,{project:w.value,budget:t.value},null,8,["project","budget"]),$(Ts,{project:w.value,timeline:i.value},null,8,["project","timeline"])]),e("div",ci,[e("nav",li,[(c(!0),l(I,null,T(B.value,P=>(c(),l("button",{key:P.id,onClick:$i=>v.value=P.id,class:g(["tab-button",{active:v.value===P.id}])},[e("i",{class:g(P.icon)},null,2),f(" "+o(P.label)+" ",1),P.badge?(c(),l("span",ri,o(P.badge),1)):C("",!0)],10,di))),128))]),e("div",pi,[A(e("div",ui,[$(ha,{project:w.value,kpis:n.value},null,8,["project","kpis"])],512),[[R,v.value==="dashboard"]]),A(e("div",vi,[$(Va,{project:w.value,phases:a.value,onPhaseUpdate:K},null,8,["project","phases"])],512),[[R,v.value==="fasi"]]),A(e("div",mi,[$(Ga,{project:w.value,properties:z.value,onPropertyUpdate:Z},null,8,["project","properties"])],512),[[R,v.value==="particelle"]]),A(e("div",bi,[$(Qa,{project:w.value,tasks:E.value,notifications:V.value,onTaskUpdate:H},null,8,["project","tasks","notifications"])],512),[[R,v.value==="tasks"]]),A(e("div",gi,[$(Ha,{project:w.value,budget:t.value,expenses:q.value},null,8,["project","budget","expenses"])],512),[[R,v.value==="economico"]]),A(e("div",_i,[$(Xa,{project:w.value,documents:x.value},null,8,["project","documents"])],512),[[R,v.value==="documenti"]]),A(e("div",hi,[$(ti,{project:w.value,analytics:G.value},null,8,["project","analytics"])],512),[[R,v.value==="report"]])])])])):C("",!0)])])]))}}),ji=y(fi,[["__scopeId","data-v-5c06952f"]]);export{ji as default};
