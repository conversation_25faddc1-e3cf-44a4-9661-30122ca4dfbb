import{_ as B,g as k,J as F,p as Z,c as a,o as n,b as e,t as r,i as E,h as c,j as m,v as $,k as h,e as P,F as V,q as N,f,a as j,B as q,w as X,n as A,E as L}from"./index-Bibk5VaL.js";import{A as Y}from"./AdminNavbar-jBevcaIl.js";import{P as ee}from"./PageHeader-B4SpV5zp.js";/* empty css                        */import"./BaseNavbar-BSvAFYsY.js";const te={class:"modal-container"},oe={class:"modal-header"},se={class:"modal-title"},ie={class:"form-section"},le={class:"form-grid"},ne={class:"form-group"},ae={class:"form-group"},re={key:0,class:"form-group"},de={class:"form-section"},ue={class:"form-grid"},pe={class:"form-group"},ce={class:"form-group"},me={class:"form-section"},ve={class:"form-grid"},be={class:"form-group"},fe={class:"form-group"},ge={class:"form-group"},ye={class:"form-group"},_e={class:"form-section"},ke={class:"form-grid"},$e={class:"form-group"},Ce={class:"form-group"},ze={class:"form-group"},Ue={key:0,class:"permissions-info"},Ae={class:"permission-list"},Ee={class:"modal-actions"},we=["disabled"],Se={key:0,class:"loading-spinner"},he={__name:"UserModal",props:{user:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(y,{emit:v}){const d=y,b=v,g=k(!1),l=F({full_name:"",email:"",password:"",role:"lettore",status:"active",organization:"",department:"",professional_order:"",professional_number:"",phone:"",pec_email:"",fiscal_code:""}),z=u=>({admin_ente:[{key:"manage_users",label:"Gestione completa utenti"},{key:"manage_projects",label:"Gestione progetti"},{key:"approve_procedures",label:"Approvazione procedure"},{key:"system_config",label:"Configurazione sistema"}],rup:[{key:"manage_projects",label:"Gestione progetti"},{key:"approve_procedures",label:"Approvazione procedure"},{key:"legal_authority",label:"Autorità legale procedimenti"}],rpe:[{key:"manage_projects",label:"Gestione progetti esproprio"},{key:"approve_procedures",label:"Approvazione procedure esproprio"},{key:"property_management",label:"Gestione particelle"}],dirigente:[{key:"manage_users",label:"Gestione utenti dipartimento"},{key:"manage_projects",label:"Gestione progetti"},{key:"approve_procedures",label:"Approvazione procedure"}],tecnico:[{key:"technical_tasks",label:"Attività tecniche"},{key:"surveys",label:"Sopralluoghi e rilievi"},{key:"documentation",label:"Documentazione tecnica"}],legale:[{key:"legal_review",label:"Revisione legale"},{key:"legal_documentation",label:"Documentazione legale"},{key:"compliance",label:"Controllo conformità"}],amministrativo:[{key:"admin_tasks",label:"Attività amministrative"},{key:"notifications",label:"Gestione notifiche"},{key:"documentation",label:"Documentazione amministrativa"}],lettore:[{key:"read_only",label:"Solo lettura"},{key:"view_projects",label:"Visualizzazione progetti"}],cittadino:[{key:"citizen_portal",label:"Accesso portale cittadini"},{key:"own_properties",label:"Visualizzazione proprie particelle"},{key:"submit_observations",label:"Invio osservazioni"}]})[u]||[],U=async()=>{g.value=!0;try{const u=d.isEdit?`/api/users/${d.user.id}`:"/api/users",o=d.isEdit?"PATCH":"POST",i={...l};d.isEdit&&delete i.password;const _=await fetch(u,{method:o,headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!_.ok){const w=await _.json();throw new Error(w.detail||"Errore durante il salvataggio")}b("save")}catch(u){console.error("Error saving user:",u),alert(u.message||"Errore durante il salvataggio")}finally{g.value=!1}};return Z(()=>{d.isEdit&&d.user&&Object.keys(l).forEach(u=>{u!=="password"&&d.user[u]!==void 0&&(l[u]=d.user[u]||"")})}),(u,o)=>(n(),a("div",{class:"modal-overlay",onClick:o[14]||(o[14]=E(i=>u.$emit("close"),["self"]))},[e("div",te,[e("div",oe,[e("h3",se,r(y.isEdit?"Modifica Utente":"Nuovo Utente"),1),e("button",{onClick:o[0]||(o[0]=i=>u.$emit("close")),class:"close-btn"},o[15]||(o[15]=[e("i",{class:"pi pi-times close-icon"},null,-1)]))]),e("form",{onSubmit:E(U,["prevent"]),class:"modal-body"},[e("div",ie,[o[19]||(o[19]=e("h4",{class:"section-title"},"Informazioni di Base",-1)),e("div",le,[e("div",ne,[o[16]||(o[16]=e("label",{class:"form-label required"},"Nome Completo",-1)),m(e("input",{"onUpdate:modelValue":o[1]||(o[1]=i=>l.full_name=i),type:"text",class:"form-input",required:"",placeholder:"Nome e cognome"},null,512),[[$,l.full_name]])]),e("div",ae,[o[17]||(o[17]=e("label",{class:"form-label required"},"Email",-1)),m(e("input",{"onUpdate:modelValue":o[2]||(o[2]=i=>l.email=i),type:"email",class:"form-input",required:"",placeholder:"<EMAIL>"},null,512),[[$,l.email]])]),y.isEdit?c("",!0):(n(),a("div",re,[o[18]||(o[18]=e("label",{class:"form-label required"},"Password",-1)),m(e("input",{"onUpdate:modelValue":o[3]||(o[3]=i=>l.password=i),type:"password",class:"form-input",required:"",placeholder:"Password sicura",minlength:"8"},null,512),[[$,l.password]])]))])]),e("div",de,[o[24]||(o[24]=e("h4",{class:"section-title"},"Ruolo e Stato",-1)),e("div",ue,[e("div",pe,[o[21]||(o[21]=e("label",{class:"form-label required"},"Ruolo",-1)),m(e("select",{"onUpdate:modelValue":o[4]||(o[4]=i=>l.role=i),class:"form-select",required:""},o[20]||(o[20]=[P('<option value="lettore" data-v-cb92a879>Lettore</option><option value="cittadino" data-v-cb92a879>Cittadino</option><option value="amministrativo" data-v-cb92a879>Amministrativo</option><option value="tecnico" data-v-cb92a879>Tecnico</option><option value="legale" data-v-cb92a879>Ufficio Legale</option><option value="dirigente" data-v-cb92a879>Dirigente/Responsabile</option><option value="rpe" data-v-cb92a879>RPE - Responsabile Procedimento Espropriativo</option><option value="rup" data-v-cb92a879>RUP - Responsabile Unico del Procedimento</option><option value="admin_ente" data-v-cb92a879>Amministratore Ente</option>',9)]),512),[[h,l.role]])]),e("div",ce,[o[23]||(o[23]=e("label",{class:"form-label required"},"Stato",-1)),m(e("select",{"onUpdate:modelValue":o[5]||(o[5]=i=>l.status=i),class:"form-select",required:""},o[22]||(o[22]=[e("option",{value:"active"},"Attivo",-1),e("option",{value:"inactive"},"Inattivo",-1),e("option",{value:"suspended"},"Sospeso",-1),e("option",{value:"pending"},"In attesa",-1)]),512),[[h,l.status]])])])]),e("div",me,[o[30]||(o[30]=e("h4",{class:"section-title"},"Informazioni Professionali",-1)),e("div",ve,[e("div",be,[o[25]||(o[25]=e("label",{class:"form-label"},"Organizzazione",-1)),m(e("input",{"onUpdate:modelValue":o[6]||(o[6]=i=>l.organization=i),type:"text",class:"form-input",placeholder:"Nome organizzazione"},null,512),[[$,l.organization]])]),e("div",fe,[o[26]||(o[26]=e("label",{class:"form-label"},"Dipartimento",-1)),m(e("input",{"onUpdate:modelValue":o[7]||(o[7]=i=>l.department=i),type:"text",class:"form-input",placeholder:"Dipartimento/Ufficio"},null,512),[[$,l.department]])]),e("div",ge,[o[28]||(o[28]=e("label",{class:"form-label"},"Ordine Professionale",-1)),m(e("select",{"onUpdate:modelValue":o[8]||(o[8]=i=>l.professional_order=i),class:"form-select"},o[27]||(o[27]=[P('<option value="" data-v-cb92a879>Seleziona ordine</option><option value="Ordine degli Ingegneri" data-v-cb92a879>Ordine degli Ingegneri</option><option value="Ordine degli Architetti" data-v-cb92a879>Ordine degli Architetti</option><option value="Ordine dei Geometri" data-v-cb92a879>Ordine dei Geometri</option><option value="Ordine degli Avvocati" data-v-cb92a879>Ordine degli Avvocati</option><option value="Ordine dei Notai" data-v-cb92a879>Ordine dei Notai</option><option value="Altro" data-v-cb92a879>Altro</option>',7)]),512),[[h,l.professional_order]])]),e("div",ye,[o[29]||(o[29]=e("label",{class:"form-label"},"Numero Iscrizione Albo",-1)),m(e("input",{"onUpdate:modelValue":o[9]||(o[9]=i=>l.professional_number=i),type:"text",class:"form-input",placeholder:"Numero iscrizione"},null,512),[[$,l.professional_number]])])])]),e("div",_e,[o[34]||(o[34]=e("h4",{class:"section-title"},"Contatti",-1)),e("div",ke,[e("div",$e,[o[31]||(o[31]=e("label",{class:"form-label"},"Telefono",-1)),m(e("input",{"onUpdate:modelValue":o[10]||(o[10]=i=>l.phone=i),type:"tel",class:"form-input",placeholder:"+39 ************"},null,512),[[$,l.phone]])]),e("div",Ce,[o[32]||(o[32]=e("label",{class:"form-label"},"Email PEC",-1)),m(e("input",{"onUpdate:modelValue":o[11]||(o[11]=i=>l.pec_email=i),type:"email",class:"form-input",placeholder:"<EMAIL>"},null,512),[[$,l.pec_email]])]),e("div",ze,[o[33]||(o[33]=e("label",{class:"form-label"},"Codice Fiscale",-1)),m(e("input",{"onUpdate:modelValue":o[12]||(o[12]=i=>l.fiscal_code=i),type:"text",class:"form-input",placeholder:"****************",pattern:"[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]",style:{"text-transform":"uppercase"}},null,512),[[$,l.fiscal_code]])])])]),l.role?(n(),a("div",Ue,[o[36]||(o[36]=e("h4",{class:"section-title"},"Permessi del Ruolo",-1)),e("div",Ae,[(n(!0),a(V,null,N(z(l.role),i=>(n(),a("div",{key:i.key,class:"permission-item"},[o[35]||(o[35]=e("i",{class:"pi pi-check-circle permission-icon"},null,-1)),e("span",null,r(i.label),1)]))),128))])])):c("",!0),e("div",Ee,[e("button",{type:"button",onClick:o[13]||(o[13]=i=>u.$emit("close")),class:"btn btn-secondary"}," Annulla "),e("button",{type:"submit",class:"btn btn-primary",disabled:g.value},[g.value?(n(),a("span",Se)):c("",!0),f(" "+r(y.isEdit?"Aggiorna":"Crea")+" Utente ",1)],8,we)])],32)])]))}},Pe=B(he,[["__scopeId","data-v-cb92a879"]]),Re={class:"modal-container"},Ve={class:"modal-content"},Ne={class:"modal-body"},Oe={class:"modal-title"},Te={class:"modal-message"},Ie={class:"modal-actions"},Me={__name:"ConfirmModal",props:{title:{type:String,default:"Conferma Azione"},message:{type:String,required:!0},confirmText:{type:String,default:"Conferma"},cancelText:{type:String,default:"Annulla"}},emits:["confirm","cancel"],setup(y){return(v,d)=>(n(),a("div",{class:"modal-overlay",onClick:d[2]||(d[2]=E(b=>v.$emit("cancel"),["self"]))},[e("div",Re,[e("div",Ve,[d[3]||(d[3]=e("div",{class:"modal-icon"},[e("i",{class:"pi pi-exclamation-triangle warning-icon"})],-1)),e("div",Ne,[e("h3",Oe,r(y.title),1),e("p",Te,r(y.message),1)]),e("div",Ie,[e("button",{onClick:d[0]||(d[0]=b=>v.$emit("cancel")),class:"btn btn-secondary"},r(y.cancelText),1),e("button",{onClick:d[1]||(d[1]=b=>v.$emit("confirm")),class:"btn btn-danger"},r(y.confirmText),1)])])])]))}},xe=B(Me,[["__scopeId","data-v-38cc498c"]]),De={class:"admin-layout"},Ge={class:"admin-content"},je={class:"user-main"},qe={class:"filters-section"},Le={class:"filters-grid"},Be={class:"filter-group"},Fe={class:"search-input-group"},Ze={class:"filter-group"},He={class:"filter-group"},Je={class:"table-section"},Ke={class:"table-header"},We={class:"table-title"},Qe={class:"table-actions"},Xe={key:0,class:"users-grid"},Ye=["onClick"],et={class:"user-card-header"},tt={class:"user-info"},ot={class:"user-name"},st={class:"user-email"},it={class:"user-status"},lt={class:"user-card-body"},nt={class:"user-details"},at={class:"detail-item"},rt={key:0,class:"detail-item"},dt={class:"detail-value"},ut={key:1,class:"detail-item"},pt={class:"detail-value"},ct={key:2,class:"detail-item"},mt={class:"detail-value"},vt={class:"user-permissions"},bt={class:"permission-badges"},ft={key:0,class:"permission-badge"},gt={key:1,class:"permission-badge"},yt={key:2,class:"permission-badge"},_t={class:"user-card-footer"},kt={class:"user-meta"},$t={class:"meta-item"},Ct={key:0,class:"meta-item"},zt={class:"card-actions"},Ut=["onClick"],At={key:1,class:"users-table-container"},Et={class:"users-table"},wt=["onClick"],St={class:"user-cell"},ht={class:"user-cell-content"},Pt={class:"user-info-sm"},Rt={class:"user-name-sm"},Vt={class:"user-email-sm"},Nt={class:"org-info"},Ot={key:0,class:"org-name"},Tt={key:1,class:"dept-name"},It={class:"permissions-cell"},Mt={class:"permission-count"},xt={key:0,class:"date-cell"},Dt={key:1,class:"no-data"},Gt={class:"table-actions"},jt=["onClick"],qt=["onClick"],Lt={key:2,class:"loading-state"},Bt={key:3,class:"error-state"},Ft={key:4,class:"empty-state"},Qt={__name:"UserList",setup(y){const v=k([]),d=k(!1),b=k(""),g=k("cards"),l=k(null),z=k(null),U=k(!1),u=k(!1),o=k(!1),i=F({search:"",role:"",status:""}),_=async()=>{var p,t,S;d.value=!0,b.value="";try{console.log("Loading users...");const s=new URLSearchParams;i.search&&s.append("search",i.search),i.role&&s.append("role",i.role),i.status&&s.append("status",i.status);const C=s.toString(),x=C?`/users/?${C}`:"/users/";console.log("API URL:",x);const D=await L.get(x);console.log("API Response:",D.data);const G=D.data;v.value=G.users||G||[],console.log("Users loaded:",v.value.length)}catch(s){console.error("Error loading users:",s);const C=((t=(p=s.response)==null?void 0:p.data)==null?void 0:t.detail)||s.message||"Errore nel caricamento utenti";b.value=C,((S=s.response)==null?void 0:S.status)===401?(b.value="Autenticazione richiesta. Effettua il login.",console.warn("Authentication error - user needs to login")):(s.code==="ERR_NETWORK"||s.message.includes("ERR_CONNECTION_REFUSED"))&&(b.value="Errore di connessione al server. Verifica che il backend sia attivo.",console.error("Backend server connection failed. Please ensure the backend is running on localhost:8000")),v.value=[]}finally{d.value=!1}};let w=null;const H=()=>{w&&clearTimeout(w),w=setTimeout(()=>{_()},300)},O=p=>{l.value=p},T=p=>{l.value=p,u.value=!0},J=p=>{z.value=p,o.value=!0},K=async()=>{try{await L.delete(`/users/${z.value.id}`),await _(),o.value=!1,z.value=null}catch(p){console.error("Error deleting user:",p)}},I=()=>{U.value=!1,u.value=!1,l.value=null},W=async()=>{await _(),I()},M=p=>({active:"Attivo",inactive:"Inattivo",suspended:"Sospeso",pending:"In attesa"})[p]||p,Q=p=>{let t=0;return p.can_manage_projects&&t++,p.can_approve_procedures&&t++,p.can_manage_users&&t++,t},R=p=>new Date(p).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return Z(()=>{_()}),(p,t)=>{var S;return n(),a("div",De,[j(Y),e("div",Ge,[e("main",je,[j(ee,{title:"Gestione Utenti",subtitle:"Gestisci utenti, ruoli e permessi per il sistema ExProject"},{actions:X(()=>[e("button",{onClick:t[0]||(t[0]=s=>U.value=!0),class:"btn btn-primary"},t[8]||(t[8]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),f(" Nuovo Utente ")]))]),_:1}),e("div",qe,[e("div",Le,[e("div",Be,[t[10]||(t[10]=e("label",{class:"filter-label"},"Cerca",-1)),e("div",Fe,[t[9]||(t[9]=e("i",{class:"pi pi-search search-icon"},null,-1)),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>i.search=s),onInput:H,type:"text",class:"search-input",placeholder:"Cerca per nome, email, organizzazione..."},null,544),[[$,i.search]])])]),e("div",Ze,[t[12]||(t[12]=e("label",{class:"filter-label"},"Ruolo",-1)),m(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>i.role=s),onChange:_,class:"filter-select"},t[11]||(t[11]=[P('<option value="">Tutti i ruoli</option><option value="admin_ente">Amministratore Ente</option><option value="rup">RUP</option><option value="rpe">RPE</option><option value="dirigente">Dirigente</option><option value="tecnico">Tecnico</option><option value="legale">Legale</option><option value="amministrativo">Amministrativo</option><option value="lettore">Lettore</option><option value="cittadino">Cittadino</option>',10)]),544),[[h,i.role]])]),e("div",He,[t[14]||(t[14]=e("label",{class:"filter-label"},"Stato",-1)),m(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>i.status=s),onChange:_,class:"filter-select"},t[13]||(t[13]=[P('<option value="">Tutti gli stati</option><option value="active">Attivo</option><option value="inactive">Inattivo</option><option value="suspended">Sospeso</option><option value="pending">In attesa</option>',5)]),544),[[h,i.status]])])])]),e("div",Je,[e("div",Ke,[e("div",We,[e("h3",null,"Utenti ("+r(v.value.length)+")",1)]),e("div",Qe,[e("button",{onClick:t[4]||(t[4]=s=>g.value="cards"),class:A(["view-toggle",{active:g.value==="cards"}])},t[15]||(t[15]=[e("i",{class:"pi pi-th-large view-icon"},null,-1),f(" Cards ")]),2),e("button",{onClick:t[5]||(t[5]=s=>g.value="table"),class:A(["view-toggle",{active:g.value==="table"}])},t[16]||(t[16]=[e("i",{class:"pi pi-table view-icon"},null,-1),f(" Tabella ")]),2)])]),g.value==="cards"?(n(),a("div",Xe,[(n(!0),a(V,null,N(v.value,s=>(n(),a("div",{key:s.id,class:"user-card",onClick:C=>O(s)},[e("div",et,[t[17]||(t[17]=e("div",{class:"user-avatar"},[e("i",{class:"pi pi-user avatar-icon"})],-1)),e("div",tt,[e("h4",ot,r(s.full_name),1),e("p",st,r(s.email),1)]),e("div",it,[e("span",{class:A(["status-badge",s.status])},r(M(s.status)),3)])]),e("div",lt,[e("div",nt,[e("div",at,[t[18]||(t[18]=e("span",{class:"detail-label"},"Ruolo:",-1)),e("span",{class:A(["detail-value role-badge",s.role])},r(s.role_label),3)]),s.organization?(n(),a("div",rt,[t[19]||(t[19]=e("span",{class:"detail-label"},"Organizzazione:",-1)),e("span",dt,r(s.organization),1)])):c("",!0),s.department?(n(),a("div",ut,[t[20]||(t[20]=e("span",{class:"detail-label"},"Dipartimento:",-1)),e("span",pt,r(s.department),1)])):c("",!0),s.professional_order?(n(),a("div",ct,[t[21]||(t[21]=e("span",{class:"detail-label"},"Ordine Professionale:",-1)),e("span",mt,r(s.professional_order),1)])):c("",!0)]),e("div",vt,[e("div",bt,[s.can_manage_projects?(n(),a("span",ft,t[22]||(t[22]=[e("i",{class:"pi pi-briefcase permission-icon"},null,-1),f(" Gestione Progetti ")]))):c("",!0),s.can_approve_procedures?(n(),a("span",gt,t[23]||(t[23]=[e("i",{class:"pi pi-check-circle permission-icon"},null,-1),f(" Approvazione Procedure ")]))):c("",!0),s.can_manage_users?(n(),a("span",yt,t[24]||(t[24]=[e("i",{class:"pi pi-users permission-icon"},null,-1),f(" Gestione Utenti ")]))):c("",!0)])])]),e("div",_t,[e("div",kt,[e("span",$t,[t[25]||(t[25]=e("i",{class:"pi pi-calendar meta-icon"},null,-1)),f(" Creato: "+r(R(s.created_at)),1)]),s.last_login?(n(),a("span",Ct,[t[26]||(t[26]=e("i",{class:"pi pi-clock meta-icon"},null,-1)),f(" Ultimo login: "+r(R(s.last_login)),1)])):c("",!0)]),e("div",zt,[e("button",{onClick:E(C=>T(s),["stop"]),class:"btn btn-sm btn-secondary"},t[27]||(t[27]=[e("i",{class:"pi pi-pencil btn-icon"},null,-1),f(" Modifica ")]),8,Ut)])])],8,Ye))),128))])):c("",!0),g.value==="table"?(n(),a("div",At,[e("table",Et,[t[31]||(t[31]=e("thead",null,[e("tr",null,[e("th",null,"Utente"),e("th",null,"Ruolo"),e("th",null,"Organizzazione"),e("th",null,"Stato"),e("th",null,"Permessi"),e("th",null,"Ultimo Login"),e("th",null,"Azioni")])],-1)),e("tbody",null,[(n(!0),a(V,null,N(v.value,s=>(n(),a("tr",{key:s.id,class:"user-row",onClick:C=>O(s)},[e("td",St,[e("div",ht,[t[28]||(t[28]=e("div",{class:"user-avatar-sm"},[e("i",{class:"pi pi-user avatar-icon"})],-1)),e("div",Pt,[e("div",Rt,r(s.full_name),1),e("div",Vt,r(s.email),1)])])]),e("td",null,[e("span",{class:A(["role-badge",s.role])},r(s.role_label),3)]),e("td",null,[e("div",Nt,[s.organization?(n(),a("div",Ot,r(s.organization),1)):c("",!0),s.department?(n(),a("div",Tt,r(s.department),1)):c("",!0)])]),e("td",null,[e("span",{class:A(["status-badge",s.status])},r(M(s.status)),3)]),e("td",null,[e("div",It,[e("div",Mt,r(Q(s))+" permessi ",1)])]),e("td",null,[s.last_login?(n(),a("span",xt,r(R(s.last_login)),1)):(n(),a("span",Dt,"Mai"))]),e("td",null,[e("div",Gt,[e("button",{onClick:E(C=>T(s),["stop"]),class:"action-btn",title:"Modifica utente"},t[29]||(t[29]=[e("i",{class:"pi pi-pencil action-icon"},null,-1)]),8,jt),e("button",{onClick:E(C=>J(s),["stop"]),class:"action-btn danger",title:"Elimina utente"},t[30]||(t[30]=[e("i",{class:"pi pi-trash action-icon"},null,-1)]),8,qt)])])],8,wt))),128))])])])):c("",!0),d.value?(n(),a("div",Lt,t[32]||(t[32]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"Caricamento utenti...",-1)]))):c("",!0),!d.value&&b.value?(n(),a("div",Bt,[t[34]||(t[34]=e("i",{class:"pi pi-exclamation-triangle error-icon"},null,-1)),t[35]||(t[35]=e("h3",null,"Errore nel caricamento",-1)),e("p",null,r(b.value),1),e("button",{onClick:_,class:"btn btn-primary"},t[33]||(t[33]=[e("i",{class:"pi pi-refresh btn-icon"},null,-1),f(" Riprova ")]))])):c("",!0),!d.value&&!b.value&&v.value.length===0?(n(),a("div",Ft,[t[37]||(t[37]=e("i",{class:"pi pi-users empty-icon"},null,-1)),t[38]||(t[38]=e("h3",null,"Nessun utente trovato",-1)),t[39]||(t[39]=e("p",null,"Non ci sono utenti che corrispondono ai filtri selezionati.",-1)),e("button",{onClick:t[6]||(t[6]=s=>U.value=!0),class:"btn btn-primary"},t[36]||(t[36]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),f(" Crea Primo Utente ")]))])):c("",!0)]),U.value||u.value?(n(),q(Pe,{key:0,user:l.value,"is-edit":u.value,onClose:I,onSave:W},null,8,["user","is-edit"])):c("",!0),o.value?(n(),q(xe,{key:1,title:"Conferma Eliminazione",message:`Sei sicuro di voler eliminare l'utente ${(S=z.value)==null?void 0:S.full_name}?`,"confirm-text":"Elimina","cancel-text":"Annulla",onConfirm:K,onCancel:t[7]||(t[7]=s=>o.value=!1)},null,8,["message"])):c("",!0)])])])}}};export{Qt as default};
