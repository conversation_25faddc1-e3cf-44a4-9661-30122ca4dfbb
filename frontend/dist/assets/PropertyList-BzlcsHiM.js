import{d as W,g as P,m as j,c as _,b as e,h as y,f as m,i as ae,a as d,w as g,B as T,t as n,F as X,q as K,n as q,s as Pe,o as p,_ as Y,p as le,j as J,k as Q,e as Ie,D as ze,A as Ve,G as Ee,u as he}from"./index-Bibk5VaL.js";import{A as Ae}from"./AdminNavbar-jBevcaIl.js";import{P as Te}from"./PageHeader-B4SpV5zp.js";import{_ as N}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as S}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{_ as A}from"./BaseInput.vue_vue_type_script_setup_true_lang-DWl8EspK.js";import{_ as Z}from"./BaseModal.vue_vue_type_script_setup_true_lang-CIvlL0EO.js";import{L as De}from"./LoadingState-9ob6nEfb.js";import{E as Me}from"./ErrorState-DJld4rs8.js";import{E as se}from"./EmptyState-rMfszj3C.js";import{P as Re}from"./PropertyCreateForm-QSbxRY_N.js";import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const Ue={class:"sister-import-form"},je={class:"search-section"},Fe={class:"form-grid"},Ne={class:"search-actions"},Le={key:0,class:"results-section"},Be={key:0,class:"results-summary"},Oe={class:"summary-stats"},qe={class:"stat-item"},He={class:"stat-value"},Ze={class:"stat-item"},Ge={class:"stat-value"},Je={class:"selection-actions"},Qe={key:2,class:"results-table-container"},Xe={class:"sister-results-table"},Ke=["checked"],We=["checked","onChange"],Ye={class:"cadastral-ref"},xe={class:"cadastral-ref"},et={class:"cadastral-ref"},tt={class:"area-value"},at={class:"category-badge"},lt={class:"owner-info"},st={class:"owner-name"},ot={key:0,class:"owner-address"},it={class:"fiscal-code"},nt={key:1,class:"import-section"},rt={class:"import-summary"},ut={class:"import-actions"},dt={key:2,class:"import-progress"},pt={class:"progress-header"},ct={class:"progress-text"},vt={class:"progress-bar"},mt={key:3,class:"import-results"},ft={class:"results-header"},gt={class:"results-summary"},_t={class:"result-stat"},yt={class:"stat-number success"},bt={key:0,class:"result-stat"},$t={class:"stat-number error"},kt={class:"result-stat"},Ct={class:"stat-number"},wt={key:0,class:"error-details"},St={class:"error-list"},Pt={class:"results-actions"},It=W({__name:"SisterImportForm",props:{projectId:{}},emits:["close","success"],setup(L,{emit:H}){const I=L,z=P(!1),k=P(!1),V=P(!1),b=P([]),a=P([]),C=P(null),c=P({comune:"",provincia:"",foglio:"",mappale:""}),i=P({current:0,total:0}),o=j(()=>b.value.length>0&&a.value.length===b.value.length),h=async()=>{if(!(!c.value.comune||!c.value.provincia)){z.value=!0,V.value=!1;try{const w=localStorage.getItem("token"),l=new URLSearchParams({comune:c.value.comune,provincia:c.value.provincia.toUpperCase()});c.value.foglio&&l.append("foglio",c.value.foglio.toString()),c.value.mappale&&l.append("mappale",c.value.mappale.toString());const v=await fetch(`/api/sister/search?${l}`,{headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"}});if(!v.ok)throw new Error(`HTTP error! status: ${v.status}`);const M=await v.json();b.value=Array.isArray(M)?M:[],a.value=[]}catch(w){console.error("SISTER search error:",w),b.value=[]}finally{z.value=!1,V.value=!0}}},u=()=>{c.value={comune:"",provincia:"",foglio:"",mappale:""},b.value=[],a.value=[],V.value=!1,C.value=null},f=w=>a.value.some(l=>l.comune===w.comune&&l.foglio===w.foglio&&l.mappale===w.mappale&&l.subalterno===w.subalterno),$=w=>{const l=a.value.findIndex(v=>v.comune===w.comune&&v.foglio===w.foglio&&v.mappale===w.mappale&&v.subalterno===w.subalterno);l>=0?a.value.splice(l,1):a.value.push(w)},D=()=>{a.value=[...b.value]},F=()=>{a.value=[]},B=()=>{o.value?F():D()},x=async()=>{if(a.value.length!==0){k.value=!0,i.value={current:0,total:a.value.length};try{const w=localStorage.getItem("token"),l=await fetch("/api/properties/import-from-sister",{method:"POST",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify({comune:c.value.comune,provincia:c.value.provincia.toUpperCase(),foglio:c.value.foglio?parseInt(c.value.foglio):null,mappale:c.value.mappale?parseInt(c.value.mappale):null,project_id:I.projectId})});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);const v=await l.json();C.value={success:v.imported_count>0,imported_count:v.imported_count,errors:v.errors||[],total_found:v.total_found,message:v.message},i.value.current=i.value.total}catch(w){console.error("Import error:",w),C.value={success:!1,imported_count:0,errors:["Errore durante l'importazione: "+w.message],total_found:a.value.length,message:"Importazione fallita"}}finally{k.value=!1}}},O=()=>{C.value=null,a.value=[],b.value=[],V.value=!1},U=w=>w?w.toLocaleString("it-IT")+" m²":"-";return(w,l)=>(p(),_("div",Ue,[e("div",je,[l[8]||(l[8]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-database"}),m(" Ricerca nel Sistema SISTER ")],-1)),l[9]||(l[9]=e("p",{class:"section-description"}," Cerca particelle catastali nel sistema SISTER utilizzando i dati catastali ",-1)),e("form",{onSubmit:ae(h,["prevent"]),class:"search-form"},[e("div",Fe,[d(A,{modelValue:c.value.comune,"onUpdate:modelValue":l[0]||(l[0]=v=>c.value.comune=v),label:"Comune *",placeholder:"es. Milano",required:"",disabled:z.value},null,8,["modelValue","disabled"]),d(A,{modelValue:c.value.provincia,"onUpdate:modelValue":l[1]||(l[1]=v=>c.value.provincia=v),label:"Provincia *",placeholder:"es. MI",required:"",maxlength:"2",disabled:z.value},null,8,["modelValue","disabled"]),d(A,{modelValue:c.value.foglio,"onUpdate:modelValue":l[2]||(l[2]=v=>c.value.foglio=v),label:"Foglio (opzionale)",placeholder:"es. 42",type:"number",disabled:z.value},null,8,["modelValue","disabled"]),d(A,{modelValue:c.value.mappale,"onUpdate:modelValue":l[3]||(l[3]=v=>c.value.mappale=v),label:"Mappale (opzionale)",placeholder:"es. 156",type:"number",disabled:z.value},null,8,["modelValue","disabled"])]),e("div",Ne,[d(S,{type:"submit",variant:"primary",loading:z.value,disabled:!c.value.comune||!c.value.provincia,icon:"pi pi-search"},{default:g(()=>l[6]||(l[6]=[m(" Cerca in SISTER ")])),_:1,__:[6]},8,["loading","disabled"]),d(S,{type:"button",variant:"secondary",onClick:u,disabled:z.value,icon:"pi pi-times"},{default:g(()=>l[7]||(l[7]=[m(" Pulisci ")])),_:1,__:[7]},8,["disabled"])])],32)]),b.value.length>0||V.value?(p(),_("div",Le,[l[22]||(l[22]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-list"}),m(" Risultati SISTER ")],-1)),b.value.length>0?(p(),_("div",Be,[e("div",Oe,[e("div",qe,[e("span",He,n(b.value.length),1),l[10]||(l[10]=e("span",{class:"stat-label"},"Particelle trovate",-1))]),e("div",Ze,[e("span",Ge,n(a.value.length),1),l[11]||(l[11]=e("span",{class:"stat-label"},"Selezionate",-1))])]),e("div",Je,[d(S,{variant:"secondary",size:"sm",onClick:D,icon:"pi pi-check"},{default:g(()=>l[12]||(l[12]=[m(" Seleziona Tutto ")])),_:1,__:[12]}),d(S,{variant:"secondary",size:"sm",onClick:F,icon:"pi pi-times"},{default:g(()=>l[13]||(l[13]=[m(" Deseleziona Tutto ")])),_:1,__:[13]})])])):y("",!0),V.value&&b.value.length===0?(p(),T(se,{key:1,icon:"pi pi-search",title:"Nessun risultato",description:"Non sono state trovate particelle con i criteri di ricerca specificati."})):y("",!0),b.value.length>0?(p(),_("div",Qe,[e("table",Xe,[e("thead",null,[e("tr",null,[e("th",null,[e("input",{type:"checkbox",checked:o.value,onChange:B,class:"select-checkbox"},null,40,Ke)]),l[14]||(l[14]=e("th",null,"Comune",-1)),l[15]||(l[15]=e("th",null,"Foglio",-1)),l[16]||(l[16]=e("th",null,"Mappale",-1)),l[17]||(l[17]=e("th",null,"Sub.",-1)),l[18]||(l[18]=e("th",null,"Superficie",-1)),l[19]||(l[19]=e("th",null,"Categoria",-1)),l[20]||(l[20]=e("th",null,"Intestatario",-1)),l[21]||(l[21]=e("th",null,"Codice Fiscale",-1))])]),e("tbody",null,[(p(!0),_(X,null,K(b.value,v=>(p(),_("tr",{key:`${v.comune}-${v.foglio}-${v.mappale}-${v.subalterno||0}`,class:q(["result-row",{selected:f(v)}])},[e("td",null,[e("input",{type:"checkbox",checked:f(v),onChange:M=>$(v),class:"select-checkbox"},null,40,We)]),e("td",null,n(v.comune),1),e("td",null,[e("span",Ye,n(v.foglio),1)]),e("td",null,[e("span",xe,n(v.mappale),1)]),e("td",null,[e("span",et,n(v.subalterno||"-"),1)]),e("td",null,[e("span",tt,n(U(v.superficie)),1)]),e("td",null,[e("span",at,n(v.categoria),1)]),e("td",null,[e("div",lt,[e("div",st,n(v.intestato_nome),1),v.indirizzo?(p(),_("div",ot,n(v.indirizzo),1)):y("",!0)])]),e("td",null,[e("span",it,n(v.intestato_cf),1)])],2))),128))])])])):y("",!0)])):y("",!0),a.value.length>0?(p(),_("div",nt,[l[27]||(l[27]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-upload"}),m(" Importazione Particelle ")],-1)),e("div",rt,[e("p",null,[l[23]||(l[23]=m(" Stai per importare ")),e("strong",null,n(a.value.length),1),l[24]||(l[24]=m(" particelle catastali nel progetto. "))]),l[25]||(l[25]=e("p",{class:"import-note"}," I dati verranno verificati e validati durante l'importazione. ",-1))]),e("div",ut,[d(S,{variant:"primary",onClick:x,loading:k.value,disabled:a.value.length===0,icon:"pi pi-upload"},{default:g(()=>[m(" Importa "+n(a.value.length)+" Particelle ",1)]),_:1},8,["loading","disabled"]),d(S,{variant:"secondary",onClick:l[4]||(l[4]=v=>w.$emit("close")),disabled:k.value,icon:"pi pi-times"},{default:g(()=>l[26]||(l[26]=[m(" Annulla ")])),_:1,__:[26]},8,["disabled"])])])):y("",!0),k.value?(p(),_("div",dt,[e("div",pt,[l[28]||(l[28]=e("h4",null,"Importazione in corso...",-1)),e("span",ct,n(i.value.current)+" / "+n(i.value.total),1)]),e("div",vt,[e("div",{class:"progress-fill",style:Pe({width:`${i.value.current/i.value.total*100}%`})},null,4)])])):y("",!0),C.value?(p(),_("div",mt,[e("div",ft,[e("i",{class:q(["pi",C.value.success?"pi-check-circle success-icon":"pi-exclamation-triangle warning-icon"])},null,2),e("h3",null,n(C.value.success?"Importazione Completata":"Importazione Completata con Errori"),1)]),e("div",gt,[e("div",_t,[e("span",yt,n(C.value.imported_count),1),l[29]||(l[29]=e("span",{class:"stat-label"},"Particelle Importate",-1))]),C.value.errors.length>0?(p(),_("div",bt,[e("span",$t,n(C.value.errors.length),1),l[30]||(l[30]=e("span",{class:"stat-label"},"Errori",-1))])):y("",!0),e("div",kt,[e("span",Ct,n(C.value.total_found),1),l[31]||(l[31]=e("span",{class:"stat-label"},"Totale Selezionate",-1))])]),C.value.errors.length>0?(p(),_("div",wt,[l[32]||(l[32]=e("h4",null,"Dettagli Errori:",-1)),e("ul",St,[(p(!0),_(X,null,K(C.value.errors,(v,M)=>(p(),_("li",{key:M,class:"error-item"},n(v),1))),128))])])):y("",!0),e("div",Pt,[C.value.success?(p(),T(S,{key:0,variant:"primary",onClick:l[5]||(l[5]=v=>w.$emit("success",C.value)),icon:"pi pi-check"},{default:g(()=>l[33]||(l[33]=[m(" Continua ")])),_:1,__:[33]})):y("",!0),d(S,{variant:"secondary",onClick:O,icon:"pi pi-refresh"},{default:g(()=>l[34]||(l[34]=[m(" Nuova Ricerca ")])),_:1,__:[34]})])])):y("",!0)]))}}),zt=Y(It,[["__scopeId","data-v-d914a1b2"]]),Vt={class:"property-edit-form"},Et={class:"form-section"},ht={class:"form-grid"},At={class:"form-section"},Tt={class:"form-grid"},Dt={class:"form-group full-width"},Mt={class:"form-section"},Rt={class:"form-grid"},Ut={class:"form-group"},jt=["disabled"],Ft={key:0,class:"error-message"},Nt={key:0,class:"error-section"},Lt={class:"error-alert"},Bt={class:"form-actions"},Ot=W({__name:"PropertyEditForm",props:{property:{}},emits:["close","success"],setup(L,{emit:H}){const I=L,z=H,k=P(!1),V=P(""),b=P({}),a=P({comune:"",sezione:"",foglio:"",particella:"",subalterno:"",owner_name:"",owner_fiscal_code:"",owner_address:"",property_type:"",surface_area:"",cadastral_income:"",estimated_value:"",compensation_amount:""}),C=j(()=>a.value.comune&&a.value.foglio&&a.value.particella&&a.value.owner_name&&a.value.property_type),c=()=>(b.value={},a.value.comune||(b.value.comune="Il comune è obbligatorio"),a.value.foglio||(b.value.foglio="Il foglio è obbligatorio"),a.value.particella||(b.value.particella="La particella è obbligatoria"),a.value.owner_name||(b.value.owner_name="Il nome del proprietario è obbligatorio"),a.value.property_type||(b.value.property_type="Il tipo di immobile è obbligatorio"),a.value.owner_fiscal_code&&(/^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/.test(a.value.owner_fiscal_code.toUpperCase())||(b.value.owner_fiscal_code="Formato codice fiscale non valido")),Object.keys(b.value).length===0),i=async()=>{if(c()){k.value=!0,V.value="";try{const h=localStorage.getItem("token"),u={comune:a.value.comune,sezione:a.value.sezione||null,foglio:a.value.foglio,particella:a.value.particella,subalterno:a.value.subalterno||null,owner_name:a.value.owner_name,owner_fiscal_code:a.value.owner_fiscal_code||null,owner_address:a.value.owner_address||null,property_type:a.value.property_type,surface_area:a.value.surface_area?parseFloat(a.value.surface_area):null,cadastral_income:a.value.cadastral_income?parseFloat(a.value.cadastral_income):null,estimated_value:a.value.estimated_value?parseFloat(a.value.estimated_value):null,compensation_amount:a.value.compensation_amount?parseFloat(a.value.compensation_amount):null},f=await fetch(`/api/properties/${I.property.id}`,{method:"PUT",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"},body:JSON.stringify(u)});if(!f.ok){if(f.status===400){const D=await f.json();V.value=D.detail||"Errore nella validazione dei dati"}else throw new Error(`HTTP error! status: ${f.status}`);return}const $=await f.json();z("success",$)}catch(h){console.error("Update property error:",h),V.value="Errore durante l'aggiornamento della particella"}finally{k.value=!1}}},o=()=>{I.property&&(a.value={comune:I.property.comune||"",sezione:I.property.sezione||"",foglio:I.property.foglio||"",particella:I.property.particella||"",subalterno:I.property.subalterno||"",owner_name:I.property.owner_name||"",owner_fiscal_code:I.property.owner_fiscal_code||"",owner_address:I.property.owner_address||"",property_type:I.property.property_type||"",surface_area:I.property.surface_area?I.property.surface_area.toString():"",cadastral_income:I.property.cadastral_income?I.property.cadastral_income.toString():"",estimated_value:I.property.estimated_value?I.property.estimated_value.toString():"",compensation_amount:I.property.compensation_amount?I.property.compensation_amount.toString():""})};return le(()=>{o()}),(h,u)=>(p(),_("div",Vt,[e("form",{onSubmit:ae(i,["prevent"]),class:"edit-form"},[e("div",Et,[u[14]||(u[14]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-map"}),m(" Dati Catastali ")],-1)),e("div",ht,[d(A,{modelValue:a.value.comune,"onUpdate:modelValue":u[0]||(u[0]=f=>a.value.comune=f),label:"Comune *",placeholder:"es. Milano",required:"",disabled:k.value,error:b.value.comune},null,8,["modelValue","disabled","error"]),d(A,{modelValue:a.value.sezione,"onUpdate:modelValue":u[1]||(u[1]=f=>a.value.sezione=f),label:"Sezione",placeholder:"es. A",disabled:k.value},null,8,["modelValue","disabled"]),d(A,{modelValue:a.value.foglio,"onUpdate:modelValue":u[2]||(u[2]=f=>a.value.foglio=f),label:"Foglio *",placeholder:"es. 42",required:"",disabled:k.value,error:b.value.foglio},null,8,["modelValue","disabled","error"]),d(A,{modelValue:a.value.particella,"onUpdate:modelValue":u[3]||(u[3]=f=>a.value.particella=f),label:"Particella *",placeholder:"es. 156",required:"",disabled:k.value,error:b.value.particella},null,8,["modelValue","disabled","error"]),d(A,{modelValue:a.value.subalterno,"onUpdate:modelValue":u[4]||(u[4]=f=>a.value.subalterno=f),label:"Subalterno",placeholder:"es. 1",disabled:k.value},null,8,["modelValue","disabled"])])]),e("div",At,[u[15]||(u[15]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-user"}),m(" Dati Proprietario ")],-1)),e("div",Tt,[d(A,{modelValue:a.value.owner_name,"onUpdate:modelValue":u[5]||(u[5]=f=>a.value.owner_name=f),label:"Nome Proprietario *",placeholder:"es. Rossi Mario",required:"",disabled:k.value,error:b.value.owner_name},null,8,["modelValue","disabled","error"]),d(A,{modelValue:a.value.owner_fiscal_code,"onUpdate:modelValue":u[6]||(u[6]=f=>a.value.owner_fiscal_code=f),label:"Codice Fiscale",placeholder:"es. ****************",maxlength:"16",disabled:k.value,error:b.value.owner_fiscal_code},null,8,["modelValue","disabled","error"]),e("div",Dt,[d(A,{modelValue:a.value.owner_address,"onUpdate:modelValue":u[7]||(u[7]=f=>a.value.owner_address=f),label:"Indirizzo",placeholder:"es. Via Roma 123, Milano (MI)",disabled:k.value},null,8,["modelValue","disabled"])])])]),e("div",Mt,[u[18]||(u[18]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-cog"}),m(" Caratteristiche Immobile ")],-1)),e("div",Rt,[e("div",Ut,[u[17]||(u[17]=e("label",null,"Tipo Immobile *",-1)),J(e("select",{"onUpdate:modelValue":u[8]||(u[8]=f=>a.value.property_type=f),class:"form-select",required:"",disabled:k.value},u[16]||(u[16]=[Ie('<option value="" data-v-8818b08b>Seleziona tipo</option><option value="SEMINATIVO" data-v-8818b08b>Seminativo</option><option value="ABITAZIONE" data-v-8818b08b>Abitazione</option><option value="NEGOZIO" data-v-8818b08b>Negozio</option><option value="CAPANNONE" data-v-8818b08b>Capannone</option><option value="VILLA" data-v-8818b08b>Villa</option><option value="ULIVETO" data-v-8818b08b>Uliveto</option><option value="APPARTAMENTO" data-v-8818b08b>Appartamento</option>',8)]),8,jt),[[Q,a.value.property_type]]),b.value.property_type?(p(),_("span",Ft,n(b.value.property_type),1)):y("",!0)]),d(A,{modelValue:a.value.surface_area,"onUpdate:modelValue":u[9]||(u[9]=f=>a.value.surface_area=f),label:"Superficie (m²)",placeholder:"es. 1500.50",type:"number",step:"0.01",min:"0",disabled:k.value},null,8,["modelValue","disabled"]),d(A,{modelValue:a.value.cadastral_income,"onUpdate:modelValue":u[10]||(u[10]=f=>a.value.cadastral_income=f),label:"Reddito Catastale (€)",placeholder:"es. 850.00",type:"number",step:"0.01",min:"0",disabled:k.value},null,8,["modelValue","disabled"]),d(A,{modelValue:a.value.estimated_value,"onUpdate:modelValue":u[11]||(u[11]=f=>a.value.estimated_value=f),label:"Valore Stimato (€)",placeholder:"es. 250000.00",type:"number",step:"0.01",min:"0",disabled:k.value},null,8,["modelValue","disabled"]),d(A,{modelValue:a.value.compensation_amount,"onUpdate:modelValue":u[12]||(u[12]=f=>a.value.compensation_amount=f),label:"Indennità Determinata (€)",placeholder:"es. 200000.00",type:"number",step:"0.01",min:"0",disabled:k.value},null,8,["modelValue","disabled"])])]),V.value?(p(),_("div",Nt,[e("div",Lt,[u[19]||(u[19]=e("i",{class:"pi pi-exclamation-triangle"},null,-1)),e("span",null,n(V.value),1)])])):y("",!0),e("div",Bt,[d(S,{type:"button",variant:"secondary",onClick:u[13]||(u[13]=f=>h.$emit("close")),disabled:k.value,icon:"pi pi-times"},{default:g(()=>u[20]||(u[20]=[m(" Annulla ")])),_:1,__:[20]},8,["disabled"]),d(S,{type:"submit",variant:"primary",loading:k.value,disabled:!C.value,icon:"pi pi-save"},{default:g(()=>u[21]||(u[21]=[m(" Salva Modifiche ")])),_:1,__:[21]},8,["loading","disabled"])])],32)]))}}),qt=Y(Ot,[["__scopeId","data-v-8818b08b"]]),Ht={class:"property-detail-view"},Zt={class:"detail-container"},Gt={class:"detail-header"},Jt={class:"header-content"},Qt={class:"property-title"},Xt={key:0},Kt={class:"property-status"},Wt={class:"header-actions"},Yt={class:"detail-content"},xt={class:"detail-section"},ea={class:"info-grid"},ta={class:"info-item"},aa={class:"info-value"},la={key:0,class:"info-item"},sa={class:"info-value"},oa={class:"info-item"},ia={class:"info-value cadastral-ref"},na={class:"info-item"},ra={class:"info-value cadastral-ref"},ua={key:1,class:"info-item"},da={class:"info-value cadastral-ref"},pa={class:"detail-section"},ca={class:"info-grid"},va={class:"info-item"},ma={class:"info-value"},fa={key:0,class:"info-item"},ga={class:"info-value fiscal-code"},_a={key:1,class:"info-item full-width"},ya={class:"info-value"},ba={class:"detail-section"},$a={class:"info-grid"},ka={class:"info-item"},Ca={class:"info-value"},wa={class:"property-type-badge"},Sa={key:0,class:"info-item"},Pa={class:"info-value"},Ia={key:1,class:"info-item"},za={class:"info-value"},Va={key:0,class:"detail-section"},Ea={class:"info-grid"},ha={key:0,class:"info-item"},Aa={class:"info-value"},Ta={key:1,class:"info-item"},Da={class:"info-value"},Ma={class:"detail-section"},Ra={class:"timeline"},Ua={class:"timeline-item"},ja={class:"timeline-content"},Fa={key:0,class:"timeline-item"},Na={class:"timeline-content"},La={class:"detail-section"},Ba={class:"action-buttons"},Oa=W({__name:"PropertyDetailView",props:{property:{}},emits:["close","edit","delete"],setup(L,{emit:H}){const I=L,z=i=>i.estimated_value?i.estimated_value&&!i.compensation_amount?"constraint":i.compensation_amount?"utility":"preparation":"preparation",k=i=>({preparation:"In Preparazione",constraint:"Vincolo Apposto",utility:"Pubblica Utilità",compensation:"Indennità Determinata",completed:"Completato"})[i]||i,V=i=>({preparation:"status-preparation",constraint:"status-constraint",utility:"status-utility",compensation:"status-compensation",completed:"status-completed"})[i]||"status-default",b=i=>i?i.toLocaleString("it-IT")+" m²":"0 m²",a=i=>i?i.toLocaleString("it-IT",{minimumFractionDigits:2}):"0,00",C=i=>i?new Date(i).toLocaleString("it-IT"):"",c=()=>{console.log("Downloading property report for:",I.property.id)};return(i,o)=>(p(),_("div",Ht,[e("div",Zt,[e("div",Gt,[e("div",Jt,[e("h2",Qt,[m(" Foglio "+n(i.property.foglio)+", Mappale "+n(i.property.particella)+" ",1),i.property.subalterno?(p(),_("span",Xt,", Sub. "+n(i.property.subalterno),1)):y("",!0)]),e("div",Kt,[e("span",{class:q(["status-badge",V(z(i.property))])},n(k(z(i.property))),3)])]),e("div",Wt,[d(S,{variant:"secondary",onClick:o[0]||(o[0]=h=>i.$emit("edit",i.property)),icon:"pi pi-pencil"},{default:g(()=>o[4]||(o[4]=[m(" Modifica ")])),_:1,__:[4]}),d(S,{variant:"secondary",onClick:o[1]||(o[1]=h=>i.$emit("close")),icon:"pi pi-times"},{default:g(()=>o[5]||(o[5]=[m(" Chiudi ")])),_:1,__:[5]})])]),e("div",Yt,[e("div",xt,[o[11]||(o[11]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-map"}),m(" Dati Catastali ")],-1)),e("div",ea,[e("div",ta,[o[6]||(o[6]=e("span",{class:"info-label"},"Comune:",-1)),e("span",aa,n(i.property.comune),1)]),i.property.sezione?(p(),_("div",la,[o[7]||(o[7]=e("span",{class:"info-label"},"Sezione:",-1)),e("span",sa,n(i.property.sezione),1)])):y("",!0),e("div",oa,[o[8]||(o[8]=e("span",{class:"info-label"},"Foglio:",-1)),e("span",ia,n(i.property.foglio),1)]),e("div",na,[o[9]||(o[9]=e("span",{class:"info-label"},"Particella:",-1)),e("span",ra,n(i.property.particella),1)]),i.property.subalterno?(p(),_("div",ua,[o[10]||(o[10]=e("span",{class:"info-label"},"Subalterno:",-1)),e("span",da,n(i.property.subalterno),1)])):y("",!0)])]),e("div",pa,[o[15]||(o[15]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-user"}),m(" Dati Proprietario ")],-1)),e("div",ca,[e("div",va,[o[12]||(o[12]=e("span",{class:"info-label"},"Nome:",-1)),e("span",ma,n(i.property.owner_name),1)]),i.property.owner_fiscal_code?(p(),_("div",fa,[o[13]||(o[13]=e("span",{class:"info-label"},"Codice Fiscale:",-1)),e("span",ga,n(i.property.owner_fiscal_code),1)])):y("",!0),i.property.owner_address?(p(),_("div",_a,[o[14]||(o[14]=e("span",{class:"info-label"},"Indirizzo:",-1)),e("span",ya,n(i.property.owner_address),1)])):y("",!0)])]),e("div",ba,[o[19]||(o[19]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-home"}),m(" Caratteristiche Immobile ")],-1)),e("div",$a,[e("div",ka,[o[16]||(o[16]=e("span",{class:"info-label"},"Tipo:",-1)),e("span",Ca,[e("span",wa,n(i.property.property_type),1)])]),i.property.surface_area?(p(),_("div",Sa,[o[17]||(o[17]=e("span",{class:"info-label"},"Superficie:",-1)),e("span",Pa,n(b(i.property.surface_area)),1)])):y("",!0),i.property.cadastral_income?(p(),_("div",Ia,[o[18]||(o[18]=e("span",{class:"info-label"},"Reddito Catastale:",-1)),e("span",za,"€ "+n(a(i.property.cadastral_income)),1)])):y("",!0)])]),i.property.estimated_value||i.property.compensation_amount?(p(),_("div",Va,[o[22]||(o[22]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-euro"}),m(" Dati Economici ")],-1)),e("div",Ea,[i.property.estimated_value?(p(),_("div",ha,[o[20]||(o[20]=e("span",{class:"info-label"},"Valore Stimato:",-1)),e("span",Aa,"€ "+n(a(i.property.estimated_value)),1)])):y("",!0),i.property.compensation_amount?(p(),_("div",Ta,[o[21]||(o[21]=e("span",{class:"info-label"},"Indennità:",-1)),e("span",Da,"€ "+n(a(i.property.compensation_amount)),1)])):y("",!0)])])):y("",!0),e("div",Ma,[o[28]||(o[28]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-clock"}),m(" Cronologia ")],-1)),e("div",Ra,[e("div",Ua,[o[24]||(o[24]=e("div",{class:"timeline-marker active"},null,-1)),e("div",ja,[o[23]||(o[23]=e("h4",null,"Particella Creata",-1)),e("p",null,n(C(i.property.created_at)),1)])]),i.property.updated_at&&i.property.updated_at!==i.property.created_at?(p(),_("div",Fa,[o[26]||(o[26]=e("div",{class:"timeline-marker active"},null,-1)),e("div",Na,[o[25]||(o[25]=e("h4",null,"Ultima Modifica",-1)),e("p",null,n(C(i.property.updated_at)),1)])])):y("",!0),o[27]||(o[27]=e("div",{class:"timeline-item"},[e("div",{class:"timeline-marker pending"}),e("div",{class:"timeline-content"},[e("h4",null,"Fase Preparatoria"),e("p",null,"In attesa di avvio procedimento")])],-1))])]),e("div",La,[o[32]||(o[32]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-cog"}),m(" Azioni ")],-1)),e("div",Ba,[d(S,{variant:"primary",onClick:o[2]||(o[2]=h=>i.$emit("edit",i.property)),icon:"pi pi-pencil"},{default:g(()=>o[29]||(o[29]=[m(" Modifica Particella ")])),_:1,__:[29]}),d(S,{variant:"secondary",onClick:c,icon:"pi pi-download"},{default:g(()=>o[30]||(o[30]=[m(" Scarica Report ")])),_:1,__:[30]}),z(i.property)==="preparation"?(p(),T(S,{key:0,variant:"danger",onClick:o[3]||(o[3]=h=>i.$emit("delete",i.property)),icon:"pi pi-trash"},{default:g(()=>o[31]||(o[31]=[m(" Elimina Particella ")])),_:1,__:[31]})):y("",!0)])])])])]))}}),qa=Y(Oa,[["__scopeId","data-v-6f672f48"]]),Ha={class:"admin-layout"},Za={class:"admin-content"},Ga={key:0},Ja={class:"project-info"},Qa={class:"project-details"},Xa={class:"detail-item"},Ka={class:"value"},Wa={class:"detail-item"},Ya={class:"value"},xa={class:"detail-item"},el={class:"value"},tl={class:"detail-item"},al={class:"stats-section"},ll={class:"stats-grid"},sl={class:"stat-card"},ol={class:"stat-content"},il={class:"stat-card"},nl={class:"stat-content"},rl={class:"stat-card"},ul={class:"stat-content"},dl={class:"stat-card"},pl={class:"stat-content"},cl={class:"filters-grid"},vl={class:"filter-group"},ml={class:"filter-group"},fl=["value"],gl={class:"filter-group"},_l={class:"table-actions"},yl={class:"empty-actions"},bl={key:3,class:"table-container"},$l={class:"properties-table"},kl={class:"cadastral-ref"},Cl={class:"cadastral-ref"},wl={class:"cadastral-ref"},Sl={class:"owner-info"},Pl={key:0},Il={class:"area-value"},zl={class:"actions"},Vl={key:0,class:"pagination"},El={class:"pagination-info"},hl={class:"delete-confirmation"},Al={key:0},Tl={class:"modal-actions"},te=15,Dl=W({__name:"PropertyList",setup(L){const H=he(),I=ze(),z=j(()=>I.params.id),k=P(!1),V=P(""),b=P(!1),a=P(null),C=P([]),c=P(null),i=P(!1),o=P(!1),h=P(!1),u=P(!1),f=P(!1),$=P({search:"",status:"",sheet:"",destination:""}),D=P(1),F=P("foglio"),B=P("asc"),x=j(()=>[...new Set(C.value.map(t=>t.foglio))].sort((t,E)=>parseInt(t)-parseInt(E))),O=j(()=>({preparation:C.value.filter(s=>!s.estimated_value).length,constraint:C.value.filter(s=>s.estimated_value&&!s.compensation_amount).length,utility:C.value.filter(s=>s.compensation_amount).length,completed:0})),U=j(()=>{let s=[...C.value];if($.value.search){const t=$.value.search.toLowerCase();s=s.filter(E=>E.foglio.toString().includes(t)||E.particella.toString().includes(t)||E.subalterno&&E.subalterno.toString().includes(t)||E.owner_name.toLowerCase().includes(t)||E.owner_fiscal_code&&E.owner_fiscal_code.toLowerCase().includes(t))}return $.value.status&&(s=s.filter(t=>t.status===$.value.status)),$.value.sheet&&(s=s.filter(t=>t.foglio.toString()===$.value.sheet)),$.value.destination&&(s=s.filter(t=>t.property_type===$.value.destination)),s.sort((t,E)=>{let r=t[F.value],R=E[F.value];return typeof r=="string"&&(r=r.toLowerCase(),R=R.toLowerCase()),B.value==="asc"?r<R?-1:r>R?1:0:r>R?-1:r<R?1:0}),s}),w=j(()=>Math.ceil(U.value.length/te)),l=j(()=>{const s=(D.value-1)*te;return U.value.slice(s,s+te)});Ve($,()=>{D.value=1},{deep:!0});const v=async()=>{if(z.value)try{const s=localStorage.getItem("token"),t=await fetch(`/api/projects/${z.value}`,{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);a.value=await t.json()}catch(s){V.value="Errore nel caricamento del progetto",console.error("Load project error:",s)}},M=async()=>{if(z.value){k.value=!0,V.value="";try{const s=localStorage.getItem("token"),t=await fetch(`/api/properties/?project_id=${z.value}`,{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const E=await t.json();C.value=Array.isArray(E)?E:E.properties||[]}catch(s){V.value="Errore nel caricamento delle particelle",console.error("Load properties error:",s)}finally{k.value=!1}}},oe=()=>{M()},ie=()=>{const s=U.value;console.log("Exporting properties:",s)},G=s=>{F.value===s?B.value=B.value==="asc"?"desc":"asc":(F.value=s,B.value="asc")},ne=s=>{c.value=s,u.value=!0},re=s=>{c.value=s,h.value=!0},ue=s=>{c.value=s,f.value=!0},de=async()=>{if(c.value){b.value=!0;try{const s=localStorage.getItem("token"),t=await fetch(`/api/properties/${c.value.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);C.value=C.value.filter(E=>E.id!==c.value.id),f.value=!1,c.value=null}catch{V.value="Errore nell'eliminazione della particella"}finally{b.value=!1}}},pe=s=>{M(),i.value=!1,s.imported_count>0&&console.log(`✅ Importate ${s.imported_count} particelle con successo`)},ce=s=>{M(),o.value=!1,console.log("✅ Particella creata con successo:",s)},ve=s=>{M(),h.value=!1,c.value=null,console.log("✅ Particella aggiornata con successo:",s)},me=s=>{u.value=!1,c.value=s,h.value=!0},fe=s=>{u.value=!1,c.value=s,f.value=!0},ge=s=>new Date(s).toLocaleDateString("it-IT"),_e=s=>s?s.toLocaleString("it-IT"):"-",ye=s=>({comune:"Comune",provincia:"Provincia",regione:"Regione",stato:"Stato"})[s]||s,be=s=>({planning:"Pianificazione",feasibility:"Studio Fattibilità",preliminary:"Progetto Preliminare",definitive:"Progetto Definitivo",executive:"Progetto Esecutivo",expropriation:"Espropriazione",execution:"Esecuzione",completed:"Completato"})[s]||s,$e=s=>({residential:"Residenziale",commercial:"Commerciale",industrial:"Industriale",agricultural:"Agricola",mixed:"Mista"})[s]||s,ke=s=>({preparation:"In Preparazione",constraint:"Vincolo Apposto",utility:"Pubblica Utilità",compensation:"Indennità Determinata",pending_acceptance:"In Attesa Accettazione",accepted:"Accettata",rejected:"Rifiutata",decree_issued:"Decreto Emesso",expropriated:"Espropriata"})[s]||s,Ce=s=>({planning:"status-planning",feasibility:"status-feasibility",preliminary:"status-preliminary",definitive:"status-definitive",executive:"status-executive",expropriation:"status-expropriation",execution:"status-execution",completed:"status-completed"})[s]||"status-default",we=s=>({preparation:"prop-status-preparation",constraint:"prop-status-constraint",utility:"prop-status-utility",compensation:"prop-status-compensation",pending_acceptance:"prop-status-pending",accepted:"prop-status-accepted",rejected:"prop-status-rejected",decree_issued:"prop-status-decree",expropriated:"prop-status-expropriated"})[s]||"prop-status-default",Se=s=>`destination-${s}`,ee=s=>s.estimated_value?s.estimated_value&&!s.compensation_amount?"constraint":s.compensation_amount?"utility":"preparation":"preparation";return le(async()=>{await v(),await M()}),(s,t)=>{var E;return p(),_("div",Ha,[d(Ae),e("div",Za,[e("main",null,[d(Te,{title:`Particelle Catastali - ${((E=a.value)==null?void 0:E.name)||"Progetto"}`,subtitle:"Gestione particelle catastali per espropriazione secondo D.P.R. 327/2001"},{actions:g(()=>[d(S,{variant:"secondary",onClick:t[0]||(t[0]=r=>Ee(H).push("/admin/projects")),icon:"pi pi-arrow-left"},{default:g(()=>t[25]||(t[25]=[m(" Torna ai Progetti ")])),_:1,__:[25]}),d(S,{variant:"primary",onClick:t[1]||(t[1]=r=>i.value=!0),icon:"pi pi-upload"},{default:g(()=>t[26]||(t[26]=[m(" Importa da SISTER ")])),_:1,__:[26]}),d(S,{variant:"primary",onClick:t[2]||(t[2]=r=>o.value=!0),icon:"pi pi-plus"},{default:g(()=>t[27]||(t[27]=[m(" Nuova Particella ")])),_:1,__:[27]})]),_:1},8,["title"]),a.value?(p(),_("section",Ga,[d(N,null,{default:g(()=>[e("div",Ja,[e("div",Qa,[e("div",Xa,[t[28]||(t[28]=e("span",{class:"label"},"Progetto:",-1)),e("span",Ka,n(a.value.name),1)]),e("div",Wa,[t[29]||(t[29]=e("span",{class:"label"},"Ente:",-1)),e("span",Ya,n(ye(a.value.entity)),1)]),e("div",xa,[t[30]||(t[30]=e("span",{class:"label"},"Comune:",-1)),e("span",el,n(a.value.municipality)+" ("+n(a.value.province)+")",1)]),e("div",tl,[t[31]||(t[31]=e("span",{class:"label"},"Stato Progetto:",-1)),e("span",{class:q(["status-badge",Ce(a.value.status)])},n(be(a.value.status)),3)])])])]),_:1})])):y("",!0),e("section",al,[e("div",ll,[d(N,null,{default:g(()=>[e("div",sl,[t[33]||(t[33]=e("div",{class:"stat-icon preparation"},[e("i",{class:"pi pi-file-o"})],-1)),e("div",ol,[e("h3",null,n(O.value.preparation),1),t[32]||(t[32]=e("p",null,"In Preparazione",-1))])])]),_:1}),d(N,null,{default:g(()=>[e("div",il,[t[35]||(t[35]=e("div",{class:"stat-icon constraint"},[e("i",{class:"pi pi-lock"})],-1)),e("div",nl,[e("h3",null,n(O.value.constraint),1),t[34]||(t[34]=e("p",null,"Vincolo Apposto",-1))])])]),_:1}),d(N,null,{default:g(()=>[e("div",rl,[t[37]||(t[37]=e("div",{class:"stat-icon utility"},[e("i",{class:"pi pi-check"})],-1)),e("div",ul,[e("h3",null,n(O.value.utility),1),t[36]||(t[36]=e("p",null,"Pubblica Utilità",-1))])])]),_:1}),d(N,null,{default:g(()=>[e("div",dl,[t[39]||(t[39]=e("div",{class:"stat-icon completed"},[e("i",{class:"pi pi-check-circle"})],-1)),e("div",pl,[e("h3",null,n(O.value.completed),1),t[38]||(t[38]=e("p",null,"Espropriate",-1))])])]),_:1})])]),e("section",null,[d(N,{title:"Filtri di ricerca"},{default:g(()=>[e("div",cl,[d(A,{modelValue:$.value.search,"onUpdate:modelValue":t[3]||(t[3]=r=>$.value.search=r),placeholder:"Cerca per foglio, mappale, subalterno, proprietario...",label:"Ricerca",icon:"pi pi-search"},null,8,["modelValue"]),e("div",vl,[t[41]||(t[41]=e("label",null,"Stato Particella",-1)),J(e("select",{"onUpdate:modelValue":t[4]||(t[4]=r=>$.value.status=r),class:"filter-select"},t[40]||(t[40]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"preparation"},"In Preparazione",-1),e("option",{value:"constraint"},"Vincolo Apposto",-1),e("option",{value:"utility"},"Pubblica Utilità Dichiarata",-1),e("option",{value:"compensation"},"Indennità Determinata",-1),e("option",{value:"pending_acceptance"},"In Attesa Accettazione",-1),e("option",{value:"accepted"},"Accettata",-1),e("option",{value:"rejected"},"Rifiutata",-1),e("option",{value:"decree_issued"},"Decreto Emesso",-1),e("option",{value:"expropriated"},"Espropriata",-1)]),512),[[Q,$.value.status]])]),e("div",ml,[t[43]||(t[43]=e("label",null,"Foglio",-1)),J(e("select",{"onUpdate:modelValue":t[5]||(t[5]=r=>$.value.sheet=r),class:"filter-select"},[t[42]||(t[42]=e("option",{value:""},"Tutti i fogli",-1)),(p(!0),_(X,null,K(x.value,r=>(p(),_("option",{key:r,value:r}," Foglio "+n(r),9,fl))),128))],512),[[Q,$.value.sheet]])]),e("div",gl,[t[45]||(t[45]=e("label",null,"Destinazione",-1)),J(e("select",{"onUpdate:modelValue":t[6]||(t[6]=r=>$.value.destination=r),class:"filter-select"},t[44]||(t[44]=[e("option",{value:""},"Tutte le destinazioni",-1),e("option",{value:"residential"},"Residenziale",-1),e("option",{value:"commercial"},"Commerciale",-1),e("option",{value:"industrial"},"Industriale",-1),e("option",{value:"agricultural"},"Agricola",-1),e("option",{value:"mixed"},"Mista",-1)]),512),[[Q,$.value.destination]])])])]),_:1})]),e("section",null,[d(N,{title:"Particelle Catastali",subtitle:`${U.value.length} particelle trovate`},{actions:g(()=>[e("div",_l,[d(S,{variant:"secondary",size:"sm",onClick:ie,icon:"pi pi-download"},{default:g(()=>t[46]||(t[46]=[m(" Esporta ")])),_:1,__:[46]}),d(S,{variant:"secondary",size:"sm",onClick:oe,icon:"pi pi-refresh"},{default:g(()=>t[47]||(t[47]=[m(" Aggiorna ")])),_:1,__:[47]})])]),default:g(()=>[k.value?(p(),T(De,{key:0,message:"Caricamento particelle..."})):V.value?(p(),T(Me,{key:1,message:V.value,onRetry:M},null,8,["message"])):U.value.length===0?(p(),T(se,{key:2,icon:"pi pi-map",title:"Nessuna particella trovata",description:$.value.search||$.value.status||$.value.sheet?"Nessuna particella corrisponde ai filtri selezionati.":"Non ci sono ancora particelle catastali in questo progetto."},{default:g(()=>[e("div",yl,[!$.value.search&&!$.value.status&&!$.value.sheet?(p(),T(S,{key:0,variant:"primary",onClick:t[7]||(t[7]=r=>i.value=!0),icon:"pi pi-upload"},{default:g(()=>t[48]||(t[48]=[m(" Importa da SISTER ")])),_:1,__:[48]})):y("",!0),!$.value.search&&!$.value.status&&!$.value.sheet?(p(),T(S,{key:1,variant:"secondary",onClick:t[8]||(t[8]=r=>o.value=!0),icon:"pi pi-plus"},{default:g(()=>t[49]||(t[49]=[m(" Aggiungi Manualmente ")])),_:1,__:[49]})):y("",!0)])]),_:1},8,["description"])):(p(),_("div",bl,[e("table",$l,[e("thead",null,[e("tr",null,[e("th",{onClick:t[9]||(t[9]=r=>G("foglio")),class:"sortable"},t[50]||(t[50]=[m(" Foglio "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),e("th",{onClick:t[10]||(t[10]=r=>G("particella")),class:"sortable"},t[51]||(t[51]=[m(" Mappale "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[54]||(t[54]=e("th",null,"Subalterno",-1)),t[55]||(t[55]=e("th",null,"Proprietario",-1)),e("th",{onClick:t[11]||(t[11]=r=>G("surface_area")),class:"sortable"},t[52]||(t[52]=[m(" Superficie (mq) "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[56]||(t[56]=e("th",null,"Destinazione",-1)),e("th",{onClick:t[12]||(t[12]=r=>G("status")),class:"sortable"},t[53]||(t[53]=[m(" Stato "),e("i",{class:"pi pi-sort-alt sort-icon"},null,-1)])),t[57]||(t[57]=e("th",null,"Ultima Modifica",-1)),t[58]||(t[58]=e("th",null,"Azioni",-1))])]),e("tbody",null,[(p(!0),_(X,null,K(l.value,r=>(p(),_("tr",{key:r.id,class:"property-row"},[e("td",null,[e("span",kl,n(r.foglio),1)]),e("td",null,[e("span",Cl,n(r.particella),1)]),e("td",null,[e("span",wl,n(r.subalterno||"-"),1)]),e("td",null,[e("div",Sl,[e("h4",null,n(r.owner_name),1),r.owner_fiscal_code?(p(),_("p",Pl,n(r.owner_fiscal_code),1)):y("",!0)])]),e("td",null,[e("span",Il,n(_e(r.surface_area)),1)]),e("td",null,[e("span",{class:q(["destination-badge",Se(r.property_type)])},n($e(r.property_type)),3)]),e("td",null,[e("span",{class:q(["status-badge",we(ee(r))])},n(ke(ee(r))),3)]),e("td",null,n(ge(r.updated_at)),1),e("td",null,[e("div",zl,[d(S,{variant:"secondary",size:"sm",onClick:R=>ne(r),icon:"pi pi-eye",title:"Visualizza dettagli"},null,8,["onClick"]),d(S,{variant:"secondary",size:"sm",onClick:R=>re(r),icon:"pi pi-pencil",title:"Modifica particella"},null,8,["onClick"]),ee(r)==="preparation"?(p(),T(S,{key:0,variant:"danger",size:"sm",onClick:R=>ue(r),icon:"pi pi-trash",title:"Elimina particella"},null,8,["onClick"])):y("",!0)])])]))),128))])]),w.value>1?(p(),_("div",Vl,[d(S,{variant:"secondary",size:"sm",disabled:D.value===1,onClick:t[13]||(t[13]=r=>D.value--),icon:"pi pi-chevron-left"},null,8,["disabled"]),e("span",El," Pagina "+n(D.value)+" di "+n(w.value)+" ("+n(U.value.length)+" particelle) ",1),d(S,{variant:"secondary",size:"sm",disabled:D.value===w.value,onClick:t[14]||(t[14]=r=>D.value++),icon:"pi pi-chevron-right"},null,8,["disabled"])])):y("",!0)]))]),_:1},8,["subtitle"])]),i.value?(p(),T(Z,{key:1,onClose:t[16]||(t[16]=r=>i.value=!1),title:"Importa da SISTER",size:"extra-large"},{default:g(()=>[d(zt,{"project-id":parseInt(z.value),onClose:t[15]||(t[15]=r=>i.value=!1),onSuccess:pe},null,8,["project-id"])]),_:1})):y("",!0),o.value?(p(),T(Z,{key:2,onClose:t[18]||(t[18]=r=>o.value=!1),title:"Nuova Particella",size:"large"},{default:g(()=>[d(Re,{"project-id":parseInt(z.value),onClose:t[17]||(t[17]=r=>o.value=!1),onSuccess:ce},null,8,["project-id"])]),_:1})):y("",!0),h.value&&c.value?(p(),T(Z,{key:3,onClose:t[20]||(t[20]=r=>h.value=!1),title:"Modifica Particella",size:"large"},{default:g(()=>[d(qt,{property:c.value,onClose:t[19]||(t[19]=r=>h.value=!1),onSuccess:ve},null,8,["property"])]),_:1})):y("",!0),u.value&&c.value?(p(),T(Z,{key:4,onClose:t[22]||(t[22]=r=>u.value=!1),title:"Dettagli Particella",size:"extra-large"},{default:g(()=>[d(qa,{property:c.value,onClose:t[21]||(t[21]=r=>u.value=!1),onEdit:me,onDelete:fe},null,8,["property"])]),_:1})):y("",!0),f.value&&c.value?(p(),T(Z,{key:5,onClose:t[24]||(t[24]=r=>f.value=!1),title:"Conferma Eliminazione",size:"small"},{default:g(()=>[e("div",hl,[t[63]||(t[63]=e("div",{class:"warning-icon"},[e("i",{class:"pi pi-exclamation-triangle"})],-1)),t[64]||(t[64]=e("h3",null,"Elimina Particella",-1)),e("p",null,[t[59]||(t[59]=m(" Sei sicuro di voler eliminare la particella ")),e("strong",null,"Foglio "+n(c.value.foglio)+", Mappale "+n(c.value.particella),1),c.value.subalterno?(p(),_("span",Al,", Subalterno "+n(c.value.subalterno),1)):y("",!0),t[60]||(t[60]=m("? "))]),t[65]||(t[65]=e("p",{class:"warning-text"}," Questa azione eliminerà anche tutti i procedimenti associati alla particella. ",-1)),e("div",Tl,[d(S,{variant:"secondary",onClick:t[23]||(t[23]=r=>f.value=!1)},{default:g(()=>t[61]||(t[61]=[m(" Annulla ")])),_:1,__:[61]}),d(S,{variant:"danger",onClick:de,loading:b.value},{default:g(()=>t[62]||(t[62]=[m(" Elimina Particella ")])),_:1,__:[62]},8,["loading"])])])]),_:1})):y("",!0)])])])}}}),Jl=Y(Dl,[["__scopeId","data-v-8d686b73"]]);export{Jl as default};
