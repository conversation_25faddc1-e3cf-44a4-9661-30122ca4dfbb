import{c}from"./design-system-BvlP813R.js";import{d as p,m,c as s,n as u,h as a,b as r,H as o,t as l,o as t}from"./index-Bibk5VaL.js";const y={key:0,class:"px-6 py-4 border-b border-gray-200"},b={class:"flex items-center justify-between"},f={key:0,class:"text-lg font-medium text-gray-900"},h={key:1,class:"mt-1 text-sm text-gray-600"},g={key:0,class:"flex items-center space-x-2"},C={class:"px-6 py-4"},v={key:1,class:"px-6 py-4 border-t border-gray-200 bg-gray-50"},B=p({__name:"BaseCard",props:{title:{},subtitle:{},actions:{type:Boolean},className:{},padding:{type:Boolean,default:!0}},setup(i){const n=i,d=m(()=>`${c()} ${n.className||""}`);return(e,$)=>(t(),s("div",{class:u(d.value)},[e.title||e.subtitle||e.$slots.header||e.actions?(t(),s("div",y,[o(e.$slots,"header",{},()=>[r("div",b,[r("div",null,[e.title?(t(),s("h3",f,l(e.title),1)):a("",!0),e.subtitle?(t(),s("p",h,l(e.subtitle),1)):a("",!0)]),e.actions?(t(),s("div",g,[o(e.$slots,"actions")])):a("",!0)])])])):a("",!0),r("div",C,[o(e.$slots,"default")]),e.$slots.footer?(t(),s("div",v,[o(e.$slots,"footer")])):a("",!0)],2))}});export{B as _};
