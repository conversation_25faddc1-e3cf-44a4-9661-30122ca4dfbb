import{d as V,c as b,b as e,F as S,q as w,a as s,w as i,f as l,n as C,t as m,r as F,o as f,y as G,g as x,m as T,D as h,h as j,G as B,j as N,k as L}from"./index-Bibk5VaL.js";import{_ as o}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{_ as z}from"./BaseInput.vue_vue_type_script_setup_true_lang-DWl8EspK.js";import{_ as c}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as H}from"./BaseTable.vue_vue_type_script_setup_true_lang-PS-eZbzg.js";import{_ as $}from"./BaseModal.vue_vue_type_script_setup_true_lang-CIvlL0EO.js";import"./design-system-BvlP813R.js";const q={class:"layout-sidebar"},O={class:"sidebar-nav"},Q=V({__name:"Sidebar",setup(P){const y=[{name:"Dashboard",href:"/admin/dashboard",icon:"pi pi-home"},{name:"Progetti",href:"/admin/projects",icon:"pi pi-folder"},{name:"Procedimenti",href:"/admin/procedures",icon:"pi pi-file-o"},{name:"Particelle Catastali",href:"/admin/properties",icon:"pi pi-map"},{name:"Workflow",href:"/admin/workflows",icon:"pi pi-sitemap"},{name:"Report",href:"/admin/reports",icon:"pi pi-chart-bar"},{name:"Design System",href:"/admin/design-system",icon:"pi pi-palette"}];return(p,u)=>{const g=F("router-link");return f(),b("div",q,[u[0]||(u[0]=e("div",{class:"sidebar-header"},[e("h1",{class:"sidebar-title"},"ExProject")],-1)),e("nav",O,[(f(),b(S,null,w(y,n=>s(g,{key:n.name,to:n.href,class:C(["nav-link",[p.$route.path===n.href?"nav-active":"nav-item"]])},{default:i(()=>[e("i",{class:C([n.icon,"nav-icon"])},null,2),l(" "+m(n.name),1)]),_:2},1032,["to","class"])),64))])])}}}),W={class:"layout-topbar"},J={class:"flex-1"},K={class:"page-title"},X={class:"topbar-actions"},Y={class:"user-menu-container"},Z={class:"user-menu-content"},ee={class:"user-avatar"},te={class:"user-name"},se={key:0,class:"dropdown-menu"},ie=V({__name:"Topbar",setup(P){const y=h(),p=G(),u=x(!1),g=T(()=>({"/dashboard":"Dashboard","/projects":"Gestione Progetti","/procedures":"Procedimenti Espropriativi","/properties":"Gestione Immobili","/documents":"Gestione Documenti","/reports":"Report e Analytics","/design-system":"Design System"})[y.path]||"ExProject"),n=T(()=>{var d;return(d=p.user)!=null&&d.full_name?p.user.full_name.split(" ").map(r=>r.charAt(0)).join("").toUpperCase():"U"}),v=()=>{p.logout(),u.value=!1};return(d,r)=>{var k;return f(),b("div",W,[e("div",J,[e("h2",K,m(g.value),1)]),e("div",X,[r[4]||(r[4]=e("button",{class:"notification-button"},[e("i",{class:"pi pi-bell"})],-1)),e("div",Y,[e("button",{onClick:r[0]||(r[0]=E=>u.value=!u.value),class:"user-menu-button"},[e("div",Z,[e("div",ee,[e("span",null,m(n.value),1)]),e("span",te,m((k=B(p).user)==null?void 0:k.full_name),1),r[1]||(r[1]=e("i",{class:"pi pi-chevron-down dropdown-icon"},null,-1))])]),u.value?(f(),b("div",se,[e("div",{class:"py-1"},[r[2]||(r[2]=e("a",{href:"#",class:"dropdown-item"},"Profilo",-1)),r[3]||(r[3]=e("a",{href:"#",class:"dropdown-item"},"Impostazioni",-1)),e("button",{onClick:v,class:"dropdown-item"}," Esci ")])])):j("",!0)])])])}}}),ae={class:"layout-wrapper"},oe={class:"layout-main"},le={class:"layout-content"},re={class:"space-y-8"},ne={class:"space-y-6"},de={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},me={class:"space-y-2"},ue={class:"flex-1"},pe={class:"text-sm font-medium"},ge={class:"text-xs text-gray-500"},ce={class:"space-y-2"},xe={class:"flex-1"},be={class:"text-sm font-medium"},fe={class:"text-xs text-gray-500"},ve={class:"space-y-2"},ye={class:"flex-1"},_e={class:"text-sm font-medium"},ze={class:"text-xs text-gray-500"},Ce={class:"space-y-6"},ke={class:"space-y-6"},Se={class:"space-y-6"},we={class:"flex flex-wrap gap-4"},$e={class:"flex flex-wrap items-center gap-4"},Ve={class:"flex flex-wrap gap-4"},Pe={class:"space-y-6"},Ee={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Te={class:"space-y-4"},De={class:"space-y-6"},Me={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ae={class:"flex justify-between items-center"},Ue={class:"space-y-6"},Ie={class:"flex space-x-2"},Re={class:"space-y-6"},Fe={class:"flex flex-wrap gap-4"},Ge={class:"space-y-6"},he={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},je={class:"space-y-4"},Be={class:"space-y-2"},Je=V({__name:"DesignSystem",setup(P){const y=x(""),p=x("<EMAIL>"),u=x(""),g=x(!1),n=x(!1),v=x(!1),d=x({name:"",email:"",role:""}),r=[{name:"Primary 50",bg:"bg-primary-50",hex:"#eff6ff"},{name:"Primary 100",bg:"bg-primary-100",hex:"#dbeafe"},{name:"Primary 500",bg:"bg-primary-500",hex:"#3b82f6"},{name:"Primary 600",bg:"bg-primary-600",hex:"#2563eb"},{name:"Primary 900",bg:"bg-primary-900",hex:"#1e3a8a"}],k=[{name:"Gray 50",bg:"bg-gray-50",hex:"#f9fafb"},{name:"Gray 100",bg:"bg-gray-100",hex:"#f3f4f6"},{name:"Gray 500",bg:"bg-gray-500",hex:"#6b7280"},{name:"Gray 700",bg:"bg-gray-700",hex:"#374151"},{name:"Gray 900",bg:"bg-gray-900",hex:"#111827"}],E=[{name:"Success",bg:"bg-green-500",usage:"Operazioni completate"},{name:"Warning",bg:"bg-yellow-500",usage:"Attenzione richiesta"},{name:"Error",bg:"bg-red-500",usage:"Errori e problemi"},{name:"Info",bg:"bg-blue-500",usage:"Informazioni generali"}],D=[{key:"name",title:"Nome",sortable:!0},{key:"role",title:"Ruolo",sortable:!0},{key:"email",title:"Email",sortable:!0},{key:"status",title:"Stato",sortable:!1},{key:"actions",title:"Azioni",sortable:!1,align:"center"}],M=[{id:1,name:"Mario Rossi",role:"RUP",email:"<EMAIL>",status:"Attivo"},{id:2,name:"Luigi Bianchi",role:"RPE",email:"<EMAIL>",status:"Attivo"},{id:3,name:"Anna Verdi",role:"Tecnico",email:"<EMAIL>",status:"Inattivo"},{id:4,name:"Paolo Neri",role:"Legale",email:"<EMAIL>",status:"Attivo"}],A=_=>_==="Attivo"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",U=(_,t)=>{console.log("Sort:",_,t)},I=_=>{console.log("Selected:",_)},R=()=>{console.log("Saving:",d.value),n.value=!1};return(_,t)=>(f(),b("div",ae,[s(Q),e("div",oe,[s(ie),e("div",le,[e("div",re,[t[57]||(t[57]=e("div",{class:"border-b border-gray-200 pb-6"},[e("h1",{class:"text-3xl font-bold text-gray-900"},"Design System"),e("p",{class:"mt-2 text-lg text-gray-600"}," Componenti, colori e pattern utilizzati in tutta l'applicazione ExProject ")],-1)),e("section",ne,[t[20]||(t[20]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Palette Colori",-1)),e("div",de,[e("div",null,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Colori Primari",-1)),e("div",me,[(f(),b(S,null,w(r,a=>e("div",{key:a.name,class:"flex items-center space-x-3"},[e("div",{class:C([a.bg,"w-12 h-8 rounded border border-gray-200"])},null,2),e("div",ue,[e("div",pe,m(a.name),1),e("div",ge,m(a.hex),1)])])),64))])]),e("div",null,[t[18]||(t[18]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Colori Grigi",-1)),e("div",ce,[(f(),b(S,null,w(k,a=>e("div",{key:a.name,class:"flex items-center space-x-3"},[e("div",{class:C([a.bg,"w-12 h-8 rounded border border-gray-200"])},null,2),e("div",xe,[e("div",be,m(a.name),1),e("div",fe,m(a.hex),1)])])),64))])]),e("div",null,[t[19]||(t[19]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Colori di Stato",-1)),e("div",ve,[(f(),b(S,null,w(E,a=>e("div",{key:a.name,class:"flex items-center space-x-3"},[e("div",{class:C([a.bg,"w-12 h-8 rounded border border-gray-200"])},null,2),e("div",ye,[e("div",_e,m(a.name),1),e("div",ze,m(a.usage),1)])])),64))])])])]),e("section",Ce,[t[22]||(t[22]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Tipografia",-1)),s(c,null,{default:i(()=>t[21]||(t[21]=[e("div",{class:"space-y-6"},[e("div",{class:"text-4xl font-bold text-gray-900"},"Heading 1 - Titoli principali"),e("div",{class:"text-3xl font-bold text-gray-900"},"Heading 2 - Sottotitoli sezioni"),e("div",{class:"text-2xl font-semibold text-gray-900"},"Heading 3 - Titoli componenti"),e("div",{class:"text-xl font-medium text-gray-900"},"Heading 4 - Sottotitoli"),e("div",{class:"text-lg font-medium text-gray-900"},"Heading 5 - Etichette"),e("div",{class:"text-base text-gray-900"},"Body Text - Testo principale dell'applicazione"),e("div",{class:"text-sm text-gray-600"},"Small Text - Testo secondario e descrizioni"),e("div",{class:"text-xs text-gray-500"},"Caption - Didascalie e metainformazioni")],-1)])),_:1,__:[21]})]),e("section",ke,[t[39]||(t[39]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Bottoni",-1)),s(c,{title:"Varianti",subtitle:"Diverse varianti di bottoni per azioni specifiche"},{default:i(()=>[e("div",Se,[e("div",null,[t[28]||(t[28]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Varianti",-1)),e("div",we,[s(o,{variant:"primary"},{default:i(()=>t[23]||(t[23]=[l("Primary")])),_:1,__:[23]}),s(o,{variant:"secondary"},{default:i(()=>t[24]||(t[24]=[l("Secondary")])),_:1,__:[24]}),s(o,{variant:"outline"},{default:i(()=>t[25]||(t[25]=[l("Outline")])),_:1,__:[25]}),s(o,{variant:"ghost"},{default:i(()=>t[26]||(t[26]=[l("Ghost")])),_:1,__:[26]}),s(o,{variant:"destructive"},{default:i(()=>t[27]||(t[27]=[l("Destructive")])),_:1,__:[27]})])]),e("div",null,[t[33]||(t[33]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Dimensioni",-1)),e("div",$e,[s(o,{size:"sm"},{default:i(()=>t[29]||(t[29]=[l("Small")])),_:1,__:[29]}),s(o,{size:"md"},{default:i(()=>t[30]||(t[30]=[l("Medium")])),_:1,__:[30]}),s(o,{size:"lg"},{default:i(()=>t[31]||(t[31]=[l("Large")])),_:1,__:[31]}),s(o,{size:"xl"},{default:i(()=>t[32]||(t[32]=[l("Extra Large")])),_:1,__:[32]})])]),e("div",null,[t[38]||(t[38]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Stati",-1)),e("div",Ve,[s(o,null,{default:i(()=>t[34]||(t[34]=[l("Normale")])),_:1,__:[34]}),s(o,{loading:!0},{default:i(()=>t[35]||(t[35]=[l("Caricamento")])),_:1,__:[35]}),s(o,{disabled:!0},{default:i(()=>t[36]||(t[36]=[l("Disabilitato")])),_:1,__:[36]}),s(o,{icon:"pi pi-plus","icon-position":"left"},{default:i(()=>t[37]||(t[37]=[l("Con Icona")])),_:1,__:[37]})])])])]),_:1})]),e("section",Pe,[t[41]||(t[41]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Elementi Form",-1)),s(c,{title:"Input Fields",subtitle:"Campi di input per la raccolta dati"},{default:i(()=>[e("div",Ee,[e("div",Te,[s(z,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=a=>y.value=a),label:"Input Standard",placeholder:"Inserisci testo...",hint:"Questo è un input di esempio"},null,8,["modelValue"]),s(z,{modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=a=>p.value=a),type:"email",label:"Email",placeholder:"<EMAIL>",variant:"success",icon:"pi pi-check"},null,8,["modelValue"]),s(z,{modelValue:u.value,"onUpdate:modelValue":t[2]||(t[2]=a=>u.value=a),label:"Input con Errore",placeholder:"Campo obbligatorio",error:"Questo campo è obbligatorio",required:!0},null,8,["modelValue"]),s(z,{label:"Input Disabilitato",placeholder:"Campo non modificabile",disabled:!0,value:"Valore bloccato"})]),t[40]||(t[40]=e("div",{class:"space-y-4"},[e("div",{class:"space-y-2"},[e("label",{class:"block text-sm font-medium text-gray-700"},"Select"),e("select",{class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"},[e("option",null,"Seleziona opzione..."),e("option",null,"Opzione 1"),e("option",null,"Opzione 2"),e("option",null,"Opzione 3")])]),e("div",{class:"space-y-2"},[e("label",{class:"block text-sm font-medium text-gray-700"},"Textarea"),e("textarea",{rows:"3",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"Inserisci descrizione..."})]),e("div",{class:"space-y-3"},[e("label",{class:"block text-sm font-medium text-gray-700"},"Checkbox e Radio"),e("div",{class:"space-y-2"},[e("label",{class:"flex items-center"},[e("input",{type:"checkbox",class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"}),e("span",{class:"ml-2 text-sm text-gray-700"},"Checkbox opzione 1")]),e("label",{class:"flex items-center"},[e("input",{type:"checkbox",class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500",checked:""}),e("span",{class:"ml-2 text-sm text-gray-700"},"Checkbox opzione 2 (selezionata)")])]),e("div",{class:"space-y-2"},[e("label",{class:"flex items-center"},[e("input",{type:"radio",name:"radio-example",class:"border-gray-300 text-primary-600 focus:ring-primary-500"}),e("span",{class:"ml-2 text-sm text-gray-700"},"Radio opzione 1")]),e("label",{class:"flex items-center"},[e("input",{type:"radio",name:"radio-example",class:"border-gray-300 text-primary-600 focus:ring-primary-500",checked:""}),e("span",{class:"ml-2 text-sm text-gray-700"},"Radio opzione 2 (selezionata)")])])])],-1))])]),_:1})]),e("section",De,[t[48]||(t[48]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Cards",-1)),e("div",Me,[s(c,{title:"Card Semplice",subtitle:"Card con titolo e sottotitolo"},{default:i(()=>t[42]||(t[42]=[e("p",{class:"text-gray-700"}," Contenuto della card. Le card sono utilizzate per raggruppare contenuti correlati in sezioni ben definite e visivamente separate. ",-1)])),_:1,__:[42]}),s(c,{title:"Card con Azioni",subtitle:"Card con bottoni nel header",actions:!0},{actions:i(()=>[s(o,{variant:"outline",size:"sm"},{default:i(()=>t[43]||(t[43]=[l("Modifica")])),_:1,__:[43]}),s(o,{size:"sm"},{default:i(()=>t[44]||(t[44]=[l("Salva")])),_:1,__:[44]})]),footer:i(()=>[e("div",Ae,[t[46]||(t[46]=e("span",{class:"text-sm text-gray-500"},"Ultima modifica: oggi",-1)),s(o,{variant:"ghost",size:"sm"},{default:i(()=>t[45]||(t[45]=[l("Vedi dettagli")])),_:1,__:[45]})])]),default:i(()=>[t[47]||(t[47]=e("p",{class:"text-gray-700"}," Questa card include azioni nel header per operazioni rapide sui contenuti. ",-1))]),_:1,__:[47]})])]),e("section",Ue,[t[49]||(t[49]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Tabelle",-1)),s(c,{title:"Tabella Standard",subtitle:"Tabella con ordinamento e paginazione"},{default:i(()=>[s(H,{columns:D,data:M,pagination:!0,selectable:!0,onSort:U,onSelect:I},{"cell-status":i(({value:a})=>[e("span",{class:C([A(a),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},m(a),3)]),"cell-actions":i(({row:a})=>[e("div",Ie,[s(o,{variant:"ghost",size:"sm",icon:"pi pi-pencil"}),s(o,{variant:"ghost",size:"sm",icon:"pi pi-trash"})])]),_:1})]),_:1})]),e("section",Re,[t[53]||(t[53]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Modali",-1)),s(c,{title:"Finestre Modali",subtitle:"Dialog e overlay per azioni importanti"},{default:i(()=>[e("div",Fe,[s(o,{onClick:t[3]||(t[3]=a=>g.value=!0)},{default:i(()=>t[50]||(t[50]=[l("Modal Semplice")])),_:1,__:[50]}),s(o,{onClick:t[4]||(t[4]=a=>n.value=!0)},{default:i(()=>t[51]||(t[51]=[l("Modal con Form")])),_:1,__:[51]}),s(o,{onClick:t[5]||(t[5]=a=>v.value=!0),variant:"destructive"},{default:i(()=>t[52]||(t[52]=[l("Modal di Conferma")])),_:1,__:[52]})])]),_:1})]),e("section",Ge,[t[56]||(t[56]=e("h2",{class:"text-2xl font-semibold text-gray-900"},"Template Pagine",-1)),e("div",he,[s(c,{title:"Lista con Filtri",subtitle:"Template per pagine di elenco"},{default:i(()=>t[54]||(t[54]=[e("div",{class:"space-y-4"},[e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-table text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Header con titolo e azioni")]),e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-filter text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Sezione filtri")]),e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-list text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Contenuto principale (tabella/griglia)")])],-1)])),_:1,__:[54]}),s(c,{title:"Form di Dettaglio",subtitle:"Template per pagine di modifica"},{default:i(()=>t[55]||(t[55]=[e("div",{class:"space-y-4"},[e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-arrow-left text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Breadcrumb e navigazione")]),e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-bars text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Tabs di sezione")]),e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},[e("i",{class:"pi pi-file-edit text-3xl text-gray-400 mb-2"}),e("p",{class:"text-gray-600"},"Form con layout a colonne")])],-1)])),_:1,__:[55]})])])])])]),s($,{visible:g.value,"onUpdate:visible":t[8]||(t[8]=a=>g.value=a),title:"Modal Semplice",width:"24rem"},{footer:i(()=>[s(o,{variant:"outline",onClick:t[6]||(t[6]=a=>g.value=!1)},{default:i(()=>t[58]||(t[58]=[l("Annulla")])),_:1,__:[58]}),s(o,{onClick:t[7]||(t[7]=a=>g.value=!1)},{default:i(()=>t[59]||(t[59]=[l("Conferma")])),_:1,__:[59]})]),default:i(()=>[t[60]||(t[60]=e("p",{class:"text-gray-700"}," Questo è un esempio di modal semplice con contenuto testuale. ",-1))]),_:1,__:[60]},8,["visible"]),s($,{visible:n.value,"onUpdate:visible":t[13]||(t[13]=a=>n.value=a),title:"Modal con Form",width:"32rem"},{footer:i(()=>[s(o,{variant:"outline",onClick:t[12]||(t[12]=a=>n.value=!1)},{default:i(()=>t[63]||(t[63]=[l("Annulla")])),_:1,__:[63]}),s(o,{onClick:R},{default:i(()=>t[64]||(t[64]=[l("Salva")])),_:1,__:[64]})]),default:i(()=>[e("div",je,[s(z,{modelValue:d.value.name,"onUpdate:modelValue":t[9]||(t[9]=a=>d.value.name=a),label:"Nome",placeholder:"Inserisci nome...",required:!0},null,8,["modelValue"]),s(z,{modelValue:d.value.email,"onUpdate:modelValue":t[10]||(t[10]=a=>d.value.email=a),type:"email",label:"Email",placeholder:"<EMAIL>",required:!0},null,8,["modelValue"]),e("div",Be,[t[62]||(t[62]=e("label",{class:"block text-sm font-medium text-gray-700"},"Ruolo",-1)),N(e("select",{"onUpdate:modelValue":t[11]||(t[11]=a=>d.value.role=a),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"},t[61]||(t[61]=[e("option",{value:""},"Seleziona ruolo...",-1),e("option",{value:"admin"},"Amministratore",-1),e("option",{value:"rup"},"RUP",-1),e("option",{value:"rpe"},"RPE",-1),e("option",{value:"tecnico"},"Tecnico",-1)]),512),[[L,d.value.role]])])])]),_:1},8,["visible"]),s($,{visible:v.value,"onUpdate:visible":t[16]||(t[16]=a=>v.value=a),title:"Conferma Eliminazione",width:"24rem"},{footer:i(()=>[s(o,{variant:"outline",onClick:t[14]||(t[14]=a=>v.value=!1)},{default:i(()=>t[65]||(t[65]=[l("Annulla")])),_:1,__:[65]}),s(o,{variant:"destructive",onClick:t[15]||(t[15]=a=>v.value=!1)},{default:i(()=>t[66]||(t[66]=[l("Elimina")])),_:1,__:[66]})]),default:i(()=>[t[67]||(t[67]=e("div",{class:"flex items-start space-x-3"},[e("div",{class:"flex-shrink-0"},[e("i",{class:"pi pi-exclamation-triangle text-2xl text-error-500"})]),e("div",null,[e("p",{class:"text-gray-700"}," Sei sicuro di voler eliminare questo elemento? Questa azione non può essere annullata. ")])],-1))]),_:1,__:[67]},8,["visible"])]))}});export{Je as default};
