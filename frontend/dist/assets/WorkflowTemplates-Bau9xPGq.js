import{d as x,g as c,p as S,c as a,a as V,b as s,h as d,t as i,F as k,q as h,i as T,n as _,f as p,o,_ as N}from"./index-Bibk5VaL.js";import{A as I}from"./AdminNavbar-jBevcaIl.js";import"./BaseNavbar-BSvAFYsY.js";const G={class:"admin-layout"},q={class:"admin-content"},A={class:"workflows-main"},B={key:0,class:"loading-state"},E={key:1,class:"error-state"},O={key:2,class:"templates-section"},R={class:"templates-grid"},W=["onClick"],j={class:"template-header"},F={class:"template-meta"},M={class:"template-version"},$={class:"template-content"},L={class:"template-title"},H={class:"template-description"},J={class:"template-details"},K={class:"detail-row"},Q={class:"detail-value"},U={class:"detail-row"},X={class:"detail-value"},Y={class:"detail-row"},Z={class:"detail-value"},ss={class:"detail-row"},es={class:"detail-value"},ts={class:"detail-row"},is={class:"detail-value"},as={class:"template-footer"},os={class:"template-actions"},ls=["onClick"],ns={class:"modal-header"},ds={key:0,class:"modal-body"},rs={class:"template-overview"},cs={class:"overview-grid"},ps={class:"overview-item"},us={class:"item-value"},ms={class:"overview-item"},vs={class:"item-value"},_s={class:"overview-item"},gs={class:"item-value"},fs={class:"overview-item"},ks={class:"item-value"},hs={class:"template-description-full"},ws={class:"workflow-timeline"},ys={class:"timeline-container"},bs={class:"step-marker"},Cs={class:"step-number"},Ts={class:"step-content"},zs={class:"step-header"},Ds={class:"step-badges"},Ps={key:0,class:"badge critical"},xs={class:"badge type"},Ss={class:"step-description"},Vs={class:"step-details"},Ns={class:"step-timing"},Is={class:"timing-item"},Gs={key:0,class:"timing-item legal"},qs={key:0,class:"step-dependencies"},As={class:"deps-list"},Bs={key:1,class:"step-fields"},Es={class:"fields-list"},Os={key:0,class:"pi pi-asterisk"},Rs=x({__name:"WorkflowTemplates",setup(Ws){const w=c([]),n=c(null),g=c(!1),f=c(!1),u=c(null),y=async()=>{try{f.value=!0,u.value=null;const l=localStorage.getItem("token"),e=await fetch("/api/procedures/workflow-templates",{headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}});if(!e.ok)throw new Error("Errore caricamento workflow templates");const m=await e.json();w.value=m}catch(l){console.error("Errore caricamento templates:",l),u.value="Impossibile caricare i workflow templates"}finally{f.value=!1}},z=l=>{const e={"Procedure Ordinarie":"pi pi-file-text","Operazioni Preparatorie":"pi pi-cog","Vincolo Preordinato":"pi pi-map-marker","Procedure Speciali":"pi pi-exclamation-triangle","Gestione Termini":"pi pi-clock",Correzioni:"pi pi-file-edit","Gestione Indennità":"pi pi-euro",default:"pi pi-cog"};return e[l]||e.default},D=l=>{const e={"Procedure Ordinarie":"icon-primary","Operazioni Preparatorie":"icon-info","Vincolo Preordinato":"icon-warning","Procedure Speciali":"icon-danger","Gestione Termini":"icon-success",Correzioni:"icon-secondary","Gestione Indennità":"icon-success",default:"icon-default"};return e[l]||e.default},P=l=>({data_collection:"Raccolta Dati",document_generation:"Generazione Documenti",notification:"Notifica",deadline_tracking:"Monitoraggio Scadenze",approval:"Approvazione",conditional:"Condizionale"})[l]||l,b=l=>{n.value=l,g.value=!0},C=()=>{g.value=!1,n.value=null};return S(()=>{y()}),(l,e)=>{var m;return o(),a("div",G,[V(I),s("div",q,[s("main",A,[e[21]||(e[21]=s("div",{class:"page-header"},[s("div",{class:"page-header-content"},[s("h2",null,"Template Workflow D.P.R. 327/2001"),s("p",null,"Consulta i procedimenti espropriativi disponibili secondo la normativa italiana")])],-1)),f.value?(o(),a("div",B,e[1]||(e[1]=[s("div",{class:"loading-spinner"},null,-1),s("p",null,"Caricamento template workflow...",-1)]))):u.value?(o(),a("div",E,[s("p",null,i(u.value),1),s("button",{onClick:y,class:"btn btn-primary"},"Riprova")])):(o(),a("section",O,[s("div",R,[(o(!0),a(k,null,h(w.value,t=>{var v;return o(),a("div",{key:t.id,class:"template-card",onClick:r=>b(t)},[s("div",j,[s("div",{class:_(["template-icon",D(t.category)])},[s("i",{class:_(z(t.category))},null,2)],2),s("div",F,[e[2]||(e[2]=s("span",{class:"template-status active"}," Conforme D.P.R. 327/2001 ",-1)),s("span",M,"v"+i(t.version),1)])]),s("div",$,[s("h4",L,i(t.name),1),s("p",H,i(t.description),1),s("div",J,[s("div",K,[e[3]||(e[3]=s("span",{class:"detail-label"},"Normativa:",-1)),s("span",Q,i(t.legal_framework),1)]),s("div",U,[e[4]||(e[4]=s("span",{class:"detail-label"},"Categoria:",-1)),s("span",X,i(t.category),1)]),s("div",Y,[e[5]||(e[5]=s("span",{class:"detail-label"},"Step procedurali:",-1)),s("span",Z,i(((v=t.steps)==null?void 0:v.length)||0)+" fasi",1)]),s("div",ss,[e[6]||(e[6]=s("span",{class:"detail-label"},"Durata stimata:",-1)),s("span",es,i(t.total_estimated_days)+" giorni",1)]),s("div",ts,[e[7]||(e[7]=s("span",{class:"detail-label"},"Scadenza legale max:",-1)),s("span",is,i(t.max_legal_days||"Variabile")+" giorni",1)])])]),s("div",as,[e[9]||(e[9]=s("div",{class:"template-usage"},[s("span",{class:"usage-info"},[s("i",{class:"pi pi-check-circle"}),p(" Template approvato e operativo ")])],-1)),s("div",os,[s("button",{onClick:T(r=>b(t),["stop"]),class:"btn-view",title:"Visualizza dettagli"},e[8]||(e[8]=[s("i",{class:"pi pi-eye"},null,-1),p(" Dettagli ")]),8,ls)])])],8,W)}),128))])])),g.value?(o(),a("div",{key:3,class:"modal-overlay",onClick:C},[s("div",{class:"modal-content template-details-modal",onClick:e[0]||(e[0]=T(()=>{},["stop"]))},[s("div",ns,[s("h3",null,i((m=n.value)==null?void 0:m.name),1),s("button",{onClick:C,class:"btn-close"},e[10]||(e[10]=[s("i",{class:"pi pi-times"},null,-1)]))]),n.value?(o(),a("div",ds,[s("div",rs,[s("div",cs,[s("div",ps,[e[11]||(e[11]=s("span",{class:"item-label"},"Normativa di riferimento",-1)),s("span",us,i(n.value.legal_framework),1)]),s("div",ms,[e[12]||(e[12]=s("span",{class:"item-label"},"Categoria",-1)),s("span",vs,i(n.value.category),1)]),s("div",_s,[e[13]||(e[13]=s("span",{class:"item-label"},"Versione",-1)),s("span",gs,i(n.value.version),1)]),s("div",fs,[e[14]||(e[14]=s("span",{class:"item-label"},"Durata procedimento",-1)),s("span",ks,i(n.value.total_estimated_days)+" giorni stimati",1)])]),s("div",hs,[e[15]||(e[15]=s("h4",null,"Descrizione",-1)),s("p",null,i(n.value.description),1)])]),s("div",ws,[e[20]||(e[20]=s("h4",null,"Fasi del Procedimento",-1)),s("div",ys,[(o(!0),a(k,null,h(n.value.steps,(t,v)=>(o(),a("div",{key:t.id,class:_(["timeline-step",{critical:t.critical}])},[s("div",bs,[s("span",Cs,i(v+1),1)]),s("div",Ts,[s("div",zs,[s("h5",null,i(t.name),1),s("div",Ds,[t.critical?(o(),a("span",Ps,"Critico")):d("",!0),s("span",xs,i(P(t.step_type)),1)])]),s("p",Ss,i(t.description),1),s("div",Vs,[s("div",Ns,[s("span",Is,[e[16]||(e[16]=s("i",{class:"pi pi-clock"},null,-1)),p(" Durata stimata: "+i(t.estimated_days)+" giorni ",1)]),t.legal_deadline_days?(o(),a("span",Gs,[e[17]||(e[17]=s("i",{class:"pi pi-exclamation-triangle"},null,-1)),p(" Scadenza legale: "+i(t.legal_deadline_days)+" giorni ",1)])):d("",!0)]),t.depends_on&&t.depends_on.length>0?(o(),a("div",qs,[e[18]||(e[18]=s("span",{class:"deps-label"},"Dipende da:",-1)),s("span",As,i(t.depends_on.join(", ")),1)])):d("",!0),t.fields&&t.fields.length>0?(o(),a("div",Bs,[e[19]||(e[19]=s("span",{class:"fields-label"},"Campi richiesti:",-1)),s("div",Es,[(o(!0),a(k,null,h(t.fields,r=>(o(),a("span",{key:r.name,class:_(["field-tag",{required:r.required}])},[p(i(r.label)+" ",1),r.required?(o(),a("i",Os)):d("",!0)],2))),128))])])):d("",!0)])])],2))),128))])])])):d("",!0)])])):d("",!0)])])])}}}),$s=N(Rs,[["__scopeId","data-v-72b508c6"]]);export{$s as default};
