import{d as ae,g as p,m as x,D as ie,p as le,c,a as n,b as t,B as k,h as re,w as a,E as w,f as m,j as f,k as b,F as z,q as V,t as u,r as ne,n as S,s as de,i as U,v as ue,u as pe,o as d,_ as ce}from"./index-Bibk5VaL.js";import{A as me}from"./AdminNavbar-jBevcaIl.js";import{_ as I}from"./BaseCard.vue_vue_type_script_setup_true_lang-Czc9muh2.js";import{_ as E}from"./BaseInput.vue_vue_type_script_setup_true_lang-DWl8EspK.js";import{_}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{_ as ve}from"./BaseTable.vue_vue_type_script_setup_true_lang-PS-eZbzg.js";import{_ as fe}from"./BaseModal.vue_vue_type_script_setup_true_lang-CIvlL0EO.js";import{P as ge}from"./PageHeader-B4SpV5zp.js";import{L as be}from"./LoadingState-9ob6nEfb.js";import{E as _e}from"./ErrorState-DJld4rs8.js";import{E as ye}from"./EmptyState-rMfszj3C.js";import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const ke={class:"admin-layout"},Pe={class:"admin-content"},Ce={class:"procedures-main"},we={class:"filters-section"},ze={class:"filters-grid"},Ve={class:"filter-group"},Se={class:"filter-group"},je={class:"filter-group"},he={class:"filter-group"},De=["value"],$e={class:"procedures-section"},Te={class:"progress-container"},xe={class:"progress-bar"},Ue={class:"progress-text"},Ie={class:"table-actions"},Ee=["onClick"],Le=["onClick"],Ae={class:"templates-section"},Ne={class:"templates-grid"},Me=["onClick"],Re={class:"template-header"},Be={class:"template-description"},Fe={class:"template-meta"},qe={class:"template-steps"},Oe={class:"template-duration"},We={class:"template-actions"},Ge={class:"form-grid"},He={class:"form-group"},Je=["value"],Ke={class:"form-group"},Qe={class:"form-group"},Xe=["value"],Ye={class:"form-group form-full"},Ze={class:"form-group"},et={class:"form-actions"},tt=ae({__name:"ProcedureList",setup(ot){const P=pe(),j=ie(),h=p([]),D=p([]),$=p([]),C=p(!0),y=p(null),T=p(!1),g=p(!1),i=p({search:"",phase:"",status:"",project:""}),r=p({title:"",project_id:"",phase:"preparatorie",template_id:"",description:"",deadline:"",priority:"medium"}),R=[{key:"title",title:"Titolo",sortable:!0},{key:"project_name",title:"Progetto",sortable:!0},{key:"phase",title:"Fase",sortable:!0},{key:"status",title:"Stato",sortable:!0},{key:"progress",title:"Progresso",width:"120px"},{key:"deadline",title:"Scadenza",sortable:!0},{key:"actions",title:"Azioni",width:"140px"}],B=x(()=>j.params.id?"Procedimenti del Progetto":"Procedimenti Espropriativi"),F=x(()=>j.params.id?"Procedure specifiche di questo progetto":"Gestione workflow e tempistiche secondo D.P.R. 327/2001"),L=x(()=>h.value.filter(s=>{var M;const e=!i.value.search||s.title.toLowerCase().includes(i.value.search.toLowerCase())||((M=s.project_name)==null?void 0:M.toLowerCase().includes(i.value.search.toLowerCase())),l=!i.value.phase||s.phase===i.value.phase,o=!i.value.status||s.status===i.value.status,v=!i.value.project||s.project_id===parseInt(i.value.project);return e&&l&&o&&v})),A=async()=>{var s,e;try{C.value=!0,y.value=null;const l=j.params.id,o=l?`/projects/${l}/procedures/`:"/procedures/",v=await w.get(o);h.value=v.data,l&&(i.value.project=l.toString())}catch(l){console.error("Error loading procedures:",l),y.value=((e=(s=l.response)==null?void 0:s.data)==null?void 0:e.message)||"Errore caricamento procedimenti"}finally{C.value=!1}},q=async()=>{try{const s=await w.get("/projects/");D.value=s.data}catch(s){console.error("Error loading projects:",s)}},O=async()=>{try{const s=await w.get("/workflow-templates/");$.value=s.data}catch(s){console.error("Error loading workflow templates:",s)}},W=async()=>{var s,e;try{T.value=!0;const l=await w.post("/procedures/",r.value);h.value.unshift(l.data),Object.assign(r.value,{title:"",project_id:"",phase:"preparatorie",template_id:"",description:"",deadline:"",priority:"medium"}),g.value=!1}catch(l){console.error("Error creating procedure:",l),y.value=((e=(s=l.response)==null?void 0:s.data)==null?void 0:e.message)||"Errore creazione procedimento"}finally{T.value=!1}},G=s=>{P.push(`/admin/procedures/${s.id}/edit`)},H=s=>{P.push(`/admin/procedures/${s.id}/workflow`)},J=s=>{P.push(`/admin/procedures/${s.id}`)},N=s=>{P.push(`/admin/workflow-templates/${s.id}`)},K=s=>{r.value.template_id=s.id,g.value=!0},Q=s=>({preparatorie:"phase-preparatorie",vincolo:"phase-vincolo",pubblica_utilita:"phase-pubblica-utilita",indennita:"phase-indennita"})[s]||"phase-default",X=s=>({preparatorie:"Operazioni Preparatorie",vincolo:"Apposizione Vincolo",pubblica_utilita:"Pubblica Utilità",indennita:"Determinazione Indennità"})[s]||s,Y=s=>({active:"status-active",pending:"status-pending",completed:"status-completed",suspended:"status-suspended",expired:"status-expired"})[s]||"status-default",Z=s=>({active:"Attivo",pending:"In Attesa",completed:"Completato",suspended:"Sospeso",expired:"Scaduto"})[s]||s,ee=s=>({preparatorie:"category-preparatorie",vincolo:"category-vincolo",pubblica_utilita:"category-pubblica-utilita",indennita:"category-indennita",speciali:"category-speciali"})[s]||"category-default",te=s=>({preparatorie:"Operazioni Preparatorie",vincolo:"Vincolo Preordinato",pubblica_utilita:"Pubblica Utilità",indennita:"Determinazione Indennità",speciali:"Procedure Speciali"})[s]||s,oe=s=>{if(!s)return"";const e=new Date,l=new Date(s),o=Math.ceil((l.getTime()-e.getTime())/(1e3*60*60*24));return o<0?"deadline-expired":o<=7?"deadline-urgent":o<=30?"deadline-warning":"deadline-normal"},se=s=>s?new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"Non specificata";return le(()=>{A(),q(),O()}),(s,e)=>{const l=ne("router-link");return d(),c("div",ke,[n(me),t("div",Pe,[t("main",Ce,[n(ge,{title:B.value,subtitle:F.value},{actions:a(()=>[n(_,{onClick:e[0]||(e[0]=o=>g.value=!0),variant:"primary"},{default:a(()=>e[15]||(e[15]=[t("i",{class:"pi pi-plus"},null,-1),m(" Nuovo Procedimento ")])),_:1,__:[15]})]),_:1},8,["title","subtitle"]),t("section",we,[n(I,{title:"Filtri procedimenti"},{default:a(()=>[t("div",ze,[t("div",Ve,[n(E,{modelValue:i.value.search,"onUpdate:modelValue":e[1]||(e[1]=o=>i.value.search=o),placeholder:"Cerca per nome o progetto...",label:"Ricerca",icon:"pi pi-search"},null,8,["modelValue"])]),t("div",Se,[e[17]||(e[17]=t("label",{class:"filter-label"},"Fase",-1)),f(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>i.value.phase=o),class:"form-select"},e[16]||(e[16]=[t("option",{value:""},"Tutte le fasi",-1),t("option",{value:"preparatorie"},"Operazioni Preparatorie",-1),t("option",{value:"vincolo"},"Apposizione Vincolo",-1),t("option",{value:"pubblica_utilita"},"Dichiarazione Pubblica Utilità",-1),t("option",{value:"indennita"},"Determinazione Indennità",-1)]),512),[[b,i.value.phase]])]),t("div",je,[e[19]||(e[19]=t("label",{class:"filter-label"},"Stato",-1)),f(t("select",{"onUpdate:modelValue":e[3]||(e[3]=o=>i.value.status=o),class:"form-select"},e[18]||(e[18]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"active"},"Attivo",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"completed"},"Completato",-1),t("option",{value:"suspended"},"Sospeso",-1),t("option",{value:"expired"},"Scaduto",-1)]),512),[[b,i.value.status]])]),t("div",he,[e[21]||(e[21]=t("label",{class:"filter-label"},"Progetto",-1)),f(t("select",{"onUpdate:modelValue":e[4]||(e[4]=o=>i.value.project=o),class:"form-select"},[e[20]||(e[20]=t("option",{value:""},"Tutti i progetti",-1)),(d(!0),c(z,null,V(D.value,o=>(d(),c("option",{key:o.id,value:o.id},u(o.name),9,De))),128))],512),[[b,i.value.project]])])])]),_:1})]),t("section",$e,[n(I,null,{default:a(()=>[C.value?(d(),k(be,{key:0,message:"Caricamento procedimenti..."})):y.value?(d(),k(_e,{key:1,message:y.value},{actions:a(()=>[n(_,{onClick:A,variant:"primary"},{default:a(()=>e[22]||(e[22]=[m(" Riprova ")])),_:1,__:[22]})]),_:1},8,["message"])):L.value.length===0?(d(),k(ye,{key:2,icon:"pi pi-clipboard",title:"Nessun procedimento trovato",description:"Non ci sono procedimenti che corrispondono ai criteri di ricerca."},{actions:a(()=>[n(_,{onClick:e[5]||(e[5]=o=>g.value=!0),variant:"primary"},{default:a(()=>e[23]||(e[23]=[t("i",{class:"pi pi-plus"},null,-1),m(" Crea Primo Procedimento ")])),_:1,__:[23]})]),_:1})):(d(),k(ve,{key:3,columns:R,data:L.value,loading:C.value,onRowClick:J},{phase:a(({row:o})=>[t("span",{class:S([Q(o.phase),"phase-badge"])},u(X(o.phase)),3)]),status:a(({row:o})=>[t("span",{class:S([Y(o.status),"status-badge"])},u(Z(o.status)),3)]),progress:a(({row:o})=>[t("div",Te,[t("div",xe,[t("div",{class:"progress-fill",style:de({width:`${o.progress||0}%`})},null,4)]),t("span",Ue,u(o.progress||0)+"%",1)])]),deadline:a(({row:o})=>[t("span",{class:S([oe(o.deadline),"deadline-text"])},u(se(o.deadline)),3)]),actions:a(({row:o})=>[t("div",Ie,[n(l,{to:`/admin/procedures/${o.id}`,class:"btn btn-sm btn-outline",title:"Visualizza dettagli"},{default:a(()=>e[24]||(e[24]=[t("i",{class:"pi pi-eye"},null,-1)])),_:2,__:[24]},1032,["to"]),t("button",{onClick:v=>G(o),class:"btn btn-sm btn-outline",title:"Modifica procedimento"},e[25]||(e[25]=[t("i",{class:"pi pi-pencil"},null,-1)]),8,Ee),t("button",{onClick:v=>H(o),class:"btn btn-sm btn-primary",title:"Gestisci workflow"},e[26]||(e[26]=[t("i",{class:"pi pi-cog"},null,-1)]),8,Le)])]),_:1},8,["data","loading"]))]),_:1})]),t("section",Ae,[n(I,{title:"Template Workflow Disponibili"},{default:a(()=>[t("div",Ne,[(d(!0),c(z,null,V($.value,o=>(d(),c("div",{key:o.id,class:"template-card",onClick:v=>N(o)},[t("div",Re,[t("h4",null,u(o.name),1),t("span",{class:S([ee(o.category),"template-category"])},u(te(o.category)),3)]),t("p",Be,u(o.description),1),t("div",Fe,[t("span",qe,[e[27]||(e[27]=t("i",{class:"pi pi-list"},null,-1)),m(" "+u(o.steps_count||0)+" step ",1)]),t("span",Oe,[e[28]||(e[28]=t("i",{class:"pi pi-clock"},null,-1)),m(" "+u(o.estimated_days||0)+"gg ",1)])]),t("div",We,[n(_,{variant:"outline",size:"sm",onClick:U(v=>N(o),["stop"])},{default:a(()=>e[29]||(e[29]=[t("i",{class:"pi pi-eye"},null,-1),m(" Visualizza ")])),_:2,__:[29]},1032,["onClick"]),n(_,{variant:"primary",size:"sm",onClick:U(v=>K(o),["stop"])},{default:a(()=>e[30]||(e[30]=[t("i",{class:"pi pi-play"},null,-1),m(" Usa Template ")])),_:2,__:[30]},1032,["onClick"])])],8,Me))),128))])]),_:1})])])]),g.value?(d(),k(fe,{key:0,onClose:e[14]||(e[14]=o=>g.value=!1),title:"Nuovo Procedimento",width:"700px"},{default:a(()=>[t("form",{onSubmit:U(W,["prevent"]),class:"procedure-form"},[t("div",Ge,[n(E,{modelValue:r.value.title,"onUpdate:modelValue":e[6]||(e[6]=o=>r.value.title=o),label:"Titolo Procedimento *",placeholder:"es. Esproprio per realizzazione infrastruttura",required:""},null,8,["modelValue"]),t("div",He,[e[32]||(e[32]=t("label",{class:"form-label"},"Progetto *",-1)),f(t("select",{"onUpdate:modelValue":e[7]||(e[7]=o=>r.value.project_id=o),class:"form-select",required:""},[e[31]||(e[31]=t("option",{value:""},"Seleziona progetto",-1)),(d(!0),c(z,null,V(D.value,o=>(d(),c("option",{key:o.id,value:o.id},u(o.name),9,Je))),128))],512),[[b,r.value.project_id]])]),t("div",Ke,[e[34]||(e[34]=t("label",{class:"form-label"},"Fase Iniziale *",-1)),f(t("select",{"onUpdate:modelValue":e[8]||(e[8]=o=>r.value.phase=o),class:"form-select",required:""},e[33]||(e[33]=[t("option",{value:"preparatorie"},"Operazioni Preparatorie",-1),t("option",{value:"vincolo"},"Apposizione Vincolo",-1),t("option",{value:"pubblica_utilita"},"Dichiarazione Pubblica Utilità",-1),t("option",{value:"indennita"},"Determinazione Indennità",-1)]),512),[[b,r.value.phase]])]),t("div",Qe,[e[36]||(e[36]=t("label",{class:"form-label"},"Template Workflow",-1)),f(t("select",{"onUpdate:modelValue":e[9]||(e[9]=o=>r.value.template_id=o),class:"form-select"},[e[35]||(e[35]=t("option",{value:""},"Nessun template",-1)),(d(!0),c(z,null,V($.value,o=>(d(),c("option",{key:o.id,value:o.id},u(o.name),9,Xe))),128))],512),[[b,r.value.template_id]])]),t("div",Ye,[e[37]||(e[37]=t("label",{class:"form-label"},"Descrizione",-1)),f(t("textarea",{"onUpdate:modelValue":e[10]||(e[10]=o=>r.value.description=o),class:"form-textarea",rows:"3",placeholder:"Descrizione dettagliata del procedimento..."},null,512),[[ue,r.value.description]])]),n(E,{modelValue:r.value.deadline,"onUpdate:modelValue":e[11]||(e[11]=o=>r.value.deadline=o),type:"date",label:"Scadenza"},null,8,["modelValue"]),t("div",Ze,[e[39]||(e[39]=t("label",{class:"form-label"},"Priorità",-1)),f(t("select",{"onUpdate:modelValue":e[12]||(e[12]=o=>r.value.priority=o),class:"form-select"},e[38]||(e[38]=[t("option",{value:"low"},"Bassa",-1),t("option",{value:"medium"},"Media",-1),t("option",{value:"high"},"Alta",-1),t("option",{value:"urgent"},"Urgente",-1)]),512),[[b,r.value.priority]])])]),t("div",et,[n(_,{type:"button",variant:"secondary",onClick:e[13]||(e[13]=o=>g.value=!1)},{default:a(()=>e[40]||(e[40]=[m(" Annulla ")])),_:1,__:[40]}),n(_,{type:"submit",variant:"primary",loading:T.value},{default:a(()=>e[41]||(e[41]=[m(" Crea Procedimento ")])),_:1,__:[41]},8,["loading"])])],32)]),_:1})):re("",!0)])}}}),gt=ce(tt,[["__scopeId","data-v-71fd27e1"]]);export{gt as default};
