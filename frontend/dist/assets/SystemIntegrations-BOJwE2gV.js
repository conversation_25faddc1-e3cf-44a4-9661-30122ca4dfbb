import{d as Q,g as E,J as W,m as F,A as Y,c as v,b as i,F as B,q as Z,h as w,j as d,v as b,k as I,l as D,n as g,f as C,t as $,o as c,_ as J,e as V,p as X,a as h,i as ii,B as H}from"./index-Bibk5VaL.js";import{A as ti}from"./AdminNavbar-jBevcaIl.js";import"./BaseNavbar-BSvAFYsY.js";const ei={class:"sister-config-wizard"},si={class:"wizard-steps"},oi={class:"step-marker"},ni={key:0,class:"pi pi-check"},ai={key:1,class:"pi pi-times"},ri={key:2},li={class:"step-info"},di={class:"wizard-content"},pi={key:0,class:"config-step"},ui={class:"form-grid"},mi={class:"form-group"},ci={class:"form-group"},fi={class:"form-group"},vi={class:"form-group"},gi={class:"form-group"},bi={class:"form-group"},yi={key:1,class:"config-step"},Si={class:"form-grid"},_i={class:"form-group full-width"},zi={class:"form-group full-width"},$i={class:"form-group full-width"},wi={class:"form-group"},Ci={class:"form-group"},ki={key:2,class:"config-step"},Ti={class:"form-grid"},Pi={class:"form-group full-width"},Ei={class:"form-group"},xi={class:"form-group"},Mi={class:"form-group"},Vi={class:"form-group full-width"},Ii={class:"checkbox-group"},Ui={class:"checkbox-label"},Ai={key:3,class:"config-step"},Wi={class:"test-results"},Gi={class:"test-actions"},ji=["disabled"],Li={class:"test-actions"},qi=["disabled"],Ri={class:"test-actions"},Di=["disabled"],Oi={class:"test-actions"},Ni=["disabled"],Fi={key:0,class:"test-log"},Bi={class:"log-container"},Zi={class:"log-time"},Ki={class:"log-message"},Qi={class:"wizard-actions"},Ji=["disabled"],Hi=["disabled"],Yi=Q({__name:"SisterConfigWizard",props:{initialConfig:{default:()=>({})}},emits:["save","close"],setup(O,{emit:L}){const q=O,x=L,m=E(0),k=E(!1),p=W({base_url:"https://sister.agenziaterritori.it",api_key:"",client_id:"",client_secret:"",timeout:3e4,max_retries:3,wms_endpoint:"",wfs_endpoint:"",wcs_endpoint:"",projection:"EPSG:4326",coordinate_precision:6,comuni_codes:[],max_results_per_query:1e3,cache_duration:24,output_format:"GeoJSON",auto_sync:!0,...q.initialConfig}),l=E(""),n=W({connection:!1,wms:!1,wfs:!1,query:!1}),y=E([]),f=W({connection:"pending",wms:"pending",wfs:"pending",query:"pending"}),z=[{id:"base",title:"Configurazione Base",description:"URL e credenziali SISTER",hasError:!1},{id:"services",title:"Servizi Cartografici",description:"Endpoint WMS/WFS",hasError:!1},{id:"data",title:"Query e Dati",description:"Filtri e formati",hasError:!1},{id:"test",title:"Test e Validazione",description:"Verifica configurazione",hasError:!1}],P=F(()=>{switch(m.value){case 0:return p.base_url&&p.api_key&&p.client_id&&p.client_secret;case 1:return p.wms_endpoint&&p.wfs_endpoint&&p.projection;case 2:return p.output_format;case 3:return M.value;default:return!0}}),M=F(()=>Object.values(f).every(_=>_==="success"));Y(()=>l.value,_=>{_?p.comuni_codes=_.split(/[,\n]/).map(o=>o.trim()).filter(o=>o.length>0):p.comuni_codes=[]});const U=()=>{m.value<z.length-1&&m.value++},G=()=>{m.value>0&&m.value--},A=_=>{const o=f[_];return{"test-pending":o==="pending","test-success":o==="success","test-error":o==="error","test-running":n[_]}},T=(_,o)=>{y.value.push({type:_,message:o,timestamp:new Date})},j=async()=>{n.connection=!0,f.connection="pending";try{T("info","Testando connessione API SISTER..."),await new Promise(_=>setTimeout(_,2e3)),p.api_key.length>10?(f.connection="success",T("success","Connessione API SISTER riuscita")):(f.connection="error",T("error","API Key non valida o connessione fallita"))}catch(_){f.connection="error",T("error",`Errore connessione: ${_.message}`)}finally{n.connection=!1}},u=async()=>{n.wms=!0,f.wms="pending";try{T("info","Testando servizio WMS..."),await new Promise(_=>setTimeout(_,1500)),f.wms="success",T("success","Servizio WMS raggiungibile e funzionante")}catch(_){f.wms="error",T("error",`Errore WMS: ${_.message}`)}finally{n.wms=!1}},s=async()=>{n.wfs=!0,f.wfs="pending";try{T("info","Testando servizio WFS..."),await new Promise(_=>setTimeout(_,1500)),f.wfs="success",T("success","Servizio WFS attivo, dati vettoriali disponibili")}catch(_){f.wfs="error",T("error",`Errore WFS: ${_.message}`)}finally{n.wfs=!1}},S=async()=>{n.query=!0,f.query="pending";try{T("info","Testando query particelle..."),await new Promise(_=>setTimeout(_,2e3)),f.query="success",T("success","Query test completata: trovate 15 particelle esempio")}catch(_){f.query="error",T("error",`Errore query: ${_.message}`)}finally{n.query=!1}},R=_=>_.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),K=async()=>{k.value=!0;try{const _={...p,configured_at:new Date().toISOString(),test_results:f};await new Promise(o=>setTimeout(o,1e3)),x("save",_),T("success","Configurazione SISTER salvata con successo")}catch(_){T("error",`Errore salvataggio: ${_.message}`)}finally{k.value=!1}};return(_,o)=>(c(),v("div",ei,[i("div",si,[(c(),v(B,null,Z(z,(a,e)=>i("div",{key:a.id,class:g(["wizard-step",{active:m.value===e,completed:e<m.value,error:a.hasError}])},[i("div",oi,[e<m.value&&!a.hasError?(c(),v("i",ni)):a.hasError?(c(),v("i",ai)):(c(),v("span",ri,$(e+1),1))]),i("div",li,[i("h4",null,$(a.title),1),i("p",null,$(a.description),1)])],2)),64))]),i("div",di,[m.value===0?(c(),v("div",pi,[o[26]||(o[26]=i("h3",null,"Configurazione Base SISTER",-1)),o[27]||(o[27]=i("p",null,"Inserisci i parametri di connessione al portale SISTER dell'Agenzia delle Entrate",-1)),i("div",ui,[i("div",mi,[o[16]||(o[16]=i("label",{for:"base_url"},"URL Base SISTER *",-1)),d(i("input",{id:"base_url","onUpdate:modelValue":o[0]||(o[0]=a=>p.base_url=a),type:"url",class:"form-input",placeholder:"https://sister.agenziaterritori.it",required:""},null,512),[[b,p.base_url]]),o[17]||(o[17]=i("span",{class:"form-help"},"Endpoint principale del portale SISTER",-1))]),i("div",ci,[o[18]||(o[18]=i("label",{for:"api_key"},"API Key *",-1)),d(i("input",{id:"api_key","onUpdate:modelValue":o[1]||(o[1]=a=>p.api_key=a),type:"password",class:"form-input",placeholder:"Inserisci la tua API Key",required:""},null,512),[[b,p.api_key]]),o[19]||(o[19]=i("span",{class:"form-help"},"Chiave API fornita dall'Agenzia delle Entrate",-1))]),i("div",fi,[o[20]||(o[20]=i("label",{for:"client_id"},"Client ID *",-1)),d(i("input",{id:"client_id","onUpdate:modelValue":o[2]||(o[2]=a=>p.client_id=a),type:"text",class:"form-input",placeholder:"ID client registrato",required:""},null,512),[[b,p.client_id]])]),i("div",vi,[o[21]||(o[21]=i("label",{for:"client_secret"},"Client Secret *",-1)),d(i("input",{id:"client_secret","onUpdate:modelValue":o[3]||(o[3]=a=>p.client_secret=a),type:"password",class:"form-input",placeholder:"Secret OAuth2",required:""},null,512),[[b,p.client_secret]])]),i("div",gi,[o[22]||(o[22]=i("label",{for:"timeout"},"Timeout (ms)",-1)),d(i("input",{id:"timeout","onUpdate:modelValue":o[4]||(o[4]=a=>p.timeout=a),type:"number",class:"form-input",min:"5000",max:"120000",step:"1000"},null,512),[[b,p.timeout,void 0,{number:!0}]]),o[23]||(o[23]=i("span",{class:"form-help"},"Timeout per le richieste API (default: 30000ms)",-1))]),i("div",bi,[o[24]||(o[24]=i("label",{for:"max_retries"},"Max Tentativi",-1)),d(i("input",{id:"max_retries","onUpdate:modelValue":o[5]||(o[5]=a=>p.max_retries=a),type:"number",class:"form-input",min:"1",max:"10"},null,512),[[b,p.max_retries,void 0,{number:!0}]]),o[25]||(o[25]=i("span",{class:"form-help"},"Numero massimo di tentativi in caso di errore",-1))])])])):w("",!0),m.value===1?(c(),v("div",yi,[o[38]||(o[38]=i("h3",null,"Configurazione Servizi Cartografici",-1)),o[39]||(o[39]=i("p",null,"Configura gli endpoint WMS/WFS per l'accesso alle mappe e dati catastali",-1)),i("div",Si,[i("div",_i,[o[28]||(o[28]=i("label",{for:"wms_endpoint"},"Endpoint WMS (Mappe Raster) *",-1)),d(i("input",{id:"wms_endpoint","onUpdate:modelValue":o[6]||(o[6]=a=>p.wms_endpoint=a),type:"url",class:"form-input",placeholder:"https://wms.sister.agenziaterritori.it/geoserver/wms",required:""},null,512),[[b,p.wms_endpoint]]),o[29]||(o[29]=i("span",{class:"form-help"},"Servizio WMS per le mappe catastali raster",-1))]),i("div",zi,[o[30]||(o[30]=i("label",{for:"wfs_endpoint"},"Endpoint WFS (Dati Vettoriali) *",-1)),d(i("input",{id:"wfs_endpoint","onUpdate:modelValue":o[7]||(o[7]=a=>p.wfs_endpoint=a),type:"url",class:"form-input",placeholder:"https://wfs.sister.agenziaterritori.it/geoserver/wfs",required:""},null,512),[[b,p.wfs_endpoint]]),o[31]||(o[31]=i("span",{class:"form-help"},"Servizio WFS per i dati vettoriali delle particelle",-1))]),i("div",$i,[o[32]||(o[32]=i("label",{for:"wcs_endpoint"},"Endpoint WCS (Ortofoto)",-1)),d(i("input",{id:"wcs_endpoint","onUpdate:modelValue":o[8]||(o[8]=a=>p.wcs_endpoint=a),type:"url",class:"form-input",placeholder:"https://wcs.sister.agenziaterritori.it/geoserver/wcs"},null,512),[[b,p.wcs_endpoint]]),o[33]||(o[33]=i("span",{class:"form-help"},"Servizio WCS per le ortofoto (opzionale)",-1))]),i("div",wi,[o[35]||(o[35]=i("label",{for:"projection"},"Sistema di Riferimento *",-1)),d(i("select",{id:"projection","onUpdate:modelValue":o[9]||(o[9]=a=>p.projection=a),class:"form-select",required:""},o[34]||(o[34]=[i("option",{value:"EPSG:4326"},"EPSG:4326 (WGS84)",-1),i("option",{value:"EPSG:3857"},"EPSG:3857 (Web Mercator)",-1),i("option",{value:"EPSG:32632"},"EPSG:32632 (UTM 32N Italy)",-1),i("option",{value:"EPSG:25832"},"EPSG:25832 (ETRS89 UTM 32N)",-1)]),512),[[I,p.projection]])]),i("div",Ci,[o[37]||(o[37]=i("label",{for:"coordinate_precision"},"Precisione Coordinate",-1)),d(i("select",{id:"coordinate_precision","onUpdate:modelValue":o[10]||(o[10]=a=>p.coordinate_precision=a),class:"form-select"},o[36]||(o[36]=[i("option",{value:4},"4 decimali (±11.1m)",-1),i("option",{value:5},"5 decimali (±1.1m)",-1),i("option",{value:6},"6 decimali (±0.11m)",-1),i("option",{value:7},"7 decimali (±0.011m)",-1)]),512),[[I,p.coordinate_precision,void 0,{number:!0}]])])])])):w("",!0),m.value===2?(c(),v("div",ki,[o[50]||(o[50]=i("h3",null,"Configurazione Query e Dati",-1)),o[51]||(o[51]=i("p",null,"Imposta i filtri e formati per le query al sistema catastale",-1)),i("div",Ti,[i("div",Pi,[o[40]||(o[40]=i("label",{for:"comuni_codes"},"Codici Comuni Abilitati",-1)),d(i("textarea",{id:"comuni_codes","onUpdate:modelValue":o[11]||(o[11]=a=>l.value=a),class:"form-textarea",rows:"4",placeholder:"A001, A002, A003... (uno per riga o separati da virgola)"},null,512),[[b,l.value]]),o[41]||(o[41]=i("span",{class:"form-help"},"Lista codici catastali dei comuni di interesse (lascia vuoto per tutti)",-1))]),i("div",Ei,[o[42]||(o[42]=i("label",{for:"max_results"},"Max Risultati per Query",-1)),d(i("input",{id:"max_results","onUpdate:modelValue":o[12]||(o[12]=a=>p.max_results_per_query=a),type:"number",class:"form-input",min:"100",max:"10000",step:"100"},null,512),[[b,p.max_results_per_query,void 0,{number:!0}]]),o[43]||(o[43]=i("span",{class:"form-help"},"Limite risultati per singola query (default: 1000)",-1))]),i("div",xi,[o[44]||(o[44]=i("label",{for:"cache_duration"},"Durata Cache (ore)",-1)),d(i("input",{id:"cache_duration","onUpdate:modelValue":o[13]||(o[13]=a=>p.cache_duration=a),type:"number",class:"form-input",min:"1",max:"168"},null,512),[[b,p.cache_duration,void 0,{number:!0}]]),o[45]||(o[45]=i("span",{class:"form-help"},"Durata cache locale dei dati (default: 24 ore)",-1))]),i("div",Mi,[o[47]||(o[47]=i("label",{for:"output_format"},"Formato Output *",-1)),d(i("select",{id:"output_format","onUpdate:modelValue":o[14]||(o[14]=a=>p.output_format=a),class:"form-select",required:""},o[46]||(o[46]=[i("option",{value:"GeoJSON"},"GeoJSON",-1),i("option",{value:"Shapefile"},"Shapefile",-1),i("option",{value:"KML"},"KML",-1),i("option",{value:"GML"},"GML",-1)]),512),[[I,p.output_format]])]),i("div",Vi,[i("div",Ii,[i("label",Ui,[d(i("input",{"onUpdate:modelValue":o[15]||(o[15]=a=>p.auto_sync=a),type:"checkbox",class:"form-checkbox"},null,512),[[D,p.auto_sync]]),o[48]||(o[48]=i("span",{class:"checkbox-text"},"Sincronizzazione Automatica",-1))]),o[49]||(o[49]=i("span",{class:"form-help"},"Aggiornamento automatico quotidiano dei dati catastali",-1))])])])])):w("",!0),m.value===3?(c(),v("div",Ai,[o[61]||(o[61]=i("h3",null,"Test e Validazione",-1)),o[62]||(o[62]=i("p",null,"Verifica la configurazione e testa la connessione con SISTER",-1)),i("div",Wi,[i("div",{class:g(["test-item",A("connection")])},[o[53]||(o[53]=i("div",{class:"test-info"},[i("h4",null,"Test Connessione API"),i("p",null,"Verifica credenziali e endpoint base")],-1)),i("div",Gi,[i("button",{onClick:j,class:"btn-test",disabled:n.connection},[i("i",{class:g(["pi",n.connection?"pi-spin pi-spinner":"pi-play"])},null,2),o[52]||(o[52]=C(" Test "))],8,ji)])],2),i("div",{class:g(["test-item",A("wms")])},[o[55]||(o[55]=i("div",{class:"test-info"},[i("h4",null,"Test Servizio WMS"),i("p",null,"Verifica caricamento mappe catastali")],-1)),i("div",Li,[i("button",{onClick:u,class:"btn-test",disabled:n.wms},[i("i",{class:g(["pi",n.wms?"pi-spin pi-spinner":"pi-play"])},null,2),o[54]||(o[54]=C(" Test "))],8,qi)])],2),i("div",{class:g(["test-item",A("wfs")])},[o[57]||(o[57]=i("div",{class:"test-info"},[i("h4",null,"Test Servizio WFS"),i("p",null,"Verifica accesso dati vettoriali")],-1)),i("div",Ri,[i("button",{onClick:s,class:"btn-test",disabled:n.wfs},[i("i",{class:g(["pi",n.wfs?"pi-spin pi-spinner":"pi-play"])},null,2),o[56]||(o[56]=C(" Test "))],8,Di)])],2),i("div",{class:g(["test-item",A("query")])},[o[59]||(o[59]=i("div",{class:"test-info"},[i("h4",null,"Test Query Particelle"),i("p",null,"Verifica ricerca particelle catastali")],-1)),i("div",Oi,[i("button",{onClick:S,class:"btn-test",disabled:n.query},[i("i",{class:g(["pi",n.query?"pi-spin pi-spinner":"pi-play"])},null,2),o[58]||(o[58]=C(" Test "))],8,Ni)])],2)]),y.value.length>0?(c(),v("div",Fi,[o[60]||(o[60]=i("h4",null,"Log Test",-1)),i("div",Bi,[(c(!0),v(B,null,Z(y.value,(a,e)=>(c(),v("div",{key:e,class:g(["log-entry",a.type])},[i("span",Zi,$(R(a.timestamp)),1),i("span",Ki,$(a.message),1)],2))),128))])])):w("",!0)])):w("",!0)]),i("div",Qi,[m.value>0?(c(),v("button",{key:0,onClick:G,class:"btn btn-secondary"},o[63]||(o[63]=[i("i",{class:"pi pi-arrow-left"},null,-1),C(" Indietro ")]))):w("",!0),o[66]||(o[66]=i("div",{class:"action-spacer"},null,-1)),m.value<z.length-1?(c(),v("button",{key:1,onClick:U,class:"btn btn-primary",disabled:!P.value},o[64]||(o[64]=[C(" Avanti "),i("i",{class:"pi pi-arrow-right"},null,-1)]),8,Ji)):w("",!0),m.value===z.length-1?(c(),v("button",{key:2,onClick:K,class:"btn btn-success",disabled:!M.value||k.value},[i("i",{class:g(["pi",k.value?"pi-spin pi-spinner":"pi-check"])},null,2),o[65]||(o[65]=C(" Salva Configurazione "))],8,Hi)):w("",!0)])]))}}),Xi=J(Yi,[["__scopeId","data-v-7cda17e7"]]),hi={class:"pec-config-wizard"},it={class:"wizard-steps"},tt={class:"step-marker"},et={key:0,class:"pi pi-check"},st={key:1,class:"pi pi-times"},ot={key:2},nt={class:"step-info"},at={class:"wizard-content"},rt={key:0,class:"config-step"},lt={class:"form-grid"},dt={class:"form-group"},pt={class:"form-group"},ut={class:"form-group"},mt={class:"form-group"},ct={class:"form-group"},ft={class:"form-group"},vt={key:1,class:"config-step"},gt={class:"form-grid"},bt={class:"form-group"},yt={class:"form-group"},St={class:"form-group"},_t={class:"form-group"},zt={class:"form-group full-width"},$t={class:"checkbox-group"},wt={class:"checkbox-label"},Ct={class:"form-group full-width"},kt={class:"checkbox-group"},Tt={class:"checkbox-label"},Pt={key:2,class:"config-step"},Et={class:"form-grid"},xt={class:"form-group full-width"},Mt={class:"form-group full-width"},Vt={class:"form-group full-width"},It={class:"form-group"},Ut={class:"form-group"},At={class:"form-group full-width"},Wt={class:"checkbox-group"},Gt={class:"checkbox-label"},jt={key:3,class:"config-step"},Lt={class:"test-results"},qt={class:"test-actions"},Rt=["disabled"],Dt={class:"test-actions"},Ot=["disabled"],Nt={class:"test-actions"},Ft=["disabled"],Bt={class:"test-actions"},Zt=["disabled"],Kt={key:0,class:"test-log"},Qt={class:"log-container"},Jt={class:"log-time"},Ht={class:"log-message"},Yt={class:"wizard-actions"},Xt=["disabled"],ht=["disabled"],ie=Q({__name:"PecConfigWizard",props:{initialConfig:{default:()=>({})}},emits:["save","close"],setup(O,{emit:L}){const q=O,x=L,m=E(0),k=E(!1),p=E(""),l=W({provider:"",pec_address:"",smtp_server:"",smtp_port:587,username:"",password:"",encryption:"TLS",timeout:30,max_retries:3,retry_delay:5,verify_certificates:!0,require_authentication:!0,sender_name:"",default_subject:"Notifica Procedimento Espropriativo - Rif. {project_id}",signature:"",notification_expiry_days:30,receipt_timeout_hours:24,auto_archive:!0,...q.initialConfig}),n=W({connection:!1,authentication:!1,send:!1,receipt:!1}),y=E([]),f=W({connection:"pending",authentication:"pending",send:"pending",receipt:"pending"}),z=[{id:"provider",title:"Provider PEC",description:"Configurazione server",hasError:!1},{id:"security",title:"Sicurezza",description:"Crittografia e autenticazione",hasError:!1},{id:"templates",title:"Template",description:"Messaggi e firme",hasError:!1},{id:"test",title:"Test e Validazione",description:"Verifica configurazione",hasError:!1}],P={aruba:{smtp_server:"smtps.pec.aruba.it",smtp_port:465,encryption:"SSL"},legalmail:{smtp_server:"smtp.legalmail.it",smtp_port:587,encryption:"TLS"},poste:{smtp_server:"smtp.postecert.it",smtp_port:587,encryption:"TLS"},register:{smtp_server:"smtp.pec.register.it",smtp_port:587,encryption:"TLS"},tim:{smtp_server:"smtp.pec.tim.it",smtp_port:587,encryption:"TLS"}},M=F(()=>{switch(m.value){case 0:return l.provider&&l.pec_address&&l.smtp_server&&l.smtp_port&&l.username&&l.password;case 1:return l.encryption;case 2:return l.sender_name;case 3:return U.value;default:return!0}}),U=F(()=>Object.values(f).every(a=>a==="success")),G=()=>{const a=P[l.provider];a&&Object.assign(l,a)},A=()=>{m.value<z.length-1&&m.value++},T=()=>{m.value>0&&m.value--},j=a=>{const e=f[a];return{"test-pending":e==="pending","test-success":e==="success","test-error":e==="error","test-running":n[a]}},u=(a,e)=>{y.value.push({type:a,message:e,timestamp:new Date})},s=async()=>{n.connection=!0,f.connection="pending";try{u("info","Testando connessione SMTP..."),await new Promise(a=>setTimeout(a,2e3)),l.smtp_server&&l.smtp_port?(f.connection="success",u("success","Connessione SMTP riuscita")):(f.connection="error",u("error","Parametri SMTP non validi"))}catch(a){f.connection="error",u("error",`Errore connessione: ${a.message}`)}finally{n.connection=!1}},S=async()=>{n.authentication=!0,f.authentication="pending";try{u("info","Testando autenticazione..."),await new Promise(a=>setTimeout(a,1500)),l.username&&l.password?(f.authentication="success",u("success","Autenticazione riuscita")):(f.authentication="error",u("error","Credenziali non valide"))}catch(a){f.authentication="error",u("error",`Errore autenticazione: ${a.message}`)}finally{n.authentication=!1}},R=async()=>{n.send=!0,f.send="pending";try{u("info",`Inviando email di test a ${p.value}...`),await new Promise(a=>setTimeout(a,3e3)),f.send="success",u("success","Email PEC di test inviata con successo")}catch(a){f.send="error",u("error",`Errore invio: ${a.message}`)}finally{n.send=!1}},K=async()=>{n.receipt=!0,f.receipt="pending";try{u("info","Testando gestione ricevute..."),await new Promise(a=>setTimeout(a,2e3)),f.receipt="success",u("success","Sistema ricevute configurato correttamente")}catch(a){f.receipt="error",u("error",`Errore ricevute: ${a.message}`)}finally{n.receipt=!1}},_=a=>a.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),o=async()=>{k.value=!0;try{const a={...l,configured_at:new Date().toISOString(),test_results:f};await new Promise(e=>setTimeout(e,1e3)),x("save",a),u("success","Configurazione PEC salvata con successo")}catch(a){u("error",`Errore salvataggio: ${a.message}`)}finally{k.value=!1}};return(a,e)=>(c(),v("div",hi,[i("div",it,[(c(),v(B,null,Z(z,(t,r)=>i("div",{key:t.id,class:g(["wizard-step",{active:m.value===r,completed:r<m.value,error:t.hasError}])},[i("div",tt,[r<m.value&&!t.hasError?(c(),v("i",et)):t.hasError?(c(),v("i",st)):(c(),v("span",ot,$(r+1),1))]),i("div",nt,[i("h4",null,$(t.title),1),i("p",null,$(t.description),1)])],2)),64))]),i("div",at,[m.value===0?(c(),v("div",rt,[e[30]||(e[30]=i("h3",null,"Configurazione Provider PEC",-1)),e[31]||(e[31]=i("p",null,"Configura il provider di posta elettronica certificata per l'invio delle notifiche legali",-1)),i("div",lt,[i("div",dt,[e[20]||(e[20]=i("label",{for:"provider"},"Provider PEC *",-1)),d(i("select",{id:"provider","onUpdate:modelValue":e[0]||(e[0]=t=>l.provider=t),class:"form-select",required:"",onChange:G},e[19]||(e[19]=[V('<option value="" data-v-ffb26eac>Seleziona provider</option><option value="aruba" data-v-ffb26eac>Aruba PEC</option><option value="legalmail" data-v-ffb26eac>Legalmail</option><option value="poste" data-v-ffb26eac>Poste Italiane</option><option value="register" data-v-ffb26eac>Register.it</option><option value="tim" data-v-ffb26eac>TIM PEC</option><option value="custom" data-v-ffb26eac>Altro/Personalizzato</option>',7)]),544),[[I,l.provider]]),e[21]||(e[21]=i("span",{class:"form-help"},"Seleziona il tuo provider PEC certificato",-1))]),i("div",pt,[e[22]||(e[22]=i("label",{for:"pec_address"},"Indirizzo PEC *",-1)),d(i("input",{id:"pec_address","onUpdate:modelValue":e[1]||(e[1]=t=>l.pec_address=t),type:"email",class:"form-input",placeholder:"<EMAIL>",required:""},null,512),[[b,l.pec_address]]),e[23]||(e[23]=i("span",{class:"form-help"},"Indirizzo email PEC dell'ente mittente",-1))]),i("div",ut,[e[24]||(e[24]=i("label",{for:"smtp_server"},"Server SMTP *",-1)),d(i("input",{id:"smtp_server","onUpdate:modelValue":e[2]||(e[2]=t=>l.smtp_server=t),type:"text",class:"form-input",placeholder:"smtp.pec.domain.it",required:""},null,512),[[b,l.smtp_server]]),e[25]||(e[25]=i("span",{class:"form-help"},"Server SMTP del provider PEC",-1))]),i("div",mt,[e[26]||(e[26]=i("label",{for:"smtp_port"},"Porta SMTP *",-1)),d(i("input",{id:"smtp_port","onUpdate:modelValue":e[3]||(e[3]=t=>l.smtp_port=t),type:"number",class:"form-input",min:"25",max:"65535",required:""},null,512),[[b,l.smtp_port,void 0,{number:!0}]]),e[27]||(e[27]=i("span",{class:"form-help"},"Porta SMTP (di solito 465 o 587)",-1))]),i("div",ct,[e[28]||(e[28]=i("label",{for:"username"},"Username *",-1)),d(i("input",{id:"username","onUpdate:modelValue":e[4]||(e[4]=t=>l.username=t),type:"text",class:"form-input",placeholder:"username o indirizzo email",required:""},null,512),[[b,l.username]])]),i("div",ft,[e[29]||(e[29]=i("label",{for:"password"},"Password *",-1)),d(i("input",{id:"password","onUpdate:modelValue":e[5]||(e[5]=t=>l.password=t),type:"password",class:"form-input",placeholder:"Password PEC",required:""},null,512),[[b,l.password]])])])])):w("",!0),m.value===1?(c(),v("div",vt,[e[45]||(e[45]=i("h3",null,"Sicurezza e Crittografia",-1)),e[46]||(e[46]=i("p",null,"Configura le impostazioni di sicurezza per le comunicazioni PEC",-1)),i("div",gt,[i("div",bt,[e[33]||(e[33]=i("label",{for:"encryption"},"Tipo Crittografia *",-1)),d(i("select",{id:"encryption","onUpdate:modelValue":e[6]||(e[6]=t=>l.encryption=t),class:"form-select",required:""},e[32]||(e[32]=[i("option",{value:"TLS"},"TLS/STARTTLS",-1),i("option",{value:"SSL"},"SSL",-1),i("option",{value:"NONE"},"Nessuna (sconsigliato)",-1)]),512),[[I,l.encryption]]),e[34]||(e[34]=i("span",{class:"form-help"},"Metodo di crittografia per la connessione SMTP",-1))]),i("div",yt,[e[35]||(e[35]=i("label",{for:"timeout"},"Timeout Connessione (secondi)",-1)),d(i("input",{id:"timeout","onUpdate:modelValue":e[7]||(e[7]=t=>l.timeout=t),type:"number",class:"form-input",min:"10",max:"300"},null,512),[[b,l.timeout,void 0,{number:!0}]]),e[36]||(e[36]=i("span",{class:"form-help"},"Timeout per la connessione SMTP (default: 30s)",-1))]),i("div",St,[e[37]||(e[37]=i("label",{for:"max_retries"},"Tentativi Massimi",-1)),d(i("input",{id:"max_retries","onUpdate:modelValue":e[8]||(e[8]=t=>l.max_retries=t),type:"number",class:"form-input",min:"1",max:"10"},null,512),[[b,l.max_retries,void 0,{number:!0}]]),e[38]||(e[38]=i("span",{class:"form-help"},"Numero massimo di tentativi in caso di errore",-1))]),i("div",_t,[e[39]||(e[39]=i("label",{for:"retry_delay"},"Ritardo tra Tentativi (secondi)",-1)),d(i("input",{id:"retry_delay","onUpdate:modelValue":e[9]||(e[9]=t=>l.retry_delay=t),type:"number",class:"form-input",min:"1",max:"60"},null,512),[[b,l.retry_delay,void 0,{number:!0}]]),e[40]||(e[40]=i("span",{class:"form-help"},"Tempo di attesa tra un tentativo e l'altro",-1))]),i("div",zt,[i("div",$t,[i("label",wt,[d(i("input",{"onUpdate:modelValue":e[10]||(e[10]=t=>l.verify_certificates=t),type:"checkbox",class:"form-checkbox"},null,512),[[D,l.verify_certificates]]),e[41]||(e[41]=i("span",{class:"checkbox-text"},"Verifica Certificati SSL",-1))]),e[42]||(e[42]=i("span",{class:"form-help"},"Verifica la validità dei certificati SSL del server",-1))])]),i("div",Ct,[i("div",kt,[i("label",Tt,[d(i("input",{"onUpdate:modelValue":e[11]||(e[11]=t=>l.require_authentication=t),type:"checkbox",class:"form-checkbox"},null,512),[[D,l.require_authentication]]),e[43]||(e[43]=i("span",{class:"checkbox-text"},"Richiedi Autenticazione SMTP",-1))]),e[44]||(e[44]=i("span",{class:"form-help"},"Usa autenticazione per la connessione SMTP",-1))])])])])):w("",!0),m.value===2?(c(),v("div",Pt,[e[59]||(e[59]=i("h3",null,"Template Messaggi",-1)),e[60]||(e[60]=i("p",null,"Configura i template per i diversi tipi di notifiche PEC",-1)),i("div",Et,[i("div",xt,[e[47]||(e[47]=i("label",{for:"sender_name"},"Nome Mittente *",-1)),d(i("input",{id:"sender_name","onUpdate:modelValue":e[12]||(e[12]=t=>l.sender_name=t),type:"text",class:"form-input",placeholder:"Comune di Roma - Ufficio Espropri",required:""},null,512),[[b,l.sender_name]]),e[48]||(e[48]=i("span",{class:"form-help"},"Nome che apparirà come mittente nelle email PEC",-1))]),i("div",Mt,[e[49]||(e[49]=i("label",{for:"default_subject"},"Oggetto Predefinito",-1)),d(i("input",{id:"default_subject","onUpdate:modelValue":e[13]||(e[13]=t=>l.default_subject=t),type:"text",class:"form-input",placeholder:"Notifica Procedimento Espropriativo - Rif. {project_id}"},null,512),[[b,l.default_subject]]),e[50]||(e[50]=i("span",{class:"form-help"},"Template oggetto (usa {project_id}, {procedure_name} per dati dinamici)",-1))]),i("div",Vt,[e[51]||(e[51]=i("label",{for:"signature"},"Firma Email",-1)),d(i("textarea",{id:"signature","onUpdate:modelValue":e[14]||(e[14]=t=>l.signature=t),class:"form-textarea",rows:"4",placeholder:`--
Ufficio Espropri
Comune di Roma
Tel: 06-12345678
Email: <EMAIL>`},null,512),[[b,l.signature]]),e[52]||(e[52]=i("span",{class:"form-help"},"Firma automatica da aggiungere a tutte le email PEC",-1))]),i("div",It,[e[53]||(e[53]=i("label",{for:"notification_expiry"},"Scadenza Notifiche (giorni)",-1)),d(i("input",{id:"notification_expiry","onUpdate:modelValue":e[15]||(e[15]=t=>l.notification_expiry_days=t),type:"number",class:"form-input",min:"1",max:"365"},null,512),[[b,l.notification_expiry_days,void 0,{number:!0}]]),e[54]||(e[54]=i("span",{class:"form-help"},"Giorni dopo i quali le notifiche scadono",-1))]),i("div",Ut,[e[55]||(e[55]=i("label",{for:"receipt_timeout"},"Timeout Ricevuta (ore)",-1)),d(i("input",{id:"receipt_timeout","onUpdate:modelValue":e[16]||(e[16]=t=>l.receipt_timeout_hours=t),type:"number",class:"form-input",min:"1",max:"72"},null,512),[[b,l.receipt_timeout_hours,void 0,{number:!0}]]),e[56]||(e[56]=i("span",{class:"form-help"},"Ore massime per ricevere la ricevuta di consegna",-1))]),i("div",At,[i("div",Wt,[i("label",Gt,[d(i("input",{"onUpdate:modelValue":e[17]||(e[17]=t=>l.auto_archive=t),type:"checkbox",class:"form-checkbox"},null,512),[[D,l.auto_archive]]),e[57]||(e[57]=i("span",{class:"checkbox-text"},"Archiviazione Automatica",-1))]),e[58]||(e[58]=i("span",{class:"form-help"},"Archivia automaticamente email e ricevute nel sistema",-1))])])])])):w("",!0),m.value===3?(c(),v("div",jt,[e[70]||(e[70]=i("h3",null,"Test e Validazione",-1)),e[71]||(e[71]=i("p",null,"Verifica la configurazione e testa l'invio PEC",-1)),i("div",Lt,[i("div",{class:g(["test-item",j("connection")])},[e[62]||(e[62]=i("div",{class:"test-info"},[i("h4",null,"Test Connessione SMTP"),i("p",null,"Verifica connessione al server PEC")],-1)),i("div",qt,[i("button",{onClick:s,class:"btn-test",disabled:n.connection},[i("i",{class:g(["pi",n.connection?"pi-spin pi-spinner":"pi-play"])},null,2),e[61]||(e[61]=C(" Test "))],8,Rt)])],2),i("div",{class:g(["test-item",j("authentication")])},[e[64]||(e[64]=i("div",{class:"test-info"},[i("h4",null,"Test Autenticazione"),i("p",null,"Verifica credenziali di accesso")],-1)),i("div",Dt,[i("button",{onClick:S,class:"btn-test",disabled:n.authentication},[i("i",{class:g(["pi",n.authentication?"pi-spin pi-spinner":"pi-play"])},null,2),e[63]||(e[63]=C(" Test "))],8,Ot)])],2),i("div",{class:g(["test-item",j("send")])},[e[66]||(e[66]=i("div",{class:"test-info"},[i("h4",null,"Test Invio PEC"),i("p",null,"Invia email di prova")],-1)),i("div",Nt,[d(i("input",{"onUpdate:modelValue":e[18]||(e[18]=t=>p.value=t),type:"email",class:"test-email-input",placeholder:"<EMAIL>"},null,512),[[b,p.value]]),i("button",{onClick:R,class:"btn-test",disabled:n.send||!p.value},[i("i",{class:g(["pi",n.send?"pi-spin pi-spinner":"pi-play"])},null,2),e[65]||(e[65]=C(" Invia "))],8,Ft)])],2),i("div",{class:g(["test-item",j("receipt")])},[e[68]||(e[68]=i("div",{class:"test-info"},[i("h4",null,"Test Ricevuta"),i("p",null,"Verifica ricezione ricevute di consegna")],-1)),i("div",Bt,[i("button",{onClick:K,class:"btn-test",disabled:n.receipt},[i("i",{class:g(["pi",n.receipt?"pi-spin pi-spinner":"pi-play"])},null,2),e[67]||(e[67]=C(" Test "))],8,Zt)])],2)]),y.value.length>0?(c(),v("div",Kt,[e[69]||(e[69]=i("h4",null,"Log Test",-1)),i("div",Qt,[(c(!0),v(B,null,Z(y.value,(t,r)=>(c(),v("div",{key:r,class:g(["log-entry",t.type])},[i("span",Jt,$(_(t.timestamp)),1),i("span",Ht,$(t.message),1)],2))),128))])])):w("",!0)])):w("",!0)]),i("div",Yt,[m.value>0?(c(),v("button",{key:0,onClick:T,class:"btn btn-secondary"},e[72]||(e[72]=[i("i",{class:"pi pi-arrow-left"},null,-1),C(" Indietro ")]))):w("",!0),e[75]||(e[75]=i("div",{class:"action-spacer"},null,-1)),m.value<z.length-1?(c(),v("button",{key:1,onClick:A,class:"btn btn-primary",disabled:!M.value},e[73]||(e[73]=[C(" Avanti "),i("i",{class:"pi pi-arrow-right"},null,-1)]),8,Xt)):w("",!0),m.value===z.length-1?(c(),v("button",{key:2,onClick:o,class:"btn btn-success",disabled:!U.value||k.value},[i("i",{class:g(["pi",k.value?"pi-spin pi-spinner":"pi-check"])},null,2),e[74]||(e[74]=C(" Salva Configurazione "))],8,ht)):w("",!0)])]))}}),te=J(ie,[["__scopeId","data-v-ffb26eac"]]),ee={class:"gis-config-wizard"},se={class:"wizard-steps"},oe={class:"step-marker"},ne={key:0,class:"pi pi-check"},ae={key:1,class:"pi pi-times"},re={key:2},le={class:"step-info"},de={class:"wizard-content"},pe={key:0,class:"config-step"},ue={class:"form-grid"},me={class:"form-group"},ce={class:"form-group"},fe={class:"form-group full-width"},ve={class:"form-group"},ge={class:"form-group"},be={class:"form-group"},ye={key:1,class:"config-step"},Se={class:"form-grid"},_e={class:"form-group"},ze={class:"form-group"},$e={class:"form-group"},we={class:"form-group"},Ce={class:"form-group"},ke={class:"form-group"},Te={key:2,class:"config-step"},Pe={class:"form-grid"},Ee={class:"form-group full-width"},xe={class:"form-group full-width"},Me={class:"form-group"},Ve={class:"form-group"},Ie={class:"form-group full-width"},Ue={class:"form-group"},Ae={key:3,class:"config-step"},We={class:"form-grid"},Ge={class:"form-group full-width"},je={class:"checkbox-group"},Le={class:"checkbox-label"},qe={class:"form-group full-width"},Re={class:"checkbox-group"},De={class:"checkbox-label"},Oe={class:"form-group"},Ne={class:"form-group"},Fe={class:"form-group full-width"},Be={class:"checkbox-group"},Ze={class:"checkbox-label"},Ke={key:0,class:"form-group full-width"},Qe={class:"form-group"},Je={key:4,class:"config-step"},He={class:"test-results"},Ye={class:"test-actions"},Xe=["disabled"],he={class:"test-actions"},is=["disabled"],ts={class:"test-actions"},es=["disabled"],ss={class:"test-actions"},os=["disabled"],ns={key:0,class:"test-log"},as={class:"log-container"},rs={class:"log-time"},ls={class:"log-message"},ds={class:"wizard-actions"},ps=["disabled"],us=["disabled"],ms=Q({__name:"GisConfigWizard",props:{initialConfig:{default:()=>({})}},emits:["save","close"],setup(O,{emit:L}){const q=O,x=L,m=E(0),k=E(!1),p=E(""),l=E("Roma, Italia"),n=W({primary_provider:"openstreetmap",api_key:"",tile_server_url:"https://tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:"© OpenStreetMap contributors",max_zoom:18,min_zoom:1,default_projection:"EPSG:4326",display_projection:"EPSG:3857",coordinate_precision:6,default_center:{lat:42.5,lng:12.5},default_zoom:6,wms_server:"",wfs_server:"",wms_version:"1.3.0",wfs_version:"2.0.0",available_layers:[],request_timeout:1e4,enable_drawing:!0,enable_measurement:!0,measurement_unit:"metric",area_unit:"m2",enable_geocoding:!0,geocoding_service:"nominatim",default_buffer_meters:100,...q.initialConfig}),y=W({map_load:!1,projection:!1,wms:!1,geocoding:!1}),f=E([]),z=W({map_load:"pending",projection:"pending",wms:"pending",geocoding:"pending"}),P=[{id:"providers",title:"Provider Mappe",description:"Servizi cartografici base",hasError:!1},{id:"coordinates",title:"Coordinate",description:"Sistemi di riferimento",hasError:!1},{id:"services",title:"Servizi WMS/WFS",description:"Layer e dati aggiuntivi",hasError:!1},{id:"tools",title:"Strumenti",description:"Disegno e analisi",hasError:!1},{id:"test",title:"Test e Validazione",description:"Verifica configurazione",hasError:!1}],M={openstreetmap:{tile_server_url:"https://tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:"© OpenStreetMap contributors",api_key:""},google:{tile_server_url:"https://maps.googleapis.com/maps/vt?pb=!1m5!1m4!1i{z}!2i{x}!3i{y}!4i256!2m3!1e0!2sm!3i{VERSION}!3m17!2sen!3sUS!5e18!12m4!1e68!2m2!1sset!2sRoadmap!12m3!1e37!2m1!1ssmartmaps!12m4!1e26!2m2!1sstyles!2zcy5lOmx8cC5jOiNmZmZmZmZmZixzLmU6bC50LmYscC5jOiNmZjAwMDAwMCxzLmU6bC50LnMscC5jOiNmZjMzMzMzMw!4e0!5m1!5f2",attribution:"© Google",api_key:""},mapbox:{tile_server_url:"https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token={API_KEY}",attribution:"© Mapbox © OpenStreetMap",api_key:""}},U=F(()=>{switch(m.value){case 0:return n.primary_provider&&n.attribution;case 1:return n.default_projection;case 2:return!0;case 3:return!0;case 4:return G.value;default:return!0}}),G=F(()=>Object.values(z).every(e=>e==="success"));Y(()=>p.value,e=>{e?n.available_layers=e.split(/[,\n]/).map(t=>t.trim()).filter(t=>t.length>0):n.available_layers=[]});const A=()=>{const e=M[n.primary_provider];e&&Object.assign(n,e)},T=()=>{m.value<P.length-1&&m.value++},j=()=>{m.value>0&&m.value--},u=e=>{const t=z[e];return{"test-pending":t==="pending","test-success":t==="success","test-error":t==="error","test-running":y[e]}},s=(e,t)=>{f.value.push({type:e,message:t,timestamp:new Date})},S=async()=>{y.map_load=!0,z.map_load="pending";try{s("info","Testando caricamento tile mappa..."),await new Promise(e=>setTimeout(e,2e3)),n.tile_server_url?(z.map_load="success",s("success","Tile server raggiungibile e funzionante")):(z.map_load="error",s("error","URL tile server non configurato"))}catch(e){z.map_load="error",s("error",`Errore caricamento mappa: ${e.message}`)}finally{y.map_load=!1}},R=async()=>{y.projection=!0,z.projection="pending";try{s("info","Testando trasformazioni coordinate..."),await new Promise(t=>setTimeout(t,1500));const e=[12.4964,41.9028];z.projection="success",s("success",`Trasformazione coordinate funzionante: ${e[1]}, ${e[0]} → WebMercator`)}catch(e){z.projection="error",s("error",`Errore proiezioni: ${e.message}`)}finally{y.projection=!1}},K=async()=>{y.wms=!0,z.wms="pending";try{s("info","Testando servizi WMS/WFS..."),await new Promise(e=>setTimeout(e,2e3)),n.wms_server||n.wfs_server?(z.wms="success",s("success","Servizi WMS/WFS configurati e raggiungibili")):(z.wms="error",s("error","Nessun servizio WMS/WFS configurato"))}catch(e){z.wms="error",s("error",`Errore servizi WMS/WFS: ${e.message}`)}finally{y.wms=!1}},_=async()=>{y.geocoding=!0,z.geocoding="pending";try{s("info",`Testando geocoding per "${l.value}"...`),await new Promise(e=>setTimeout(e,2e3)),n.enable_geocoding&&n.geocoding_service?(z.geocoding="success",s("success",`Geocoding funzionante: ${l.value} → 41.9028, 12.4964`)):(z.geocoding="error",s("error","Geocoding non abilitato o servizio non configurato"))}catch(e){z.geocoding="error",s("error",`Errore geocoding: ${e.message}`)}finally{y.geocoding=!1}},o=e=>e.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),a=async()=>{k.value=!0;try{const e={...n,configured_at:new Date().toISOString(),test_results:z};await new Promise(t=>setTimeout(t,1e3)),x("save",e),s("success","Configurazione GIS salvata con successo")}catch(e){s("error",`Errore salvataggio: ${e.message}`)}finally{k.value=!1}};return(e,t)=>(c(),v("div",ee,[i("div",se,[(c(),v(B,null,Z(P,(r,N)=>i("div",{key:r.id,class:g(["wizard-step",{active:m.value===N,completed:N<m.value,error:r.hasError}])},[i("div",oe,[N<m.value&&!r.hasError?(c(),v("i",ne)):r.hasError?(c(),v("i",ae)):(c(),v("span",re,$(N+1),1))]),i("div",le,[i("h4",null,$(r.title),1),i("p",null,$(r.description),1)])],2)),64))]),i("div",de,[m.value===0?(c(),v("div",pe,[t[39]||(t[39]=i("h3",null,"Provider Mappe",-1)),t[40]||(t[40]=i("p",null,"Configura i provider di mappe e i servizi di base per la visualizzazione cartografica",-1)),i("div",ue,[i("div",me,[t[27]||(t[27]=i("label",{for:"primary_provider"},"Provider Principale *",-1)),d(i("select",{id:"primary_provider","onUpdate:modelValue":t[0]||(t[0]=r=>n.primary_provider=r),class:"form-select",required:"",onChange:A},t[26]||(t[26]=[V('<option value="" data-v-2cb0dfb4>Seleziona provider</option><option value="openstreetmap" data-v-2cb0dfb4>OpenStreetMap</option><option value="google" data-v-2cb0dfb4>Google Maps</option><option value="bing" data-v-2cb0dfb4>Bing Maps</option><option value="esri" data-v-2cb0dfb4>Esri ArcGIS</option><option value="mapbox" data-v-2cb0dfb4>Mapbox</option><option value="custom" data-v-2cb0dfb4>Tile Server Personalizzato</option>',7)]),544),[[I,n.primary_provider]]),t[28]||(t[28]=i("span",{class:"form-help"},"Provider per le mappe di base",-1))]),i("div",ce,[t[29]||(t[29]=i("label",{for:"api_key"},"API Key",-1)),d(i("input",{id:"api_key","onUpdate:modelValue":t[1]||(t[1]=r=>n.api_key=r),type:"password",class:"form-input",placeholder:"Inserisci API Key se richiesta"},null,512),[[b,n.api_key]]),t[30]||(t[30]=i("span",{class:"form-help"},"Necessaria per Google Maps, Bing, Mapbox",-1))]),i("div",fe,[t[31]||(t[31]=i("label",{for:"tile_server_url"},"URL Tile Server",-1)),d(i("input",{id:"tile_server_url","onUpdate:modelValue":t[2]||(t[2]=r=>n.tile_server_url=r),type:"url",class:"form-input",placeholder:"https://tile.openstreetmap.org/{z}/{x}/{y}.png"},null,512),[[b,n.tile_server_url]]),t[32]||(t[32]=i("span",{class:"form-help"},"Template URL per i tile. Usa {z}, {x}, {y} per coordinata/zoom",-1))]),i("div",ve,[t[33]||(t[33]=i("label",{for:"attribution"},"Attribuzione *",-1)),d(i("input",{id:"attribution","onUpdate:modelValue":t[3]||(t[3]=r=>n.attribution=r),type:"text",class:"form-input",placeholder:"© OpenStreetMap contributors",required:""},null,512),[[b,n.attribution]]),t[34]||(t[34]=i("span",{class:"form-help"},"Testo di attribuzione obbligatorio",-1))]),i("div",ge,[t[35]||(t[35]=i("label",{for:"max_zoom"},"Zoom Massimo",-1)),d(i("input",{id:"max_zoom","onUpdate:modelValue":t[4]||(t[4]=r=>n.max_zoom=r),type:"number",class:"form-input",min:"1",max:"25"},null,512),[[b,n.max_zoom,void 0,{number:!0}]]),t[36]||(t[36]=i("span",{class:"form-help"},"Livello massimo di zoom (default: 18)",-1))]),i("div",be,[t[37]||(t[37]=i("label",{for:"min_zoom"},"Zoom Minimo",-1)),d(i("input",{id:"min_zoom","onUpdate:modelValue":t[5]||(t[5]=r=>n.min_zoom=r),type:"number",class:"form-input",min:"1",max:"25"},null,512),[[b,n.min_zoom,void 0,{number:!0}]]),t[38]||(t[38]=i("span",{class:"form-help"},"Livello minimo di zoom (default: 1)",-1))])])])):w("",!0),m.value===1?(c(),v("div",ye,[t[55]||(t[55]=i("h3",null,"Sistemi di Coordinate",-1)),t[56]||(t[56]=i("p",null,"Configura i sistemi di riferimento geografico per l'Italia e la visualizzazione",-1)),i("div",Se,[i("div",_e,[t[42]||(t[42]=i("label",{for:"default_projection"},"Proiezione Principale *",-1)),d(i("select",{id:"default_projection","onUpdate:modelValue":t[6]||(t[6]=r=>n.default_projection=r),class:"form-select",required:""},t[41]||(t[41]=[V('<option value="EPSG:4326" data-v-2cb0dfb4>EPSG:4326 (WGS84 - Lat/Lng)</option><option value="EPSG:3857" data-v-2cb0dfb4>EPSG:3857 (Web Mercator)</option><option value="EPSG:32632" data-v-2cb0dfb4>EPSG:32632 (UTM 32N - Italia Centro-Nord)</option><option value="EPSG:32633" data-v-2cb0dfb4>EPSG:32633 (UTM 33N - Italia Sud)</option><option value="EPSG:25832" data-v-2cb0dfb4>EPSG:25832 (ETRS89 UTM 32N)</option><option value="EPSG:25833" data-v-2cb0dfb4>EPSG:25833 (ETRS89 UTM 33N)</option>',6)]),512),[[I,n.default_projection]]),t[43]||(t[43]=i("span",{class:"form-help"},"Sistema di riferimento predefinito per le coordinate",-1))]),i("div",ze,[t[45]||(t[45]=i("label",{for:"display_projection"},"Proiezione Display",-1)),d(i("select",{id:"display_projection","onUpdate:modelValue":t[7]||(t[7]=r=>n.display_projection=r),class:"form-select"},t[44]||(t[44]=[i("option",{value:"EPSG:3857"},"EPSG:3857 (Web Mercator)",-1),i("option",{value:"EPSG:4326"},"EPSG:4326 (WGS84)",-1)]),512),[[I,n.display_projection]]),t[46]||(t[46]=i("span",{class:"form-help"},"Proiezione usata per la visualizzazione (default: Web Mercator)",-1))]),i("div",$e,[t[48]||(t[48]=i("label",{for:"coordinate_precision"},"Precisione Coordinate",-1)),d(i("select",{id:"coordinate_precision","onUpdate:modelValue":t[8]||(t[8]=r=>n.coordinate_precision=r),class:"form-select"},t[47]||(t[47]=[i("option",{value:4},"4 decimali (±11.1m)",-1),i("option",{value:5},"5 decimali (±1.1m)",-1),i("option",{value:6},"6 decimali (±0.11m)",-1),i("option",{value:7},"7 decimali (±0.011m)",-1),i("option",{value:8},"8 decimali (±1.1mm)",-1)]),512),[[I,n.coordinate_precision,void 0,{number:!0}]])]),i("div",we,[t[49]||(t[49]=i("label",{for:"default_center_lat"},"Centro Mappa - Latitudine",-1)),d(i("input",{id:"default_center_lat","onUpdate:modelValue":t[9]||(t[9]=r=>n.default_center.lat=r),type:"number",class:"form-input",step:"0.000001",min:"36",max:"47"},null,512),[[b,n.default_center.lat,void 0,{number:!0}]]),t[50]||(t[50]=i("span",{class:"form-help"},"Latitudine centro Italia: ~42.5",-1))]),i("div",Ce,[t[51]||(t[51]=i("label",{for:"default_center_lng"},"Centro Mappa - Longitudine",-1)),d(i("input",{id:"default_center_lng","onUpdate:modelValue":t[10]||(t[10]=r=>n.default_center.lng=r),type:"number",class:"form-input",step:"0.000001",min:"6",max:"19"},null,512),[[b,n.default_center.lng,void 0,{number:!0}]]),t[52]||(t[52]=i("span",{class:"form-help"},"Longitudine centro Italia: ~12.5",-1))]),i("div",ke,[t[53]||(t[53]=i("label",{for:"default_zoom_level"},"Zoom Iniziale",-1)),d(i("input",{id:"default_zoom_level","onUpdate:modelValue":t[11]||(t[11]=r=>n.default_zoom=r),type:"number",class:"form-input",min:"1",max:"18"},null,512),[[b,n.default_zoom,void 0,{number:!0}]]),t[54]||(t[54]=i("span",{class:"form-help"},"Livello di zoom all'apertura della mappa",-1))])])])):w("",!0),m.value===2?(c(),v("div",Te,[t[69]||(t[69]=i("h3",null,"Servizi WMS/WFS",-1)),t[70]||(t[70]=i("p",null,"Configura i servizi per layer aggiuntivi e dati vettoriali",-1)),i("div",Pe,[i("div",Ee,[t[57]||(t[57]=i("label",{for:"wms_server"},"Server WMS",-1)),d(i("input",{id:"wms_server","onUpdate:modelValue":t[12]||(t[12]=r=>n.wms_server=r),type:"url",class:"form-input",placeholder:"https://wms.pcn.minambiente.it/ogc?map=/ms_ogc/WMS_v1.3/raster/ortofoto_colore_06.map"},null,512),[[b,n.wms_server]]),t[58]||(t[58]=i("span",{class:"form-help"},"URL server WMS per ortofoto e layer raster",-1))]),i("div",xe,[t[59]||(t[59]=i("label",{for:"wfs_server"},"Server WFS",-1)),d(i("input",{id:"wfs_server","onUpdate:modelValue":t[13]||(t[13]=r=>n.wfs_server=r),type:"url",class:"form-input",placeholder:"https://wfs.pcn.minambiente.it/ogc"},null,512),[[b,n.wfs_server]]),t[60]||(t[60]=i("span",{class:"form-help"},"URL server WFS per dati vettoriali",-1))]),i("div",Me,[t[62]||(t[62]=i("label",{for:"wms_version"},"Versione WMS",-1)),d(i("select",{id:"wms_version","onUpdate:modelValue":t[14]||(t[14]=r=>n.wms_version=r),class:"form-select"},t[61]||(t[61]=[i("option",{value:"1.3.0"},"1.3.0",-1),i("option",{value:"1.1.1"},"1.1.1",-1),i("option",{value:"1.1.0"},"1.1.0",-1)]),512),[[I,n.wms_version]])]),i("div",Ve,[t[64]||(t[64]=i("label",{for:"wfs_version"},"Versione WFS",-1)),d(i("select",{id:"wfs_version","onUpdate:modelValue":t[15]||(t[15]=r=>n.wfs_version=r),class:"form-select"},t[63]||(t[63]=[i("option",{value:"2.0.0"},"2.0.0",-1),i("option",{value:"1.1.0"},"1.1.0",-1),i("option",{value:"1.0.0"},"1.0.0",-1)]),512),[[I,n.wfs_version]])]),i("div",Ie,[t[65]||(t[65]=i("label",{for:"available_layers"},"Layer Disponibili",-1)),d(i("textarea",{id:"available_layers","onUpdate:modelValue":t[16]||(t[16]=r=>p.value=r),class:"form-textarea",rows:"4",placeholder:"ortofoto,catasto,edifici,strade (uno per riga o separati da virgola)"},null,512),[[b,p.value]]),t[66]||(t[66]=i("span",{class:"form-help"},"Lista dei layer disponibili per l'applicazione",-1))]),i("div",Ue,[t[67]||(t[67]=i("label",{for:"request_timeout"},"Timeout Richieste (ms)",-1)),d(i("input",{id:"request_timeout","onUpdate:modelValue":t[17]||(t[17]=r=>n.request_timeout=r),type:"number",class:"form-input",min:"1000",max:"60000",step:"1000"},null,512),[[b,n.request_timeout,void 0,{number:!0}]]),t[68]||(t[68]=i("span",{class:"form-help"},"Timeout per richieste WMS/WFS (default: 10000ms)",-1))])])])):w("",!0),m.value===3?(c(),v("div",Ae,[t[85]||(t[85]=i("h3",null,"Strumenti di Disegno e Analisi",-1)),t[86]||(t[86]=i("p",null,"Configura le funzionalità di disegno e misurazione sulla mappa",-1)),i("div",We,[i("div",Ge,[i("div",je,[i("label",Le,[d(i("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>n.enable_drawing=r),type:"checkbox",class:"form-checkbox"},null,512),[[D,n.enable_drawing]]),t[71]||(t[71]=i("span",{class:"checkbox-text"},"Abilita Strumenti di Disegno",-1))]),t[72]||(t[72]=i("span",{class:"form-help"},"Permetti di disegnare poligoni, linee e punti sulla mappa",-1))])]),i("div",qe,[i("div",Re,[i("label",De,[d(i("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>n.enable_measurement=r),type:"checkbox",class:"form-checkbox"},null,512),[[D,n.enable_measurement]]),t[73]||(t[73]=i("span",{class:"checkbox-text"},"Abilita Strumenti di Misurazione",-1))]),t[74]||(t[74]=i("span",{class:"form-help"},"Permetti di misurare distanze e aree",-1))])]),i("div",Oe,[t[76]||(t[76]=i("label",{for:"measurement_unit"},"Unità di Misura",-1)),d(i("select",{id:"measurement_unit","onUpdate:modelValue":t[20]||(t[20]=r=>n.measurement_unit=r),class:"form-select"},t[75]||(t[75]=[i("option",{value:"metric"},"Metrico (m, km, m²)",-1),i("option",{value:"imperial"},"Imperiale (ft, mi, ft²)",-1)]),512),[[I,n.measurement_unit]])]),i("div",Ne,[t[78]||(t[78]=i("label",{for:"area_unit"},"Unità Superficie",-1)),d(i("select",{id:"area_unit","onUpdate:modelValue":t[21]||(t[21]=r=>n.area_unit=r),class:"form-select"},t[77]||(t[77]=[i("option",{value:"m2"},"Metri quadri (m²)",-1),i("option",{value:"ha"},"Ettari (ha)",-1),i("option",{value:"km2"},"Chilometri quadri (km²)",-1)]),512),[[I,n.area_unit]])]),i("div",Fe,[i("div",Be,[i("label",Ze,[d(i("input",{"onUpdate:modelValue":t[22]||(t[22]=r=>n.enable_geocoding=r),type:"checkbox",class:"form-checkbox"},null,512),[[D,n.enable_geocoding]]),t[79]||(t[79]=i("span",{class:"checkbox-text"},"Abilita Geocoding",-1))]),t[80]||(t[80]=i("span",{class:"form-help"},"Ricerca indirizzi e luoghi sulla mappa",-1))])]),n.enable_geocoding?(c(),v("div",Ke,[t[82]||(t[82]=i("label",{for:"geocoding_service"},"Servizio Geocoding",-1)),d(i("select",{id:"geocoding_service","onUpdate:modelValue":t[23]||(t[23]=r=>n.geocoding_service=r),class:"form-select"},t[81]||(t[81]=[i("option",{value:"nominatim"},"Nominatim (OpenStreetMap)",-1),i("option",{value:"google"},"Google Geocoding",-1),i("option",{value:"bing"},"Bing Maps Geocoding",-1)]),512),[[I,n.geocoding_service]])])):w("",!0),i("div",Qe,[t[83]||(t[83]=i("label",{for:"default_buffer"},"Buffer Predefinito (metri)",-1)),d(i("input",{id:"default_buffer","onUpdate:modelValue":t[24]||(t[24]=r=>n.default_buffer_meters=r),type:"number",class:"form-input",min:"1",max:"10000"},null,512),[[b,n.default_buffer_meters,void 0,{number:!0}]]),t[84]||(t[84]=i("span",{class:"form-help"},"Distanza buffer per analisi di prossimità",-1))])])])):w("",!0),m.value===4?(c(),v("div",Je,[t[96]||(t[96]=i("h3",null,"Test e Validazione",-1)),t[97]||(t[97]=i("p",null,"Verifica la configurazione GIS e testa i servizi",-1)),i("div",He,[i("div",{class:g(["test-item",u("map_load")])},[t[88]||(t[88]=i("div",{class:"test-info"},[i("h4",null,"Test Caricamento Mappa"),i("p",null,"Verifica caricamento tile del provider principale")],-1)),i("div",Ye,[i("button",{onClick:S,class:"btn-test",disabled:y.map_load},[i("i",{class:g(["pi",y.map_load?"pi-spin pi-spinner":"pi-play"])},null,2),t[87]||(t[87]=C(" Test "))],8,Xe)])],2),i("div",{class:g(["test-item",u("projection")])},[t[90]||(t[90]=i("div",{class:"test-info"},[i("h4",null,"Test Proiezioni"),i("p",null,"Verifica trasformazioni coordinate")],-1)),i("div",he,[i("button",{onClick:R,class:"btn-test",disabled:y.projection},[i("i",{class:g(["pi",y.projection?"pi-spin pi-spinner":"pi-play"])},null,2),t[89]||(t[89]=C(" Test "))],8,is)])],2),i("div",{class:g(["test-item",u("wms")])},[t[92]||(t[92]=i("div",{class:"test-info"},[i("h4",null,"Test Servizi WMS"),i("p",null,"Verifica disponibilità server WMS")],-1)),i("div",ts,[i("button",{onClick:K,class:"btn-test",disabled:y.wms},[i("i",{class:g(["pi",y.wms?"pi-spin pi-spinner":"pi-play"])},null,2),t[91]||(t[91]=C(" Test "))],8,es)])],2),i("div",{class:g(["test-item",u("geocoding")])},[t[94]||(t[94]=i("div",{class:"test-info"},[i("h4",null,"Test Geocoding"),i("p",null,"Verifica ricerca indirizzi")],-1)),i("div",ss,[d(i("input",{"onUpdate:modelValue":t[25]||(t[25]=r=>l.value=r),type:"text",class:"test-address-input",placeholder:"Roma, Italia"},null,512),[[b,l.value]]),i("button",{onClick:_,class:"btn-test",disabled:y.geocoding||!l.value},[i("i",{class:g(["pi",y.geocoding?"pi-spin pi-spinner":"pi-play"])},null,2),t[93]||(t[93]=C(" Test "))],8,os)])],2)]),f.value.length>0?(c(),v("div",ns,[t[95]||(t[95]=i("h4",null,"Log Test",-1)),i("div",as,[(c(!0),v(B,null,Z(f.value,(r,N)=>(c(),v("div",{key:N,class:g(["log-entry",r.type])},[i("span",rs,$(o(r.timestamp)),1),i("span",ls,$(r.message),1)],2))),128))])])):w("",!0)])):w("",!0)]),i("div",ds,[m.value>0?(c(),v("button",{key:0,onClick:j,class:"btn btn-secondary"},t[98]||(t[98]=[i("i",{class:"pi pi-arrow-left"},null,-1),C(" Indietro ")]))):w("",!0),t[101]||(t[101]=i("div",{class:"action-spacer"},null,-1)),m.value<P.length-1?(c(),v("button",{key:1,onClick:T,class:"btn btn-primary",disabled:!U.value},t[99]||(t[99]=[C(" Avanti "),i("i",{class:"pi pi-arrow-right"},null,-1)]),8,ps)):w("",!0),m.value===P.length-1?(c(),v("button",{key:2,onClick:a,class:"btn btn-success",disabled:!G.value||k.value},[i("i",{class:g(["pi",k.value?"pi-spin pi-spinner":"pi-check"])},null,2),t[100]||(t[100]=C(" Salva Configurazione "))],8,us)):w("",!0)])]))}}),cs=J(ms,[["__scopeId","data-v-2cb0dfb4"]]),fs={class:"admin-layout"},vs={class:"admin-content"},gs={class:"integrations-main"},bs={class:"page-header"},ys={class:"page-header-actions"},Ss=["disabled"],_s={class:"integrations-container"},zs={class:"integration-category"},$s={class:"integrations-grid"},ws={class:"integration-card critical"},Cs={class:"card-header"},ks={class:"integration-info"},Ts={class:"integration-actions"},Ps=["disabled"],Es={class:"integration-card critical"},xs={class:"card-header"},Ms={class:"integration-info"},Vs={class:"integration-actions"},Is={class:"integration-card critical"},Us={class:"card-header"},As={class:"integration-info"},Ws={class:"integration-actions"},Gs={class:"integration-card critical"},js={class:"card-header"},Ls={class:"integration-info"},qs={class:"integration-actions"},Rs={class:"integration-card"},Ds={class:"card-header"},Os={class:"integration-info"},Ns={class:"integration-actions"},Fs={class:"integration-category"},Bs={class:"integrations-grid"},Zs={class:"integration-card important"},Ks={class:"card-header"},Qs={class:"integration-info"},Js={class:"integration-actions"},Hs={class:"integration-card important"},Ys={class:"card-header"},Xs={class:"integration-info"},hs={class:"integration-actions"},io={class:"integration-card"},to={class:"card-header"},eo={class:"integration-info"},so={class:"integration-actions"},oo={class:"integration-category"},no={class:"integrations-grid"},ao={class:"integration-card"},ro={class:"card-header"},lo={class:"integration-info"},po={class:"integration-actions"},uo={class:"integration-card"},mo={class:"card-header"},co={class:"integration-info"},fo={class:"integration-actions"},vo={class:"integration-card"},go={class:"card-header"},bo={class:"integration-info"},yo={class:"integration-actions"},So={class:"modal-header"},_o={class:"modal-body"},zo={key:3,class:"placeholder-config"},$o=Q({__name:"SystemIntegrations",setup(O){E(!1);const L=E(!1),q=E(!1),x=E(""),m=W({sister:!1,agenzia_entrate:!1,pec:!1,digital_signature:!1,spid:!1,gis:!1,cad_bim:!1,office:!1,cdp:!1,omi:!1,banking:!1}),k=W({}),p=E(!0),l=E(""),n=W({}),y=u=>{const s=k[u];return{"status-configured":s==="configured","status-error":s==="error","status-not-configured":s==="not_configured"}},f=u=>({configured:"Configurato",error:"Errore",not_configured:"Non Configurato"})[k[u]]||"Sconosciuto",z=u=>({sister:"SISTER - Portale Catasto",agenzia_entrate:"Agenzia delle Entrate",pec:"PEC - Posta Certificata",digital_signature:"Firma Digitale",spid:"SPID",gis:"GIS - Sistemi Geografici",cad_bim:"CAD/BIM",office:"Microsoft Office",cdp:"Cassa Depositi e Prestiti",omi:"OMI - Valori Immobiliari",banking:"Sistemi Bancari"})[u]||u,P=async u=>{m[u]=!0;try{const s=localStorage.getItem("token"),S=await fetch(`/api/integrations/${u}/test`,{method:"POST",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!S.ok)throw new Error(`HTTP error! status: ${S.status}`);const R=await S.json();k[u]=R.status==="success"?"configured":"error"}catch(s){console.error(`Error testing ${u}:`,s),k[u]="error"}finally{m[u]=!1}},M=u=>{x.value=u,q.value=!0},U=()=>{q.value=!1,x.value=""},G=u=>n[u]||{},A=u=>{n[x.value]=u,k[x.value]="configured",U()},T=async()=>{L.value=!0;try{const u=localStorage.getItem("token"),s=await fetch("/api/integrations/configurations",{method:"POST",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify({statuses:k,configs:n})});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);console.log("All configurations saved")}catch(u){console.error("Error saving configurations:",u)}finally{L.value=!1}},j=async()=>{try{p.value=!0,l.value="";const u=localStorage.getItem("token"),s=await fetch("/api/integrations/",{headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"}});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const S=await s.json();Object.assign(k,S.statuses||{}),Object.assign(n,S.configs||{})}catch(u){console.error("Error loading integrations:",u),l.value="Errore nel caricamento delle integrazioni",Object.assign(k,{sister:"not_configured",agenzia_entrate:"not_configured",pec:"not_configured",digital_signature:"not_configured",spid:"not_configured",gis:"not_configured",cad_bim:"not_configured",office:"not_configured",cdp:"not_configured",omi:"not_configured",banking:"not_configured"})}finally{p.value=!1}};return X(()=>{j()}),(u,s)=>(c(),v("div",fs,[h(ti),i("div",vs,[i("main",gs,[i("div",bs,[s[26]||(s[26]=i("div",{class:"page-header-content"},[i("h2",null,"Configurazione Sistemi Terzi"),i("p",null,"Gestisci le integrazioni con sistemi esterni governativi e tecnici")],-1)),i("div",ys,[i("button",{onClick:s[0]||(s[0]=(...S)=>u.exportConfigurations&&u.exportConfigurations(...S)),class:"btn btn-secondary"},s[24]||(s[24]=[i("i",{class:"pi pi-download"},null,-1),C(" Esporta Configurazioni ")])),i("button",{onClick:T,class:"btn btn-primary",disabled:L.value},s[25]||(s[25]=[i("i",{class:"pi pi-save"},null,-1),C(" Salva Tutte ")]),8,Ss)])]),i("div",_s,[i("section",zs,[s[46]||(s[46]=i("div",{class:"category-header"},[i("h3",null,[i("i",{class:"pi pi-building"}),C(" Sistemi Governativi ")]),i("p",null,"Integrazioni obbligatorie con enti pubblici e piattaforme nazionali")],-1)),i("div",$s,[i("div",ws,[i("div",Cs,[i("div",ks,[s[27]||(s[27]=i("h4",null,"SISTER - Portale Catasto",-1)),i("span",{class:g(["integration-status",y("sister")])},$(f("sister")),3)]),i("div",Ts,[i("button",{onClick:s[1]||(s[1]=S=>P("sister")),class:"btn-test",disabled:m.sister},[i("i",{class:g(["pi",m.sister?"pi-spin pi-spinner":"pi-play"])},null,2)],8,Ps),i("button",{onClick:s[2]||(s[2]=S=>M("sister")),class:"btn-config"},s[28]||(s[28]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[29]||(s[29]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Import automatico dati catastali, visure particelle, mappe WMS/WFS</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-globe" data-v-cef4d349></i> Web Services API </span><span class="detail-item critical" data-v-cef4d349><i class="pi pi-exclamation-triangle" data-v-cef4d349></i> Integrazione Critica </span></div></div>',1))]),i("div",Es,[i("div",xs,[i("div",Ms,[s[30]||(s[30]=i("h4",null,"Agenzia delle Entrate",-1)),i("span",{class:g(["integration-status",y("agenzia_entrate")])},$(f("agenzia_entrate")),3)]),i("div",Vs,[i("button",{onClick:s[3]||(s[3]=S=>P("agenzia_entrate")),class:"btn-test"},s[31]||(s[31]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[4]||(s[4]=S=>M("agenzia_entrate")),class:"btn-config"},s[32]||(s[32]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[33]||(s[33]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Validazione CF, indirizzi, valori OMI, aggiornamenti catastali</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-shield" data-v-cef4d349></i> API SIATEL </span><span class="detail-item critical" data-v-cef4d349><i class="pi pi-exclamation-triangle" data-v-cef4d349></i> Validazione Dati </span></div></div>',1))]),i("div",Is,[i("div",Us,[i("div",As,[s[34]||(s[34]=i("h4",null,"PEC - Posta Certificata",-1)),i("span",{class:g(["integration-status",y("pec")])},$(f("pec")),3)]),i("div",Ws,[i("button",{onClick:s[5]||(s[5]=S=>P("pec")),class:"btn-test"},s[35]||(s[35]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[6]||(s[6]=S=>M("pec")),class:"btn-config"},s[36]||(s[36]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[37]||(s[37]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Invio notifiche certificate, ricevute di consegna, valore legale</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-envelope" data-v-cef4d349></i> SMTP/PEC </span><span class="detail-item critical" data-v-cef4d349><i class="pi pi-exclamation-triangle" data-v-cef4d349></i> Obbligatorio per Legge </span></div></div>',1))]),i("div",Gs,[i("div",js,[i("div",Ls,[s[38]||(s[38]=i("h4",null,"Firma Digitale",-1)),i("span",{class:g(["integration-status",y("digital_signature")])},$(f("digital_signature")),3)]),i("div",qs,[i("button",{onClick:s[7]||(s[7]=S=>P("digital_signature")),class:"btn-test"},s[39]||(s[39]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[8]||(s[8]=S=>M("digital_signature")),class:"btn-config"},s[40]||(s[40]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[41]||(s[41]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Firma digitale documenti ufficiali, certificati PADES/CADES</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-key" data-v-cef4d349></i> CAD Compliance </span><span class="detail-item critical" data-v-cef4d349><i class="pi pi-exclamation-triangle" data-v-cef4d349></i> Autenticazione Documenti </span></div></div>',1))]),i("div",Rs,[i("div",Ds,[i("div",Os,[s[42]||(s[42]=i("h4",null,"SPID",-1)),i("span",{class:g(["integration-status",y("spid")])},$(f("spid")),3)]),i("div",Ns,[i("button",{onClick:s[9]||(s[9]=S=>P("spid")),class:"btn-test"},s[43]||(s[43]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[10]||(s[10]=S=>M("spid")),class:"btn-config"},s[44]||(s[44]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[45]||(s[45]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Autenticazione sicura cittadini per portale soggetti esproprio</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-users" data-v-cef4d349></i> SAML 2.0 </span><span class="detail-item" data-v-cef4d349><i class="pi pi-info" data-v-cef4d349></i> Portale Cittadini </span></div></div>',1))])])]),i("section",Fs,[s[59]||(s[59]=i("div",{class:"category-header"},[i("h3",null,[i("i",{class:"pi pi-desktop"}),C(" Sistemi Tecnici ")]),i("p",null,"Integrazioni con software CAD, BIM, GIS e sistemi di progettazione")],-1)),i("div",Bs,[i("div",Zs,[i("div",Ks,[i("div",Qs,[s[47]||(s[47]=i("h4",null,"GIS - Sistemi Geografici",-1)),i("span",{class:g(["integration-status",y("gis")])},$(f("gis")),3)]),i("div",Js,[i("button",{onClick:s[11]||(s[11]=S=>P("gis")),class:"btn-test"},s[48]||(s[48]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[12]||(s[12]=S=>M("gis")),class:"btn-config"},s[49]||(s[49]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[50]||(s[50]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Mappe interattive, analisi geografiche, overlay progetti su cartografia</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-map" data-v-cef4d349></i> WMS/WFS/Shapefile </span><span class="detail-item important" data-v-cef4d349><i class="pi pi-star" data-v-cef4d349></i> Analisi Spaziale </span></div></div>',1))]),i("div",Hs,[i("div",Ys,[i("div",Xs,[s[51]||(s[51]=i("h4",null,"CAD/BIM",-1)),i("span",{class:g(["integration-status",y("cad_bim")])},$(f("cad_bim")),3)]),i("div",hs,[i("button",{onClick:s[13]||(s[13]=S=>P("cad_bim")),class:"btn-test"},s[52]||(s[52]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[14]||(s[14]=S=>M("cad_bim")),class:"btn-config"},s[53]||(s[53]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[54]||(s[54]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Import progetti tecnici, elaborati AutoCAD, modelli BIM</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-file" data-v-cef4d349></i> DXF/DWG/IFC </span><span class="detail-item important" data-v-cef4d349><i class="pi pi-star" data-v-cef4d349></i> Progetti Tecnici </span></div></div>',1))]),i("div",io,[i("div",to,[i("div",eo,[s[55]||(s[55]=i("h4",null,"Microsoft Office",-1)),i("span",{class:g(["integration-status",y("office")])},$(f("office")),3)]),i("div",so,[i("button",{onClick:s[15]||(s[15]=S=>P("office")),class:"btn-test"},s[56]||(s[56]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[16]||(s[16]=S=>M("office")),class:"btn-config"},s[57]||(s[57]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[58]||(s[58]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Template documenti Word, export Excel, timeline Project</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-file-word" data-v-cef4d349></i> Word/Excel/Project </span><span class="detail-item" data-v-cef4d349><i class="pi pi-info" data-v-cef4d349></i> Templates </span></div></div>',1))])])]),i("section",oo,[s[72]||(s[72]=i("div",{class:"category-header"},[i("h3",null,[i("i",{class:"pi pi-euro"}),C(" Sistemi Finanziari ")]),i("p",null,"Integrazioni per gestione pagamenti, depositi e valutazioni immobiliari")],-1)),i("div",no,[i("div",ao,[i("div",ro,[i("div",lo,[s[60]||(s[60]=i("h4",null,"Cassa Depositi e Prestiti",-1)),i("span",{class:g(["integration-status",y("cdp")])},$(f("cdp")),3)]),i("div",po,[i("button",{onClick:s[17]||(s[17]=S=>P("cdp")),class:"btn-test"},s[61]||(s[61]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[18]||(s[18]=S=>M("cdp")),class:"btn-config"},s[62]||(s[62]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[63]||(s[63]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Deposito indennità non accettate, gestione escrow</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-money-bill" data-v-cef4d349></i> Depositi Ufficiali </span><span class="detail-item" data-v-cef4d349><i class="pi pi-info" data-v-cef4d349></i> Pagamenti </span></div></div>',1))]),i("div",uo,[i("div",mo,[i("div",co,[s[64]||(s[64]=i("h4",null,"OMI - Valori Immobiliari",-1)),i("span",{class:g(["integration-status",y("omi")])},$(f("omi")),3)]),i("div",fo,[i("button",{onClick:s[19]||(s[19]=S=>P("omi")),class:"btn-test"},s[65]||(s[65]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[20]||(s[20]=S=>M("omi")),class:"btn-config"},s[66]||(s[66]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[67]||(s[67]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Valori di mercato per calcolo indennità espropriative</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-chart-line" data-v-cef4d349></i> Valutazioni Ufficiali </span><span class="detail-item" data-v-cef4d349><i class="pi pi-info" data-v-cef4d349></i> Stime Indennità </span></div></div>',1))]),i("div",vo,[i("div",go,[i("div",bo,[s[68]||(s[68]=i("h4",null,"Sistemi Bancari",-1)),i("span",{class:g(["integration-status",y("banking")])},$(f("banking")),3)]),i("div",yo,[i("button",{onClick:s[21]||(s[21]=S=>P("banking")),class:"btn-test"},s[69]||(s[69]=[i("i",{class:"pi pi-play"},null,-1)])),i("button",{onClick:s[22]||(s[22]=S=>M("banking")),class:"btn-config"},s[70]||(s[70]=[i("i",{class:"pi pi-cog"},null,-1)]))])]),s[71]||(s[71]=V('<div class="card-content" data-v-cef4d349><p data-v-cef4d349>Validazione IBAN, elaborazione pagamenti, bonifici</p><div class="integration-details" data-v-cef4d349><span class="detail-item" data-v-cef4d349><i class="pi pi-credit-card" data-v-cef4d349></i> IBAN/SEPA </span><span class="detail-item" data-v-cef4d349><i class="pi pi-info" data-v-cef4d349></i> Pagamenti </span></div></div>',1))])])])]),q.value?(c(),v("div",{key:0,class:"modal-overlay",onClick:U},[i("div",{class:"modal-content config-modal",onClick:s[23]||(s[23]=ii(()=>{},["stop"]))},[i("div",So,[i("h3",null,"Configurazione "+$(z(x.value)),1),i("button",{onClick:U,class:"btn-close"},s[73]||(s[73]=[i("i",{class:"pi pi-times"},null,-1)]))]),i("div",_o,[x.value==="sister"?(c(),H(Xi,{key:0,"initial-config":G(x.value),onSave:A,onClose:U},null,8,["initial-config"])):x.value==="pec"?(c(),H(te,{key:1,"initial-config":G(x.value),onSave:A,onClose:U},null,8,["initial-config"])):x.value==="gis"?(c(),H(cs,{key:2,"initial-config":G(x.value),onSave:A,onClose:U},null,8,["initial-config"])):(c(),v("div",zo,[i("p",null,"Configurazione per "+$(z(x.value))+" in sviluppo",1)]))])])])):w("",!0)])])]))}}),To=J($o,[["__scopeId","data-v-cef4d349"]]);export{To as default};
