import{d as z,y,g as d,c as p,a as t,b as i,i as S,h as w,j as c,v as m,f as r,l as k,w as u,r as A,t as v,u as h,o as f,_ as C}from"./index-Bibk5VaL.js";import{P as R,a as x}from"./PublicFooter-_OR7ne0j.js";import"./BaseNavbar-BSvAFYsY.js";const P={class:"citizen-login-page"},M={class:"login-container"},V={class:"login-card"},N={class:"form-group"},I=["aria-invalid"],H={class:"form-group"},U=["aria-invalid"],X={class:"form-options"},B={class:"checkbox-label"},E=["disabled"],L={key:0,class:"error-message",role:"alert","aria-live":"polite"},q={class:"login-info"},D={class:"help-links"},F=z({__name:"CitizenLogin",setup(T){const b=h();y();const o=d({email:"",password:"",remember:!1}),l=d(!1),s=d(""),_=async()=>{l.value=!0,s.value="";try{o.value.email==="****************"&&o.value.password==="citizen123"?(localStorage.setItem("citizen_token","citizen-demo-token"),localStorage.setItem("citizen_user",JSON.stringify({id:"citizen-1",name:"Mario Rossi",fiscal_code:"****************",email:"<EMAIL>",type:"citizen"})),b.push("/citizen/dashboard")):s.value="Credenziali non valide. Usa **************** / citizen123 per la demo."}catch{s.value="Errore durante l'accesso. Riprova più tardi."}finally{l.value=!1}};return(g,e)=>{const n=A("router-link");return f(),p("div",P,[t(R),i("div",M,[i("div",V,[e[13]||(e[13]=i("div",{class:"login-header"},[i("h1",null,"Accesso Area Cittadini"),i("p",null,"Accedi per consultare le procedure che ti riguardano")],-1)),i("form",{onSubmit:S(_,["prevent"]),class:"login-form",novalidate:""},[i("div",N,[e[3]||(e[3]=i("label",{for:"email"},"Codice Fiscale o Email *",-1)),c(i("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>o.value.email=a),type:"text",class:"form-input",placeholder:"RSSMRA80A01H501<NAME_EMAIL>",required:"","aria-describedby":"email-help","aria-invalid":s.value?"true":"false"},null,8,I),[[m,o.value.email]]),e[4]||(e[4]=i("div",{id:"email-help",class:"form-help"}," Inserisci il tuo codice fiscale o indirizzo email ",-1))]),i("div",H,[e[5]||(e[5]=i("label",{for:"password"},"Password *",-1)),c(i("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=a=>o.value.password=a),type:"password",class:"form-input",placeholder:"Inserisci la tua password",required:"","aria-invalid":s.value?"true":"false"},null,8,U),[[m,o.value.password]])]),i("div",X,[i("label",B,[c(i("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=a=>o.value.remember=a)},null,512),[[k,o.value.remember]]),e[6]||(e[6]=i("span",{class:"checkmark"},null,-1)),e[7]||(e[7]=r(" Ricordami "))]),t(n,{to:"/citizen/reset-password",class:"forgot-link"},{default:u(()=>e[8]||(e[8]=[r(" Password dimenticata? ")])),_:1,__:[8]})]),i("button",{type:"submit",class:"btn btn-primary btn-large",disabled:l.value},v(l.value?"Accesso in corso...":"Accedi"),9,E),s.value?(f(),p("div",L,v(s.value),1)):w("",!0)],32),i("div",q,[e[11]||(e[11]=i("div",{class:"info-section"},[i("h3",null,"Come ottenere le credenziali?"),i("p",null," Le credenziali di accesso vengono fornite dall'ente che gestisce la procedura espropriativa di tuo interesse. Se non hai ricevuto le credenziali, contatta direttamente l'ufficio espropri competente. ")],-1)),e[12]||(e[12]=i("div",{class:"info-section"},[i("h3",null,"Problemi di accesso?"),i("p",null," Se hai difficoltà ad accedere, verifica di aver inserito correttamente il codice fiscale e la password. Per assistenza, contatta il supporto dell'ente di riferimento. ")],-1)),i("div",D,[t(n,{to:"/contact"},{default:u(()=>e[9]||(e[9]=[r("Contatta il Supporto")])),_:1,__:[9]}),t(n,{to:"/privacy"},{default:u(()=>e[10]||(e[10]=[r("Privacy Policy")])),_:1,__:[10]})])])])]),t(x)])}}}),G=C(F,[["__scopeId","data-v-5b9edf95"]]);export{G as default};
