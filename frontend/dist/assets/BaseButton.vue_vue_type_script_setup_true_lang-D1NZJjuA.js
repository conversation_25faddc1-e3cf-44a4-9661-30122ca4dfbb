import{b as r}from"./design-system-BvlP813R.js";import{d,m,c as a,M as p,h as n,H as u,n as i,o}from"./index-Bibk5VaL.js";const c=["disabled"],f={key:0,class:"pi pi-spin pi-spinner mr-2"},y=d({__name:"BaseButton",props:{variant:{default:"primary"},size:{default:"md"},loading:{type:Boolean,default:!1},icon:{},iconPosition:{default:"left"},className:{},disabled:{type:Boolean}},setup(l){const e=l,t=m(()=>r(e.variant,e.size)+(e.className?` ${e.className}`:""));return(s,b)=>(o(),a("button",p({class:t.value,disabled:s.disabled||s.loading},s.$attrs),[s.loading?(o(),a("i",f)):s.icon&&s.iconPosition==="left"?(o(),a("i",{key:1,class:i([s.icon,"mr-2"])},null,2)):n("",!0),u(s.$slots,"default"),s.icon&&s.iconPosition==="right"&&!s.loading?(o(),a("i",{key:2,class:i([s.icon,"ml-2"])},null,2)):n("",!0)],16,c))}});export{y as _};
