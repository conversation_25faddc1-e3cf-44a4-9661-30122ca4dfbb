import{d as I,g as L,m as N,A as M,c as n,n as l,b as i,h as o,a as w,r as A,w as u,F as v,q as p,H as h,t as r,D as S,o as s,B as m,f,_ as $}from"./index-Bibk5VaL.js";const T=["aria-label"],V={class:"navbar-brand"},z={key:0,class:"brand-icon"},E={class:"brand-text"},P={class:"brand-title"},j={key:0,class:"brand-subtitle"},D={key:0,class:"navbar-nav"},F={key:1,class:"nav-badge"},q={key:1,class:"navbar-actions"},H=["aria-expanded"],R={key:3,id:"mobile-menu",class:"mobile-menu"},W={class:"mobile-nav"},G={key:0,class:"mobile-actions"},J=<PERSON>({__name:"BaseNavbar",props:{variant:{default:"public"},brandTitle:{default:"ExProject"},brandSubtitle:{default:""},brandIcon:{default:"pi pi-building"},brandLink:{default:"/"},brandAriaLabel:{default:"ExProject - Torna alla homepage"},navigationItems:{default:()=>[]},responsive:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},ariaLabel:{default:"Navigazione principale"}},setup(_){const k=_,c=S(),t=L(!1),g=N(()=>[`navbar-${k.variant}`,{"navbar-mobile-open":t.value}]),b=a=>c.path===a||c.path.startsWith(a+"/"),y=a=>["nav-item",{"nav-active":a.active||b(a.href)}],B=()=>{t.value=!t.value},C=()=>{t.value=!1};return M(()=>c.path,()=>{t.value=!1}),(a,K)=>{const d=A("router-link");return s(),n("nav",{class:l(["base-navbar",g.value]),role:"navigation","aria-label":a.ariaLabel},[i("div",V,[w(d,{to:a.brandLink,class:"brand-link","aria-label":a.brandAriaLabel},{default:u(()=>[a.showIcon?(s(),n("div",z,[i("i",{class:l(a.brandIcon)},null,2)])):o("",!0),i("div",E,[i("h1",P,r(a.brandTitle),1),a.brandSubtitle?(s(),n("span",j,r(a.brandSubtitle),1)):o("",!0)])]),_:1},8,["to","aria-label"])]),a.navigationItems.length>0?(s(),n("div",D,[(s(!0),n(v,null,p(a.navigationItems,e=>(s(),m(d,{key:e.name,to:e.href,class:l(["nav-link",y(e)]),"aria-current":b(e.href)?"page":!1},{default:u(()=>[e.icon?(s(),n("i",{key:0,class:l([e.icon,"nav-icon"])},null,2)):o("",!0),f(" "+r(e.name)+" ",1),e.badge?(s(),n("span",F,r(e.badge),1)):o("",!0)]),_:2},1032,["to","class","aria-current"]))),128))])):o("",!0),a.$slots.actions?(s(),n("div",q,[h(a.$slots,"actions",{},void 0,!0)])):o("",!0),a.responsive?(s(),n("button",{key:2,onClick:B,class:"mobile-menu-toggle","aria-expanded":t.value,"aria-controls":"mobile-menu","aria-label":"Menu di navigazione"},[i("i",{class:l(t.value?"pi pi-times":"pi pi-bars")},null,2)],8,H)):o("",!0),a.responsive&&t.value?(s(),n("div",R,[i("div",W,[(s(!0),n(v,null,p(a.navigationItems,e=>(s(),m(d,{key:e.name,to:e.href,class:"mobile-nav-link",onClick:C},{default:u(()=>[e.icon?(s(),n("i",{key:0,class:l([e.icon,"mobile-nav-icon"])},null,2)):o("",!0),f(" "+r(e.name),1)]),_:2},1032,["to"]))),128))]),a.$slots.mobileActions?(s(),n("div",G,[h(a.$slots,"mobileActions",{},void 0,!0)])):o("",!0)])):o("",!0)],10,T)}}}),Q=$(J,[["__scopeId","data-v-13417383"]]);export{Q as B};
