import{g as b}from"./design-system-BvlP813R.js";import{d as g,g as C,m as d,c as t,h as o,b as l,f as k,t as n,M as V,n as _,o as a}from"./index-Bibk5VaL.js";const B={class:"space-y-2"},$=["for"],q={key:0,class:"text-error-500 ml-1"},N={class:"relative"},I=["id","type","placeholder","value","disabled","required"],x={key:0,class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},z={key:1,class:"text-sm text-error-600"},M={key:2,class:"text-sm text-gray-500"},E=g({__name:"BaseInput",props:{modelValue:{},type:{default:"text"},placeholder:{},label:{},error:{},hint:{},disabled:{type:<PERSON>olean},required:{type:Boolean},variant:{default:"default"},icon:{},className:{}},emits:["update:modelValue"],setup(u,{emit:c}){const s=u,p=c,i=C(`input-${Math.random().toString(36).substr(2,9)}`),m=d(()=>{const e=s.error?"error":s.variant,r=b(e),v="px-3 py-2 text-sm",f=s.icon?"pr-10":"";return`${r} ${v} ${f} ${s.className||""}`}),h=d(()=>s.error?"text-error-400":s.variant==="success"?"text-success-400":"text-gray-400"),y=e=>{const r=e.target;p("update:modelValue",r.value)};return(e,r)=>(a(),t("div",B,[e.label?(a(),t("label",{key:0,for:i.value,class:"block text-sm font-medium text-gray-700"},[k(n(e.label)+" ",1),e.required?(a(),t("span",q,"*")):o("",!0)],8,$)):o("",!0),l("div",N,[l("input",V({id:i.value,class:m.value,type:e.type,placeholder:e.placeholder,value:e.modelValue,disabled:e.disabled,required:e.required,onInput:y},e.$attrs),null,16,I),e.icon?(a(),t("div",x,[l("i",{class:_([e.icon,h.value])},null,2)])):o("",!0)]),e.error?(a(),t("p",z,n(e.error),1)):e.hint?(a(),t("p",M,n(e.hint),1)):o("",!0)]))}});export{E as _};
