var je=Object.defineProperty;var Ie=(F,u,r)=>u in F?je(F,u,{enumerable:!0,configurable:!0,writable:!0,value:r}):F[u]=r;var _e=(F,u,r)=>Ie(F,typeof u!="symbol"?u+"":u,r);import{d as ie,m as U,c as d,b as e,h as y,n as D,t as n,H as Pe,f as I,o as a,_ as ae,E as le,g as j,J as Ae,s as he,F as J,q as G,a as B,T as Ve,w as se,A as oe,p as fe,B as ee,G as N,j as W,l as ye,v as ne,k as re,e as de,i as xe,L as ze,r as Te,u as Ee}from"./index-Bibk5VaL.js";import{A as Fe}from"./AdminNavbar-jBevcaIl.js";import{_ as ge}from"./BaseInput.vue_vue_type_script_setup_true_lang-DWl8EspK.js";import{_ as ve}from"./BaseButton.vue_vue_type_script_setup_true_lang-D1NZJjuA.js";import{_ as Ue}from"./BaseModal.vue_vue_type_script_setup_true_lang-CIvlL0EO.js";import{P as Re}from"./PropertyCreateForm-QSbxRY_N.js";import"./BaseNavbar-BSvAFYsY.js";import"./design-system-BvlP813R.js";const Me={class:"base-wizard-step"},Be={class:"step-header"},Le={key:0,class:"pi pi-check"},Ne={key:1,class:"pi pi-times"},qe={key:2},Oe={class:"step-info"},He={key:0,class:"step-content"},Je={key:1,class:"step-actions"},Ge=["disabled"],We=["disabled"],Qe=ie({__name:"BaseWizardStep",props:{stepNumber:{},title:{},description:{},status:{default:"pending"},active:{type:Boolean,default:!1},canProceed:{type:Boolean,default:!1},showActions:{type:Boolean,default:!0},showPrevious:{type:Boolean,default:!0},showNext:{type:Boolean,default:!0},showSubmit:{type:Boolean,default:!1},nextLabel:{default:"Avanti"},submitLabel:{default:"Completa"},submitting:{type:Boolean,default:!1}},emits:["previous","next","submit"],setup(F,{emit:u}){const r=F,k=U(()=>({"step-pending":r.status==="pending","step-active":r.status==="active","step-completed":r.status==="completed","step-error":r.status==="error"}));return(i,o)=>(a(),d("div",Me,[e("div",Be,[e("div",{class:D(["step-marker",k.value])},[i.status==="completed"?(a(),d("i",Le)):i.status==="error"?(a(),d("i",Ne)):(a(),d("span",qe,n(i.stepNumber),1))],2),e("div",Oe,[e("h3",null,n(i.title),1),e("p",null,n(i.description),1)])]),i.active?(a(),d("div",He,[Pe(i.$slots,"default",{},void 0,!0)])):y("",!0),i.active&&i.showActions?(a(),d("div",Je,[i.showPrevious?(a(),d("button",{key:0,onClick:o[0]||(o[0]=v=>i.$emit("previous")),class:"btn btn-secondary",type:"button"},o[3]||(o[3]=[e("i",{class:"pi pi-arrow-left"},null,-1),I(" Indietro ")]))):y("",!0),o[5]||(o[5]=e("div",{class:"action-spacer"},null,-1)),i.showNext?(a(),d("button",{key:1,onClick:o[1]||(o[1]=v=>i.$emit("next")),class:"btn btn-primary",disabled:!i.canProceed,type:"button"},[I(n(i.nextLabel)+" ",1),o[4]||(o[4]=e("i",{class:"pi pi-arrow-right"},null,-1))],8,Ge)):y("",!0),i.showSubmit?(a(),d("button",{key:2,onClick:o[2]||(o[2]=v=>i.$emit("submit")),class:"btn btn-success",disabled:!i.canProceed||i.submitting,type:"button"},[e("i",{class:D(["pi",i.submitting?"pi-spin pi-spinner":"pi-check"])},null,2),I(" "+n(i.submitLabel),1)],8,We)):y("",!0)])):y("",!0)]))}}),me=ae(Qe,[["__scopeId","data-v-cce50249"]]);class Ke{constructor(){_e(this,"baseUrl","/ai/project")}async analyzeProject(u){try{return(await le.post(`${this.baseUrl}/analyze`,u)).data}catch(r){throw console.error("Error analyzing project:",r),new Error("Failed to analyze project data")}}async getProjectSuggestions(u){try{return(await le.post(`${this.baseUrl}/suggestions`,u)).data}catch(r){throw console.error("Error getting suggestions:",r),new Error("Failed to get project suggestions")}}async validateProject(u){try{return(await le.post(`${this.baseUrl}/validate`,u)).data}catch(r){throw console.error("Error validating project:",r),new Error("Failed to validate project data")}}async validateField(u){try{return(await le.post(`${this.baseUrl}/validate-field`,u)).data}catch(r){throw console.error("Error validating field:",r),new Error("Failed to validate field")}}async analyzeBudget(u){try{return(await le.post(`${this.baseUrl}/budget-analysis`,u)).data}catch(r){throw console.error("Error analyzing budget:",r),new Error("Failed to analyze budget")}}async checkHealth(){try{return(await le.get(`${this.baseUrl}/health`)).data}catch(u){return console.error("Error checking AI health:",u),{status:"unhealthy",error:u.message}}}async getCapabilities(){try{return(await le.get(`${this.baseUrl}/capabilities`)).data}catch(u){throw console.error("Error getting AI capabilities:",u),new Error("Failed to get AI capabilities")}}extractOverview(u){return!u.success||!u.data.overview?null:u.data.overview}extractSuggestions(u){var r;return u.success?(r=u.data.suggestions)!=null&&r.all?u.data.suggestions.all:u.data.suggestions&&Array.isArray(u.data.suggestions)?u.data.suggestions:[]:[]}extractPrioritySuggestions(u){var r;return!u.success||!((r=u.data.suggestions)!=null&&r.priority_suggestions)?[]:u.data.suggestions.priority_suggestions}extractInsights(u){return!u.success||!u.data.insights?[]:u.data.insights}extractNextSteps(u){return!u.success||!u.data.next_steps?[]:u.data.next_steps}extractFieldValidation(u,r){if(!u.success||!u.data.field_validations)return null;const k=u.data.field_validations[r];return k?{valid:k.valid,message:k.message,severity:k.severity,suggestions:k.suggestions||[]}:null}extractBudgetEstimation(u){return!u.success||!u.data.budget_analysis?null:u.data.budget_analysis}formatConfidence(u){return`${Math.round(u*100)}%`}getConfidenceColorClass(u){return u>=.8?"text-green-600":u>=.6?"text-yellow-600":"text-red-600"}groupSuggestionsByField(u){return u.reduce((r,k)=>{const i=k.field_name;return r[i]||(r[i]=[]),r[i].push(k),r},{})}filterSuggestionsByCategory(u,r){return u.filter(k=>k.category===r)}getHighConfidenceSuggestions(u){return u.filter(r=>r.confidence>=.7)}}const Z=new Ke,Xe=(F,u)=>{let r;return function(...i){const o=()=>{clearTimeout(r),F(...i)};clearTimeout(r),r=setTimeout(o,u)}};function Ye(){const F=j(!1),u=j(!1),r=j(!1),k=j(null),i=j([]),o=j(null),v=j([]),x=j([]),$=Ae({}),p=j(null),m=(_,h,R)=>{console.log(`[${_.toUpperCase()}] ${h}: ${R}`)},P=U(()=>k.value!==null),L=U(()=>{var _;return((_=k.value)==null?void 0:_.confidence)||0}),Q=U(()=>i.value.length),K=U(()=>i.value.filter(_=>_.confidence>=.8)),V=U(()=>{var _;return((_=o.value)==null?void 0:_.status)||"incomplete"}),C=U(()=>{var _;return((_=o.value)==null?void 0:_.completeness_percentage)||0}),E=async _=>{if(!F.value){F.value=!0;try{const h=await Z.analyzeProject(_);if(h.success)k.value=h,i.value=Z.extractSuggestions(h),o.value=Z.extractOverview(h),v.value=Z.extractInsights(h),x.value=Z.extractNextSteps(h),m("success","Analisi AI completata",`Analisi completata con confidenza del ${Z.formatConfidence(h.confidence)}`);else throw new Error(h.message||"Analisi fallita")}catch(h){console.error("AI Analysis error:",h),m("error","Errore Analisi AI","Impossibile completare l'analisi del progetto")}finally{F.value=!1}}},q=async _=>{if(!r.value){r.value=!0;try{const h=await Z.getProjectSuggestions(_);h.success&&(i.value=Z.extractSuggestions(h))}catch(h){console.error("Suggestions error:",h)}finally{r.value=!1}}},O=async _=>{if(u.value)return!1;u.value=!0;try{const h=await Z.validateProject(_);return h.success?(Object.assign($,h.data.field_validations||{}),h.data.overall_valid||!1):!1}catch(h){return console.error("Validation error:",h),!1}finally{u.value=!1}},te=async(_,h,R={})=>{try{const H=await Z.validateField({field_name:_,field_value:h,context:R});H.success&&H.data.field_validation&&($[_]=H.data.field_validation)}catch(H){console.error(`Field validation error for ${_}:`,H)}},X=Xe(te,1e3);return{isAnalyzing:F,isValidating:u,isSuggestionsLoading:r,lastAnalysis:k,currentSuggestions:i,currentOverview:o,insights:v,nextSteps:x,validationResults:$,aiHealth:p,hasAnalysis:P,analysisConfidence:L,totalSuggestions:Q,highPrioritySuggestions:K,projectStatus:V,completenessPercentage:C,analyzeProject:E,getSuggestions:q,validateProject:O,validateField:te,debouncedFieldValidation:X,analyzeBudget:async _=>{try{const h=await Z.analyzeBudget(_);return h.success?Z.extractBudgetEstimation(h):null}catch(h){return console.error("Budget analysis error:",h),null}},applySuggestion:(_,h)=>{const R={...h};return _.suggested_value!==""&&(R[_.field_name]=_.suggested_value),i.value=i.value.filter(H=>H.id!==_.id),m("info","Suggerimento applicato",_.title),R},dismissSuggestion:_=>{i.value=i.value.filter(h=>h.id!==_)},getSuggestionsForField:_=>i.value.filter(h=>h.field_name===_),getFieldValidation:_=>$[_]||null,hasFieldError:_=>{const h=$[_];return h&&!h.valid},getFieldValidationMessage:_=>{const h=$[_];return(h==null?void 0:h.message)||""},getFieldValidationSeverity:_=>{const h=$[_];return(h==null?void 0:h.severity)||"success"},checkAIHealth:async()=>{try{p.value=await Z.checkHealth()}catch(_){console.error("AI health check error:",_),p.value={status:"unhealthy",error:_.message}}},clearAIState:()=>{k.value=null,i.value=[],o.value=null,v.value=[],x.value=[],Object.keys($).forEach(_=>delete $[_])},getConfidenceColorClass:_=>Z.getConfidenceColorClass(_),formatConfidence:_=>Z.formatConfidence(_)}}const Ze={key:0,class:"ai-overview-panel"},et={class:"overview-header"},tt={class:"status-indicator"},st={class:"status-content"},it={class:"status-title"},at={class:"status-details"},ot={class:"completion-percentage"},lt={key:0,class:"ai-confidence"},nt={class:"completion-progress"},rt={class:"progress-bar-container"},dt={class:"data-quality-grid"},ut={key:0,class:"pi pi-check text-green-600"},ct={key:1,class:"pi pi-times text-red-600"},pt={key:0,class:"pi pi-check text-green-600"},gt={key:1,class:"pi pi-times text-red-600"},vt={key:0,class:"pi pi-check text-green-600"},mt={key:1,class:"pi pi-times text-red-600"},ft={class:"quality-item"},bt={class:"field-completion"},yt={key:0,class:"insights-section"},_t={class:"insights-grid"},ht={class:"insight-content"},xt={class:"insight-title"},$t={class:"insight-description"},wt={key:1,class:"next-steps-section"},kt={class:"next-steps-list"},St={class:"step-priority"},Ct={class:"step-content"},Dt={class:"step-action"},jt={class:"step-description"},It={class:"step-time"},Pt=ie({__name:"AIProjectOverview",props:{overview:{},insights:{},nextSteps:{},confidence:{}},setup(F){const u=p=>({ready:"pi pi-check-circle text-green-600",needs_review:"pi pi-exclamation-triangle text-yellow-600",incomplete:"pi pi-info-circle text-blue-600"})[p]||"pi pi-info-circle text-gray-600",r=p=>({ready:"progress-ready",needs_review:"progress-review",incomplete:"progress-incomplete"})[p]||"",k=p=>p>=.8?"confidence-high":p>=.6?"confidence-medium":"confidence-low",i=p=>`${Math.round(p*100)}%`,o=p=>({positive:"insight-positive",warning:"insight-warning",info:"insight-info"})[p]||"insight-info",v=p=>({high:"priority-high",medium:"priority-medium",low:"priority-low"})[p]||"priority-medium",x=p=>({high:"Alta",medium:"Media",low:"Bassa"})[p]||p,$=p=>({high:"badge-danger",medium:"badge-warning",low:"badge-info"})[p]||"badge-info";return(p,m)=>p.overview?(a(),d("div",Ze,[e("div",et,[e("div",tt,[e("i",{class:D([u(p.overview.status),"status-icon"])},null,2),e("div",st,[e("h3",it,n(p.overview.status_message),1),e("div",at,[m[0]||(m[0]=e("span",{class:"completion-text"},"Completezza: ",-1)),e("strong",ot,n(Math.round(p.overview.completeness_percentage))+"%",1)])])]),p.confidence?(a(),d("div",lt,[e("div",{class:D(["confidence-badge",k(p.confidence)])},[m[1]||(m[1]=e("i",{class:"pi pi-star-fill"},null,-1)),e("span",null,n(i(p.confidence)),1)],2)])):y("",!0)]),e("div",nt,[e("div",rt,[e("div",{class:D(["progress-bar-fill",r(p.overview.status)]),style:he({width:`${p.overview.completeness_percentage}%`})},null,6)])]),e("div",dt,[e("div",{class:D(["quality-item",{"quality-success":p.overview.data_quality.validation_passed}])},[m[2]||(m[2]=e("i",{class:"pi pi-check-circle quality-icon"},null,-1)),m[3]||(m[3]=e("span",{class:"quality-label"},"Validazione",-1)),p.overview.data_quality.validation_passed?(a(),d("i",ut)):(a(),d("i",ct))],2),e("div",{class:D(["quality-item",{"quality-success":p.overview.data_quality.has_budget_estimate}])},[m[4]||(m[4]=e("i",{class:"pi pi-calculator quality-icon"},null,-1)),m[5]||(m[5]=e("span",{class:"quality-label"},"Budget",-1)),p.overview.data_quality.has_budget_estimate?(a(),d("i",pt)):(a(),d("i",gt))],2),e("div",{class:D(["quality-item",{"quality-success":p.overview.data_quality.ai_suggestions_available}])},[m[6]||(m[6]=e("i",{class:"pi pi-lightbulb quality-icon"},null,-1)),m[7]||(m[7]=e("span",{class:"quality-label"},"Suggerimenti",-1)),p.overview.data_quality.ai_suggestions_available?(a(),d("i",vt)):(a(),d("i",mt))],2),e("div",ft,[m[8]||(m[8]=e("i",{class:"pi pi-list quality-icon"},null,-1)),m[9]||(m[9]=e("span",{class:"quality-label"},"Campi",-1)),e("span",bt,n(p.overview.data_quality.required_fields_completed),1)])]),p.insights&&p.insights.length>0?(a(),d("div",yt,[m[10]||(m[10]=e("h4",{class:"insights-title"},[e("i",{class:"pi pi-eye"}),I(" Insights AI ")],-1)),e("div",_t,[(a(!0),d(J,null,G(p.insights,P=>(a(),d("div",{key:P.title,class:D(["insight-card",o(P.level)])},[e("i",{class:D([P.icon,"insight-icon"])},null,2),e("div",ht,[e("h5",xt,n(P.title),1),e("p",$t,n(P.description),1)])],2))),128))])])):y("",!0),p.nextSteps&&p.nextSteps.length>0?(a(),d("div",wt,[m[11]||(m[11]=e("h4",{class:"next-steps-title"},[e("i",{class:"pi pi-arrow-right"}),I(" Prossimi Passi ")],-1)),e("div",kt,[(a(!0),d(J,null,G(p.nextSteps.slice(0,3),(P,L)=>(a(),d("div",{key:L,class:D(["next-step-item",v(P.priority)])},[e("div",St,[e("span",{class:D([$(P.priority),"priority-badge"])},n(x(P.priority)),3)]),e("div",Ct,[e("h6",Dt,n(P.action),1),e("p",jt,n(P.description),1),e("span",It,"⏱️ "+n(P.estimated_time),1)])],2))),128))])])):y("",!0)])):y("",!0)}}),At=ae(Pt,[["__scopeId","data-v-276c7cca"]]),Vt={key:0,class:"ai-suggestions-panel"},zt={class:"panel-header"},Tt={class:"header-content"},Et={class:"suggestions-count"},Ft={class:"suggestions-content"},Ut={class:"suggestion-header"},Rt={class:"suggestion-info"},Mt={class:"suggestion-title"},Bt={class:"suggestion-meta"},Lt={class:"confidence-indicator"},Nt={class:"suggestion-actions"},qt=["onClick"],Ot=["onClick"],Ht={class:"suggestion-body"},Jt={class:"suggestion-description"},Gt={key:0,class:"suggested-value"},Wt={class:"suggested-value-code"},Qt={key:1,class:"suggestion-reasoning"},Kt={class:"reasoning-details"},Xt={class:"reasoning-text"},Yt={key:0,class:"show-more-section"},Zt=ie({__name:"AISuggestionsPanel",props:{suggestions:{},maxDisplay:{default:3},showCategories:{default:()=>[]}},emits:["apply-suggestion","dismiss-suggestion","dismiss-all"],setup(F,{emit:u}){const r=F,k=u,i=j(!1),o=U(()=>r.showCategories.length===0?r.suggestions:r.suggestions.filter(V=>r.showCategories.includes(V.category))),v=U(()=>i.value?o.value:o.value.slice(0,r.maxDisplay)),x=U(()=>!i.value&&o.value.length>r.maxDisplay),$=()=>{i.value=!0},p=()=>{k("dismiss-all")},m=V=>{const C="suggestion-card",E=V.confidence>=.8?"high-confidence":V.confidence>=.6?"medium-confidence":"low-confidence";return`${C} ${E}`},P=V=>({quality:"Qualità",compliance:"Conformità",budget:"Budget",planning:"Pianificazione",data_quality:"Dati",consistency:"Coerenza",risk_management:"Rischi"})[V]||V,L=V=>({quality:"badge-info",compliance:"badge-warning",budget:"badge-success",planning:"badge-info",data_quality:"badge-secondary",consistency:"badge-info",risk_management:"badge-danger"})[V]||"badge-info",Q=V=>`${Math.round(V*100)}%`,K=V=>typeof V=="number"?V.toLocaleString("it-IT"):String(V);return(V,C)=>V.suggestions.length>0?(a(),d("div",Vt,[e("div",zt,[e("div",Tt,[C[0]||(C[0]=e("i",{class:"pi pi-lightbulb text-blue-500"},null,-1)),C[1]||(C[1]=e("h3",{class:"panel-title"},"Suggerimenti AI",-1)),e("span",Et,n(V.suggestions.length),1)]),e("div",{class:"header-actions"},[e("button",{class:"dismiss-all-btn",onClick:p,title:"Nascondi tutti i suggerimenti"},C[2]||(C[2]=[e("i",{class:"pi pi-times"},null,-1)]))])]),e("div",Ft,[B(Ve,{name:"suggestion",tag:"div"},{default:se(()=>[(a(!0),d(J,null,G(v.value,E=>(a(),d("div",{key:E.id,class:D(["suggestion-card",m(E)])},[e("div",Ut,[e("div",Rt,[e("h4",Mt,n(E.title),1),e("div",Bt,[e("span",{class:D([L(E.category),"category-badge"])},n(P(E.category)),3),e("span",Lt,[C[3]||(C[3]=e("i",{class:"pi pi-chart-line text-xs"},null,-1)),I(" "+n(Q(E.confidence)),1)])])]),e("div",Nt,[E.suggested_value?(a(),d("button",{key:0,class:"apply-btn",onClick:q=>V.$emit("apply-suggestion",E),title:"Applica suggerimento"},C[4]||(C[4]=[e("i",{class:"pi pi-check"},null,-1)]),8,qt)):y("",!0),e("button",{class:"dismiss-btn",onClick:q=>V.$emit("dismiss-suggestion",E.id),title:"Nascondi suggerimento"},C[5]||(C[5]=[e("i",{class:"pi pi-times"},null,-1)]),8,Ot)])]),e("div",Ht,[e("p",Jt,n(E.description),1),E.suggested_value?(a(),d("div",Gt,[C[6]||(C[6]=e("label",{class:"suggested-value-label"},"Valore suggerito:",-1)),e("code",Wt,n(K(E.suggested_value)),1)])):y("",!0),E.reasoning?(a(),d("div",Qt,[e("details",Kt,[C[7]||(C[7]=e("summary",{class:"reasoning-summary"},[e("i",{class:"pi pi-info-circle"}),I(" Perché questo suggerimento? ")],-1)),e("p",Xt,n(E.reasoning),1)])])):y("",!0)])],2))),128))]),_:1}),x.value?(a(),d("div",Yt,[e("button",{class:"show-more-btn",onClick:$},C[8]||(C[8]=[e("i",{class:"pi pi-chevron-down"},null,-1),I(" Mostra altri suggerimenti ")]))])):y("",!0)])])):y("",!0)}}),es=ae(Zt,[["__scopeId","data-v-064720b5"]]),ts={key:0,class:"field-suggestions"},ss={class:"suggestion-content"},is={class:"suggestion-text"},as={class:"suggestion-confidence"},os={class:"suggestion-actions"},ls=["onClick"],ns=["onClick"],rs=ie({__name:"FieldSuggestions",props:{suggestions:{},maxVisible:{default:2}},emits:["apply","dismiss"],setup(F,{emit:u}){const r=i=>i>=.8?"high-confidence":i>=.6?"medium-confidence":"low-confidence",k=i=>`${Math.round(i*100)}%`;return(i,o)=>i.suggestions.length>0?(a(),d("div",ts,[(a(!0),d(J,null,G(i.suggestions.slice(0,i.maxVisible),v=>(a(),d("div",{key:v.id,class:D(["suggestion-pill",r(v.confidence)])},[e("div",ss,[e("div",is,n(v.title),1),e("div",as,n(k(v.confidence)),1)]),e("div",os,[v.suggested_value?(a(),d("button",{key:0,type:"button",class:"suggestion-btn apply-btn",onClick:x=>i.$emit("apply",v),title:"Applica suggerimento"},o[0]||(o[0]=[e("i",{class:"pi pi-check"},null,-1)]),8,ls)):y("",!0),e("button",{type:"button",class:"suggestion-btn dismiss-btn",onClick:x=>i.$emit("dismiss",v.id),title:"Nascondi suggerimento"},o[1]||(o[1]=[e("i",{class:"pi pi-times"},null,-1)]),8,ns)])],2))),128))])):y("",!0)}}),ue=ae(rs,[["__scopeId","data-v-d641fbbb"]]),ds={key:0,class:"validation-message"},us={key:1,class:"ai-validation-message error"},cs=ie({__name:"ValidationMessage",props:{validation:{},error:{}},setup(F){const u=F,r=U(()=>u.validation&&u.validation.message||u.error),k=i=>{const o={error:"pi pi-times-circle",warning:"pi pi-exclamation-triangle",success:"pi pi-check-circle",info:"pi pi-info-circle"};return o[i]||o.info};return(i,o)=>r.value?(a(),d("div",ds,[i.validation&&i.validation.message?(a(),d("div",{key:0,class:D(["ai-validation-message",i.validation.severity])},[o[0]||(o[0]=e("span",{class:"ai-badge"},"AI",-1)),e("i",{class:D(k(i.validation.severity))},null,2),e("span",null,n(i.validation.message),1)],2)):i.error?(a(),d("div",us,[o[1]||(o[1]=e("i",{class:"pi pi-exclamation-triangle"},null,-1)),e("span",null,n(i.error),1)])):y("",!0)])):y("",!0)}}),ce=ae(cs,[["__scopeId","data-v-f63b766e"]]),ps={class:"space-y-6"},gs={class:"section-header"},vs={class:"ai-toggle-container"},ms={class:"ai-toggle-switch"},fs=["title"],bs={class:"suggestions-count-badge"},ys={class:"form-grid"},_s={class:"form-group full-width"},hs={class:"field-container"},xs={class:"md:col-span-2"},$s={class:"field-container"},ws={class:"field-container"},ks={class:"field-container"},Ss={class:"bg-white border border-gray-200 rounded-xl p-6"},Cs={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ds={class:"field-container"},js={key:0,class:"text-sm text-danger mt-1"},Is={class:"field-container"},Ps={class:"budget-input-container"},As={class:"bg-white border border-gray-200 rounded-xl p-6"},Vs={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},zs={key:0,class:"text-sm text-danger mt-1"},Ts=["min"],Es={class:"bg-white border border-gray-200 rounded-xl p-6"},Fs={class:"ai-suggestions-modal"},Us={class:"modal-header"},Rs={class:"modal-body"},Ms={key:2,class:"fixed bottom-4 right-4 bg-white border border-blue-200 rounded-lg shadow-lg p-4 flex items-center gap-3 z-50"},Bs=ie({__name:"ProjectSetupForm",props:{modelValue:{}},emits:["update:modelValue","validation-change"],setup(F,{emit:u}){const r=(T,l)=>{let w;return function(...Ce){const De=()=>{clearTimeout(w),T(...Ce)};clearTimeout(w),w=setTimeout(De,l)}},k=F,i=u,o=j({...k.modelValue}),v=j({}),{isAnalyzing:x,currentSuggestions:$,currentOverview:p,insights:m,nextSteps:P,analysisConfidence:L,getSuggestions:Q,debouncedFieldValidation:K,applySuggestion:V,dismissSuggestion:C,getSuggestionsForField:E,getFieldValidation:q,hasFieldError:O,getFieldValidationSeverity:te,analyzeBudget:X}=Ye(),g=j(localStorage.getItem("ai-enabled")!=="false"),c=j(!0),b=j(null),s=U(()=>p.value),t=U(()=>m.value),f=U(()=>P.value),S=U(()=>Object.keys(v.value).length===0&&o.value.title.trim()!==""&&o.value.description.trim()!==""&&o.value.legal_reference!==""&&o.value.category!==""&&o.value.municipality.trim()!==""&&o.value.region!==""&&o.value.start_date!==null),A=()=>{if(v.value={},o.value.title.trim()?o.value.title.length<5&&(v.value.title="Il titolo deve essere di almeno 5 caratteri"):v.value.title="Il titolo del progetto è obbligatorio",o.value.description.trim()?o.value.description.length<20&&(v.value.description="La descrizione deve essere di almeno 20 caratteri"):v.value.description="La descrizione è obbligatoria",o.value.legal_reference||(v.value.legal_reference="Il riferimento normativo è obbligatorio"),o.value.category||(v.value.category="La categoria del progetto è obbligatoria"),o.value.municipality.trim()||(v.value.municipality="Il comune è obbligatorio"),o.value.region||(v.value.region="La regione è obbligatoria"),!o.value.start_date)v.value.start_date="La data di inizio è obbligatoria";else{const T=new Date(o.value.start_date),l=new Date;l.setHours(0,0,0,0),T<l&&(v.value.start_date="La data di inizio non può essere nel passato")}if(o.value.start_date&&o.value.expected_end_date){const T=new Date(o.value.start_date);new Date(o.value.expected_end_date)<=T&&(v.value.expected_end_date="La data di fine deve essere successiva alla data di inizio")}i("validation-change",S.value)};oe(o,T=>{i("update:modelValue",{...T}),ze(()=>{A()})},{deep:!0}),oe(()=>k.modelValue,T=>{const l=JSON.stringify(o.value),w=JSON.stringify(T);l!==w&&(o.value={...T})},{deep:!0});const z=T=>E(T),M=T=>{const l=O(T),w=te(T);return l?"border-red-300 focus:border-red-500 focus:ring-red-200":w==="warning"?"border-yellow-300 focus:border-yellow-500 focus:ring-yellow-200":w==="success"?"border-green-300 focus:border-green-500 focus:ring-green-200":""},Y=(T,l)=>{K(T,l,o.value),g.value&&pe(T,l)&&_()},pe=(T,l)=>!!(T==="title"&&l&&l.length>10||T==="category"&&l||T==="municipality"&&l&&l.length>2),_=r(async()=>{h()&&await Q(o.value)},1500),h=()=>o.value.title.length>5||o.value.category!==""||o.value.municipality.length>2,R=async T=>{const l=V(T,o.value);o.value=l,(T.field_name==="category"||T.field_name==="municipality")&&await H()},H=async()=>{var T;if(o.value.category&&o.value.municipality&&!o.value.budget){const l=await X(o.value);(T=l==null?void 0:l.estimated_range)!=null&&T.recommended&&(b.value=l.estimated_range.recommended)}},$e=()=>{b.value&&(o.value.budget=b.value,b.value=null)},we=T=>new Intl.NumberFormat("it-IT").format(T),ke=()=>{localStorage.setItem("ai-enabled",g.value.toString()),g.value||Se()},Se=()=>{b.value=null,c.value=!1};return fe(async()=>{g.value&&h()&&await Q(o.value)}),A(),(T,l)=>(a(),d("div",ps,[g.value&&s.value?(a(),ee(At,{key:0,overview:s.value,insights:t.value,"next-steps":f.value,confidence:N(L)},null,8,["overview","insights","next-steps","confidence"])):y("",!0),e("div",{class:D(["bg-white border border-gray-200 rounded-xl p-6",{"ai-enhanced-section":g.value}])},[e("div",gs,[l[24]||(l[24]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-info-circle text-primary"}),I(" Informazioni Base ")],-1)),e("div",vs,[l[23]||(l[23]=e("span",{class:"ai-toggle-label"},"Assistente AI",-1)),e("label",ms,[W(e("input",{"onUpdate:modelValue":l[0]||(l[0]=w=>g.value=w),type:"checkbox",class:"ai-toggle-input",onChange:ke},null,544),[[ye,g.value]]),l[21]||(l[21]=e("span",{class:"ai-toggle-slider"},[e("i",{class:"pi pi-magic-wand ai-toggle-icon"})],-1))]),g.value&&N($).length>0?(a(),d("button",{key:0,type:"button",class:"ai-suggestions-trigger",onClick:l[1]||(l[1]=w=>T.showSuggestionsModal=!0),title:`${N($).length} suggerimenti AI disponibili`},[l[22]||(l[22]=e("i",{class:"pi pi-lightbulb"},null,-1)),e("span",bs,n(N($).length),1)],8,fs)):y("",!0)])]),e("div",ys,[e("div",_s,[l[25]||(l[25]=e("label",{class:"form-label"}," Titolo Progetto * ",-1)),e("div",hs,[W(e("input",{"onUpdate:modelValue":l[2]||(l[2]=w=>o.value.title=w),type:"text",class:D(["form-input",M("title")]),placeholder:"Es. Ampliamento SS7 Appia - Tratto Cisterna-Velletri",onInput:l[3]||(l[3]=w=>Y("title",w.target.value))},null,34),[[ne,o.value.title]]),g.value&&z("title").length>0?(a(),ee(ue,{key:0,suggestions:z("title"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("title"),error:v.value.title},null,8,["validation","error"])]),e("div",xs,[l[26]||(l[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione * ",-1)),e("div",$s,[W(e("textarea",{"onUpdate:modelValue":l[4]||(l[4]=w=>o.value.description=w),rows:"3",class:D(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors resize-none",M("description")]),placeholder:"Descrizione dettagliata del progetto di esproprio...",onInput:l[5]||(l[5]=w=>Y("description",w.target.value))},null,34),[[ne,o.value.description]]),g.value&&z("description").length>0?(a(),ee(ue,{key:0,suggestions:z("description"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("description"),error:v.value.description},null,8,["validation","error"])]),e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Riferimento Normativo * ",-1)),e("div",ws,[W(e("select",{"onUpdate:modelValue":l[6]||(l[6]=w=>o.value.legal_reference=w),class:D(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",M("legal_reference")]),onChange:l[7]||(l[7]=w=>Y("legal_reference",w.target.value))},l[27]||(l[27]=[de('<option value="" data-v-517d809e>Seleziona riferimento</option><option value="dpr_327_2001" data-v-517d809e>D.P.R. 327/2001</option><option value="legge_865_1971" data-v-517d809e>Legge 865/1971</option><option value="dlgs_302_2002" data-v-517d809e>D.Lgs. 302/2002</option><option value="altro" data-v-517d809e>Altro</option>',5)]),34),[[re,o.value.legal_reference]]),g.value&&z("legal_reference").length>0?(a(),ee(ue,{key:0,suggestions:z("legal_reference"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("legal_reference"),error:v.value.legal_reference},null,8,["validation","error"])]),e("div",null,[l[30]||(l[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Categoria Progetto * ",-1)),e("div",ks,[W(e("select",{"onUpdate:modelValue":l[8]||(l[8]=w=>o.value.category=w),class:D(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",M("category")]),onChange:l[9]||(l[9]=w=>Y("category",w.target.value))},l[29]||(l[29]=[de('<option value="" data-v-517d809e>Seleziona categoria</option><option value="infrastructure" data-v-517d809e>Infrastrutture</option><option value="utilities" data-v-517d809e>Servizi Pubblici</option><option value="housing" data-v-517d809e>Edilizia Residenziale</option><option value="industrial" data-v-517d809e>Insediamenti Produttivi</option><option value="environmental" data-v-517d809e>Opere Ambientali</option><option value="other" data-v-517d809e>Altro</option>',7)]),34),[[re,o.value.category]]),g.value&&z("category").length>0?(a(),ee(ue,{key:0,suggestions:z("category"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("category"),error:v.value.category},null,8,["validation","error"])])])],2),e("div",Ss,[l[38]||(l[38]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-4 flex items-center"},[e("i",{class:"pi pi-building text-primary mr-2"}),I(" Dettagli Amministrativi ")],-1)),e("div",Cs,[e("div",null,[l[31]||(l[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Comune * ",-1)),e("div",Ds,[W(e("input",{"onUpdate:modelValue":l[10]||(l[10]=w=>o.value.municipality=w),type:"text",class:D(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",M("municipality")]),placeholder:"Es. Roma",onInput:l[11]||(l[11]=w=>Y("municipality",w.target.value))},null,34),[[ne,o.value.municipality]]),g.value&&z("municipality").length>0?(a(),ee(ue,{key:0,suggestions:z("municipality"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("municipality"),error:v.value.municipality},null,8,["validation","error"])]),e("div",null,[l[33]||(l[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Regione * ",-1)),W(e("select",{"onUpdate:modelValue":l[12]||(l[12]=w=>o.value.region=w),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"},l[32]||(l[32]=[de('<option value="" data-v-517d809e>Seleziona regione</option><option value="lazio" data-v-517d809e>Lazio</option><option value="lombardia" data-v-517d809e>Lombardia</option><option value="campania" data-v-517d809e>Campania</option><option value="sicilia" data-v-517d809e>Sicilia</option><option value="veneto" data-v-517d809e>Veneto</option><option value="emilia-romagna" data-v-517d809e>Emilia-Romagna</option><option value="piemonte" data-v-517d809e>Piemonte</option><option value="puglia" data-v-517d809e>Puglia</option><option value="toscana" data-v-517d809e>Toscana</option><option value="calabria" data-v-517d809e>Calabria</option><option value="sardegna" data-v-517d809e>Sardegna</option><option value="liguria" data-v-517d809e>Liguria</option><option value="marche" data-v-517d809e>Marche</option><option value="abruzzo" data-v-517d809e>Abruzzo</option><option value="friuli-venezia-giulia" data-v-517d809e>Friuli-Venezia Giulia</option><option value="trentino-alto-adige" data-v-517d809e>Trentino-Alto Adige</option><option value="umbria" data-v-517d809e>Umbria</option><option value="basilicata" data-v-517d809e>Basilicata</option><option value="molise" data-v-517d809e>Molise</option><option value="valle-aosta" data-v-517d809e>Valle d&#39;Aosta</option>',21)]),512),[[re,o.value.region]]),v.value.region?(a(),d("p",js,n(v.value.region),1)):y("",!0)]),e("div",null,[l[35]||(l[35]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Priorità ",-1)),W(e("select",{"onUpdate:modelValue":l[13]||(l[13]=w=>o.value.priority=w),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"},l[34]||(l[34]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[re,o.value.priority]])]),e("div",null,[l[37]||(l[37]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Budget Stimato (€) ",-1)),e("div",Is,[e("div",Ps,[W(e("input",{"onUpdate:modelValue":l[14]||(l[14]=w=>o.value.budget=w),type:"number",min:"0",step:"1000",class:D(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",M("budget")]),placeholder:"Es. 500000",onInput:l[15]||(l[15]=w=>Y("budget",w.target.value))},null,34),[[ne,o.value.budget,void 0,{number:!0}]]),g.value&&b.value&&!o.value.budget?(a(),d("button",{key:0,type:"button",class:"ml-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors flex items-center gap-1",onClick:$e},[l[36]||(l[36]=e("i",{class:"pi pi-magic-wand"},null,-1)),I(" AI: €"+n(we(b.value)),1)])):y("",!0)]),g.value&&z("budget").length>0?(a(),ee(ue,{key:0,suggestions:z("budget"),onApply:R,onDismiss:N(C)},null,8,["suggestions","onDismiss"])):y("",!0)]),B(ce,{validation:N(q)("budget"),error:v.value.budget},null,8,["validation","error"])])])]),e("div",As,[l[41]||(l[41]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-4 flex items-center"},[e("i",{class:"pi pi-calendar text-primary mr-2"}),I(" Tempistiche ")],-1)),e("div",Vs,[e("div",null,[l[39]||(l[39]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Data Inizio Prevista * ",-1)),W(e("input",{"onUpdate:modelValue":l[16]||(l[16]=w=>o.value.start_date=w),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"},null,512),[[ne,o.value.start_date]]),v.value.start_date?(a(),d("p",zs,n(v.value.start_date),1)):y("",!0)]),e("div",null,[l[40]||(l[40]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Data Fine Prevista ",-1)),W(e("input",{"onUpdate:modelValue":l[17]||(l[17]=w=>o.value.expected_end_date=w),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",min:o.value.start_date},null,8,Ts),[[ne,o.value.expected_end_date]])])])]),e("div",Es,[l[42]||(l[42]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-4 flex items-center"},[e("i",{class:"pi pi-file-edit text-primary mr-2"}),I(" Note Aggiuntive ")],-1)),W(e("textarea",{"onUpdate:modelValue":l[18]||(l[18]=w=>o.value.notes=w),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors resize-none",placeholder:"Note, osservazioni o dettagli aggiuntivi sul progetto..."},null,512),[[ne,o.value.notes]])]),T.showSuggestionsModal&&N($).length>0?(a(),d("div",{key:1,class:"modal-overlay",onClick:l[20]||(l[20]=xe(w=>T.showSuggestionsModal=!1,["self"]))},[e("div",Fs,[e("div",Us,[l[44]||(l[44]=e("h3",{class:"modal-title"},[e("i",{class:"pi pi-lightbulb text-primary mr-2"}),I(" Suggerimenti AI ")],-1)),e("button",{type:"button",class:"btn-close",onClick:l[19]||(l[19]=w=>T.showSuggestionsModal=!1)},l[43]||(l[43]=[e("i",{class:"pi pi-times"},null,-1)]))]),e("div",Rs,[B(es,{suggestions:N($),"max-display":10,onApplySuggestion:T.applySuggestionFromModal,onDismissSuggestion:N(C),onDismissAll:T.dismissAllSuggestionsFromModal},null,8,["suggestions","onApplySuggestion","onDismissSuggestion","onDismissAll"])])])])):y("",!0),N(x)?(a(),d("div",Ms,l[45]||(l[45]=[e("div",{class:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"},null,-1),e("span",{class:"text-sm text-gray-700"},"AI sta analizzando...",-1)]))):y("",!0)]))}}),Ls=ae(Bs,[["__scopeId","data-v-517d809e"]]),Ns={class:"modal-overlay"},qs={class:"modal-content"},Os={class:"modal-header"},Hs={class:"modal-title"},Js={class:"modal-body"},Gs={class:"user-list"},Ws={class:"user-info-container"},Qs={class:"user-avatar"},Ks={class:"user-details"},Xs={class:"user-name"},Ys={class:"user-role"},Zs={class:"user-email"},ei={class:"user-checkbox"},ti=["value"],si={key:0,class:"empty-state"},ii={class:"modal-footer"},ai={class:"selection-count"},oi={class:"count-number"},li={class:"modal-actions"},ni=ie({__name:"TeamSelectionModal",props:{title:{},availableUsers:{},selectedUsers:{}},emits:["close","save"],setup(F,{emit:u}){const r=F,k=u,i=j([...r.selectedUsers]);oe(()=>r.selectedUsers,v=>{i.value=[...v]});const o=()=>{k("save",i.value)};return(v,x)=>(a(),d("div",Ns,[e("div",qs,[e("div",Os,[e("h3",Hs,n(v.title),1),e("button",{onClick:x[0]||(x[0]=$=>v.$emit("close")),class:"modal-close-btn"},x[3]||(x[3]=[e("i",{class:"pi pi-times"},null,-1)]))]),e("div",Js,[e("div",Gs,[(a(!0),d(J,null,G(v.availableUsers,$=>(a(),d("div",{key:$.id,class:"user-item"},[e("div",Ws,[e("div",Qs,n($.name.charAt(0)),1),e("div",Ks,[e("p",Xs,n($.name),1),e("p",Ys,n($.role),1),e("p",Zs,n($.email),1)])]),e("label",ei,[W(e("input",{type:"checkbox",value:$.id,"onUpdate:modelValue":x[1]||(x[1]=p=>i.value=p),class:"checkbox-input"},null,8,ti),[[ye,i.value]])])]))),128))]),v.availableUsers.length===0?(a(),d("div",si," Nessun utente disponibile per questo ruolo ")):y("",!0)]),e("div",ii,[e("p",ai,[e("span",oi,n(i.value.length),1),x[4]||(x[4]=I(" utenti selezionati "))]),e("div",li,[e("button",{onClick:x[2]||(x[2]=$=>v.$emit("close")),class:"btn btn-secondary"}," Annulla "),e("button",{onClick:o,class:"btn btn-primary"}," Conferma Selezione ")])])])]))}}),be=ae(ni,[["__scopeId","data-v-8d2143e7"]]),ri={class:"space-y-6"},di={class:"bg-white border border-gray-200 rounded-xl p-6"},ui={class:"space-y-6"},ci=["value"],pi={key:0,class:"text-sm text-danger mt-1"},gi=["value"],vi={key:0,class:"text-sm text-danger mt-1"},mi=["value"],fi={key:0,class:"text-sm text-danger mt-1"},bi={class:"bg-white border border-gray-200 rounded-xl p-6"},yi={class:"flex items-center justify-between mb-4"},_i={key:0,class:"space-y-3"},hi={class:"user-cell-content"},xi={class:"user-info-sm"},$i={class:"user-name-sm"},wi={class:"user-email-sm"},ki={class:"role-badge tecnico"},Si=["onClick"],Ci={key:1,class:"text-center py-8 text-gray-500 text-sm bg-gray-50 rounded-lg border-2 border-dashed border-gray-200"},Di={class:"bg-white border border-gray-200 rounded-xl p-6"},ji={class:"flex items-center justify-between mb-4"},Ii={key:0,class:"space-y-3"},Pi={class:"user-cell-content"},Ai={class:"user-info-sm"},Vi={class:"user-name-sm"},zi={class:"user-email-sm"},Ti={class:"role-badge legale"},Ei=["onClick"],Fi={key:1,class:"text-center py-8 text-gray-500 text-sm bg-gray-50 rounded-lg border-2 border-dashed border-gray-200"},Ui={class:"bg-white border border-gray-200 rounded-xl p-6"},Ri={class:"flex items-center justify-between mb-4"},Mi={key:0,class:"space-y-3"},Bi={class:"user-cell-content"},Li={class:"user-info-sm"},Ni={class:"user-name-sm"},qi={class:"user-email-sm"},Oi={class:"role-badge amministrativo"},Hi=["onClick"],Ji={key:1,class:"text-center py-8 text-gray-500 text-sm bg-gray-50 rounded-lg border-2 border-dashed border-gray-200"},Gi={key:3,class:"text-center py-8"},Wi={key:4,class:"bg-red-50 border border-red-200 rounded-lg p-4"},Qi={class:"flex items-center gap-2 text-red-800"},Ki=ie({__name:"RoleAssignmentForm",props:{modelValue:{},projectData:{}},emits:["update:modelValue","validation-change"],setup(F,{emit:u}){const r=F,k=u,i=j({...r.modelValue}),o=j(!1),v=j(""),x=j(!1),$=j(!1),p=j(!1),m=j({}),P=j({rup:[],rpe:[],dirigente:[],technical:[],legal:[],administrative:[]}),L=U(()=>P.value.technical.filter(s=>i.value.technical_team.includes(s.id))),Q=U(()=>P.value.legal.filter(s=>i.value.legal_team.includes(s.id))),K=U(()=>P.value.administrative.filter(s=>i.value.administrative_team.includes(s.id))),V=U(()=>Object.keys(m.value).length===0&&i.value.rup_user_id!==null&&i.value.rpe_user_id!==null&&i.value.dirigente_user_id!==null&&!C()),C=()=>{const s=[i.value.rup_user_id,i.value.rpe_user_id,i.value.dirigente_user_id];return new Set(s.filter(f=>f!==null)).size!==s.filter(f=>f!==null).length},E=()=>{if(m.value={},i.value.rup_user_id||(m.value.rup_user_id="Il RUP è obbligatorio"),i.value.rpe_user_id||(m.value.rpe_user_id="Il RPE è obbligatorio"),i.value.dirigente_user_id||(m.value.dirigente_user_id="Il Dirigente è obbligatorio"),C()){const s="La stessa persona non può ricoprire più ruoli obbligatori";i.value.rup_user_id===i.value.rpe_user_id&&(m.value.rpe_user_id=s),i.value.rup_user_id===i.value.dirigente_user_id&&(m.value.dirigente_user_id=s),i.value.rpe_user_id===i.value.dirigente_user_id&&(m.value.dirigente_user_id=s)}k("validation-change",V.value)},q=s=>{const t=L.value[s].id;i.value.technical_team=i.value.technical_team.filter(f=>f!==t)},O=s=>{const t=Q.value[s].id;i.value.legal_team=i.value.legal_team.filter(f=>f!==t)},te=s=>{const t=K.value[s].id;i.value.administrative_team=i.value.administrative_team.filter(f=>f!==t)},X=s=>{i.value.technical_team=s,x.value=!1},g=s=>{i.value.legal_team=s,$.value=!1},c=s=>{i.value.administrative_team=s,p.value=!1};oe(i,s=>{k("update:modelValue",{...s})},{deep:!0}),oe(()=>r.modelValue,s=>{const t=JSON.stringify(i.value),f=JSON.stringify(s);t!==f&&(i.value={...s})},{deep:!0});const b=async()=>{console.log("🔄 Loading users from API...");try{o.value=!0,v.value="";const s=localStorage.getItem("token");console.log("🔑 Token found:",!!s),console.log("🔍 Token value (first 20 chars):",s?s.substring(0,20)+"...":"NULL"),console.log("👤 User in localStorage:",localStorage.getItem("user"));const t=await fetch("/api/users/",{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(console.log("📡 API Response status:",t.status),!t.ok){const z=await t.text();throw console.error("❌ API Error:",t.status,z),new Error(`HTTP error! status: ${t.status} - ${z}`)}const f=await t.json();console.log("👥 Raw response from API:",f),console.log("📝 Type of response:",typeof f);const S=f.users||f||[];console.log("🔍 Extracted users array:",S),console.log("📊 Total users found:",S.length);const A=S.map(z=>({id:z.id,name:z.full_name||z.name||"Nome non specificato",email:z.email,role:z.role||"user"}));console.log("🔧 Transformed users:",A),P.value={rup:A,rpe:A,dirigente:A,technical:A,legal:A,administrative:A},console.log("✅ Available users loaded:",P.value),console.log("🎯 RUP users:",P.value.rup.length),console.log("🎯 RPE users:",P.value.rpe.length),console.log("🎯 Dirigente users:",P.value.dirigente.length)}catch(s){console.error("❌ Error loading users:",s),v.value="Errore nel caricamento degli utenti"}finally{o.value=!1,console.log("🏁 LoadUsers completed. Loading state:",o.value)}};return fe(()=>{console.log("🚀 RoleAssignmentForm mounted - starting to load users"),E(),b()}),(s,t)=>(a(),d("div",ri,[e("div",di,[t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-4 flex items-center"},[e("i",{class:"pi pi-users text-primary mr-2"}),I(" Ruoli Obbligatori ")],-1)),e("div",ui,[e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," RUP - Responsabile Unico del Procedimento * ",-1)),W(e("select",{"onUpdate:modelValue":t[0]||(t[0]=f=>i.value.rup_user_id=f),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",onChange:E},[t[9]||(t[9]=e("option",{value:""},"Seleziona RUP",-1)),(a(!0),d(J,null,G(P.value.rup,f=>(a(),d("option",{key:f.id,value:f.id},n(f.name)+" - "+n(f.email),9,ci))),128))],544),[[re,i.value.rup_user_id]]),t[11]||(t[11]=e("p",{class:"text-xs text-gray-500 mt-1"}," Il RUP coordina e gestisce l'intero procedimento espropriativo ",-1)),m.value.rup_user_id?(a(),d("p",pi,n(m.value.rup_user_id),1)):y("",!0)]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," RPE - Responsabile del Procedimento di Esproprio * ",-1)),W(e("select",{"onUpdate:modelValue":t[1]||(t[1]=f=>i.value.rpe_user_id=f),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",onChange:E},[t[12]||(t[12]=e("option",{value:""},"Seleziona RPE",-1)),(a(!0),d(J,null,G(P.value.rpe,f=>(a(),d("option",{key:f.id,value:f.id},n(f.name)+" - "+n(f.email),9,gi))),128))],544),[[re,i.value.rpe_user_id]]),t[14]||(t[14]=e("p",{class:"text-xs text-gray-500 mt-1"}," Il RPE gestisce gli aspetti tecnici e amministrativi dell'esproprio ",-1)),m.value.rpe_user_id?(a(),d("p",vi,n(m.value.rpe_user_id),1)):y("",!0)]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dirigente Responsabile * ",-1)),W(e("select",{"onUpdate:modelValue":t[2]||(t[2]=f=>i.value.dirigente_user_id=f),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",onChange:E},[t[15]||(t[15]=e("option",{value:""},"Seleziona Dirigente",-1)),(a(!0),d(J,null,G(P.value.dirigente,f=>(a(),d("option",{key:f.id,value:f.id},n(f.name)+" - "+n(f.email),9,mi))),128))],544),[[re,i.value.dirigente_user_id]]),t[17]||(t[17]=e("p",{class:"text-xs text-gray-500 mt-1"}," Il Dirigente supervisiona e approva le decisioni strategiche ",-1)),m.value.dirigente_user_id?(a(),d("p",fi,n(m.value.dirigente_user_id),1)):y("",!0)])])]),e("div",bi,[e("div",yi,[t[20]||(t[20]=e("h3",{class:"text-lg font-semibold text-gray-800 flex items-center"},[e("i",{class:"pi pi-cog text-primary mr-2"}),I(" Team Tecnico ")],-1)),e("button",{type:"button",onClick:t[3]||(t[3]=f=>x.value=!0),class:"btn btn-primary btn-sm"},t[19]||(t[19]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),I(" Aggiungi Tecnici ")]))]),t[24]||(t[24]=e("p",{class:"text-sm text-gray-600 mb-4"}," Seleziona i tecnici specializzati per valutazioni, perizie e sopralluoghi ",-1)),i.value.technical_team.length>0?(a(),d("div",_i,[(a(!0),d(J,null,G(L.value,(f,S)=>(a(),d("div",{key:f.id,class:"users-table-row"},[e("div",hi,[t[21]||(t[21]=e("div",{class:"user-avatar-sm"},[e("i",{class:"pi pi-user avatar-icon"})],-1)),e("div",xi,[e("div",$i,n(f.name),1),e("div",wi,n(f.email),1)])]),e("div",ki,n(f.role),1),e("button",{type:"button",onClick:A=>q(S),class:"action-btn danger",title:"Rimuovi dal team"},t[22]||(t[22]=[e("i",{class:"pi pi-trash action-icon"},null,-1)]),8,Si)]))),128))])):(a(),d("div",Ci,t[23]||(t[23]=[e("i",{class:"pi pi-users text-2xl mb-2 block"},null,-1),I(" Nessun tecnico selezionato ")])))]),e("div",Di,[e("div",ji,[t[26]||(t[26]=e("h3",{class:"text-lg font-semibold text-gray-800 flex items-center"},[e("i",{class:"pi pi-balance-scale text-primary mr-2"}),I(" Team Legale ")],-1)),e("button",{type:"button",onClick:t[4]||(t[4]=f=>$.value=!0),class:"btn btn-primary btn-sm"},t[25]||(t[25]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),I(" Aggiungi Legali ")]))]),t[30]||(t[30]=e("p",{class:"text-sm text-gray-600 mb-4"}," Seleziona gli esperti legali per consulenze normative e contenzioso ",-1)),i.value.legal_team.length>0?(a(),d("div",Ii,[(a(!0),d(J,null,G(Q.value,(f,S)=>(a(),d("div",{key:f.id,class:"users-table-row"},[e("div",Pi,[t[27]||(t[27]=e("div",{class:"user-avatar-sm"},[e("i",{class:"pi pi-user avatar-icon"})],-1)),e("div",Ai,[e("div",Vi,n(f.name),1),e("div",zi,n(f.email),1)])]),e("div",Ti,n(f.role),1),e("button",{type:"button",onClick:A=>O(S),class:"action-btn danger",title:"Rimuovi dal team"},t[28]||(t[28]=[e("i",{class:"pi pi-trash action-icon"},null,-1)]),8,Ei)]))),128))])):(a(),d("div",Fi,t[29]||(t[29]=[e("i",{class:"pi pi-balance-scale text-2xl mb-2 block"},null,-1),I(" Nessun legale selezionato ")])))]),e("div",Ui,[e("div",Ri,[t[32]||(t[32]=e("h3",{class:"text-lg font-semibold text-gray-800 flex items-center"},[e("i",{class:"pi pi-clipboard text-primary mr-2"}),I(" Team Amministrativo ")],-1)),e("button",{type:"button",onClick:t[5]||(t[5]=f=>p.value=!0),class:"btn btn-primary btn-sm"},t[31]||(t[31]=[e("i",{class:"pi pi-plus btn-icon"},null,-1),I(" Aggiungi Amministrativi ")]))]),t[36]||(t[36]=e("p",{class:"text-sm text-gray-600 mb-4"}," Seleziona gli operatori per gestione documentale e comunicazioni ",-1)),i.value.administrative_team.length>0?(a(),d("div",Mi,[(a(!0),d(J,null,G(K.value,(f,S)=>(a(),d("div",{key:f.id,class:"users-table-row"},[e("div",Bi,[t[33]||(t[33]=e("div",{class:"user-avatar-sm"},[e("i",{class:"pi pi-user avatar-icon"})],-1)),e("div",Li,[e("div",Ni,n(f.name),1),e("div",qi,n(f.email),1)])]),e("div",Oi,n(f.role),1),e("button",{type:"button",onClick:A=>te(S),class:"action-btn danger",title:"Rimuovi dal team"},t[34]||(t[34]=[e("i",{class:"pi pi-trash action-icon"},null,-1)]),8,Hi)]))),128))])):(a(),d("div",Ji,t[35]||(t[35]=[e("i",{class:"pi pi-clipboard text-2xl mb-2 block"},null,-1),I(" Nessun amministrativo selezionato ")])))]),x.value?(a(),ee(be,{key:0,title:"Seleziona Team Tecnico","available-users":P.value.technical,"selected-users":i.value.technical_team,onClose:t[6]||(t[6]=f=>x.value=!1),onSave:X},null,8,["available-users","selected-users"])):y("",!0),$.value?(a(),ee(be,{key:1,title:"Seleziona Team Legale","available-users":P.value.legal,"selected-users":i.value.legal_team,onClose:t[7]||(t[7]=f=>$.value=!1),onSave:g},null,8,["available-users","selected-users"])):y("",!0),p.value?(a(),ee(be,{key:2,title:"Seleziona Team Amministrativo","available-users":P.value.administrative,"selected-users":i.value.administrative_team,onClose:t[8]||(t[8]=f=>p.value=!1),onSave:c},null,8,["available-users","selected-users"])):y("",!0),o.value?(a(),d("div",Gi,t[37]||(t[37]=[e("div",{class:"inline-flex items-center gap-2 text-gray-600"},[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),I(" Caricamento utenti... ")],-1)]))):y("",!0),v.value?(a(),d("div",Wi,[e("div",Qi,[t[38]||(t[38]=e("i",{class:"pi pi-exclamation-triangle"},null,-1)),I(" "+n(v.value),1)])])):y("",!0)]))}}),Xi=ae(Ki,[["__scopeId","data-v-f9a785ef"]]),Yi={class:"property-import-form"},Zi={class:"import-source-section"},ea={class:"import-options"},ta={key:0,class:"import-content"},sa={class:"project-info-card"},ia={class:"info-grid"},aa={class:"info-item"},oa={class:"info-value"},la={class:"info-item"},na={class:"info-value"},ra={class:"info-item"},da={class:"info-value"},ua={class:"sister-search-form"},ca={class:"form-grid"},pa={class:"search-actions"},ga={key:0,class:"sister-results"},va={class:"results-header"},ma={class:"results-actions"},fa={class:"results-table-container"},ba={class:"sister-table"},ya=["checked"],_a=["checked","onChange"],ha={class:"cadastral-ref"},xa={class:"cadastral-ref"},$a={class:"cadastral-ref"},wa={class:"category-badge"},ka={key:1,class:"import-content"},Sa={class:"manual-import"},Ca={class:"manual-actions"},Da={key:2,class:"import-content"},ja={key:3,class:"properties-summary"},Ia={class:"summary-header"},Pa={class:"summary-stats"},Aa={class:"stat-item"},Va={class:"stat-value"},za={class:"stat-item"},Ta={class:"stat-value"},Ea={class:"stat-item"},Fa={class:"stat-value"},Ua={class:"properties-grid"},Ra={class:"property-cadastral"},Ma={key:0},Ba={class:"property-owner"},La={class:"property-area"},Na={key:0,class:"properties-more"},qa=ie({__name:"PropertyImportForm",props:{modelValue:{},projectData:{},projectId:{}},emits:["update:modelValue","validation-change"],setup(F,{emit:u}){const r=F,k=u,i=j(!1),o=j(!1),v=j([]),x=j([]),$=j(!1),p=j({comune:"",provincia:"",foglio:"",mappale:""}),m=U(()=>v.value.length>0&&x.value.length===v.value.length),P=U(()=>r.modelValue.properties.length>0&&r.modelValue.validation_status==="validated");oe(()=>r.projectData.municipality,g=>{p.value.comune=g}),oe(P,g=>{k("validation-change",g)});const L=g=>{const c={...r.modelValue,import_source:g};k("update:modelValue",c)},Q=async()=>{if(p.value.provincia){i.value=!0;try{const g=localStorage.getItem("token"),c=new URLSearchParams({comune:p.value.comune,provincia:p.value.provincia.toUpperCase()});p.value.foglio&&c.append("foglio",p.value.foglio),p.value.mappale&&c.append("mappale",p.value.mappale);const b=await fetch(`/api/sister/search?${c}`,{headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!b.ok)throw new Error(`HTTP error! status: ${b.status}`);const s=await b.json();v.value=Array.isArray(s)?s:[],x.value=[]}catch(g){console.error("SISTER search error:",g),v.value=[]}finally{i.value=!1}}},K=g=>x.value.some(c=>c.foglio===g.foglio&&c.mappale===g.mappale&&c.subalterno===g.subalterno),V=g=>{const c=x.value.findIndex(b=>b.foglio===g.foglio&&b.mappale===g.mappale&&b.subalterno===g.subalterno);c>=0?x.value.splice(c,1):x.value.push(g)},C=()=>{x.value=[...v.value]},E=()=>{m.value?x.value=[]:C()},q=async()=>{if(!(x.value.length===0||!r.projectId)){o.value=!0;try{const g=localStorage.getItem("token"),c=await fetch("/api/properties/import-from-sister",{method:"POST",headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"},body:JSON.stringify({comune:p.value.comune,provincia:p.value.provincia.toUpperCase(),foglio:p.value.foglio?parseInt(p.value.foglio):null,mappale:p.value.mappale?parseInt(p.value.mappale):null,project_id:r.projectId})});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);(await c.json()).imported_count>0&&await O()}catch(g){console.error("Import error:",g)}finally{o.value=!1}}},O=async()=>{if(r.projectId)try{const g=localStorage.getItem("token"),c=await fetch(`/api/properties/?project_id=${r.projectId}`,{headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const b=await c.json(),s=b.reduce((S,A)=>S+(A.surface_area||0),0),t=[...new Set(b.map(S=>S.comune))],f={...r.modelValue,properties:b,total_area:s,affected_municipalities:t,validation_status:b.length>0?"validated":"pending"};k("update:modelValue",f)}catch(g){console.error("Load properties error:",g)}},te=g=>{$.value=!1,O()},X=g=>g?g.toLocaleString("it-IT")+" m²":"0 m²";return fe(()=>{r.projectData.municipality&&(p.value.comune=r.projectData.municipality),r.projectId&&O()}),(g,c)=>(a(),d("div",Yi,[e("div",Zi,[c[13]||(c[13]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-upload"}),I(" Metodo di Importazione ")],-1)),e("div",ea,[e("div",{class:D(["import-option",{active:g.modelValue.import_source==="sister"}]),onClick:c[0]||(c[0]=b=>L("sister"))},c[10]||(c[10]=[de('<div class="option-icon" data-v-c38ef222><i class="pi pi-database" data-v-c38ef222></i></div><div class="option-content" data-v-c38ef222><h4 data-v-c38ef222>Sistema SISTER</h4><p data-v-c38ef222>Importa automaticamente da banca dati catastale</p></div><div class="option-check" data-v-c38ef222><i class="pi pi-check" data-v-c38ef222></i></div>',3)]),2),e("div",{class:D(["import-option",{active:g.modelValue.import_source==="manual"}]),onClick:c[1]||(c[1]=b=>L("manual"))},c[11]||(c[11]=[de('<div class="option-icon" data-v-c38ef222><i class="pi pi-pencil" data-v-c38ef222></i></div><div class="option-content" data-v-c38ef222><h4 data-v-c38ef222>Inserimento Manuale</h4><p data-v-c38ef222>Aggiungi particelle una per volta</p></div><div class="option-check" data-v-c38ef222><i class="pi pi-check" data-v-c38ef222></i></div>',3)]),2),e("div",{class:D(["import-option",{active:g.modelValue.import_source==="file"}]),onClick:c[2]||(c[2]=b=>L("file"))},c[12]||(c[12]=[de('<div class="option-icon" data-v-c38ef222><i class="pi pi-file" data-v-c38ef222></i></div><div class="option-content" data-v-c38ef222><h4 data-v-c38ef222>Import da File</h4><p data-v-c38ef222>Carica un file CSV o Excel</p></div><div class="option-check" data-v-c38ef222><i class="pi pi-check" data-v-c38ef222></i></div>',3)]),2)])]),g.modelValue.import_source==="sister"?(a(),d("div",ta,[c[25]||(c[25]=e("div",{class:"import-header"},[e("h3",{class:"section-title"},[e("i",{class:"pi pi-database"}),I(" Ricerca SISTER ")]),e("p",{class:"section-description"}," Cerca e importa particelle dal sistema catastale SISTER utilizzando i dati del progetto ")],-1)),e("div",sa,[e("div",ia,[e("div",aa,[c[14]||(c[14]=e("span",{class:"info-label"},"Comune:",-1)),e("span",oa,n(g.projectData.municipality),1)]),e("div",la,[c[15]||(c[15]=e("span",{class:"info-label"},"Regione:",-1)),e("span",na,n(g.projectData.region),1)]),e("div",ra,[c[16]||(c[16]=e("span",{class:"info-label"},"Progetto:",-1)),e("span",da,n(g.projectData.title),1)])])]),e("div",ua,[e("form",{onSubmit:xe(Q,["prevent"]),class:"search-form"},[e("div",ca,[B(ge,{modelValue:p.value.comune,"onUpdate:modelValue":c[3]||(c[3]=b=>p.value.comune=b),label:"Comune",value:g.projectData.municipality,disabled:"",icon:"pi pi-map-marker"},null,8,["modelValue","value"]),B(ge,{modelValue:p.value.provincia,"onUpdate:modelValue":c[4]||(c[4]=b=>p.value.provincia=b),label:"Provincia",placeholder:"es. MI",required:"",maxlength:"2",disabled:i.value},null,8,["modelValue","disabled"]),B(ge,{modelValue:p.value.foglio,"onUpdate:modelValue":c[5]||(c[5]=b=>p.value.foglio=b),label:"Foglio (opzionale)",placeholder:"es. 42",type:"number",disabled:i.value},null,8,["modelValue","disabled"]),B(ge,{modelValue:p.value.mappale,"onUpdate:modelValue":c[6]||(c[6]=b=>p.value.mappale=b),label:"Mappale (opzionale)",placeholder:"es. 156",type:"number",disabled:i.value},null,8,["modelValue","disabled"])]),e("div",pa,[B(ve,{type:"submit",variant:"primary",loading:i.value,disabled:!p.value.provincia,icon:"pi pi-search"},{default:se(()=>c[17]||(c[17]=[I(" Cerca in SISTER ")])),_:1,__:[17]},8,["loading","disabled"])])],32)]),v.value.length>0?(a(),d("div",ga,[e("div",va,[e("h4",null,"Risultati Trovati: "+n(v.value.length),1),e("div",ma,[B(ve,{variant:"secondary",size:"sm",onClick:C,icon:"pi pi-check"},{default:se(()=>c[18]||(c[18]=[I(" Seleziona Tutto ")])),_:1,__:[18]}),B(ve,{variant:"primary",onClick:q,disabled:x.value.length===0,loading:o.value,icon:"pi pi-upload"},{default:se(()=>[I(" Importa "+n(x.value.length)+" Particelle ",1)]),_:1},8,["disabled","loading"])])]),e("div",fa,[e("table",ba,[e("thead",null,[e("tr",null,[e("th",null,[e("input",{type:"checkbox",checked:m.value,onChange:E},null,40,ya)]),c[19]||(c[19]=e("th",null,"Foglio",-1)),c[20]||(c[20]=e("th",null,"Mappale",-1)),c[21]||(c[21]=e("th",null,"Sub.",-1)),c[22]||(c[22]=e("th",null,"Superficie",-1)),c[23]||(c[23]=e("th",null,"Categoria",-1)),c[24]||(c[24]=e("th",null,"Proprietario",-1))])]),e("tbody",null,[(a(!0),d(J,null,G(v.value,b=>(a(),d("tr",{key:`${b.foglio}-${b.mappale}-${b.subalterno||0}`,class:D({selected:K(b)})},[e("td",null,[e("input",{type:"checkbox",checked:K(b),onChange:s=>V(b)},null,40,_a)]),e("td",null,[e("span",ha,n(b.foglio),1)]),e("td",null,[e("span",xa,n(b.mappale),1)]),e("td",null,[e("span",$a,n(b.subalterno||"-"),1)]),e("td",null,n(X(b.superficie)),1),e("td",null,[e("span",wa,n(b.categoria),1)]),e("td",null,n(b.intestato_nome),1)],2))),128))])])])])):y("",!0)])):y("",!0),g.modelValue.import_source==="manual"?(a(),d("div",ka,[e("div",Sa,[c[27]||(c[27]=e("div",{class:"import-header"},[e("h3",{class:"section-title"},[e("i",{class:"pi pi-pencil"}),I(" Inserimento Manuale ")]),e("p",{class:"section-description"}," Aggiungi particelle catastali manualmente compilando il form ")],-1)),e("div",Ca,[B(ve,{variant:"primary",onClick:c[7]||(c[7]=b=>$.value=!0),icon:"pi pi-plus"},{default:se(()=>c[26]||(c[26]=[I(" Aggiungi Particella ")])),_:1,__:[26]})])])])):y("",!0),g.modelValue.import_source==="file"?(a(),d("div",Da,c[28]||(c[28]=[de('<div class="file-import" data-v-c38ef222><div class="import-header" data-v-c38ef222><h3 class="section-title" data-v-c38ef222><i class="pi pi-file" data-v-c38ef222></i> Import da File </h3><p class="section-description" data-v-c38ef222> Carica un file CSV o Excel con i dati delle particelle </p></div><div class="file-upload-placeholder" data-v-c38ef222><i class="pi pi-cloud-upload" data-v-c38ef222></i><p data-v-c38ef222>Funzionalità in sviluppo</p></div></div>',1)]))):y("",!0),g.modelValue.properties.length>0?(a(),d("div",ja,[e("div",Ia,[c[32]||(c[32]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-list"}),I(" Particelle Importate ")],-1)),e("div",Pa,[e("div",Aa,[e("span",Va,n(g.modelValue.properties.length),1),c[29]||(c[29]=e("span",{class:"stat-label"},"Particelle",-1))]),e("div",za,[e("span",Ta,n(X(g.modelValue.total_area)),1),c[30]||(c[30]=e("span",{class:"stat-label"},"Superficie Totale",-1))]),e("div",Ea,[e("span",Fa,n(g.modelValue.affected_municipalities.length),1),c[31]||(c[31]=e("span",{class:"stat-label"},"Comuni",-1))])])]),e("div",Ua,[(a(!0),d(J,null,G(g.modelValue.properties.slice(0,6),b=>(a(),d("div",{key:b.id,class:"property-card"},[e("div",Ra,[I(" Foglio "+n(b.foglio)+", Mappale "+n(b.particella)+" ",1),b.subalterno?(a(),d("span",Ma,", Sub. "+n(b.subalterno),1)):y("",!0)]),e("div",Ba,n(b.owner_name),1),e("div",La,n(X(b.surface_area)),1)]))),128))]),g.modelValue.properties.length>6?(a(),d("div",Na,[e("p",null,"... e altre "+n(g.modelValue.properties.length-6)+" particelle",1)])):y("",!0)])):y("",!0),$.value?(a(),ee(Ue,{key:4,onClose:c[9]||(c[9]=b=>$.value=!1),title:"Aggiungi Particella",size:"large"},{default:se(()=>[B(Re,{"project-id":g.projectId,onClose:c[8]||(c[8]=b=>$.value=!1),onSuccess:te},null,8,["project-id"])]),_:1})):y("",!0)]))}}),Oa=ae(qa,[["__scopeId","data-v-c38ef222"]]),Ha={class:"project-review-form"},Ja={class:"card"},Ga={class:"grid-2"},Wa={class:"info-section"},Qa={class:"field-value"},Ka={class:"field-value"},Xa={class:"field-value"},Ya={class:"field-value"},Za={class:"info-section"},eo={class:"field-value"},to={key:0},so={class:"field-value"},io={class:"field-value"},ao={key:0},oo={key:0,class:"notes-section"},lo={class:"field-value"},no={class:"bg-white border border-gray-200 rounded-xl p-6"},ro={class:"space-y-6"},uo={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},co={class:"border border-gray-200 rounded-lg p-4"},po={class:"flex items-center space-x-2"},go={class:"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-xs font-medium"},vo={class:"text-sm font-medium text-gray-900"},mo={class:"text-xs text-gray-500"},fo={class:"border border-gray-200 rounded-lg p-4"},bo={class:"flex items-center space-x-2"},yo={class:"w-8 h-8 bg-success text-white rounded-full flex items-center justify-center text-xs font-medium"},_o={class:"text-sm font-medium text-gray-900"},ho={class:"text-xs text-gray-500"},xo={class:"border border-gray-200 rounded-lg p-4"},$o={class:"flex items-center space-x-2"},wo={class:"w-8 h-8 bg-warning text-white rounded-full flex items-center justify-center text-xs font-medium"},ko={class:"text-sm font-medium text-gray-900"},So={class:"text-xs text-gray-500"},Co={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Do={class:"space-y-2"},jo={key:0,class:"text-sm text-gray-500"},Io={key:1},Po={class:"text-sm text-gray-900"},Ao={class:"flex -space-x-2"},Vo={key:0,class:"w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},zo={class:"space-y-2"},To={key:0,class:"text-sm text-gray-500"},Eo={key:1},Fo={class:"text-sm text-gray-900"},Uo={class:"flex -space-x-2"},Ro={key:0,class:"w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},Mo={class:"space-y-2"},Bo={key:0,class:"text-sm text-gray-500"},Lo={key:1},No={class:"text-sm text-gray-900"},qo={class:"flex -space-x-2"},Oo={key:0,class:"w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},Ho={class:"bg-white border border-gray-200 rounded-xl p-6"},Jo={class:"space-y-6"},Go={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Wo={class:"bg-primary-50 border border-primary-200 rounded-lg p-4 text-center"},Qo={class:"text-2xl font-bold text-primary"},Ko={class:"bg-success-50 border border-success-200 rounded-lg p-4 text-center"},Xo={class:"text-2xl font-bold text-success"},Yo={class:"bg-info-50 border border-info-200 rounded-lg p-4 text-center"},Zo={class:"text-2xl font-bold text-info"},el={class:"bg-warning-50 border border-warning-200 rounded-lg p-4 text-center"},tl={class:"flex items-center justify-center space-x-2"},sl={class:"flex items-center space-x-2"},il={class:"text-sm text-gray-900"},al={key:0},ol={class:"flex flex-wrap gap-2"},ll={class:"bg-white border border-gray-200 rounded-xl p-6"},nl={class:"space-y-4"},rl={class:"flex items-center space-x-3"},dl={class:"flex items-center space-x-3"},ul={class:"flex items-center space-x-3"},cl={class:"flex items-center space-x-3"},pl={class:"mt-6 pt-6 border-t border-gray-200"},gl={class:"flex items-center justify-between"},vl={class:"flex items-center space-x-3"},ml={key:0,class:"text-sm text-gray-500"},fl={class:"bg-primary-50 border border-primary-200 rounded-xl p-6"},bl={class:"space-y-4"},yl={class:"flex items-center space-x-2"},_l=ie({__name:"ProjectReviewForm",props:{projectData:{},roleData:{},propertyData:{}},emits:["validation-change"],setup(F,{emit:u}){const r=F,k=u,i=j(!1),o=j([]),v=U(()=>r.projectData.title&&r.projectData.description&&r.projectData.legal_reference&&r.projectData.category&&r.projectData.municipality&&r.projectData.region&&r.projectData.start_date),x=U(()=>r.roleData.rup_user_id&&r.roleData.rpe_user_id&&r.roleData.dirigente_user_id),$=U(()=>r.propertyData.properties.length>0&&r.propertyData.validation_status==="validated"),p=U(()=>!0),m=U(()=>v.value&&x.value&&$.value&&p.value&&i.value),P=U(()=>{const s=[];return v.value||s.push("Dati progetto incompleti"),x.value||s.push("Ruoli non assegnati"),$.value||s.push("Dati catastali non validati"),p.value||s.push("Sistemi esterni non configurati"),s}),L=j(!1),Q=j(""),K=async()=>{try{L.value=!0,Q.value="";const s=localStorage.getItem("token"),t=await fetch("/api/users/",{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const f=await t.json(),S=f.users||f||[];o.value=S.map(A=>({id:A.id,name:A.full_name||A.name||"Nome non specificato",email:A.email}))}catch(s){console.error("Error loading users:",s),Q.value="Errore nel caricamento degli utenti"}finally{L.value=!1}},V=s=>o.value.find(t=>t.id===s),C=s=>o.value.find(t=>t.id===s),E=s=>s?s.split(" ").map(t=>t.charAt(0)).join("").toUpperCase():"?",q=s=>s?new Date(s).toLocaleDateString("it-IT"):"",O=s=>({dpr_327_2001:"D.P.R. 327/2001",legge_865_1971:"Legge 865/1971",dlgs_302_2002:"D.Lgs. 302/2002",altro:"Altro"})[s]||s,te=s=>({infrastructure:"Infrastrutture",utilities:"Servizi Pubblici",housing:"Edilizia Residenziale",industrial:"Insediamenti Produttivi",environmental:"Opere Ambientali",other:"Altro"})[s]||s,X=s=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[s]||s,g=s=>({low:"bg-gray-100 text-gray-800",medium:"bg-warning-100 text-warning-800",high:"bg-danger-100 text-danger-800",urgent:"bg-danger text-white"})[s]||"bg-gray-100 text-gray-800",c=s=>({pending:"In attesa",validated:"Validato",error:"Errore"})[s]||"Sconosciuto",b=s=>({sister:"Sistema SISTER",manual:"Inserimento Manuale",file:"Import da File"})[s]||s;return oe(m,s=>{k("validation-change",s)}),fe(()=>{k("validation-change",m.value),K()}),(s,t)=>{var f,S,A,z,M,Y,pe,_,h;return a(),d("div",Ha,[e("div",Ja,[t[10]||(t[10]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-eye"}),I(" Riepilogo Progetto ")],-1)),e("div",Ga,[e("div",Wa,[e("div",null,[t[1]||(t[1]=e("p",{class:"field-label"},"Titolo Progetto",-1)),e("p",Qa,n(s.projectData.title),1)]),e("div",null,[t[2]||(t[2]=e("p",{class:"field-label"},"Descrizione",-1)),e("p",Ka,n(s.projectData.description),1)]),e("div",null,[t[3]||(t[3]=e("p",{class:"field-label"},"Riferimento Normativo",-1)),e("p",Xa,n(O(s.projectData.legal_reference)),1)]),e("div",null,[t[4]||(t[4]=e("p",{class:"field-label"},"Categoria",-1)),e("p",Ya,n(te(s.projectData.category)),1)])]),e("div",Za,[e("div",null,[t[5]||(t[5]=e("p",{class:"field-label"},"Localizzazione",-1)),e("p",eo,n(s.projectData.municipality)+", "+n(s.projectData.region),1)]),e("div",null,[t[6]||(t[6]=e("p",{class:"field-label"},"Priorità",-1)),e("span",{class:D(["badge",g(s.projectData.priority)])},n(X(s.projectData.priority)),3)]),s.projectData.budget?(a(),d("div",to,[t[7]||(t[7]=e("p",{class:"field-label"},"Budget Stimato",-1)),e("p",so,"€ "+n(s.projectData.budget.toLocaleString()),1)])):y("",!0),e("div",null,[t[8]||(t[8]=e("p",{class:"field-label"},"Tempistiche",-1)),e("p",io,[I(n(q(s.projectData.start_date))+" ",1),s.projectData.expected_end_date?(a(),d("span",ao," - "+n(q(s.projectData.expected_end_date)),1)):y("",!0)])])])]),s.projectData.notes?(a(),d("div",oo,[t[9]||(t[9]=e("p",{class:"field-label mb-2"},"Note Aggiuntive",-1)),e("p",lo,n(s.projectData.notes),1)])):y("",!0)]),e("div",no,[t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-6 flex items-center"},[e("i",{class:"pi pi-users text-primary mr-2"}),I(" Assegnazione Ruoli e Team ")],-1)),e("div",ro,[e("div",null,[t[14]||(t[14]=e("h4",{class:"text-sm font-semibold text-gray-800 mb-4"},"Ruoli Chiave",-1)),e("div",uo,[e("div",co,[t[11]||(t[11]=e("p",{class:"text-xs font-medium text-gray-700 mb-2"},"RUP",-1)),e("div",po,[e("div",go,n(E((f=V(s.roleData.rup_user_id))==null?void 0:f.name)),1),e("div",null,[e("p",vo,n((S=V(s.roleData.rup_user_id))==null?void 0:S.name),1),e("p",mo,n((A=V(s.roleData.rup_user_id))==null?void 0:A.email),1)])])]),e("div",fo,[t[12]||(t[12]=e("p",{class:"text-xs font-medium text-gray-700 mb-2"},"RPE",-1)),e("div",bo,[e("div",yo,n(E((z=V(s.roleData.rpe_user_id))==null?void 0:z.name)),1),e("div",null,[e("p",_o,n((M=V(s.roleData.rpe_user_id))==null?void 0:M.name),1),e("p",ho,n((Y=V(s.roleData.rpe_user_id))==null?void 0:Y.email),1)])])]),e("div",xo,[t[13]||(t[13]=e("p",{class:"text-xs font-medium text-gray-700 mb-2"},"Dirigente",-1)),e("div",$o,[e("div",wo,n(E((pe=V(s.roleData.dirigente_user_id))==null?void 0:pe.name)),1),e("div",null,[e("p",ko,n((_=V(s.roleData.dirigente_user_id))==null?void 0:_.name),1),e("p",So,n((h=V(s.roleData.dirigente_user_id))==null?void 0:h.email),1)])])])])]),e("div",Co,[e("div",null,[t[15]||(t[15]=e("h4",{class:"text-sm font-semibold text-gray-800 mb-3"},"Team Tecnico",-1)),e("div",Do,[s.roleData.technical_team.length===0?(a(),d("div",jo," Nessun membro assegnato ")):(a(),d("div",Io,[e("p",Po,n(s.roleData.technical_team.length)+" membri assegnati",1),e("div",Ao,[(a(!0),d(J,null,G(s.roleData.technical_team.slice(0,3),R=>{var H;return a(),d("div",{key:R,class:"w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},n(E((H=C(R))==null?void 0:H.name)),1)}),128)),s.roleData.technical_team.length>3?(a(),d("div",Vo," +"+n(s.roleData.technical_team.length-3),1)):y("",!0)])]))])]),e("div",null,[t[16]||(t[16]=e("h4",{class:"text-sm font-semibold text-gray-800 mb-3"},"Team Legale",-1)),e("div",zo,[s.roleData.legal_team.length===0?(a(),d("div",To," Nessun membro assegnato ")):(a(),d("div",Eo,[e("p",Fo,n(s.roleData.legal_team.length)+" membri assegnati",1),e("div",Uo,[(a(!0),d(J,null,G(s.roleData.legal_team.slice(0,3),R=>{var H;return a(),d("div",{key:R,class:"w-6 h-6 bg-info text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},n(E((H=C(R))==null?void 0:H.name)),1)}),128)),s.roleData.legal_team.length>3?(a(),d("div",Ro," +"+n(s.roleData.legal_team.length-3),1)):y("",!0)])]))])]),e("div",null,[t[17]||(t[17]=e("h4",{class:"text-sm font-semibold text-gray-800 mb-3"},"Team Amministrativo",-1)),e("div",Mo,[s.roleData.administrative_team.length===0?(a(),d("div",Bo," Nessun membro assegnato ")):(a(),d("div",Lo,[e("p",No,n(s.roleData.administrative_team.length)+" membri assegnati",1),e("div",qo,[(a(!0),d(J,null,G(s.roleData.administrative_team.slice(0,3),R=>{var H;return a(),d("div",{key:R,class:"w-6 h-6 bg-warning text-white rounded-full flex items-center justify-center text-xs font-medium border-2 border-white"},n(E((H=C(R))==null?void 0:H.name)),1)}),128)),s.roleData.administrative_team.length>3?(a(),d("div",Oo," +"+n(s.roleData.administrative_team.length-3),1)):y("",!0)])]))])])])])]),e("div",Ho,[t[25]||(t[25]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-6 flex items-center"},[e("i",{class:"pi pi-map text-primary mr-2"}),I(" Dati Catastali ")],-1)),e("div",Jo,[e("div",Go,[e("div",Wo,[e("p",Qo,n(s.propertyData.properties.length),1),t[19]||(t[19]=e("p",{class:"text-sm text-primary-700"},"Particelle",-1))]),e("div",Ko,[e("p",Xo,n(s.propertyData.total_area.toLocaleString())+"m²",1),t[20]||(t[20]=e("p",{class:"text-sm text-success-700"},"Superficie",-1))]),e("div",Yo,[e("p",Zo,n(s.propertyData.affected_municipalities.length),1),t[21]||(t[21]=e("p",{class:"text-sm text-info-700"},"Comuni",-1))]),e("div",el,[e("div",tl,[e("div",{class:D(["w-3 h-3 rounded-full",{"bg-warning":s.propertyData.validation_status==="pending","bg-success":s.propertyData.validation_status==="validated","bg-danger":s.propertyData.validation_status==="error"}])},null,2),e("p",{class:D(["text-sm font-medium",{"text-warning":s.propertyData.validation_status==="pending","text-success":s.propertyData.validation_status==="validated","text-danger":s.propertyData.validation_status==="error"}])},n(c(s.propertyData.validation_status)),3)]),t[22]||(t[22]=e("p",{class:"text-sm text-gray-600"},"Validazione",-1))])]),e("div",null,[t[23]||(t[23]=e("p",{class:"text-sm font-medium text-gray-700 mb-2"},"Fonte Importazione",-1)),e("div",sl,[e("i",{class:D(["pi",{"pi-database text-primary":s.propertyData.import_source==="sister","pi-pencil text-warning":s.propertyData.import_source==="manual","pi-file text-info":s.propertyData.import_source==="file"}])},null,2),e("span",il,n(b(s.propertyData.import_source)),1)])]),s.propertyData.affected_municipalities.length>0?(a(),d("div",al,[t[24]||(t[24]=e("p",{class:"text-sm font-medium text-gray-700 mb-2"},"Comuni Interessati",-1)),e("div",ol,[(a(!0),d(J,null,G(s.propertyData.affected_municipalities,R=>(a(),d("span",{key:R,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"},n(R),1))),128))])])):y("",!0)])]),e("div",ll,[t[30]||(t[30]=e("h3",{class:"text-lg font-semibold text-gray-800 mb-6 flex items-center"},[e("i",{class:"pi pi-check-circle text-primary mr-2"}),I(" Checklist di Validazione ")],-1)),e("div",nl,[e("div",rl,[e("div",{class:D(["w-5 h-5 rounded-full flex items-center justify-center",v.value?"bg-success text-white":"bg-gray-200 text-gray-400"])},t[26]||(t[26]=[e("i",{class:"pi pi-check text-xs"},null,-1)]),2),e("span",{class:D(["text-sm",v.value?"text-gray-900":"text-gray-500"])}," Dati del progetto completi e validi ",2)]),e("div",dl,[e("div",{class:D(["w-5 h-5 rounded-full flex items-center justify-center",x.value?"bg-success text-white":"bg-gray-200 text-gray-400"])},t[27]||(t[27]=[e("i",{class:"pi pi-check text-xs"},null,-1)]),2),e("span",{class:D(["text-sm",x.value?"text-gray-900":"text-gray-500"])}," Ruoli obbligatori assegnati ",2)]),e("div",ul,[e("div",{class:D(["w-5 h-5 rounded-full flex items-center justify-center",$.value?"bg-success text-white":"bg-gray-200 text-gray-400"])},t[28]||(t[28]=[e("i",{class:"pi pi-check text-xs"},null,-1)]),2),e("span",{class:D(["text-sm",$.value?"text-gray-900":"text-gray-500"])}," Dati catastali importati e validati ",2)]),e("div",cl,[e("div",{class:D(["w-5 h-5 rounded-full flex items-center justify-center",p.value?"bg-success text-white":"bg-gray-200 text-gray-400"])},t[29]||(t[29]=[e("i",{class:"pi pi-check text-xs"},null,-1)]),2),e("span",{class:D(["text-sm",p.value?"text-gray-900":"text-gray-500"])}," Sistemi esterni configurati ",2)])]),e("div",pl,[e("div",gl,[e("div",vl,[e("div",{class:D(["w-6 h-6 rounded-full flex items-center justify-center",m.value?"bg-success text-white":"bg-warning text-white"])},[e("i",{class:D(["pi text-sm",m.value?"pi-check":"pi-exclamation-triangle"])},null,2)],2),e("span",{class:D(["font-medium",m.value?"text-success":"text-warning"])},n(m.value?"Progetto pronto per la creazione":"Completare la configurazione richiesta"),3)]),m.value?y("",!0):(a(),d("div",ml,n(P.value.length)+" elementi da completare ",1))])])]),e("div",fl,[t[33]||(t[33]=e("h3",{class:"text-lg font-semibold text-primary mb-4 flex items-center"},[e("i",{class:"pi pi-info-circle mr-2"}),I(" Conferma Creazione Progetto ")],-1)),e("div",bl,[t[32]||(t[32]=e("p",{class:"text-sm text-primary-800"}," Confermando la creazione, il progetto verrà salvato e inizierà la fase preparatoria del procedimento espropriativo secondo il D.P.R. 327/2001. ",-1)),e("div",yl,[W(e("input",{"onUpdate:modelValue":t[0]||(t[0]=R=>i.value=R),type:"checkbox",id:"confirmation",class:"w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2"},null,512),[[ye,i.value]]),t[31]||(t[31]=e("label",{for:"confirmation",class:"text-sm font-medium text-primary-800 cursor-pointer"}," Confermo di aver verificato tutti i dati e di voler creare il progetto ",-1))])])])])}}}),hl=ae(_l,[["__scopeId","data-v-5a8212be"]]),xl={class:"admin-layout"},$l={class:"admin-content"},wl={class:"project-create-main admin-main-content"},kl={class:"page-header"},Sl={class:"page-header-actions"},Cl={class:"wizard-progress"},Dl={class:"progress-steps"},jl={class:"step-circle"},Il={key:0,class:"pi pi-check"},Pl={key:1,class:"pi pi-times"},Al={key:2},Vl={class:"step-label"},zl={class:"progress-bar"},Tl={class:"wizard-container"},El=ie({__name:"ProjectCreate",setup(F){const u=Ee(),r=j(0),k=j(!1),i=j(null),o=j(!1),v=j(!1),x=j(!1),$=j(!1),p=j({title:"",description:"",legal_reference:"",budget:null,start_date:null,expected_end_date:null,priority:"medium",category:"infrastructure",municipality:"",region:"",notes:""}),m=j({rup_user_id:null,rpe_user_id:null,dirigente_user_id:null,technical_team:[],legal_team:[],administrative_team:[]}),P=j({properties:[],total_area:0,affected_municipalities:[],import_source:"sister",validation_status:"pending"}),L=[{id:"setup",title:"Setup Iniziale",description:"Informazioni base progetto"},{id:"roles",title:"Ruoli e Team",description:"Assegnazione responsabilità"},{id:"properties",title:"Particelle",description:"Import dati catastali"},{id:"review",title:"Conferma",description:"Revisione e attivazione"}],Q=U(()=>(r.value+1)/L.length*100),K=U(()=>o.value),V=U(()=>v.value),C=U(()=>x.value),E=U(()=>$.value),q=S=>S<r.value?"step-completed":S===r.value?"step-active":"step-pending",O=S=>S<r.value?"completed":S===r.value?"active":"pending",te=async()=>{r.value<L.length-1&&(r.value===1&&!i.value&&await t(),r.value++)},X=()=>{r.value>0&&r.value--},g=S=>{o.value=S},c=S=>{v.value=S},b=S=>{x.value=S},s=S=>{$.value=S},t=async()=>{if(!i.value)try{const S={...p.value,...m.value,status:"draft",phase:"setup"},A=localStorage.getItem("token"),z=await fetch("/api/projects/",{method:"POST",headers:{Authorization:`Bearer ${A}`,"Content-Type":"application/json"},body:JSON.stringify(S)});if(!z.ok)throw new Error(`HTTP error! status: ${z.status}`);const M=await z.json();i.value=M.id}catch(S){throw console.error("Error creating draft project:",S),S}},f=async()=>{k.value=!0;try{const S=localStorage.getItem("token");if(i.value){const A={...p.value,...m.value,status:"active",phase:"preparatorie"},z=await fetch(`/api/projects/${i.value}`,{method:"PUT",headers:{Authorization:`Bearer ${S}`,"Content-Type":"application/json"},body:JSON.stringify(A)});if(!z.ok)throw new Error(`HTTP error! status: ${z.status}`);const M=await z.json();u.push(`/admin/projects/${M.id}`)}else{const A={...p.value,...m.value,status:"active",phase:"preparatorie"},z=await fetch("/api/projects/",{method:"POST",headers:{Authorization:`Bearer ${S}`,"Content-Type":"application/json"},body:JSON.stringify(A)});if(!z.ok)throw new Error(`HTTP error! status: ${z.status}`);const M=await z.json();u.push(`/admin/projects/${M.id}?created=true`)}}catch(S){console.error("Error creating project:",S)}finally{k.value=!1}};return(S,A)=>{const z=Te("router-link");return a(),d("div",xl,[B(Fe),e("div",$l,[e("main",wl,[e("div",kl,[A[4]||(A[4]=e("div",{class:"page-header-content"},[e("h1",{class:"page-title"},"Nuovo Progetto Espropriativo"),e("p",{class:"page-subtitle"},"Procedimento secondo D.P.R. 327/2001")],-1)),e("div",Sl,[B(z,{to:"/admin/projects",class:"btn btn-secondary"},{default:se(()=>A[3]||(A[3]=[e("i",{class:"pi pi-times"},null,-1),I(" Annulla ")])),_:1,__:[3]})])]),e("div",Cl,[e("div",Dl,[(a(),d(J,null,G(L,(M,Y)=>e("div",{key:M.id,class:D(["progress-step",q(Y)])},[e("div",jl,[O(Y)==="completed"?(a(),d("i",Il)):O(Y)==="error"?(a(),d("i",Pl)):(a(),d("span",Al,n(Y+1),1))]),e("div",Vl,n(M.title),1)],2)),64))]),e("div",zl,[e("div",{class:"progress-fill",style:he({width:`${Q.value}%`})},null,4)])]),e("div",Tl,[r.value===0?(a(),ee(me,{key:0,"step-number":1,title:"Setup Progetto Iniziale",description:"Definisci le informazioni base del procedimento espropriativo",status:O(0),active:!0,"can-proceed":K.value,"show-actions":!0,"show-previous":!1,"show-next":!0,"show-submit":!1,"next-label":"Avanti","submit-label":"Completa",submitting:!1,onNext:te},{default:se(()=>[B(Ls,{modelValue:p.value,"onUpdate:modelValue":A[0]||(A[0]=M=>p.value=M),onValidationChange:g},null,8,["modelValue"])]),_:1},8,["status","can-proceed"])):y("",!0),r.value===1?(a(),ee(me,{key:1,"step-number":2,title:"Assegnazione Ruoli",description:"Assegna RUP, RPE e team di lavoro al progetto",status:O(1),active:!0,"can-proceed":V.value,"show-actions":!0,"show-previous":!0,"show-next":!0,"show-submit":!1,"next-label":"Avanti","submit-label":"Completa",submitting:!1,onPrevious:X,onNext:te},{default:se(()=>[B(Xi,{modelValue:m.value,"onUpdate:modelValue":A[1]||(A[1]=M=>m.value=M),"project-data":p.value,onValidationChange:c},null,8,["modelValue","project-data"])]),_:1},8,["status","can-proceed"])):y("",!0),r.value===2?(a(),ee(me,{key:2,"step-number":3,title:"Import Particelle SISTER",description:"Importa e valida i dati catastali delle particelle interessate",status:O(2),active:!0,"can-proceed":C.value,"show-actions":!0,"show-previous":!0,"show-next":!0,"show-submit":!1,"next-label":"Avanti","submit-label":"Completa",submitting:!1,onPrevious:X,onNext:te},{default:se(()=>[B(Oa,{modelValue:P.value,"onUpdate:modelValue":A[2]||(A[2]=M=>P.value=M),"project-data":p.value,"project-id":i.value,onValidationChange:b},null,8,["modelValue","project-data","project-id"])]),_:1},8,["status","can-proceed"])):y("",!0),r.value===3?(a(),ee(me,{key:3,"step-number":4,title:"Configurazione e Conferma",description:"Rivedi i dati inseriti e attiva il procedimento",status:O(3),active:!0,"can-proceed":E.value,"show-previous":!0,"show-next":!1,"show-submit":!0,"submit-label":"Crea Progetto",submitting:k.value,onPrevious:X,onSubmit:f},{default:se(()=>[B(hl,{"project-data":p.value,"role-data":m.value,"property-data":P.value,onValidationChange:s},null,8,["project-data","role-data","property-data"])]),_:1},8,["status","can-proceed","submitting"])):y("",!0)])])])])}}}),Jl=ae(El,[["__scopeId","data-v-5c9b771f"]]);export{Jl as default};
