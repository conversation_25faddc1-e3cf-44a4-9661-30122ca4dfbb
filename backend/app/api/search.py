"""
Manual Search and Monitoring Endpoints
Replaces background jobs with on-demand manual checks
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.database import get_db
from app.api.deps import get_current_active_user
from app.models.user_simple import User
from app.models.task import Task, DeadlineTracker
from app.models.procedure import Procedure
from app.models.project import Project

router = APIRouter()

@router.get("/deadlines/check")
async def check_deadlines_manual(
    days_ahead: int = Query(7, ge=1, le=30, description="<PERSON><PERSON><PERSON> in anticipo per scadenze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Controllo manuale scadenze - sostituisce background job
    Trova task e scadenze imminenti o scadute
    """
    now = datetime.now()
    future_date = now + timedelta(days=days_ahead)
    
    # Trova scadenze imminenti
    upcoming_deadlines = db.query(Task).filter(
        and_(
            Task.due_date <= future_date,
            Task.due_date >= now,
            Task.status != "completed"
        )
    ).all()
    
    # Trova scadenze scadute
    overdue_tasks = db.query(Task).filter(
        and_(
            Task.due_date < now,
            Task.status != "completed"
        )
    ).all()
    
    # Trova deadline tracker critici
    critical_deadlines = db.query(DeadlineTracker).filter(
        and_(
            DeadlineTracker.deadline_date <= future_date,
            DeadlineTracker.status.in_(["upcoming", "due"])
        )
    ).all()
    
    return {
        "upcoming_deadlines": len(upcoming_deadlines),
        "overdue_tasks": len(overdue_tasks),
        "critical_deadlines": len(critical_deadlines),
        "last_check": now.isoformat(),
        "check_range_days": days_ahead,
        "details": {
            "upcoming": [
                {
                    "id": task.id,
                    "title": task.title,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "priority": task.priority,
                    "procedure_id": task.procedure_id
                }
                for task in upcoming_deadlines[:10]  # Limit to 10 for performance
            ],
            "overdue": [
                {
                    "id": task.id,
                    "title": task.title,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "days_overdue": (now - task.due_date).days if task.due_date else 0,
                    "priority": task.priority
                }
                for task in overdue_tasks[:10]  # Limit to 10 for performance
            ]
        }
    }

@router.get("/procedures/status")
async def check_procedures_status(
    project_id: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Controllo manuale stato procedure
    Analizza avanzamento e blocchi
    """
    query = db.query(Procedure)
    
    if project_id:
        query = query.filter(Procedure.project_id == project_id)
    
    procedures = query.all()
    
    status_summary = {}
    for procedure in procedures:
        status = procedure.status
        if status not in status_summary:
            status_summary[status] = 0
        status_summary[status] += 1
    
    # Trova procedure bloccate (senza aggiornamenti da più di 30 giorni)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    stalled_procedures = db.query(Procedure).filter(
        or_(
            Procedure.updated_at < thirty_days_ago,
            Procedure.updated_at.is_(None)
        ),
        Procedure.status.in_(["active", "draft"])
    ).all()
    
    return {
        "total_procedures": len(procedures),
        "status_breakdown": status_summary,
        "stalled_procedures": len(stalled_procedures),
        "last_check": datetime.now().isoformat(),
        "stalled_details": [
            {
                "id": proc.id,
                "title": proc.title,
                "status": proc.status,
                "last_update": proc.updated_at.isoformat() if proc.updated_at else None,
                "project_id": proc.project_id
            }
            for proc in stalled_procedures[:5]
        ]
    }

@router.get("/projects/health")
async def check_projects_health(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Controllo manuale salute progetti
    Identifica progetti con problemi
    """
    projects = db.query(Project).all()
    
    health_summary = {
        "total_projects": len(projects),
        "healthy": 0,
        "warning": 0,
        "critical": 0,
        "issues": []
    }
    
    for project in projects:
        # Conta procedure per progetto
        procedure_count = db.query(Procedure).filter(
            Procedure.project_id == project.id
        ).count()
        
        # Determina salute progetto
        if procedure_count == 0:
            health_summary["critical"] += 1
            health_summary["issues"].append({
                "project_id": project.id,
                "project_name": project.name,
                "issue": "No procedures defined",
                "severity": "critical"
            })
        elif procedure_count < 3:
            health_summary["warning"] += 1
            health_summary["issues"].append({
                "project_id": project.id,
                "project_name": project.name,
                "issue": f"Only {procedure_count} procedures",
                "severity": "warning"
            })
        else:
            health_summary["healthy"] += 1
    
    return {
        **health_summary,
        "last_check": datetime.now().isoformat()
    }

@router.post("/refresh/all")
async def refresh_all_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Refresh completo di tutti i dati
    Simula quello che farebbe un background job
    """
    start_time = datetime.now()
    
    # Esegui tutti i controlli
    deadlines_result = await check_deadlines_manual(db=db, current_user=current_user)
    procedures_result = await check_procedures_status(db=db, current_user=current_user)
    projects_result = await check_projects_health(db=db, current_user=current_user)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    return {
        "refresh_completed": True,
        "processing_time_seconds": processing_time,
        "timestamp": end_time.isoformat(),
        "summary": {
            "deadlines": {
                "upcoming": deadlines_result["upcoming_deadlines"],
                "overdue": deadlines_result["overdue_tasks"]
            },
            "procedures": {
                "total": procedures_result["total_procedures"],
                "stalled": procedures_result["stalled_procedures"]
            },
            "projects": {
                "total": projects_result["total_projects"],
                "healthy": projects_result["healthy"],
                "issues": projects_result["critical"] + projects_result["warning"]
            }
        }
    }
