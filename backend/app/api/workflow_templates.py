"""
Workflow Templates API
Manage workflow templates from JSON files and their execution state
"""
import json
import os
from typing import List, Optional, Dict, Any
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..api.deps import get_current_active_user, get_db
from ..models.user_simple import User
from ..models.procedure import Procedure

router = APIRouter()

# Path to workflow templates directory
TEMPLATES_DIR = Path(__file__).parent.parent / "workflows" / "templates"

def load_template_from_json(template_id: str) -> Optional[Dict[str, Any]]:
    """Load template data from JSON file"""
    try:
        template_file = TEMPLATES_DIR / f"{template_id}.json"
        if not template_file.exists():
            return None
            
        with open(template_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading template {template_id}: {e}")
        return None

def get_all_templates() -> List[Dict[str, Any]]:
    """Get all templates from JSON files"""
    templates = []
    
    if not TEMPLATES_DIR.exists():
        return templates
    
    for json_file in TEMPLATES_DIR.glob("*.json"):
        if json_file.name == "README_CRITICAL_DO_NOT_MODIFY.md":
            continue
            
        template_id = json_file.stem
        template_data = load_template_from_json(template_id)
        
        if template_data:
            # Add computed fields
            template_data["id"] = template_id
            template_data["active"] = True  # All JSON templates are active
            templates.append(template_data)
    
    return templates

@router.get("/")
async def get_workflow_templates(
    active_only: bool = Query(True, description="Return only active templates"),
    category: Optional[str] = Query(None, description="Filter by category"),
    current_user: User = Depends(get_current_active_user)
):
    """Get list of workflow templates from JSON files"""
    templates = get_all_templates()
    
    # Filter by category if specified
    if category:
        templates = [t for t in templates if t.get("category") == category]
    
    # Sort by name
    templates.sort(key=lambda x: x.get("name", ""))
    
    return templates

@router.get("/categories")
async def get_template_categories(
    current_user: User = Depends(get_current_active_user)
):
    """Get list of template categories from JSON files"""
    templates = get_all_templates()
    categories = set()
    
    for template in templates:
        category = template.get("category")
        if category:
            categories.add(category)
    
    return {
        "categories": sorted(list(categories))
    }

@router.get("/{template_id}")
async def get_workflow_template(
    template_id: str,
    include_steps: bool = Query(True, description="Include step details from JSON"),
    current_user: User = Depends(get_current_active_user)
):
    """Get specific workflow template from JSON file"""
    template_data = load_template_from_json(template_id)
    
    if not template_data:
        raise HTTPException(status_code=404, detail="Workflow template not found")
    
    # Add computed fields
    template_data["id"] = template_id
    template_data["active"] = True
    
    # Steps are always included from JSON
    if not include_steps and "steps" in template_data:
        del template_data["steps"]
    
    return template_data

@router.get("/{template_id}/procedures")
async def get_template_procedures(
    template_id: str,
    status: Optional[str] = Query(None, description="Filter by workflow status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get procedures using this template"""
    # Verify template exists
    template_data = load_template_from_json(template_id)
    if not template_data:
        raise HTTPException(status_code=404, detail="Workflow template not found")
    
    # Get procedures with this template
    procedures = db.query(Procedure).filter(Procedure.workflow_template_id == template_id).all()
    
    # Get workflow states for these procedures
    procedure_ids = [p.id for p in procedures]
    workflow_states = db.query(ProcedureWorkflowState).filter(
        ProcedureWorkflowState.procedure_id.in_(procedure_ids)
    ).all()
    
    # Create lookup for workflow states
    state_lookup = {ws.procedure_id: ws for ws in workflow_states}
    
    result_procedures = []
    for procedure in procedures:
        workflow_state = state_lookup.get(procedure.id)
        
        proc_data = {
            "id": procedure.id,
            "title": procedure.title,
            "description": procedure.description,
            "procedure_type": procedure.procedure_type,
            "status": procedure.status,
            "priority": procedure.priority,
            "project_id": procedure.project_id,
            "assigned_user_id": procedure.assigned_user_id,
            "created_at": procedure.created_at,
            "workflow_state": None
        }
        
        if workflow_state:
            proc_data["workflow_state"] = {
                "current_step_id": workflow_state.current_step_id,
                "current_step_name": workflow_state.current_step_name,
                "overall_status": workflow_state.overall_status,
                "completed_steps": workflow_state.completed_steps,
                "failed_steps": workflow_state.failed_steps,
                "started_at": workflow_state.started_at,
                "completed_at": workflow_state.completed_at,
                "next_deadline": workflow_state.next_deadline
            }
        
        # Filter by workflow status if requested
        if status and workflow_state and workflow_state.overall_status != status:
            continue
            
        result_procedures.append(proc_data)
    
    return {
        "template": {
            "id": template_id,
            "name": template_data.get("name", template_id)
        },
        "procedures": result_procedures,
        "total": len(result_procedures)
    }

@router.post("/{template_id}/initialize-procedure/{procedure_id}")
async def initialize_procedure_workflow(
    template_id: str,
    procedure_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Initialize workflow state for a procedure"""
    # Verify template exists
    template_data = load_template_from_json(template_id)
    if not template_data:
        raise HTTPException(status_code=404, detail="Workflow template not found")
    
    # Verify procedure exists
    procedure = db.query(Procedure).filter(Procedure.id == procedure_id).first()
    if not procedure:
        raise HTTPException(status_code=404, detail="Procedure not found")
    
    # Check if workflow state already exists
    existing_state = db.query(ProcedureWorkflowState).filter(
        ProcedureWorkflowState.procedure_id == procedure_id
    ).first()
    
    if existing_state:
        raise HTTPException(status_code=400, detail="Workflow already initialized for this procedure")
    
    # Get steps from JSON template
    from datetime import datetime
    
    try:
        steps = template_data.get("steps", [])
        
        if not steps:
            raise HTTPException(status_code=400, detail="Template has no steps defined")
        
        first_step = steps[0]
        
        # Create workflow state
        workflow_state = ProcedureWorkflowState(
            procedure_id=procedure_id,
            workflow_template_id=template_id,
            current_step_id=first_step.get("id", "step_1"),
            current_step_name=first_step.get("name", "Primo step"),
            overall_status="in_progress",
            completed_steps=[],
            failed_steps=[],
            step_data={},
            started_at=datetime.now()
        )
        
        db.add(workflow_state)
        
        # Update procedure workflow_template_id if not set
        if not procedure.workflow_template_id:
            procedure.workflow_template_id = template_id
        
        db.commit()
        db.refresh(workflow_state)
        
        return {
            "message": "Workflow initialized successfully",
            "workflow_state": {
                "id": workflow_state.id,
                "current_step_id": workflow_state.current_step_id,
                "current_step_name": workflow_state.current_step_name,
                "overall_status": workflow_state.overall_status,
                "started_at": workflow_state.started_at
            }
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error initializing workflow: {str(e)}")