"""
Enhanced User model for ExProject D.P.R. 327/2001 compliance
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, func
from ..core.database import Base
import enum

class UserRole(enum.Enum):
    """Professional roles according to D.P.R. 327/2001 hierarchy"""
    ADMIN_ENTE = "admin_ente"  # Amministratore Ente
    RUP = "rup"  # Responsabile Unico del Procedimento
    RPE = "rpe"  # Responsabile Procedimento Espropriativo
    DIRIGENTE = "dirigente"  # Dirigente/Responsabile
    TECNICO = "tecnico"  # Tecnico
    LEGALE = "legale"  # Ufficio Legale
    AMMINISTRATIVO = "amministrativo"  # Amministrativo
    LETTORE = "lettore"  # Solo lettura
    CITTADINO = "cittadino"  # Cittadino (area pubblica)

class UserStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"

class User(Base):
    __tablename__ = "users"
    
    # Basic fields
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, nullable=False)
    
    # Role and permissions - using strings for now to avoid enum issues
    role = Column(String, default='lettore', nullable=False)
    status = Column(String, default='active', nullable=False)
    
    # Professional information
    professional_order = Column(String)  # Ordine professionale (Ingegneri, Architetti, etc.)
    professional_number = Column(String)  # Numero iscrizione albo
    organization = Column(String)  # Ente/Organizzazione di appartenenza
    department = Column(String)  # Dipartimento/Ufficio
    phone = Column(String)  # Telefono
    
    # Legal compliance
    fiscal_code = Column(String)  # Codice fiscale
    pec_email = Column(String)  # Email PEC per comunicazioni ufficiali
    
    # System fields
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)  # Kept for backward compatibility
    is_verified = Column(Boolean, default=False)  # Email/identity verification
    
    # Audit fields
    last_login = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    created_by = Column(Integer)  # User ID who created this user
    
    @property
    def role_label(self):
        """Human-readable role label"""
        labels = {
            UserRole.ADMIN_ENTE: "Amministratore Ente",
            UserRole.RUP: "RUP - Responsabile Unico del Procedimento",
            UserRole.RPE: "RPE - Responsabile Procedimento Espropriativo",
            UserRole.DIRIGENTE: "Dirigente/Responsabile",
            UserRole.TECNICO: "Tecnico",
            UserRole.LEGALE: "Ufficio Legale",
            UserRole.AMMINISTRATIVO: "Amministrativo",
            UserRole.LETTORE: "Lettore",
            UserRole.CITTADINO: "Cittadino"
        }
        return labels.get(self.role, str(self.role.value))
    
    @property
    def can_manage_projects(self):
        """Check if user can manage projects"""
        return self.role in [UserRole.ADMIN_ENTE, UserRole.RUP, UserRole.RPE, UserRole.DIRIGENTE]
    
    @property
    def can_approve_procedures(self):
        """Check if user can approve procedures"""
        return self.role in [UserRole.RUP, UserRole.RPE, UserRole.DIRIGENTE]
    
    @property
    def can_manage_users(self):
        """Check if user can manage other users"""
        return self.role in [UserRole.ADMIN_ENTE, UserRole.DIRIGENTE]
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role.value}')>"