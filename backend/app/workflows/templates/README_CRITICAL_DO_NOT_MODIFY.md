# ⚠️ CRITICAL WARNING - DO NOT MODIFY THESE FILES ⚠️

## 🚨 WORKFLOW TEMPLATES - HANDS OFF! 🚨

**These JSON template files are CRITICAL and COMPLETE implementations based on the PRD.**

### 📋 Protected Files:
- `acquisizione_senza_titolo.json`
- `apposizione_vpe.json` 
- `autorizzazione_introduzione.json`
- `conclusione_vpe.json`
- `decreto_correzione.json`
- `decreto_esproprio.json`
- `occupazione_urgenza.json`
- `proroga_pubblica_utilita.json`
- `svincolo_indennita.json`

### ❌ WHAT NOT TO DO:
- **DO NOT** modify these files
- **DO NOT** delete these files
- **DO NOT** create duplicates
- **DO NOT** move these files
- **DO NOT** "improve" or "update" these files

### ✅ WHAT TO DO INSTEAD:
- Use the existing `/api/procedures/workflow-templates` API endpoint
- Reference these files via the API only
- If changes are needed, discuss with the user first
- Create separate files for new features, don't modify existing ones

### 🔒 Protection Measures:
1. **This README** - Warning file
2. **Ask user first** - Always confirm before touching this directory
3. **Read-only mindset** - Treat these as immutable references
4. **API usage** - Access via backend API, never directly

### 📝 History:
- ✅ Original templates: Perfect implementation from PRD
- ❌ 2025-06-23: Claude accidentally created duplicates (MISTAKE - corrected)
- ✅ Templates preserved and protected

---
**REMEMBER: These templates are the foundation of the D.P.R. 327/2001 workflow system. Treat them as sacred code!**