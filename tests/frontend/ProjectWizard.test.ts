/**
 * Test critici per Project Creation Wizard
 * Priorità P0 - Core user journey
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import PrimeVue from 'primevue/config'

// Mock components per evitare dipendenze complesse
vi.mock('@/components/layout/AdminNavbar.vue', () => ({
  default: { template: '<div data-testid="admin-navbar">Admin Navbar</div>' }
}))

vi.mock('@/components/project/ProjectSetupForm.vue', () => ({
  default: {
    template: '<div data-testid="project-setup-form">Project Setup Form</div>',
    emits: ['validation-change'],
    props: ['modelValue']
  }
}))

vi.mock('@/components/project/RoleAssignmentForm.vue', () => ({
  default: {
    template: '<div data-testid="role-assignment-form">Role Assignment Form</div>',
    emits: ['validation-change'],
    props: ['modelValue']
  }
}))

vi.mock('@/components/property/PropertyImportForm.vue', () => ({
  default: {
    template: '<div data-testid="property-import-form">Property Import Form</div>',
    emits: ['validation-change'],
    props: ['modelValue', 'projectId']
  }
}))

vi.mock('@/components/project/ProjectReviewForm.vue', () => ({
  default: {
    template: '<div data-testid="project-review-form">Project Review Form</div>',
    props: ['projectData', 'roleData', 'propertyData']
  }
}))

// Import del componente da testare
import ProjectCreate from '@/views/admin/ProjectCreate.vue'

// Setup router mock
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/admin/projects/create', component: ProjectCreate },
    { path: '/admin/projects/:id', component: { template: '<div>Project Detail</div>' } }
  ]
})

// Mock fetch API
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(() => 'mock-token'),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

describe('ProjectCreate Wizard', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup successful API responses
    mockFetch.mockResolvedValue({
      ok: true,
      status: 201,
      json: () => Promise.resolve({ id: 123, name: 'Test Project' })
    })
  })

  const createWrapper = () => {
    return mount(ProjectCreate, {
      global: {
        plugins: [router, PrimeVue],
        stubs: {
          'router-link': true,
          'router-view': true
        }
      }
    })
  }

  describe('Wizard Navigation', () => {
    it('should start at step 1 (Project Setup)', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="project-setup-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="role-assignment-form"]').exists()).toBe(false)
    })

    it('should show correct step titles and descriptions', () => {
      wrapper = createWrapper()
      
      const stepTitle = wrapper.find('.wizard-step-title')
      expect(stepTitle.text()).toContain('Setup Progetto Iniziale')
      
      const stepDescription = wrapper.find('.wizard-step-description')
      expect(stepDescription.text()).toContain('Definisci le informazioni base')
    })

    it('should disable next button when step 1 is invalid', async () => {
      wrapper = createWrapper()
      
      // Simulate invalid form
      await wrapper.vm.handleStep1Validation(false)
      
      const nextButton = wrapper.find('[data-testid="next-button"]')
      expect(nextButton.attributes('disabled')).toBeDefined()
    })

    it('should enable next button when step 1 is valid', async () => {
      wrapper = createWrapper()
      
      // Simulate valid form
      await wrapper.vm.handleStep1Validation(true)
      
      const nextButton = wrapper.find('[data-testid="next-button"]')
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })

    it('should navigate to step 2 when next is clicked', async () => {
      wrapper = createWrapper()
      
      // Make step 1 valid
      await wrapper.vm.handleStep1Validation(true)
      
      // Click next
      await wrapper.find('[data-testid="next-button"]').trigger('click')
      
      expect(wrapper.find('[data-testid="role-assignment-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="project-setup-form"]').exists()).toBe(false)
    })

    it('should show previous button on step 2', async () => {
      wrapper = createWrapper()
      
      // Navigate to step 2
      await wrapper.vm.handleStep1Validation(true)
      await wrapper.vm.nextStep()
      
      const prevButton = wrapper.find('[data-testid="previous-button"]')
      expect(prevButton.exists()).toBe(true)
    })

    it('should navigate back to step 1 when previous is clicked', async () => {
      wrapper = createWrapper()
      
      // Navigate to step 2
      await wrapper.vm.handleStep1Validation(true)
      await wrapper.vm.nextStep()
      
      // Click previous
      await wrapper.find('[data-testid="previous-button"]').trigger('click')
      
      expect(wrapper.find('[data-testid="project-setup-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="role-assignment-form"]').exists()).toBe(false)
    })
  })

  describe('Data Management', () => {
    it('should preserve project data when navigating between steps', async () => {
      wrapper = createWrapper()
      
      // Set project data
      const projectData = {
        name: 'Test Project',
        description: 'Test Description',
        project_type: 'infrastructure'
      }
      wrapper.vm.projectData = projectData
      
      // Navigate to step 2 and back
      await wrapper.vm.nextStep()
      await wrapper.vm.previousStep()
      
      expect(wrapper.vm.projectData).toEqual(projectData)
    })

    it('should preserve role data when navigating between steps', async () => {
      wrapper = createWrapper()
      
      // Navigate to step 2
      await wrapper.vm.nextStep()
      
      // Set role data
      const roleData = {
        responsabile_procedimento: '<EMAIL>',
        responsabile_unico: '<EMAIL>'
      }
      wrapper.vm.roleData = roleData
      
      // Navigate to step 3 and back
      await wrapper.vm.nextStep()
      await wrapper.vm.previousStep()
      
      expect(wrapper.vm.roleData).toEqual(roleData)
    })
  })

  describe('Project Creation', () => {
    it('should create draft project when reaching step 3', async () => {
      wrapper = createWrapper()
      
      // Setup data
      wrapper.vm.projectData = { name: 'Test Project', project_type: 'infrastructure' }
      wrapper.vm.roleData = { responsabile_procedimento: '<EMAIL>' }
      
      // Navigate to step 3
      await wrapper.vm.handleStep1Validation(true)
      await wrapper.vm.nextStep()
      await wrapper.vm.handleStep2Validation(true)
      await wrapper.vm.nextStep()
      
      // Should call createDraftProject
      expect(mockFetch).toHaveBeenCalledWith('/api/projects/', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer mock-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'Test Project',
          project_type: 'infrastructure',
          responsabile_procedimento: '<EMAIL>',
          status: 'draft',
          phase: 'setup'
        })
      })
    })

    it('should handle API errors gracefully', async () => {
      wrapper = createWrapper()
      
      // Mock API error
      mockFetch.mockRejectedValueOnce(new Error('API Error'))
      
      // Setup data and try to create project
      wrapper.vm.projectData = { name: 'Test Project' }
      
      // Should not throw error
      await expect(wrapper.vm.createDraftProject()).rejects.toThrow('API Error')
    })

    it('should finalize project on step 4 completion', async () => {
      wrapper = createWrapper()
      
      // Setup complete data
      wrapper.vm.projectData = { name: 'Test Project', project_type: 'infrastructure' }
      wrapper.vm.roleData = { responsabile_procedimento: '<EMAIL>' }
      wrapper.vm.createdProjectId = 123
      
      // Complete wizard
      await wrapper.vm.completeWizard()
      
      // Should update project to active status
      expect(mockFetch).toHaveBeenCalledWith('/api/projects/123', {
        method: 'PUT',
        headers: {
          'Authorization': 'Bearer mock-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'Test Project',
          project_type: 'infrastructure',
          responsabile_procedimento: '<EMAIL>',
          status: 'active',
          phase: 'preparatorie'
        })
      })
    })
  })

  describe('Validation States', () => {
    it('should track validation state for each step', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.step1Valid).toBe(false)
      expect(wrapper.vm.step2Valid).toBe(false)
      expect(wrapper.vm.step3Valid).toBe(false)
    })

    it('should update step validation when form emits validation-change', async () => {
      wrapper = createWrapper()
      
      // Simulate form validation change
      await wrapper.vm.handleStep1Validation(true)
      expect(wrapper.vm.step1Valid).toBe(true)
      
      await wrapper.vm.handleStep1Validation(false)
      expect(wrapper.vm.step1Valid).toBe(false)
    })

    it('should show correct step status indicators', async () => {
      wrapper = createWrapper()
      
      // Step 1 should be active initially
      expect(wrapper.vm.getStepStatus(0)).toBe('active')
      expect(wrapper.vm.getStepStatus(1)).toBe('pending')
      
      // Complete step 1
      await wrapper.vm.handleStep1Validation(true)
      await wrapper.vm.nextStep()
      
      expect(wrapper.vm.getStepStatus(0)).toBe('completed')
      expect(wrapper.vm.getStepStatus(1)).toBe('active')
    })
  })

  describe('Loading States', () => {
    it('should show loading state during project creation', async () => {
      wrapper = createWrapper()
      
      // Mock slow API response
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ id: 123 })
        }), 100))
      )
      
      // Start creation
      const createPromise = wrapper.vm.createDraftProject()
      
      // Should be in loading state
      expect(wrapper.vm.creating).toBe(true)
      
      await createPromise
      
      // Should not be loading anymore
      expect(wrapper.vm.creating).toBe(false)
    })

    it('should disable form during submission', async () => {
      wrapper = createWrapper()
      
      wrapper.vm.creating = true
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('[data-testid="submit-button"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      wrapper = createWrapper()
      
      const wizard = wrapper.find('.wizard-container')
      expect(wizard.attributes('role')).toBe('tablist')
      
      const step = wrapper.find('.wizard-step')
      expect(step.attributes('role')).toBe('tabpanel')
    })

    it('should support keyboard navigation', async () => {
      wrapper = createWrapper()
      
      // Make step valid
      await wrapper.vm.handleStep1Validation(true)
      
      // Simulate Enter key on next button
      const nextButton = wrapper.find('[data-testid="next-button"]')
      await nextButton.trigger('keydown.enter')
      
      expect(wrapper.vm.currentStep).toBe(1)
    })
  })
})
