#!/usr/bin/env python3
"""
Test critici per API progetti
Priorità P0 - Da eseguire sempre prima del deploy
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

# Import app components
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

from app.main import app
from app.core.database import get_db, Base, engine
from app.models.user_simple import User
from app.models.project import Project
from app.core.security import create_access_token

# Test database setup
@pytest.fixture(scope="session")
def test_db():
    """Create test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def db_session(test_db):
    """Create database session for tests"""
    from sqlalchemy.orm import sessionmaker
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()

@pytest.fixture
def override_get_db(db_session):
    """Override database dependency"""
    def _override_get_db():
        try:
            yield db_session
        finally:
            pass
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()

@pytest.fixture
def test_user(db_session):
    """Create test user"""
    user = User(
        email="<EMAIL>",
        hashed_password="$2b$12$test_hash",
        full_name="Test User",
        role="responsabile_procedimento",
        status="active"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers"""
    token = create_access_token(data={"sub": test_user.email})
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def client(override_get_db):
    """Create test client"""
    return TestClient(app)

class TestProjectAPI:
    """Test suite per API progetti"""
    
    def test_create_project_success(self, client, auth_headers, db_session):
        """Test creazione progetto con dati validi"""
        # Arrange
        project_data = {
            "name": "Test Esproprio SS16",
            "description": "Progetto test per ampliamento strada statale",
            "project_type": "infrastructure",
            "municipality": "Roma",
            "budget": 2500000,
            "legal_reference": "dpr_327_2001"
        }
        
        # Act
        response = client.post(
            "/api/projects/",
            json=project_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == project_data["name"]
        assert data["project_type"] == project_data["project_type"]
        assert data["status"] == "draft"  # Default status
        
        # Verify in database
        project = db_session.query(Project).filter(Project.name == project_data["name"]).first()
        assert project is not None
        assert project.municipality == "Roma"
    
    def test_create_project_missing_required_fields(self, client, auth_headers):
        """Test creazione progetto con campi obbligatori mancanti"""
        # Arrange
        incomplete_data = {
            "description": "Progetto senza nome"
        }
        
        # Act
        response = client.post(
            "/api/projects/",
            json=incomplete_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 422
        error_data = response.json()
        assert "detail" in error_data
        # Verify specific validation error for missing 'name'
        errors = error_data["detail"]
        name_error = next((e for e in errors if e["loc"] == ["body", "name"]), None)
        assert name_error is not None
    
    def test_create_project_unauthorized(self, client):
        """Test creazione progetto senza autenticazione"""
        # Arrange
        project_data = {
            "name": "Unauthorized Project",
            "project_type": "infrastructure"
        }
        
        # Act
        response = client.post("/api/projects/", json=project_data)
        
        # Assert
        assert response.status_code == 401
    
    def test_get_projects_list(self, client, auth_headers, db_session, test_user):
        """Test recupero lista progetti"""
        # Arrange - Create test projects
        projects = [
            Project(
                name="Progetto 1",
                description="Descrizione 1",
                project_type="infrastructure",
                status="active",
                created_by=test_user.id
            ),
            Project(
                name="Progetto 2", 
                description="Descrizione 2",
                project_type="utilities",
                status="draft",
                created_by=test_user.id
            )
        ]
        for project in projects:
            db_session.add(project)
        db_session.commit()
        
        # Act
        response = client.get("/api/projects/", headers=auth_headers)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 2
        
        # Verify project data structure
        project_names = [p["name"] for p in data]
        assert "Progetto 1" in project_names
        assert "Progetto 2" in project_names
    
    def test_get_project_by_id(self, client, auth_headers, db_session, test_user):
        """Test recupero progetto specifico"""
        # Arrange
        project = Project(
            name="Progetto Specifico",
            description="Test get by ID",
            project_type="infrastructure",
            municipality="Milano",
            budget=1500000,
            created_by=test_user.id
        )
        db_session.add(project)
        db_session.commit()
        db_session.refresh(project)
        
        # Act
        response = client.get(f"/api/projects/{project.id}", headers=auth_headers)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == project.id
        assert data["name"] == "Progetto Specifico"
        assert data["municipality"] == "Milano"
        assert data["budget"] == 1500000
    
    def test_get_project_not_found(self, client, auth_headers):
        """Test recupero progetto inesistente"""
        # Act
        response = client.get("/api/projects/99999", headers=auth_headers)
        
        # Assert
        assert response.status_code == 404
    
    def test_update_project(self, client, auth_headers, db_session, test_user):
        """Test aggiornamento progetto"""
        # Arrange
        project = Project(
            name="Progetto da Aggiornare",
            description="Descrizione originale",
            project_type="infrastructure",
            status="draft",
            created_by=test_user.id
        )
        db_session.add(project)
        db_session.commit()
        db_session.refresh(project)
        
        update_data = {
            "name": "Progetto Aggiornato",
            "description": "Descrizione aggiornata",
            "status": "active"
        }
        
        # Act
        response = client.put(
            f"/api/projects/{project.id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Progetto Aggiornato"
        assert data["description"] == "Descrizione aggiornata"
        assert data["status"] == "active"
        
        # Verify in database
        db_session.refresh(project)
        assert project.name == "Progetto Aggiornato"
        assert project.status == "active"
    
    def test_delete_project(self, client, auth_headers, db_session, test_user):
        """Test eliminazione progetto"""
        # Arrange
        project = Project(
            name="Progetto da Eliminare",
            project_type="infrastructure",
            created_by=test_user.id
        )
        db_session.add(project)
        db_session.commit()
        db_session.refresh(project)
        project_id = project.id
        
        # Act
        response = client.delete(f"/api/projects/{project_id}", headers=auth_headers)
        
        # Assert
        assert response.status_code == 204
        
        # Verify deletion in database
        deleted_project = db_session.query(Project).filter(Project.id == project_id).first()
        assert deleted_project is None

class TestProjectValidation:
    """Test validazione dati progetti"""
    
    def test_project_budget_validation(self, client, auth_headers):
        """Test validazione budget progetto"""
        # Test budget negativo
        invalid_data = {
            "name": "Test Budget",
            "project_type": "infrastructure", 
            "budget": -1000
        }
        
        response = client.post("/api/projects/", json=invalid_data, headers=auth_headers)
        assert response.status_code == 422
    
    def test_project_type_validation(self, client, auth_headers):
        """Test validazione tipo progetto"""
        # Test tipo progetto non valido
        invalid_data = {
            "name": "Test Type",
            "project_type": "invalid_type"
        }
        
        response = client.post("/api/projects/", json=invalid_data, headers=auth_headers)
        assert response.status_code == 422

class TestProjectWorkflowIntegration:
    """Test integrazione progetti con workflow"""
    
    def test_project_with_workflow_template(self, client, auth_headers):
        """Test creazione progetto con template workflow"""
        # Arrange
        project_data = {
            "name": "Progetto con Workflow",
            "project_type": "infrastructure",
            "workflow_template": "decreto_esproprio"
        }
        
        # Act
        response = client.post("/api/projects/", json=project_data, headers=auth_headers)
        
        # Assert
        assert response.status_code == 201
        data = response.json()
        assert "workflow_template" in data
        # Note: Workflow integration test depends on workflow system implementation

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
