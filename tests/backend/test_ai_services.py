#!/usr/bin/env python3
"""
Quick test script for AI services
Tests the core AI functionality without requiring the full application
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.ai.project_ai import ProjectAI
from app.services.ai.suggestion_ai import SuggestionAI
from app.services.ai.validation_ai import ValidationAI
from app.services.ai.budget_ai import BudgetAI

async def test_ai_services():
    """Test AI services with sample data"""
    
    # Test data - similar to what would come from the frontend wizard
    sample_project_data = {
        "title": "Ampliamento Strada Provinciale 45",
        "description": "Progetto per l'ampliamento della strada provinciale 45 nel tratto Roma-Fiumicino secondo D.P.R. 327/2001",
        "category": "infrastructure",
        "municipality": "Roma",
        "budget": 2500000,
        "priority": "high",
        "legal_reference": "dpr_327_2001",
        "start_date": "2024-03-01",
        "expected_end_date": "2025-09-30"
    }
    
    print("🧪 Testing AI Services...")
    print("=" * 50)
    
    # Test individual services
    print("\n1. Testing SuggestionAI...")
    suggestion_ai = SuggestionAI()
    suggestion_result = await suggestion_ai.process(sample_project_data)
    print(f"   ✅ Success: {suggestion_result.success}")
    print(f"   🎯 Confidence: {suggestion_result.confidence:.2f}")
    print(f"   💡 Suggestions: {len(suggestion_result.data.get('suggestions', []))}")
    
    print("\n2. Testing ValidationAI...")
    validation_ai = ValidationAI()
    validation_result = await validation_ai.process(sample_project_data)
    print(f"   ✅ Success: {validation_result.success}")
    print(f"   🎯 Confidence: {validation_result.confidence:.2f}")
    print(f"   ✔️ Overall Valid: {validation_result.data.get('overall_valid')}")
    print(f"   ⚠️ Issues: {validation_result.data.get('total_issues', 0)}")
    
    print("\n3. Testing BudgetAI...")
    budget_ai = BudgetAI()
    budget_result = await budget_ai.process(sample_project_data)
    print(f"   ✅ Success: {budget_result.success}")
    print(f"   🎯 Confidence: {budget_result.confidence:.2f}")
    budget_analysis = budget_result.data.get('budget_analysis', {})
    if budget_analysis.get('estimated_range'):
        estimated = budget_analysis['estimated_range']
        print(f"   💰 Budget Range: €{estimated.get('min', 0):,} - €{estimated.get('max', 0):,}")
        print(f"   📊 Similar Projects: {len(budget_analysis.get('similar_projects', []))}")
    
    print("\n4. Testing ProjectAI (Orchestrator)...")
    project_ai = ProjectAI()
    comprehensive_result = await project_ai.process(sample_project_data)
    print(f"   ✅ Success: {comprehensive_result.success}")
    print(f"   🎯 Overall Confidence: {comprehensive_result.confidence:.2f}")
    print(f"   ⏱️ Processing Time: {comprehensive_result.processing_time_ms:.1f}ms")
    
    # Test overview
    overview = comprehensive_result.data.get('overview', {})
    if overview:
        print(f"   📋 Project Status: {overview.get('status')}")
        print(f"   📊 Completeness: {overview.get('completeness_percentage', 0):.1f}%")
    
    # Test insights
    insights = comprehensive_result.data.get('insights', [])
    print(f"   💡 AI Insights: {len(insights)}")
    
    # Test next steps
    next_steps = comprehensive_result.data.get('next_steps', [])
    print(f"   🎯 Next Steps: {len(next_steps)}")
    
    print("\n" + "=" * 50)
    print("🎉 All AI Services tested successfully!")
    
    # Test with incomplete data
    print("\n🧪 Testing with incomplete data...")
    incomplete_data = {
        "title": "Nuovo progetto",
        "category": "infrastructure"
    }
    
    incomplete_result = await project_ai.process(incomplete_data)
    print(f"   ✅ Handles incomplete data: {incomplete_result.success}")
    print(f"   💡 Suggestions generated: {len(incomplete_result.data.get('suggestions', {}).get('all', []))}")
    
    return True

async def test_specific_scenarios():
    """Test specific scenarios that might be common"""
    
    print("\n🔍 Testing specific scenarios...")
    print("-" * 40)
    
    project_ai = ProjectAI()
    
    # Scenario 1: Very basic project data (user just started)
    basic_data = {
        "title": "Progetto infrastrutturale",
        "category": "infrastructure"
    }
    
    result = await project_ai.get_suggestions_only(basic_data)
    print(f"Basic project suggestions: {len(result.data.get('suggestions', []))}")
    
    # Scenario 2: Budget validation
    budget_test = {
        "title": "Test budget validation",
        "category": "utilities",
        "budget": 50000  # Very low budget
    }
    
    validation_result = await project_ai.validate_only(budget_test)
    print(f"Budget validation issues: {validation_result.data.get('total_issues', 0)}")
    
    # Scenario 3: Municipality suggestion
    municipality_test = {
        "title": "Test municipality",
        "municipality": "rom"  # Incomplete municipality name
    }
    
    validation_result2 = await project_ai.validate_only(municipality_test)
    field_validations = validation_result2.data.get('field_validations', {})
    municipality_validation = field_validations.get('municipality', {})
    print(f"Municipality suggestions: {len(municipality_validation.get('suggestions', []))}")

if __name__ == "__main__":
    try:
        asyncio.run(test_ai_services())
        asyncio.run(test_specific_scenarios())
        print("\n✅ All tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()