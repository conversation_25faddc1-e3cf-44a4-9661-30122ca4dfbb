{"name": "@xterm/addon-image", "version": "0.9.0-beta.107", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-image.js", "module": "lib/addon-image.mjs", "types": "typings/addon-image.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-image", "license": "MIT", "keywords": ["terminal", "image", "sixel", "xterm", "xterm.js"], "scripts": {"prepackage": "../../node_modules/.bin/tsc -p .", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}, "devDependencies": {"sixel": "^0.16.0", "xterm-wasm-parts": "^0.1.0"}, "commit": "e9c547c1c6b67e9f09c24ccc007e19305f536e60"}