/*! For license information please see addon-ligatures.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.LigaturesAddon=t():e.LigaturesAddon=t()}(globalThis,(()=>(()=>{var e={522:(e,t)=>{"use strict";function n(e){const t={};for(const[n,o]of Object.entries(e.individual))t[n]=s(o);for(const{range:n,entry:o}of e.range){const e=s(o);for(let s=n[0];s<n[1];s++)t[s]=e}return t}function s(e){const t={};return e.forward&&(t.forward=n(e.forward)),e.reverse&&(t.reverse=n(e.reverse)),e.lookup&&(t.lookup=e.lookup),t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=n},768:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(386),o=n(749),r=n(68),a=n(503),i=n(123),l=n(664),u=n(697),c=n(634),p=n(842),f=n(522);class h{constructor(e,t){this._lookupTrees=[],this._glyphLookups={},this._font=e,t.cacheSize>0&&(this._cache=new o({max:t.cacheSize,length:(e,t)=>t.length}));const n=(this._font.tables.gsub&&this._font.tables.gsub.features.filter((e=>"calt"===e.tag))||[]).reduce(((e,t)=>[...e,...t.feature.lookupListIndexes]),[]),s=this._font.tables.gsub&&this._font.tables.gsub.lookups||[],a=s.filter(((e,t)=>n.some((e=>e===t))));for(const[e,t]of a.entries()){const n=[];switch(t.lookupType){case 6:for(const[e,o]of t.subtables.entries())switch(o.substFormat){case 1:n.push(l.default(o,s,e));break;case 2:n.push(u.default(o,s,e));break;case 3:n.push(c.default(o,s,e))}break;case 8:for(const[e,s]of t.subtables.entries())n.push(p.default(s,e))}const o=f.default(r.default(n));this._lookupTrees.push({tree:o,processForward:8!==t.lookupType});for(const t of Object.keys(o))this._glyphLookups[t]||(this._glyphLookups[t]=[]),this._glyphLookups[t].push(e)}}findLigatures(e){const t=this._cache&&this._cache.get(e);if(t&&!Array.isArray(t))return t;const n=[];for(const t of e)n.push(this._font.charToGlyphIndex(t));if(0===this._lookupTrees.length)return{inputGlyphs:n,outputGlyphs:n,contextRanges:[]};const s=this._findInternal(n.slice()),o={inputGlyphs:n,outputGlyphs:s.sequence,contextRanges:s.ranges};return this._cache&&this._cache.set(e,o),o}findLigatureRanges(e){if(0===this._lookupTrees.length)return[];const t=this._cache&&this._cache.get(e);if(t)return Array.isArray(t)?t:t.contextRanges;const n=[];for(const t of e)n.push(this._font.charToGlyphIndex(t));const s=this._findInternal(n);return this._cache&&this._cache.set(e,s.ranges),s.ranges}_findInternal(e){const t=[];let n=this._getNextLookup(e,0);for(;null!==n.index;){const s=this._lookupTrees[n.index];if(s.processForward){let o=n.last;for(let r=n.first;r<o;r++){const n=a.default(s.tree,e,r,r);if(n){for(let t=0;t<n.substitutions.length;t++){const s=n.substitutions[t];null!==s&&(e[r+t]=s)}i.default(t,n.contextRange[0]+r,n.contextRange[1]+r),r+n.length>=o&&(o=r+n.length+1),r+=n.length-1}}}else for(let o=n.last-1;o>=n.first;o--){const n=a.default(s.tree,e,o,o);if(n){for(let t=0;t<n.substitutions.length;t++){const s=n.substitutions[t];null!==s&&(e[o+t]=s)}i.default(t,n.contextRange[0]+o,n.contextRange[1]+o),o-=n.length-1}}n=this._getNextLookup(e,n.index+1)}return{sequence:e,ranges:t}}_getNextLookup(e,t){const n={index:null,first:1/0,last:-1};for(let s=0;s<e.length;s++){const o=this._glyphLookups[e[s]];if(o)for(let e=0;e<o.length;e++){const r=o[e];if(r>=t){(null===n.index||r<=n.index)&&(n.index=r,n.first>s&&(n.first=s),n.last=s+1);break}}}return n}}async function d(e,t){const o=await Promise.resolve().then((()=>n(460))).then((t=>t.promisify(s.load)(e)));return new h(o,Object.assign({cacheSize:0},t))}t.load=async function(e,t){const[s]=await Promise.resolve().then((()=>n(734))).then((t=>t.listVariants(e)));if(!s)throw new Error(`Font ${e} not found`);return d(s.path,t)},t.loadFile=d,t.loadBuffer=function(e,t){const n=s.parse(e);return new h(n,Object.assign({cacheSize:0},t))}},68:(e,t)=>{"use strict";function n(e,t){for(const[n,o]of Object.entries(t.individual))if(e.individual[n])s(e.individual[n],o);else{let t=!1;for(const[a,{range:l,entry:u}]of e.range.entries()){const c=r(Number(n),l);if(null!==c.both){t=!0,e.individual[n]=o,s(e.individual[n],i(u)),e.range.splice(a,1);for(const t of c.second)Array.isArray(t)?e.range.push({range:t,entry:i(u)}):e.individual[t]=i(u)}}t||(e.individual[n]=o)}for(const{range:n,entry:a}of t.range){let t=[n];for(let n=0;n<e.range.length;n++){const{range:l,entry:u}=e.range[n];for(const[c,p]of t.entries()){if(!Array.isArray(p)){const o=r(p,l);if(null===o.both)continue;e.individual[p]=i(a),s(e.individual[p],i(u)),e.range.splice(n,1),n--;for(const t of o.second)Array.isArray(t)?e.range.push({range:t,entry:i(u)}):e.individual[t]=i(u);t.splice(c,1,...o.first);break}{const r=o(p,l);if(null===r.both)continue;e.range.splice(n,1),n--;const c=i(u);Array.isArray(r.both)?e.range.push({range:r.both,entry:c}):e.individual[r.both]=c,s(c,i(a));for(const t of r.second)Array.isArray(t)?e.range.push({range:t,entry:i(u)}):e.individual[t]=i(u);t=r.first}}}for(const n of Object.keys(e.individual))for(const[o,l]of t.entries()){if(Array.isArray(l)){const u=r(Number(n),l);if(null===u.both)continue;s(e.individual[n],i(a)),t.splice(o,1,...u.second);break}if(Number(n)===l){s(e.individual[n],i(a));break}}for(const n of t)Array.isArray(n)?e.range.push({range:n,entry:i(a)}):e.individual[n]=i(a)}}function s(e,t){t.lookup&&(!e.lookup||e.lookup.index>t.lookup.index||e.lookup.index===t.lookup.index&&e.lookup.subIndex>t.lookup.subIndex)&&(e.lookup=t.lookup),t.forward&&(e.forward?n(e.forward,t.forward):e.forward=t.forward),t.reverse&&(e.reverse?n(e.reverse,t.reverse):e.reverse=t.reverse)}function o(e,t){const n={first:[],second:[],both:null};if(e[0]<t[1]&&t[0]<e[1]){const s=Math.max(e[0],t[0]),o=Math.min(e[1],t[1]);n.both=a(s,o)}if(e[0]<t[0]){const s=e[0],o=Math.min(t[0],e[1]);n.first.push(a(s,o))}else if(t[0]<e[0]){const s=t[0],o=Math.min(t[1],e[0]);n.second.push(a(s,o))}if(e[1]>t[1]){const s=Math.max(e[0],t[1]),o=e[1];n.first.push(a(s,o))}else if(t[1]>e[1]){const s=Math.max(e[1],t[0]),o=t[1];n.second.push(a(s,o))}return n}function r(e,t){if(e<t[0]||e>t[1])return{first:[e],second:[t],both:null};const n={first:[],second:[],both:e};return t[0]<e&&n.second.push(a(t[0],e)),t[1]>e&&n.second.push(a(e+1,t[1])),n}function a(e,t){return t-e==1?e:[e,t]}function i(e){const t={};return e.forward&&(t.forward=l(e.forward)),e.reverse&&(t.reverse=l(e.reverse)),e.lookup&&(t.lookup={contextRange:e.lookup.contextRange.slice(),index:e.lookup.index,length:e.lookup.length,subIndex:e.lookup.subIndex,substitutions:e.lookup.substitutions.slice()}),t}function l(e){const t={};for(const[n,s]of Object.entries(e.individual))t[n]=i(s);return{individual:t,range:e.range.map((({range:e,entry:t})=>({range:e.slice(),entry:i(t)})))}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){const t={individual:{},range:[]};for(const s of e)n(t,s);return t}},123:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){let s=!1;for(let o=0;o<e.length;o++){const r=e[o];if(s){if(n<=r[0])return e[o-1][1]=n,e;if(n<=r[1])return e[o-1][1]=Math.max(n,r[1]),e.splice(o,1),s=!1,e;e.splice(o,1),o--}else{if(n<=r[0])return e.splice(o,0,[t,n]),e;if(n<=r[1])return r[0]=Math.min(t,r[0]),e;if(!(t<r[1]))continue;r[0]=Math.min(t,r[0]),s=!0}}return s?e[e.length-1][1]=n:e.push([t,n]),e}},664:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(180),o=n(330);t.default=function(e,t,n){const r={individual:{},range:[]},a=s.listGlyphsByIndex(e.coverage);for(const{glyphId:s,index:i}of a){const a=e.chainRuleSets[i];if(a)for(const[e,i]of a.entries()){let a=o.getInputTree(r,i.lookupRecords,t,0,s).map((({entry:e,substitution:t})=>({entry:e,substitutions:[t]})));for(const[e,n]of i.input.entries())a=o.processInputPosition([n],e+1,a,i.lookupRecords,t);for(const e of i.lookahead)a=o.processLookaheadPosition([e],a);for(const e of i.backtrack)a=o.processBacktrackPosition([e],a);for(const{entry:t,substitutions:s}of a)t.lookup={substitutions:s,length:i.input.length+1,index:n,subIndex:e,contextRange:[-1*i.backtrack.length,1+i.input.length+i.lookahead.length]}}}return r}},697:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(68),o=n(180),r=n(477),a=n(330);t.default=function(e,t,n){const i=[],l=o.listGlyphsByIndex(e.coverage);for(const{glyphId:s}of l){const o=r.default(e.inputClassDef,s);for(const[s,l]of o.entries()){if(null===l)continue;const o=e.chainClassSet[l];if(o)for(const[l,u]of o.entries()){const o={individual:{},range:[]};let c=a.getInputTree(o,u.lookupRecords,t,0,s).map((({entry:e,substitution:t})=>({entry:e,substitutions:[t]})));for(const[n,s]of u.input.entries())c=a.processInputPosition(r.listClassGlyphs(e.inputClassDef,s),n+1,c,u.lookupRecords,t);for(const t of u.lookahead)c=a.processLookaheadPosition(r.listClassGlyphs(e.lookaheadClassDef,t),c);for(const t of u.backtrack)c=a.processBacktrackPosition(r.listClassGlyphs(e.backtrackClassDef,t),c);for(const{entry:e,substitutions:t}of c)e.lookup={substitutions:t,index:n,subIndex:l,length:u.input.length+1,contextRange:[-1*u.backtrack.length,1+u.input.length+u.lookahead.length]};i.push(o)}}}return s.default(i)}},634:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(180),o=n(330);t.default=function(e,t,n){const r={individual:{},range:[]},a=s.listGlyphsByIndex(e.inputCoverage[0]);for(const{glyphId:i}of a){let a=o.getInputTree(r,e.lookupRecords,t,0,i).map((({entry:e,substitution:t})=>({entry:e,substitutions:[t]})));for(const[n,r]of e.inputCoverage.slice(1).entries())a=o.processInputPosition(s.listGlyphsByIndex(r).map((e=>e.glyphId)),n+1,a,e.lookupRecords,t);for(const t of e.lookaheadCoverage)a=o.processLookaheadPosition(s.listGlyphsByIndex(t).map((e=>e.glyphId)),a);for(const t of e.backtrackCoverage)a=o.processBacktrackPosition(s.listGlyphsByIndex(t).map((e=>e.glyphId)),a);for(const{entry:t,substitutions:s}of a)t.lookup={substitutions:s,index:n,subIndex:0,length:e.inputCoverage.length,contextRange:[-1*e.backtrackCoverage.length,e.inputCoverage.length+e.lookaheadCoverage.length]}}return r}},842:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(180),o=n(330);t.default=function(e,t){const n={individual:{},range:[]},r=s.listGlyphsByIndex(e.coverage);for(const{glyphId:a,index:i}of r){const r={};Array.isArray(a)?n.range.push({entry:r,range:a}):n.individual[a]=r;let l=[{entry:r,substitutions:[e.substitutes[i]]}];for(const t of e.lookaheadCoverage)l=o.processLookaheadPosition(s.listGlyphsByIndex(t).map((e=>e.glyphId)),l);for(const t of e.backtrackCoverage)l=o.processBacktrackPosition(s.listGlyphsByIndex(t).map((e=>e.glyphId)),l);for(const{entry:n,substitutions:s}of l)n.lookup={substitutions:s,index:t,subIndex:0,length:1,contextRange:[-1*e.backtrackCoverage.length,1+e.lookaheadCoverage.length]}}return n}},477:(e,t)=>{"use strict";function n(e,t){for(const n of e.ranges)if(n.start<=t&&n.end>=t)return n.classId;return null}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return 2===e.format?Array.isArray(t)?function(e,t){let s=t[0],o=n(e,s),r=t[0]+1;const a=new Map;for(;r<t[1];)n(e,r)!==o&&(r-s<=1?a.set(s,o):a.set([s,r],o)),r++;return r-s<=1?a.set(s,o):a.set([s,r],o),a}(e,t):new Map([[t,n(e,t)]]):new Map([[t,null]])},t.listClassGlyphs=function(e,t){if(2===e.format){const n=[];for(const s of e.ranges)s.classId===t&&(s.end===s.start?n.push(s.start):n.push([s.start,s.end+1]));return n}return[]}},180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){switch(e.format){case 1:const n=e.glyphs.indexOf(t);return-1!==n?n:null;case 2:const s=e.ranges.find((e=>e.start<=t&&e.end>=t));return s?s.index:null}},t.listGlyphsByIndex=function(e){switch(e.format){case 1:return e.glyphs.map(((e,t)=>({glyphId:e,index:t})));case 2:let t=[];for(const[n,s]of e.ranges.entries())s.end===s.start?t.push({glyphId:s.start,index:n}):t.push({glyphId:[s.start,s.end+1],index:n});return t}}},330:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(171);function o(e,t,n,o,a){const i=[];if(Array.isArray(a)){const r=function(e,t,n,o){for(const r of e.filter((e=>e.sequenceIndex===n)))for(const e of t[r.lookupListIndex].subtables){const t=s.getRangeSubstitutionGlyphs(e,o);if(!Array.from(t.values()).every((e=>null!==e)))return t}return new Map([[o,null]])}(t,n,o,a);for(const[t,n]of r){const s={};Array.isArray(t)?e.range.push({range:t,entry:s}):e.individual[t]={},i.push({entry:s,substitution:n})}}else e.individual[a]={},i.push({entry:e.individual[a],substitution:r(t,n,o,a)});return i}function r(e,t,n,o){for(const r of e.filter((e=>e.sequenceIndex===n)))for(const e of t[r.lookupListIndex].subtables){const t=s.getIndividualSubstitutionGlyph(e,o);if(null!==t)return t}return null}t.processInputPosition=function(e,t,n,s,r){const a=[];for(const i of n){i.entry.forward={individual:{},range:[]};for(const n of e)a.push(...o(i.entry.forward,s,r,t,n).map((({entry:e,substitution:t})=>({entry:e,substitutions:[...i.substitutions,t]}))))}return a},t.processLookaheadPosition=function(e,t){const n=[];for(const s of t)for(const t of e){const e={};s.entry.forward||(s.entry.forward={individual:{},range:[]}),n.push({entry:e,substitutions:s.substitutions}),Array.isArray(t)?s.entry.forward.range.push({entry:e,range:t}):s.entry.forward.individual[t]=e}return n},t.processBacktrackPosition=function(e,t){const n=[];for(const s of t)for(const t of e){const e={};s.entry.reverse||(s.entry.reverse={individual:{},range:[]}),n.push({entry:e,substitutions:s.substitutions}),Array.isArray(t)?s.entry.reverse.range.push({entry:e,range:t}):s.entry.reverse.individual[t]=e}return n},t.getInputTree=o},171:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(180);function o(e,t){const n=s.default(e.coverage,t);if(null===n)return null;switch(e.substFormat){case 1:return(t+e.deltaGlyphId)%65536;case 2:return null!=e.substitute[n]?e.substitute[n]:null}}t.getRangeSubstitutionGlyphs=function(e,t){let n=t[0],s=o(e,n),r=t[0]+1;const a=new Map;for(;r<t[1];)o(e,r)!==s&&(r-n<=1?a.set(n,s):a.set([n,r],s)),r++;return r-n<=1?a.set(n,s):a.set([n,r],s),a},t.getIndividualSubstitutionGlyph=o},503:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,n,s,o){let r=t[n[o]];if(!r)return;let a=r.lookup;if(r.reverse){const e=function(e,t,n){let s=e[t[--n]],o=s&&s.lookup;for(;s&&((!o&&s.lookup||s.lookup&&o&&o.index>s.lookup.index)&&(o=s.lookup),!(--n<0)&&s.reverse);)s=s.reverse[t[n]];return o}(r.reverse,n,s);(!a&&e||e&&a&&(a.index>e.index||a.index===e.index&&a.subIndex>e.subIndex))&&(a=e)}if(++o>=n.length||!r.forward)return a;const i=e(r.forward,n,s,o);return(!a&&i||i&&a&&(a.index>i.index||a.index===i.index&&a.subIndex>i.subIndex))&&(a=i),a}},987:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(486);var o,r;!function(e){e.Serif="serif",e.SansSerif="sansSerif",e.Monospace="monospace",e.Cursive="cursive",e.Unknown="unknown"}(o=t.Type||(t.Type={})),function(e){e.Regular="regular",e.Italic="italic",e.Oblique="oblique",e.Bold="bold",e.BoldItalic="boldItalic",e.BoldOblique="boldOblique",e.Other="other"}(r=t.Style||(t.Style={}));const a=[" Regular"," Bold"," Bold Italic"," Bold Oblique"," Italic"," Oblique"];function i(e){if(!e.os2&&!e.head)return r.Other;const t=e.os2?32&e.os2.fsSelection:1&e.head.macStyle,n=e.os2?1&e.os2.fsSelection:e.post?e.post.italicAngle<0:2&e.head.macStyle,s=e.os2?512&e.os2.fsSelection:e.post?e.post.italicAngle>0:0,o=e.os2?320&e.os2.fsSelection:1;return t?s?r.BoldOblique:n?r.BoldItalic:r.Bold:s?r.Oblique:n?r.Italic:o?r.Regular:r.Other}t.name=function(e,t){const n=e.names.preferredFamily&&e.names.preferredFamily[t]?e.names.preferredFamily[t]:e.names.fontFamily[t];if("win32"===s.platform()){const s=`${n} ${e.names.preferredSubfamily&&e.names.preferredSubfamily[t]?e.names.preferredSubfamily[t]:e.names.fontSubfamily[t]}`;let o=-1;for(const e of a){const t=s.lastIndexOf(e);if(-1!==t){o=t;break}}return-1!==o?s.substring(0,o):s}return n},t.type=function(e){if(e.os2)switch(e.os2.panose[0]){case 2:return 9===e.os2.panose[3]?o.Monospace:e.os2.panose[1]>=11&&e.os2.panose[1]<=15||0===e.os2.panose[1]?o.SansSerif:o.Serif;case 3:return o.Cursive}else if(e.post&&e.post.isFixedPitch)return o.Monospace;return o.Unknown},t.style=i;const l=[r.Bold,r.BoldItalic,r.BoldOblique];t.weight=function(e){return e.os2?e.os2.usWeightClass:l.includes(i(e))?700:400}},734:function(e,t,n){"use strict";var s=this&&this.__rest||function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(s=Object.getOwnPropertySymbols(e);o<s.length;o++)t.indexOf(s[o])<0&&(n[s[o]]=e[s[o]])}return n};Object.defineProperty(t,"__esModule",{value:!0});const o=n(877),r=n(533),a=n(987);var i=n(987);async function l(e){const t=Object.assign({concurrency:4,language:"en"},e),n=await o.default({extensions:["ttf","otf"]}),a=await async function(e,n,s){const o=[];let a=0;const i=async e=>{o.push(await(async e=>{try{return u(e,await r.default(e),t.language)}catch(e){if(["TypeError","SyntaxError","ReferenceError","RangeError","AssertionError"].includes(e.name))throw e}})(n[e])),a<n.length&&await i(a++)},l=[];for(;a<n.length&&a<s;a++)l.push(i(a));return await Promise.all(l),o}(0,n,t.concurrency),i={};for(let e of a.filter((e=>e))){const{name:t}=e,n=s(e,["name"]);i[t]||(i[t]=[]),i[t].push(n)}return i}function u(e,t,n){return{name:a.name(t,n),path:e,type:a.type(t),weight:a.weight(t),style:a.style(t)}}t.Type=i.Type,t.Style=i.Style,t.list=l,t.listVariants=async function(e,t){return(await l(t))[e]||[]},t.get=async function(e,t){const n=Object.assign({language:"en"},t);return u(e,await r.default(e),n.language)}},533:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(383),o=n(596),r=n(967),a=n(0),i=n(614),l=n(898),u=n(870);var c;!function(e){e[e.TrueType=0]="TrueType",e[e.CFF=1]="CFF",e[e.Woff=2]="Woff"}(c||(c={}));const p={name:{tag:Buffer.from("name"),parse:r.default},ltag:{tag:Buffer.from("ltag"),parse:a.default},os2:{tag:Buffer.from("OS/2"),parse:i.default},head:{tag:Buffer.from("head"),parse:l.default},post:{tag:Buffer.from("post"),parse:u.default}};t.default=async function(e){return new Promise(((t,n)=>{(async()=>{const t=o.default(),r=s.createReadStream(e);let a=!1;const i=()=>{a=!0};r.once("close",i),r.once("end",i),r.once("error",(e=>{a=!0,n(e)})),r.pipe(t);try{switch(function(e){if(e.equals(f.one)||e.equals(f.true)||e.equals(f.typ1))return c.TrueType;if(e.equals(f.otto))return c.CFF;if(e.equals(f.woff))return c.Woff;throw new Error(`Unsupported signature type: ${e}`)}(await t.read(4))){case c.TrueType:case c.CFF:const n=(await t.read(2)).readUInt16BE(0);await t.skip(6);const s=await async function(e,t){const n={};for(let s=0;s<t;s++){const t=await e.read(4),s=await e.read(12);for(const[e,o]of Object.entries(p))if(t.equals(o.tag)&&(n[e]={offset:s.readUInt32BE(4),length:s.readUInt32BE(8)},n.name&&n.ltag&&n.os2))return n}return n}(t,n),o=Object.entries(s).sort(((e,t)=>e[1].offset-t[1].offset)),r={};for(const[e,n]of o)await t.skip(n.offset-t.offset),r[e]=await t.read(n.length);let a=[];if(r.ltag&&(a=p.ltag.parse(r.ltag)),!r.name)throw new Error(`missing required OpenType table 'name' in font file: ${e}`);return{names:p.name.parse(r.name,a),os2:r.os2&&p.os2.parse(r.os2),head:r.head&&p.head.parse(r.head),post:r.post&&p.post.parse(r.post)};case c.Woff:default:throw new Error("provided font type is not supported yet")}}finally{r.unpipe(t),a||(r.destroy(),t.destroy())}})().then(t,n)}))};const f={one:Buffer.from([0,1,0,0]),otto:Buffer.from("OTTO"),true:Buffer.from("true"),typ1:Buffer.from("typ1"),woff:Buffer.from("wOFF")}},898:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(970);t.default=function(e){return{version:s.formatFixed(e.readUInt16BE(0),e.readUInt16BE(2)),fontRevision:s.formatFixed(e.readUInt16BE(4),e.readUInt16BE(6)),checkSumAdjustment:e.readUInt32BE(8),magicNumber:e.readUInt32BE(12),flags:e.readUInt16BE(16),unitsPerEm:e.readUInt16BE(18),created:s.formatLongDateTime(e.readUInt32BE(20),e.readUInt32BE(24)),modified:s.formatLongDateTime(e.readUInt32BE(28),e.readUInt32BE(32)),xMin:e.readInt16BE(36),yMin:e.readInt16BE(38),xMax:e.readInt16BE(40),yMax:e.readInt16BE(42),macStyle:e.readUInt16BE(44),lowestRecPPEM:e.readUInt16BE(46),fontDirectionHint:e.readInt16BE(48),indexToLocFormat:e.readInt16BE(50),glyphDataFormat:e.readInt16BE(52)}}},0:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(1!==e.readUInt32BE(0))throw new Error("Unsupported ltag table version.");const t=e.readUInt32BE(8),n=[];for(let s=0;s<t;s++){let t="";const o=e.readUInt16BE(12+4*s),r=e.readUInt16BE(14+4*s);for(let n=o;n<o+r;++n)t+=String.fromCharCode(e.readInt8(n));n.push(t)}return n}},967:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=["copyright","fontFamily","fontSubfamily","uniqueID","fullName","version","postScriptName","trademark","manufacturer","designer","description","manufacturerURL","designerURL","license","licenseURL","reserved","preferredFamily","preferredSubfamily","compatibleFullName","sampleText","postScriptFindFontName","wwsFamily","wwsSubfamily"],s={0:"en",1:"fr",2:"de",3:"it",4:"nl",5:"sv",6:"es",7:"da",8:"pt",9:"no",10:"he",11:"ja",12:"ar",13:"fi",14:"el",15:"is",16:"mt",17:"tr",18:"hr",19:"zh-Hant",20:"ur",21:"hi",22:"th",23:"ko",24:"lt",25:"pl",26:"hu",27:"es",28:"lv",29:"se",30:"fo",31:"fa",32:"ru",33:"zh",34:"nl-BE",35:"ga",36:"sq",37:"ro",38:"cz",39:"sk",40:"si",41:"yi",42:"sr",43:"mk",44:"bg",45:"uk",46:"be",47:"uz",48:"kk",49:"az-Cyrl",50:"az-Arab",51:"hy",52:"ka",53:"mo",54:"ky",55:"tg",56:"tk",57:"mn-CN",58:"mn",59:"ps",60:"ks",61:"ku",62:"sd",63:"bo",64:"ne",65:"sa",66:"mr",67:"bn",68:"as",69:"gu",70:"pa",71:"or",72:"ml",73:"kn",74:"ta",75:"te",76:"si",77:"my",78:"km",79:"lo",80:"vi",81:"id",82:"tl",83:"ms",84:"ms-Arab",85:"am",86:"ti",87:"om",88:"so",89:"sw",90:"rw",91:"rn",92:"ny",93:"mg",94:"eo",128:"cy",129:"eu",130:"ca",131:"la",132:"qu",133:"gn",134:"ay",135:"tt",136:"ug",137:"dz",138:"jv",139:"su",140:"gl",141:"af",142:"br",143:"iu",144:"gd",145:"gv",146:"ga",147:"to",148:"el-polyton",149:"kl",150:"az",151:"nn"},o={1078:"af",1052:"sq",1156:"gsw",1118:"am",5121:"ar-DZ",15361:"ar-BH",3073:"ar",2049:"ar-IQ",11265:"ar-JO",13313:"ar-KW",12289:"ar-LB",4097:"ar-LY",6145:"ary",8193:"ar-OM",16385:"ar-QA",1025:"ar-SA",10241:"ar-SY",7169:"aeb",14337:"ar-AE",9217:"ar-YE",1067:"hy",1101:"as",2092:"az-Cyrl",1068:"az",1133:"ba",1069:"eu",1059:"be",2117:"bn",1093:"bn-IN",8218:"bs-Cyrl",5146:"bs",1150:"br",1026:"bg",1027:"ca",3076:"zh-HK",5124:"zh-MO",2052:"zh",4100:"zh-SG",1028:"zh-TW",1155:"co",1050:"hr",4122:"hr-BA",1029:"cs",1030:"da",1164:"prs",1125:"dv",2067:"nl-BE",1043:"nl",3081:"en-AU",10249:"en-BZ",4105:"en-CA",9225:"en-029",16393:"en-IN",6153:"en-IE",8201:"en-JM",17417:"en-MY",5129:"en-NZ",13321:"en-PH",18441:"en-SG",7177:"en-ZA",11273:"en-TT",2057:"en-GB",1033:"en",12297:"en-ZW",1061:"et",1080:"fo",1124:"fil",1035:"fi",2060:"fr-BE",3084:"fr-CA",1036:"fr",5132:"fr-LU",6156:"fr-MC",4108:"fr-CH",1122:"fy",1110:"gl",1079:"ka",3079:"de-AT",1031:"de",5127:"de-LI",4103:"de-LU",2055:"de-CH",1032:"el",1135:"kl",1095:"gu",1128:"ha",1037:"he",1081:"hi",1038:"hu",1039:"is",1136:"ig",1057:"id",1117:"iu",2141:"iu-Latn",2108:"ga",1076:"xh",1077:"zu",1040:"it",2064:"it-CH",1041:"ja",1099:"kn",1087:"kk",1107:"km",1158:"quc",1159:"rw",1089:"sw",1111:"kok",1042:"ko",1088:"ky",1108:"lo",1062:"lv",1063:"lt",2094:"dsb",1134:"lb",1071:"mk",2110:"ms-BN",1086:"ms",1100:"ml",1082:"mt",1153:"mi",1146:"arn",1102:"mr",1148:"moh",1104:"mn",2128:"mn-CN",1121:"ne",1044:"nb",2068:"nn",1154:"oc",1096:"or",1123:"ps",1045:"pl",1046:"pt",2070:"pt-PT",1094:"pa",1131:"qu-BO",2155:"qu-EC",3179:"qu",1048:"ro",1047:"rm",1049:"ru",9275:"smn",4155:"smj-NO",5179:"smj",3131:"se-FI",1083:"se",2107:"se-SE",8251:"sms",6203:"sma-NO",7227:"sms",1103:"sa",7194:"sr-Cyrl-BA",3098:"sr",6170:"sr-Latn-BA",2074:"sr-Latn",1132:"nso",1074:"tn",1115:"si",1051:"sk",1060:"sl",11274:"es-AR",16394:"es-BO",13322:"es-CL",9226:"es-CO",5130:"es-CR",7178:"es-DO",12298:"es-EC",17418:"es-SV",4106:"es-GT",18442:"es-HN",2058:"es-MX",19466:"es-NI",6154:"es-PA",15370:"es-PY",10250:"es-PE",20490:"es-PR",3082:"es",1034:"es",21514:"es-US",14346:"es-UY",8202:"es-VE",2077:"sv-FI",1053:"sv",1114:"syr",1064:"tg",2143:"tzm",1097:"ta",1092:"tt",1098:"te",1054:"th",1105:"bo",1055:"tr",1090:"tk",1152:"ug",1058:"uk",1070:"hsb",1056:"ur",2115:"uz-Cyrl",1091:"uz",1066:"vi",1106:"cy",1160:"wo",1157:"sah",1144:"ii",1130:"yo"};function r(e,t,n){switch(e){case 0:if(65535===t)return"und";if(n)return n[t];break;case 1:return s[t];case 3:return o[t]}}const a="utf-16",i={0:"macintosh",1:"x-mac-japanese",2:"x-mac-chinesetrad",3:"x-mac-korean",6:"x-mac-greek",7:"x-mac-cyrillic",9:"x-mac-devanagai",10:"x-mac-gurmukhi",11:"x-mac-gujarati",12:"x-mac-oriya",13:"x-mac-bengali",14:"x-mac-tamil",15:"x-mac-telugu",16:"x-mac-kannada",17:"x-mac-malayalam",18:"x-mac-sinhalese",19:"x-mac-burmese",20:"x-mac-khmer",21:"x-mac-thai",22:"x-mac-lao",23:"x-mac-georgian",24:"x-mac-armenian",25:"x-mac-chinesesimp",26:"x-mac-tibetan",27:"x-mac-mongolian",28:"x-mac-ethiopic",29:"x-mac-ce",30:"x-mac-vietnamese",31:"x-mac-extarabic"},l={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"};function u(e,t,n){switch(e){case 0:return a;case 1:return l[n]||i[t];case 3:if(1===t||10===t)return a}}t.default=function(e,t){const s={},o=e.readUInt16BE(2),i=e.readUInt16BE(4);let l=6;for(let c=0;c<o;c++){const o=e.readUInt16BE(l+0),c=e.readUInt16BE(l+2),f=e.readUInt16BE(l+4),h=e.readUInt16BE(l+6),d=n[h]||String(h),g=e.readUInt16BE(l+8),m=e.readUInt16BE(l+10),y=r(o,f,t),v=u(o,c,f);if(l+=12,void 0!==v&&void 0!==y){let t;if(v===a){const n=g/2,s=Array(n);for(let t=0;t<n;t++)s[t]=e.readUInt16BE(i+m+2*t);t=String.fromCharCode(...s)}else t=p(e,i+m,g,v);if(t){let e=s[d];void 0===e&&(e=s[d]={}),e[y]=t}}}return s};const c={"x-mac-croatian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊©⁄€‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ","x-mac-cyrillic":"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю","x-mac-gaelic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØḂ±≤≥ḃĊċḊḋḞḟĠġṀæøṁṖṗɼƒſṠ«»… ÀÃÕŒœ–—“”‘’ṡẛÿŸṪ€‹›Ŷŷṫ·Ỳỳ⁊ÂÊÁËÈÍÎÏÌÓÔ♣ÒÚÛÙıÝýŴŵẄẅẀẁẂẃ","x-mac-greek":"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦€ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ­","x-mac-icelandic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ°ᒡᒥᒦ•¶ᒧ®©™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ",macintosh:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-romanian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂȘ∞±≤≥¥µ∂∑∏π∫ªºΩăș¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›Țț‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-turkish":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙˆ˜¯˘˙˚¸˝˛ˇ"};function p(e,t,n,s){const o=c[s];if(void 0===o)return;let r="";for(let s=0;s<n;s++){const n=e.readUInt8(t+s);r+=n<=127?String.fromCharCode(n):o[127&n]}return r}},614:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){const t={version:e.readUInt16BE(0),xAvgCharWidth:e.readUInt16BE(2),usWeightClass:e.readUInt16BE(4),usWidthClass:e.readUInt16BE(6),fsType:e.readUInt16BE(8),ySubscriptXSize:e.readInt16BE(10),ySubscriptYSize:e.readInt16BE(12),ySubscriptXOffset:e.readInt16BE(14),ySubscriptYOffset:e.readInt16BE(16),ySuperscriptXSize:e.readInt16BE(18),ySuperscriptYSize:e.readInt16BE(20),ySuperscriptXOffset:e.readInt16BE(22),ySuperscriptYOffset:e.readInt16BE(24),yStrikeoutSize:e.readInt16BE(26),yStrikeoutPosition:e.readInt16BE(28),sFamilyClass:e.readInt16BE(30),panose:[e.readUInt8(32),e.readUInt8(33),e.readUInt8(34),e.readUInt8(35),e.readUInt8(36),e.readUInt8(37),e.readUInt8(38),e.readUInt8(39),e.readUInt8(40),e.readUInt8(41)],ulUnicodeRange1:e.readUInt32BE(42),ulUnicodeRange2:e.readUInt32BE(46),ulUnicodeRange3:e.readUInt32BE(50),ulUnicodeRange4:e.readUInt32BE(54),achVendID:String.fromCharCode(e.readUInt8(58),e.readUInt8(59),e.readUInt8(60),e.readUInt8(61)),fsSelection:e.readUInt16BE(62),usFirstCharIndex:e.readUInt16BE(64),usLastCharIndex:e.readUInt16BE(66),sTypoAscender:e.readInt16BE(68),sTypoDescender:e.readInt16BE(70),sTypoLineGap:e.readInt16BE(72),usWinAscent:e.readUInt16BE(74),usWinDescent:e.readUInt16BE(76)};return t.version>=1&&(t.ulCodePageRange1=e.readUInt32BE(78),t.ulCodePageRange2=e.readUInt32BE(82)),t.version>=2&&(t.sxHeight=e.readInt16BE(86),t.sCapHeight=e.readInt16BE(88),t.usDefaultChar=e.readUInt16BE(90),t.usBreakChar=e.readUInt16BE(92),t.usMaxContent=e.readUInt16BE(94)),t}},870:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(970);t.default=function(e){return{version:s.formatFixed(e.readUInt16BE(0),e.readUInt16BE(2)),italicAngle:s.formatFixed(e.readUInt16BE(4),e.readUInt16BE(6)),underlinePosition:e.readInt16BE(8),underlineThickness:e.readInt16BE(10),isFixedPitch:e.readUInt32BE(12),minMemType42:e.readUInt32BE(16),maxMemType42:e.readUInt32BE(20),minMemType1:e.readUInt32BE(24),maxMemType1:e.readUInt32BE(28)}}},970:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatFixed=function(e,t){return e+t/65536},t.formatLongDateTime=function(e,t){return 1e3*(e*2**32+t-2082844800)}},877:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(3),o=n(621),r=n(696),a={win32:()=>process.env.WINDIR?[s.join(process.env.WINDIR,"Fonts")]:["C:\\Windows\\Fonts"],darwin:()=>{const e=o.homedir();return[...e?[s.join(e,"/Library/Fonts")]:[],"/Library/Fonts","/Network/Library/Fonts","/System/Library/Fonts","/System Folder/Fonts"]},linux:()=>{const e=o.homedir();return["/usr/share/fonts","/usr/local/share/fonts",...e?[s.join(e,".fonts"),s.join(e,".local/share/fonts")]:[]]}};function i(e){const t=Object.assign({extensions:["ttf","otf","ttc","woff","woff2"],additionalFolders:[]},e),n=o.platform(),s=a[n];if(!s)throw new Error(`Unsupported platform: ${n}`);const i=s();return r.default([...i,...t.additionalFolders],t.extensions)}e.exports=Object.assign(i,{default:i}),t.default=i},696:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(383),o=n(460),r=n(3),a=o.promisify(s.readdir),i=o.promisify(s.stat);async function l(e,t,n=10){if(n<=0)return[];let s;try{s=await a(e)}catch(e){return[]}const o=[];return await Promise.all(s.map((async s=>{const a=r.join(e,s);let u;try{u=await i(a)}catch(e){return}u.isFile()&&t.test(a)?o.push(a):u.isDirectory()&&o.push(...await l(a,t,n-1))}))),o}t.default=async function(e,t){const n=new Set;return await Promise.all(e.map((async e=>{const s=await l(r.resolve(e),new RegExp(`\\.${t.map((e=>`(?:${e})`)).join("|")}$`));for(const e of s)n.add(e)}))),[...n]}},749:(e,t,n)=>{"use strict";const s=n(799),o=Symbol("max"),r=Symbol("length"),a=Symbol("lengthCalculator"),i=Symbol("allowStale"),l=Symbol("maxAge"),u=Symbol("dispose"),c=Symbol("noDisposeOnSet"),p=Symbol("lruList"),f=Symbol("cache"),h=Symbol("updateAgeOnGet"),d=()=>1,g=(e,t,n)=>{const s=e[f].get(t);if(s){const t=s.value;if(m(e,t)){if(v(e,s),!e[i])return}else n&&(e[h]&&(s.value.now=Date.now()),e[p].unshiftNode(s));return t.value}},m=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[l]&&n>e[l]},y=e=>{if(e[r]>e[o])for(let t=e[p].tail;e[r]>e[o]&&null!==t;){const n=t.prev;v(e,t),t=n}},v=(e,t)=>{if(t){const n=t.value;e[u]&&e[u](n.key,n.value),e[r]-=n.length,e[f].delete(n.key),e[p].removeNode(t)}};class b{constructor(e,t,n,s,o){this.key=e,this.value=t,this.length=n,this.now=s,this.maxAge=o||0}}const x=(e,t,n,s)=>{let o=n.value;m(e,o)&&(v(e,n),e[i]||(o=void 0)),o&&t.call(s,o.value,o.key,e)};e.exports=class{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw new TypeError("max must be a non-negative number");this[o]=e.max||1/0;const t=e.length||d;if(this[a]="function"!=typeof t?d:t,this[i]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw new TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[u]=e.dispose,this[c]=e.noDisposeOnSet||!1,this[h]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[o]=e||1/0,y(this)}get max(){return this[o]}set allowStale(e){this[i]=!!e}get allowStale(){return this[i]}set maxAge(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[l]=e,y(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=d),e!==this[a]&&(this[a]=e,this[r]=0,this[p].forEach((e=>{e.length=this[a](e.value,e.key),this[r]+=e.length}))),y(this)}get lengthCalculator(){return this[a]}get length(){return this[r]}get itemCount(){return this[p].length}rforEach(e,t){t=t||this;for(let n=this[p].tail;null!==n;){const s=n.prev;x(this,e,n,t),n=s}}forEach(e,t){t=t||this;for(let n=this[p].head;null!==n;){const s=n.next;x(this,e,n,t),n=s}}keys(){return this[p].toArray().map((e=>e.key))}values(){return this[p].toArray().map((e=>e.value))}reset(){this[u]&&this[p]&&this[p].length&&this[p].forEach((e=>this[u](e.key,e.value))),this[f]=new Map,this[p]=new s,this[r]=0}dump(){return this[p].map((e=>!m(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)})).toArray().filter((e=>e))}dumpLru(){return this[p]}set(e,t,n){if((n=n||this[l])&&"number"!=typeof n)throw new TypeError("maxAge must be a number");const s=n?Date.now():0,i=this[a](t,e);if(this[f].has(e)){if(i>this[o])return v(this,this[f].get(e)),!1;const a=this[f].get(e).value;return this[u]&&(this[c]||this[u](e,a.value)),a.now=s,a.maxAge=n,a.value=t,this[r]+=i-a.length,a.length=i,this.get(e),y(this),!0}const h=new b(e,t,i,s,n);return h.length>this[o]?(this[u]&&this[u](e,t),!1):(this[r]+=h.length,this[p].unshift(h),this[f].set(e,this[p].head),y(this),!0)}has(e){if(!this[f].has(e))return!1;const t=this[f].get(e).value;return!m(this,t)}get(e){return g(this,e,!0)}peek(e){return g(this,e,!1)}pop(){const e=this[p].tail;return e?(v(this,e),e.value):null}del(e){v(this,this[f].get(e))}load(e){this.reset();const t=Date.now();for(let n=e.length-1;n>=0;n--){const s=e[n],o=s.e||0;if(0===o)this.set(s.k,s.v);else{const e=o-t;e>0&&this.set(s.k,s.v,e)}}}prune(){this[f].forEach(((e,t)=>g(this,t,!1)))}}},386:(e,t,n)=>{"use strict";n.r(t),n.d(t,{BoundingBox:()=>i,Font:()=>gn,Glyph:()=>ie,Path:()=>u,_parse:()=>z,load:()=>Cn,loadSync:()=>Dn,parse:()=>Bn});var s=n(741),o=n.n(s);function r(e,t,n,s,o){return Math.pow(1-o,3)*e+3*Math.pow(1-o,2)*o*t+3*(1-o)*Math.pow(o,2)*n+Math.pow(o,3)*s}function a(){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN}a.prototype.isEmpty=function(){return isNaN(this.x1)||isNaN(this.y1)||isNaN(this.x2)||isNaN(this.y2)},a.prototype.addPoint=function(e,t){"number"==typeof e&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),"number"==typeof t&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=t,this.y2=t),t<this.y1&&(this.y1=t),t>this.y2&&(this.y2=t))},a.prototype.addX=function(e){this.addPoint(e,null)},a.prototype.addY=function(e){this.addPoint(null,e)},a.prototype.addBezier=function(e,t,n,s,o,a,i,l){const u=[e,t],c=[n,s],p=[o,a],f=[i,l];this.addPoint(e,t),this.addPoint(i,l);for(let e=0;e<=1;e++){const t=6*u[e]-12*c[e]+6*p[e],n=-3*u[e]+9*c[e]-9*p[e]+3*f[e],s=3*c[e]-3*u[e];if(0===n){if(0===t)continue;const n=-s/t;0<n&&n<1&&(0===e&&this.addX(r(u[e],c[e],p[e],f[e],n)),1===e&&this.addY(r(u[e],c[e],p[e],f[e],n)));continue}const o=Math.pow(t,2)-4*s*n;if(o<0)continue;const a=(-t+Math.sqrt(o))/(2*n);0<a&&a<1&&(0===e&&this.addX(r(u[e],c[e],p[e],f[e],a)),1===e&&this.addY(r(u[e],c[e],p[e],f[e],a)));const i=(-t-Math.sqrt(o))/(2*n);0<i&&i<1&&(0===e&&this.addX(r(u[e],c[e],p[e],f[e],i)),1===e&&this.addY(r(u[e],c[e],p[e],f[e],i)))}},a.prototype.addQuad=function(e,t,n,s,o,r){const a=e+2/3*(n-e),i=t+2/3*(s-t),l=a+1/3*(o-e),u=i+1/3*(r-t);this.addBezier(e,t,a,i,l,u,o,r)};const i=a;function l(){this.commands=[],this.fill="black",this.stroke=null,this.strokeWidth=1}l.prototype.moveTo=function(e,t){this.commands.push({type:"M",x:e,y:t})},l.prototype.lineTo=function(e,t){this.commands.push({type:"L",x:e,y:t})},l.prototype.curveTo=l.prototype.bezierCurveTo=function(e,t,n,s,o,r){this.commands.push({type:"C",x1:e,y1:t,x2:n,y2:s,x:o,y:r})},l.prototype.quadTo=l.prototype.quadraticCurveTo=function(e,t,n,s){this.commands.push({type:"Q",x1:e,y1:t,x:n,y:s})},l.prototype.close=l.prototype.closePath=function(){this.commands.push({type:"Z"})},l.prototype.extend=function(e){if(e.commands)e=e.commands;else if(e instanceof i){const t=e;return this.moveTo(t.x1,t.y1),this.lineTo(t.x2,t.y1),this.lineTo(t.x2,t.y2),this.lineTo(t.x1,t.y2),void this.close()}Array.prototype.push.apply(this.commands,e)},l.prototype.getBoundingBox=function(){const e=new i;let t=0,n=0,s=0,o=0;for(let r=0;r<this.commands.length;r++){const a=this.commands[r];switch(a.type){case"M":e.addPoint(a.x,a.y),t=s=a.x,n=o=a.y;break;case"L":e.addPoint(a.x,a.y),s=a.x,o=a.y;break;case"Q":e.addQuad(s,o,a.x1,a.y1,a.x,a.y),s=a.x,o=a.y;break;case"C":e.addBezier(s,o,a.x1,a.y1,a.x2,a.y2,a.x,a.y),s=a.x,o=a.y;break;case"Z":s=t,o=n;break;default:throw new Error("Unexpected path command "+a.type)}}return e.isEmpty()&&e.addPoint(0,0),e},l.prototype.draw=function(e){e.beginPath();for(let t=0;t<this.commands.length;t+=1){const n=this.commands[t];"M"===n.type?e.moveTo(n.x,n.y):"L"===n.type?e.lineTo(n.x,n.y):"C"===n.type?e.bezierCurveTo(n.x1,n.y1,n.x2,n.y2,n.x,n.y):"Q"===n.type?e.quadraticCurveTo(n.x1,n.y1,n.x,n.y):"Z"===n.type&&e.closePath()}this.fill&&(e.fillStyle=this.fill,e.fill()),this.stroke&&(e.strokeStyle=this.stroke,e.lineWidth=this.strokeWidth,e.stroke())},l.prototype.toPathData=function(e){function t(t){return Math.round(t)===t?""+Math.round(t):t.toFixed(e)}function n(){let e="";for(let n=0;n<arguments.length;n+=1){const s=arguments[n];s>=0&&n>0&&(e+=" "),e+=t(s)}return e}e=void 0!==e?e:2;let s="";for(let e=0;e<this.commands.length;e+=1){const t=this.commands[e];"M"===t.type?s+="M"+n(t.x,t.y):"L"===t.type?s+="L"+n(t.x,t.y):"C"===t.type?s+="C"+n(t.x1,t.y1,t.x2,t.y2,t.x,t.y):"Q"===t.type?s+="Q"+n(t.x1,t.y1,t.x,t.y):"Z"===t.type&&(s+="Z")}return s},l.prototype.toSVG=function(e){let t='<path d="';return t+=this.toPathData(e),t+='"',this.fill&&"black"!==this.fill&&(null===this.fill?t+=' fill="none"':t+=' fill="'+this.fill+'"'),this.stroke&&(t+=' stroke="'+this.stroke+'" stroke-width="'+this.strokeWidth+'"'),t+="/>",t},l.prototype.toDOMElement=function(e){const t=this.toPathData(e),n=document.createElementNS("http://www.w3.org/2000/svg","path");return n.setAttribute("d",t),n};const u=l;function c(e){throw new Error(e)}function p(e,t){e||c(t)}const f={fail:c,argument:p,assert:p},h=2147483648,d={},g={},m={};function y(e){return function(){return e}}g.BYTE=function(e){return f.argument(e>=0&&e<=255,"Byte value should be between 0 and 255."),[e]},m.BYTE=y(1),g.CHAR=function(e){return[e.charCodeAt(0)]},m.CHAR=y(1),g.CHARARRAY=function(e){const t=[];for(let n=0;n<e.length;n+=1)t[n]=e.charCodeAt(n);return t},m.CHARARRAY=function(e){return e.length},g.USHORT=function(e){return[e>>8&255,255&e]},m.USHORT=y(2),g.SHORT=function(e){return e>=32768&&(e=-(65536-e)),[e>>8&255,255&e]},m.SHORT=y(2),g.UINT24=function(e){return[e>>16&255,e>>8&255,255&e]},m.UINT24=y(3),g.ULONG=function(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]},m.ULONG=y(4),g.LONG=function(e){return e>=h&&(e=-(2*h-e)),[e>>24&255,e>>16&255,e>>8&255,255&e]},m.LONG=y(4),g.FIXED=g.ULONG,m.FIXED=m.ULONG,g.FWORD=g.SHORT,m.FWORD=m.SHORT,g.UFWORD=g.USHORT,m.UFWORD=m.USHORT,g.LONGDATETIME=function(e){return[0,0,0,0,e>>24&255,e>>16&255,e>>8&255,255&e]},m.LONGDATETIME=y(8),g.TAG=function(e){return f.argument(4===e.length,"Tag should be exactly 4 ASCII characters."),[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]},m.TAG=y(4),g.Card8=g.BYTE,m.Card8=m.BYTE,g.Card16=g.USHORT,m.Card16=m.USHORT,g.OffSize=g.BYTE,m.OffSize=m.BYTE,g.SID=g.USHORT,m.SID=m.USHORT,g.NUMBER=function(e){return e>=-107&&e<=107?[e+139]:e>=108&&e<=1131?[247+((e-=108)>>8),255&e]:e>=-1131&&e<=-108?[251+((e=-e-108)>>8),255&e]:e>=-32768&&e<=32767?g.NUMBER16(e):g.NUMBER32(e)},m.NUMBER=function(e){return g.NUMBER(e).length},g.NUMBER16=function(e){return[28,e>>8&255,255&e]},m.NUMBER16=y(3),g.NUMBER32=function(e){return[29,e>>24&255,e>>16&255,e>>8&255,255&e]},m.NUMBER32=y(5),g.REAL=function(e){let t=e.toString();const n=/\.(\d*?)(?:9{5,20}|0{5,20})\d{0,2}(?:e(.+)|$)/.exec(t);if(n){const s=parseFloat("1e"+((n[2]?+n[2]:0)+n[1].length));t=(Math.round(e*s)/s).toString()}let s="";for(let e=0,n=t.length;e<n;e+=1){const n=t[e];s+="e"===n?"-"===t[++e]?"c":"b":"."===n?"a":"-"===n?"e":n}s+=1&s.length?"f":"ff";const o=[30];for(let e=0,t=s.length;e<t;e+=2)o.push(parseInt(s.substr(e,2),16));return o},m.REAL=function(e){return g.REAL(e).length},g.NAME=g.CHARARRAY,m.NAME=m.CHARARRAY,g.STRING=g.CHARARRAY,m.STRING=m.CHARARRAY,d.UTF8=function(e,t,n){const s=[],o=n;for(let n=0;n<o;n++,t+=1)s[n]=e.getUint8(t);return String.fromCharCode.apply(null,s)},d.UTF16=function(e,t,n){const s=[],o=n/2;for(let n=0;n<o;n++,t+=2)s[n]=e.getUint16(t);return String.fromCharCode.apply(null,s)},g.UTF16=function(e){const t=[];for(let n=0;n<e.length;n+=1){const s=e.charCodeAt(n);t[t.length]=s>>8&255,t[t.length]=255&s}return t},m.UTF16=function(e){return 2*e.length};const v={"x-mac-croatian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊©⁄€‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ","x-mac-cyrillic":"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю","x-mac-gaelic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØḂ±≤≥ḃĊċḊḋḞḟĠġṀæøṁṖṗɼƒſṠ«»… ÀÃÕŒœ–—“”‘’ṡẛÿŸṪ€‹›Ŷŷṫ·Ỳỳ⁊ÂÊÁËÈÍÎÏÌÓÔ♣ÒÚÛÙıÝýŴŵẄẅẀẁẂẃ","x-mac-greek":"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦€ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ­","x-mac-icelandic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ°ᒡᒥᒦ•¶ᒧ®©™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ",macintosh:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-romanian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂȘ∞±≤≥¥µ∂∑∏π∫ªºΩăș¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›Țț‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-turkish":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙˆ˜¯˘˙˚¸˝˛ˇ"};d.MACSTRING=function(e,t,n,s){const o=v[s];if(void 0===o)return;let r="";for(let s=0;s<n;s++){const n=e.getUint8(t+s);r+=n<=127?String.fromCharCode(n):o[127&n]}return r};const b="function"==typeof WeakMap&&new WeakMap;let x;function S(e){return e>=-128&&e<=127}function U(e,t,n){let s=0;const o=e.length;for(;t<o&&s<64&&0===e[t];)++t,++s;return n.push(128|s-1),t}function k(e,t,n){let s=0;const o=e.length;let r=t;for(;r<o&&s<64;){const t=e[r];if(!S(t))break;if(0===t&&r+1<o&&0===e[r+1])break;++r,++s}n.push(s-1);for(let s=t;s<r;++s)n.push(e[s]+256&255);return r}function T(e,t,n){let s=0;const o=e.length;let r=t;for(;r<o&&s<64;){const t=e[r];if(0===t)break;if(S(t)&&r+1<o&&S(e[r+1]))break;++r,++s}n.push(64|s-1);for(let s=t;s<r;++s){const t=e[s];n.push(t+65536>>8&255,t+256&255)}return r}g.MACSTRING=function(e,t){const n=function(e){if(!x){x={};for(let e in v)x[e]=new String(e)}const t=x[e];if(void 0===t)return;if(b){const e=b.get(t);if(void 0!==e)return e}const n=v[e];if(void 0===n)return;const s={};for(let e=0;e<n.length;e++)s[n.charCodeAt(e)]=e+128;return b&&b.set(t,s),s}(t);if(void 0===n)return;const s=[];for(let t=0;t<e.length;t++){let o=e.charCodeAt(t);if(o>=128&&(o=n[o],void 0===o))return;s[t]=o}return s},m.MACSTRING=function(e,t){const n=g.MACSTRING(e,t);return void 0!==n?n.length:0},g.VARDELTAS=function(e){let t=0;const n=[];for(;t<e.length;){const s=e[t];t=0===s?U(e,t,n):s>=-128&&s<=127?k(e,t,n):T(e,t,n)}return n},g.INDEX=function(e){let t=1;const n=[t],s=[];for(let o=0;o<e.length;o+=1){const r=g.OBJECT(e[o]);Array.prototype.push.apply(s,r),t+=r.length,n.push(t)}if(0===s.length)return[0,0];const o=[],r=1+Math.floor(Math.log(t)/Math.log(2))/8|0,a=[void 0,g.BYTE,g.USHORT,g.UINT24,g.ULONG][r];for(let e=0;e<n.length;e+=1){const t=a(n[e]);Array.prototype.push.apply(o,t)}return Array.prototype.concat(g.Card16(e.length),g.OffSize(r),o,s)},m.INDEX=function(e){return g.INDEX(e).length},g.DICT=function(e){let t=[];const n=Object.keys(e),s=n.length;for(let o=0;o<s;o+=1){const s=parseInt(n[o],0),r=e[s];t=t.concat(g.OPERAND(r.value,r.type)),t=t.concat(g.OPERATOR(s))}return t},m.DICT=function(e){return g.DICT(e).length},g.OPERATOR=function(e){return e<1200?[e]:[12,e-1200]},g.OPERAND=function(e,t){let n=[];if(Array.isArray(t))for(let s=0;s<t.length;s+=1)f.argument(e.length===t.length,"Not enough arguments given for type"+t),n=n.concat(g.OPERAND(e[s],t[s]));else if("SID"===t)n=n.concat(g.NUMBER(e));else if("offset"===t)n=n.concat(g.NUMBER32(e));else if("number"===t)n=n.concat(g.NUMBER(e));else{if("real"!==t)throw new Error("Unknown operand type "+t);n=n.concat(g.REAL(e))}return n},g.OP=g.BYTE,m.OP=m.BYTE;const E="function"==typeof WeakMap&&new WeakMap;function w(e,t,n){for(let e=0;e<t.length;e+=1){const n=t[e];this[n.name]=n.value}if(this.tableName=e,this.fields=t,n){const e=Object.keys(n);for(let t=0;t<e.length;t+=1){const s=e[t],o=n[s];void 0!==this[s]&&(this[s]=o)}}}function O(e,t,n){void 0===n&&(n=t.length);const s=new Array(t.length+1);s[0]={name:e+"Count",type:"USHORT",value:n};for(let n=0;n<t.length;n++)s[n+1]={name:e+n,type:"USHORT",value:t[n]};return s}function I(e,t,n){const s=t.length,o=new Array(s+1);o[0]={name:e+"Count",type:"USHORT",value:s};for(let r=0;r<s;r++)o[r+1]={name:e+r,type:"TABLE",value:n(t[r],r)};return o}function R(e,t,n){const s=t.length;let o=[];o[0]={name:e+"Count",type:"USHORT",value:s};for(let e=0;e<s;e++)o=o.concat(n(t[e],e));return o}function L(e){1===e.format?w.call(this,"coverageTable",[{name:"coverageFormat",type:"USHORT",value:1}].concat(O("glyph",e.glyphs))):f.assert(!1,"Can't create coverage table format 2 yet.")}function B(e){w.call(this,"scriptListTable",R("scriptRecord",e,(function(e,t){const n=e.script;let s=n.defaultLangSys;return f.assert(!!s,"Unable to write GSUB: script "+e.tag+" has no default language system."),[{name:"scriptTag"+t,type:"TAG",value:e.tag},{name:"script"+t,type:"TABLE",value:new w("scriptTable",[{name:"defaultLangSys",type:"TABLE",value:new w("defaultLangSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:s.reqFeatureIndex}].concat(O("featureIndex",s.featureIndexes)))}].concat(R("langSys",n.langSysRecords,(function(e,t){const n=e.langSys;return[{name:"langSysTag"+t,type:"TAG",value:e.tag},{name:"langSys"+t,type:"TABLE",value:new w("langSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:n.reqFeatureIndex}].concat(O("featureIndex",n.featureIndexes)))}]}))))}]})))}function C(e){w.call(this,"featureListTable",R("featureRecord",e,(function(e,t){const n=e.feature;return[{name:"featureTag"+t,type:"TAG",value:e.tag},{name:"feature"+t,type:"TABLE",value:new w("featureTable",[{name:"featureParams",type:"USHORT",value:n.featureParams}].concat(O("lookupListIndex",n.lookupListIndexes)))}]})))}function D(e,t){w.call(this,"lookupListTable",I("lookup",e,(function(e){let n=t[e.lookupType];return f.assert(!!n,"Unable to write GSUB lookup type "+e.lookupType+" tables."),new w("lookupTable",[{name:"lookupType",type:"USHORT",value:e.lookupType},{name:"lookupFlag",type:"USHORT",value:e.lookupFlag}].concat(I("subtable",e.subtables,n)))})))}g.CHARSTRING=function(e){if(E){const t=E.get(e);if(void 0!==t)return t}let t=[];const n=e.length;for(let s=0;s<n;s+=1){const n=e[s];t=t.concat(g[n.type](n.value))}return E&&E.set(e,t),t},m.CHARSTRING=function(e){return g.CHARSTRING(e).length},g.OBJECT=function(e){const t=g[e.type];return f.argument(void 0!==t,"No encoding function for type "+e.type),t(e.value)},m.OBJECT=function(e){const t=m[e.type];return f.argument(void 0!==t,"No sizeOf function for type "+e.type),t(e.value)},g.TABLE=function(e){let t=[];const n=e.fields.length,s=[],o=[];for(let r=0;r<n;r+=1){const n=e.fields[r],a=g[n.type];f.argument(void 0!==a,"No encoding function for field type "+n.type+" ("+n.name+")");let i=e[n.name];void 0===i&&(i=n.value);const l=a(i);"TABLE"===n.type?(o.push(t.length),t=t.concat([0,0]),s.push(l)):t=t.concat(l)}for(let n=0;n<s.length;n+=1){const r=o[n],a=t.length;f.argument(a<65536,"Table "+e.tableName+" too big."),t[r]=a>>8,t[r+1]=255&a,t=t.concat(s[n])}return t},m.TABLE=function(e){let t=0;const n=e.fields.length;for(let s=0;s<n;s+=1){const n=e.fields[s],o=m[n.type];f.argument(void 0!==o,"No sizeOf function for field type "+n.type+" ("+n.name+")");let r=e[n.name];void 0===r&&(r=n.value),t+=o(r),"TABLE"===n.type&&(t+=2)}return t},g.RECORD=g.TABLE,m.RECORD=m.TABLE,g.LITERAL=function(e){return e},m.LITERAL=function(e){return e.length},w.prototype.encode=function(){return g.TABLE(this)},w.prototype.sizeOf=function(){return m.TABLE(this)},L.prototype=Object.create(w.prototype),L.prototype.constructor=L,B.prototype=Object.create(w.prototype),B.prototype.constructor=B,C.prototype=Object.create(w.prototype),C.prototype.constructor=C,D.prototype=Object.create(w.prototype),D.prototype.constructor=D;const M={Table:w,Record:w,Coverage:L,ScriptList:B,FeatureList:C,LookupList:D,ushortList:O,tableList:I,recordList:R};function A(e,t){return e.getUint8(t)}function P(e,t){return e.getUint16(t,!1)}function G(e,t){return e.getUint32(t,!1)}function N(e,t){return e.getInt16(t,!1)+e.getUint16(t+2,!1)/65535}const F={byte:1,uShort:2,short:2,uLong:4,fixed:4,longDateTime:8,tag:4};function _(e,t){this.data=e,this.offset=t,this.relativeOffset=0}_.prototype.parseByte=function(){const e=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},_.prototype.parseChar=function(){const e=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},_.prototype.parseCard8=_.prototype.parseByte,_.prototype.parseUShort=function(){const e=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},_.prototype.parseCard16=_.prototype.parseUShort,_.prototype.parseSID=_.prototype.parseUShort,_.prototype.parseOffset16=_.prototype.parseUShort,_.prototype.parseShort=function(){const e=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},_.prototype.parseF2Dot14=function(){const e=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,e},_.prototype.parseULong=function(){const e=G(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},_.prototype.parseOffset32=_.prototype.parseULong,_.prototype.parseFixed=function(){const e=N(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},_.prototype.parseString=function(e){const t=this.data,n=this.offset+this.relativeOffset;let s="";this.relativeOffset+=e;for(let o=0;o<e;o++)s+=String.fromCharCode(t.getUint8(n+o));return s},_.prototype.parseTag=function(){return this.parseString(4)},_.prototype.parseLongDateTime=function(){let e=G(this.data,this.offset+this.relativeOffset+4);return e-=2082844800,this.relativeOffset+=8,e},_.prototype.parseVersion=function(e){const t=P(this.data,this.offset+this.relativeOffset),n=P(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,void 0===e&&(e=4096),t+n/e/10},_.prototype.skip=function(e,t){void 0===t&&(t=1),this.relativeOffset+=F[e]*t},_.prototype.parseULongList=function(e){void 0===e&&(e=this.parseULong());const t=new Array(e),n=this.data;let s=this.offset+this.relativeOffset;for(let o=0;o<e;o++)t[o]=n.getUint32(s),s+=4;return this.relativeOffset+=4*e,t},_.prototype.parseOffset16List=_.prototype.parseUShortList=function(e){void 0===e&&(e=this.parseUShort());const t=new Array(e),n=this.data;let s=this.offset+this.relativeOffset;for(let o=0;o<e;o++)t[o]=n.getUint16(s),s+=2;return this.relativeOffset+=2*e,t},_.prototype.parseShortList=function(e){const t=new Array(e),n=this.data;let s=this.offset+this.relativeOffset;for(let o=0;o<e;o++)t[o]=n.getInt16(s),s+=2;return this.relativeOffset+=2*e,t},_.prototype.parseByteList=function(e){const t=new Array(e),n=this.data;let s=this.offset+this.relativeOffset;for(let o=0;o<e;o++)t[o]=n.getUint8(s++);return this.relativeOffset+=e,t},_.prototype.parseList=function(e,t){t||(t=e,e=this.parseUShort());const n=new Array(e);for(let s=0;s<e;s++)n[s]=t.call(this);return n},_.prototype.parseList32=function(e,t){t||(t=e,e=this.parseULong());const n=new Array(e);for(let s=0;s<e;s++)n[s]=t.call(this);return n},_.prototype.parseRecordList=function(e,t){t||(t=e,e=this.parseUShort());const n=new Array(e),s=Object.keys(t);for(let o=0;o<e;o++){const e={};for(let n=0;n<s.length;n++){const o=s[n],r=t[o];e[o]=r.call(this)}n[o]=e}return n},_.prototype.parseRecordList32=function(e,t){t||(t=e,e=this.parseULong());const n=new Array(e),s=Object.keys(t);for(let o=0;o<e;o++){const e={};for(let n=0;n<s.length;n++){const o=s[n],r=t[o];e[o]=r.call(this)}n[o]=e}return n},_.prototype.parseStruct=function(e){if("function"==typeof e)return e.call(this);{const t=Object.keys(e),n={};for(let s=0;s<t.length;s++){const o=t[s],r=e[o];n[o]=r.call(this)}return n}},_.prototype.parseValueRecord=function(e){if(void 0===e&&(e=this.parseUShort()),0===e)return;const t={};return 1&e&&(t.xPlacement=this.parseShort()),2&e&&(t.yPlacement=this.parseShort()),4&e&&(t.xAdvance=this.parseShort()),8&e&&(t.yAdvance=this.parseShort()),16&e&&(t.xPlaDevice=void 0,this.parseShort()),32&e&&(t.yPlaDevice=void 0,this.parseShort()),64&e&&(t.xAdvDevice=void 0,this.parseShort()),128&e&&(t.yAdvDevice=void 0,this.parseShort()),t},_.prototype.parseValueRecordList=function(){const e=this.parseUShort(),t=this.parseUShort(),n=new Array(t);for(let s=0;s<t;s++)n[s]=this.parseValueRecord(e);return n},_.prototype.parsePointer=function(e){const t=this.parseOffset16();if(t>0)return new _(this.data,this.offset+t).parseStruct(e)},_.prototype.parsePointer32=function(e){const t=this.parseOffset32();if(t>0)return new _(this.data,this.offset+t).parseStruct(e)},_.prototype.parseListOfLists=function(e){const t=this.parseOffset16List(),n=t.length,s=this.relativeOffset,o=new Array(n);for(let s=0;s<n;s++){const n=t[s];if(0!==n)if(this.relativeOffset=n,e){const t=this.parseOffset16List(),r=new Array(t.length);for(let s=0;s<t.length;s++)this.relativeOffset=n+t[s],r[s]=e.call(this);o[s]=r}else o[s]=this.parseUShortList();else o[s]=void 0}return this.relativeOffset=s,o},_.prototype.parseCoverage=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort(),n=this.parseUShort();if(1===t)return{format:1,glyphs:this.parseUShortList(n)};if(2===t){const e=new Array(n);for(let t=0;t<n;t++)e[t]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:e}}throw new Error("0x"+e.toString(16)+": Coverage format must be 1 or 2.")},_.prototype.parseClassDef=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()};if(2===t)return{format:2,ranges:this.parseRecordList({start:_.uShort,end:_.uShort,classId:_.uShort})};throw new Error("0x"+e.toString(16)+": ClassDef format must be 1 or 2.")},_.list=function(e,t){return function(){return this.parseList(e,t)}},_.list32=function(e,t){return function(){return this.parseList32(e,t)}},_.recordList=function(e,t){return function(){return this.parseRecordList(e,t)}},_.recordList32=function(e,t){return function(){return this.parseRecordList32(e,t)}},_.pointer=function(e){return function(){return this.parsePointer(e)}},_.pointer32=function(e){return function(){return this.parsePointer32(e)}},_.tag=_.prototype.parseTag,_.byte=_.prototype.parseByte,_.uShort=_.offset16=_.prototype.parseUShort,_.uShortList=_.prototype.parseUShortList,_.uLong=_.offset32=_.prototype.parseULong,_.uLongList=_.prototype.parseULongList,_.struct=_.prototype.parseStruct,_.coverage=_.prototype.parseCoverage,_.classDef=_.prototype.parseClassDef;const H={reserved:_.uShort,reqFeatureIndex:_.uShort,featureIndexes:_.uShortList};_.prototype.parseScriptList=function(){return this.parsePointer(_.recordList({tag:_.tag,script:_.pointer({defaultLangSys:_.pointer(H),langSysRecords:_.recordList({tag:_.tag,langSys:_.pointer(H)})})}))||[]},_.prototype.parseFeatureList=function(){return this.parsePointer(_.recordList({tag:_.tag,feature:_.pointer({featureParams:_.offset16,lookupListIndexes:_.uShortList})}))||[]},_.prototype.parseLookupList=function(e){return this.parsePointer(_.list(_.pointer((function(){const t=this.parseUShort();f.argument(1<=t&&t<=9,"GPOS/GSUB lookup type "+t+" unknown.");const n=this.parseUShort(),s=16&n;return{lookupType:t,lookupFlag:n,subtables:this.parseList(_.pointer(e[t])),markFilteringSet:s?this.parseUShort():void 0}}))))||[]},_.prototype.parseFeatureVariationsList=function(){return this.parsePointer32((function(){const e=this.parseUShort(),t=this.parseUShort();return f.argument(1===e&&t<1,"GPOS/GSUB feature variations table unknown."),this.parseRecordList32({conditionSetOffset:_.offset32,featureTableSubstitutionOffset:_.offset32})}))||[]};const z={getByte:A,getCard8:A,getUShort:P,getCard16:P,getShort:function(e,t){return e.getInt16(t,!1)},getULong:G,getFixed:N,getTag:function(e,t){let n="";for(let s=t;s<t+4;s+=1)n+=String.fromCharCode(e.getInt8(s));return n},getOffset:function(e,t,n){let s=0;for(let o=0;o<n;o+=1)s<<=8,s+=e.getUint8(t+o);return s},getBytes:function(e,t,n){const s=[];for(let o=t;o<n;o+=1)s.push(e.getUint8(o));return s},bytesToString:function(e){let t="";for(let n=0;n<e.length;n+=1)t+=String.fromCharCode(e[n]);return t},Parser:_};function W(e,t,n){e.segments.push({end:t,start:t,delta:-(t-n),offset:0,glyphIndex:n})}const q={parse:function(e,t){const n={};n.version=z.getUShort(e,t),f.argument(0===n.version,"cmap table version should be 0."),n.numTables=z.getUShort(e,t+2);let s=-1;for(let o=n.numTables-1;o>=0;o-=1){const n=z.getUShort(e,t+4+8*o),r=z.getUShort(e,t+4+8*o+2);if(3===n&&(0===r||1===r||10===r)){s=z.getULong(e,t+4+8*o+4);break}}if(-1===s)throw new Error("No valid cmap sub-tables found.");const o=new z.Parser(e,t+s);if(n.format=o.parseUShort(),12===n.format)!function(e,t){let n;t.parseUShort(),e.length=t.parseULong(),e.language=t.parseULong(),e.groupCount=n=t.parseULong(),e.glyphIndexMap={};for(let s=0;s<n;s+=1){const n=t.parseULong(),s=t.parseULong();let o=t.parseULong();for(let t=n;t<=s;t+=1)e.glyphIndexMap[t]=o,o++}}(n,o);else{if(4!==n.format)throw new Error("Only format 4 and 12 cmap tables are supported (found format "+n.format+").");!function(e,t,n,s,o){let r;e.length=t.parseUShort(),e.language=t.parseUShort(),e.segCount=r=t.parseUShort()>>1,t.skip("uShort",3),e.glyphIndexMap={};const a=new z.Parser(n,s+o+14),i=new z.Parser(n,s+o+16+2*r),l=new z.Parser(n,s+o+16+4*r),u=new z.Parser(n,s+o+16+6*r);let c=s+o+16+8*r;for(let t=0;t<r-1;t+=1){let t;const s=a.parseUShort(),o=i.parseUShort(),r=l.parseShort(),p=u.parseUShort();for(let a=o;a<=s;a+=1)0!==p?(c=u.offset+u.relativeOffset-2,c+=p,c+=2*(a-o),t=z.getUShort(n,c),0!==t&&(t=t+r&65535)):t=a+r&65535,e.glyphIndexMap[a]=t}}(n,o,e,t,s)}return n},make:function(e){let t,n=!0;for(t=e.length-1;t>0;t-=1)if(e.get(t).unicode>65535){console.log("Adding CMAP format 12 (needed!)"),n=!1;break}let s=[{name:"version",type:"USHORT",value:0},{name:"numTables",type:"USHORT",value:n?1:2},{name:"platformID",type:"USHORT",value:3},{name:"encodingID",type:"USHORT",value:1},{name:"offset",type:"ULONG",value:n?12:20}];n||(s=s.concat([{name:"cmap12PlatformID",type:"USHORT",value:3},{name:"cmap12EncodingID",type:"USHORT",value:10},{name:"cmap12Offset",type:"ULONG",value:0}])),s=s.concat([{name:"format",type:"USHORT",value:4},{name:"cmap4Length",type:"USHORT",value:0},{name:"language",type:"USHORT",value:0},{name:"segCountX2",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);const o=new M.Table("cmap",s);for(o.segments=[],t=0;t<e.length;t+=1){const n=e.get(t);for(let e=0;e<n.unicodes.length;e+=1)W(o,n.unicodes[e],t);o.segments=o.segments.sort((function(e,t){return e.start-t.start}))}!function(e){e.segments.push({end:65535,start:65535,delta:1,offset:0})}(o);const r=o.segments.length;let a=0,i=[],l=[],u=[],c=[],p=[],f=[];for(t=0;t<r;t+=1){const e=o.segments[t];e.end<=65535&&e.start<=65535?(i=i.concat({name:"end_"+t,type:"USHORT",value:e.end}),l=l.concat({name:"start_"+t,type:"USHORT",value:e.start}),u=u.concat({name:"idDelta_"+t,type:"SHORT",value:e.delta}),c=c.concat({name:"idRangeOffset_"+t,type:"USHORT",value:e.offset}),void 0!==e.glyphId&&(p=p.concat({name:"glyph_"+t,type:"USHORT",value:e.glyphId}))):a+=1,n||void 0===e.glyphIndex||(f=f.concat({name:"cmap12Start_"+t,type:"ULONG",value:e.start}),f=f.concat({name:"cmap12End_"+t,type:"ULONG",value:e.end}),f=f.concat({name:"cmap12Glyph_"+t,type:"ULONG",value:e.glyphIndex}))}if(o.segCountX2=2*(r-a),o.searchRange=2*Math.pow(2,Math.floor(Math.log(r-a)/Math.log(2))),o.entrySelector=Math.log(o.searchRange/2)/Math.log(2),o.rangeShift=o.segCountX2-o.searchRange,o.fields=o.fields.concat(i),o.fields.push({name:"reservedPad",type:"USHORT",value:0}),o.fields=o.fields.concat(l),o.fields=o.fields.concat(u),o.fields=o.fields.concat(c),o.fields=o.fields.concat(p),o.cmap4Length=14+2*i.length+2+2*l.length+2*u.length+2*c.length+2*p.length,!n){const e=16+4*f.length;o.cmap12Offset=20+o.cmap4Length,o.fields=o.fields.concat([{name:"cmap12Format",type:"USHORT",value:12},{name:"cmap12Reserved",type:"USHORT",value:0},{name:"cmap12Length",type:"ULONG",value:e},{name:"cmap12Language",type:"ULONG",value:0},{name:"cmap12nGroups",type:"ULONG",value:f.length/3}]),o.fields=o.fields.concat(f)}return o}},j=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","266 ff","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"],X=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],V=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],Y=[".notdef",".null","nonmarkingreturn","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quotesingle","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","grave","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","Adieresis","Aring","Ccedilla","Eacute","Ntilde","Odieresis","Udieresis","aacute","agrave","acircumflex","adieresis","atilde","aring","ccedilla","eacute","egrave","ecircumflex","edieresis","iacute","igrave","icircumflex","idieresis","ntilde","oacute","ograve","ocircumflex","odieresis","otilde","uacute","ugrave","ucircumflex","udieresis","dagger","degree","cent","sterling","section","bullet","paragraph","germandbls","registered","copyright","trademark","acute","dieresis","notequal","AE","Oslash","infinity","plusminus","lessequal","greaterequal","yen","mu","partialdiff","summation","product","pi","integral","ordfeminine","ordmasculine","Omega","ae","oslash","questiondown","exclamdown","logicalnot","radical","florin","approxequal","Delta","guillemotleft","guillemotright","ellipsis","nonbreakingspace","Agrave","Atilde","Otilde","OE","oe","endash","emdash","quotedblleft","quotedblright","quoteleft","quoteright","divide","lozenge","ydieresis","Ydieresis","fraction","currency","guilsinglleft","guilsinglright","fi","fl","daggerdbl","periodcentered","quotesinglbase","quotedblbase","perthousand","Acircumflex","Ecircumflex","Aacute","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Oacute","Ocircumflex","apple","Ograve","Uacute","Ucircumflex","Ugrave","dotlessi","circumflex","tilde","macron","breve","dotaccent","ring","cedilla","hungarumlaut","ogonek","caron","Lslash","lslash","Scaron","scaron","Zcaron","zcaron","brokenbar","Eth","eth","Yacute","yacute","Thorn","thorn","minus","multiply","onesuperior","twosuperior","threesuperior","onehalf","onequarter","threequarters","franc","Gbreve","gbreve","Idotaccent","Scedilla","scedilla","Cacute","cacute","Ccaron","ccaron","dcroat"];function Z(e){this.font=e}function Q(e){this.cmap=e}function J(e,t){this.encoding=e,this.charset=t}function K(e){switch(e.version){case 1:this.names=Y.slice();break;case 2:this.names=new Array(e.numberOfGlyphs);for(let t=0;t<e.numberOfGlyphs;t++)e.glyphNameIndex[t]<Y.length?this.names[t]=Y[e.glyphNameIndex[t]]:this.names[t]=e.names[e.glyphNameIndex[t]-Y.length];break;case 2.5:this.names=new Array(e.numberOfGlyphs);for(let t=0;t<e.numberOfGlyphs;t++)this.names[t]=Y[t+e.glyphNameIndex[t]];break;default:this.names=[]}}Z.prototype.charToGlyphIndex=function(e){const t=e.charCodeAt(0),n=this.font.glyphs;if(n)for(let e=0;e<n.length;e+=1){const s=n.get(e);for(let n=0;n<s.unicodes.length;n+=1)if(s.unicodes[n]===t)return e}return null},Q.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.charCodeAt(0)]||0},J.prototype.charToGlyphIndex=function(e){const t=e.charCodeAt(0),n=this.encoding[t];return this.charset.indexOf(n)},K.prototype.nameToGlyphIndex=function(e){return this.names.indexOf(e)},K.prototype.glyphIndexToName=function(e){return this.names[e]};const $=function(e,t,n,s,o){e.beginPath(),e.moveTo(t,n),e.lineTo(s,o),e.stroke()};function ee(e,t,n,s,o){let r;return(t&s)>0?(r=e.parseByte(),t&o||(r=-r),r=n+r):r=(t&o)>0?n:n+e.parseShort(),r}function te(e,t,n){const s=new z.Parser(t,n);let o,r;if(e.numberOfContours=s.parseShort(),e._xMin=s.parseShort(),e._yMin=s.parseShort(),e._xMax=s.parseShort(),e._yMax=s.parseShort(),e.numberOfContours>0){const t=e.endPointIndices=[];for(let n=0;n<e.numberOfContours;n+=1)t.push(s.parseUShort());e.instructionLength=s.parseUShort(),e.instructions=[];for(let t=0;t<e.instructionLength;t+=1)e.instructions.push(s.parseByte());const n=t[t.length-1]+1;o=[];for(let e=0;e<n;e+=1)if(r=s.parseByte(),o.push(r),(8&r)>0){const t=s.parseByte();for(let n=0;n<t;n+=1)o.push(r),e+=1}if(f.argument(o.length===n,"Bad flags."),t.length>0){const a=[];let i;if(n>0){for(let e=0;e<n;e+=1)r=o[e],i={},i.onCurve=!!(1&r),i.lastPointOfContour=t.indexOf(e)>=0,a.push(i);let e=0;for(let t=0;t<n;t+=1)r=o[t],i=a[t],i.x=ee(s,r,e,2,16),e=i.x;let l=0;for(let e=0;e<n;e+=1)r=o[e],i=a[e],i.y=ee(s,r,l,4,32),l=i.y}e.points=a}else e.points=[]}else if(0===e.numberOfContours)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];let t=!0;for(;t;){o=s.parseUShort();const n={glyphIndex:s.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};(1&o)>0?(2&o)>0?(n.dx=s.parseShort(),n.dy=s.parseShort()):n.matchedPoints=[s.parseUShort(),s.parseUShort()]:(2&o)>0?(n.dx=s.parseChar(),n.dy=s.parseChar()):n.matchedPoints=[s.parseByte(),s.parseByte()],(8&o)>0?n.xScale=n.yScale=s.parseF2Dot14():(64&o)>0?(n.xScale=s.parseF2Dot14(),n.yScale=s.parseF2Dot14()):(128&o)>0&&(n.xScale=s.parseF2Dot14(),n.scale01=s.parseF2Dot14(),n.scale10=s.parseF2Dot14(),n.yScale=s.parseF2Dot14()),e.components.push(n),t=!!(32&o)}if(256&o){e.instructionLength=s.parseUShort(),e.instructions=[];for(let t=0;t<e.instructionLength;t+=1)e.instructions.push(s.parseByte())}}}function ne(e,t){const n=[];for(let s=0;s<e.length;s+=1){const o=e[s],r={x:t.xScale*o.x+t.scale01*o.y+t.dx,y:t.scale10*o.x+t.yScale*o.y+t.dy,onCurve:o.onCurve,lastPointOfContour:o.lastPointOfContour};n.push(r)}return n}function se(e){const t=new u;if(!e)return t;const n=function(e){const t=[];let n=[];for(let s=0;s<e.length;s+=1){const o=e[s];n.push(o),o.lastPointOfContour&&(t.push(n),n=[])}return f.argument(0===n.length,"There are still points left in the current contour."),t}(e);for(let e=0;e<n.length;++e){const s=n[e];let o=null,r=s[s.length-1],a=s[0];if(r.onCurve)t.moveTo(r.x,r.y);else if(a.onCurve)t.moveTo(a.x,a.y);else{const e={x:.5*(r.x+a.x),y:.5*(r.y+a.y)};t.moveTo(e.x,e.y)}for(let e=0;e<s.length;++e)if(o=r,r=a,a=s[(e+1)%s.length],r.onCurve)t.lineTo(r.x,r.y);else{let e=o,n=a;o.onCurve||(e={x:.5*(r.x+o.x),y:.5*(r.y+o.y)},t.lineTo(e.x,e.y)),a.onCurve||(n={x:.5*(r.x+a.x),y:.5*(r.y+a.y)}),t.lineTo(e.x,e.y),t.quadraticCurveTo(r.x,r.y,n.x,n.y)}t.closePath()}return t}function oe(e,t){if(t.isComposite)for(let n=0;n<t.components.length;n+=1){const s=t.components[n],o=e.get(s.glyphIndex);if(o.getPath(),o.points){let e;if(void 0===s.matchedPoints)e=ne(o.points,s);else{if(s.matchedPoints[0]>t.points.length-1||s.matchedPoints[1]>o.points.length-1)throw Error("Matched points out of range in "+t.name);const n=t.points[s.matchedPoints[0]];let r=o.points[s.matchedPoints[1]];const a={xScale:s.xScale,scale01:s.scale01,scale10:s.scale10,yScale:s.yScale,dx:0,dy:0};r=ne([r],a)[0],a.dx=n.x-r.x,a.dy=n.y-r.y,e=ne(o.points,a)}t.points=t.points.concat(e)}}return se(t.points)}const re={getPath:se,parse:function(e,t,n,s){const o=new ce.GlyphSet(s);for(let r=0;r<n.length-1;r+=1){const a=n[r];a!==n[r+1]?o.push(r,ce.ttfGlyphLoader(s,r,te,e,t+a,oe)):o.push(r,ce.glyphLoader(s,r))}return o}};function ae(e){this.bindConstructorValues(e)}ae.prototype.bindConstructorValues=function(e){this.index=e.index||0,this.name=e.name||null,this.unicode=e.unicode||void 0,this.unicodes=e.unicodes||void 0!==e.unicode?[e.unicode]:[],e.xMin&&(this.xMin=e.xMin),e.yMin&&(this.yMin=e.yMin),e.xMax&&(this.xMax=e.xMax),e.yMax&&(this.yMax=e.yMax),e.advanceWidth&&(this.advanceWidth=e.advanceWidth),Object.defineProperty(this,"path",function(e,t){let n=t||new u;return{configurable:!0,get:function(){return"function"==typeof n&&(n=n()),n},set:function(e){n=e}}}(0,e.path))},ae.prototype.addUnicode=function(e){0===this.unicodes.length&&(this.unicode=e),this.unicodes.push(e)},ae.prototype.getBoundingBox=function(){return this.path.getBoundingBox()},ae.prototype.getPath=function(e,t,n,s,o){let r,a;e=void 0!==e?e:0,t=void 0!==t?t:0,n=void 0!==n?n:72,s||(s={});let i=s.xScale,l=s.yScale;if(s.hinting&&o&&o.hinting&&(a=this.path&&o.hinting.exec(this,n)),a)r=re.getPath(a).commands,e=Math.round(e),t=Math.round(t),i=l=1;else{r=this.path.commands;const e=1/this.path.unitsPerEm*n;void 0===i&&(i=e),void 0===l&&(l=e)}const c=new u;for(let n=0;n<r.length;n+=1){const s=r[n];"M"===s.type?c.moveTo(e+s.x*i,t+-s.y*l):"L"===s.type?c.lineTo(e+s.x*i,t+-s.y*l):"Q"===s.type?c.quadraticCurveTo(e+s.x1*i,t+-s.y1*l,e+s.x*i,t+-s.y*l):"C"===s.type?c.curveTo(e+s.x1*i,t+-s.y1*l,e+s.x2*i,t+-s.y2*l,e+s.x*i,t+-s.y*l):"Z"===s.type&&c.closePath()}return c},ae.prototype.getContours=function(){if(void 0===this.points)return[];const e=[];let t=[];for(let n=0;n<this.points.length;n+=1){const s=this.points[n];t.push(s),s.lastPointOfContour&&(e.push(t),t=[])}return f.argument(0===t.length,"There are still points left in the current contour."),e},ae.prototype.getMetrics=function(){const e=this.path.commands,t=[],n=[];for(let s=0;s<e.length;s+=1){const o=e[s];"Z"!==o.type&&(t.push(o.x),n.push(o.y)),"Q"!==o.type&&"C"!==o.type||(t.push(o.x1),n.push(o.y1)),"C"===o.type&&(t.push(o.x2),n.push(o.y2))}const s={xMin:Math.min.apply(null,t),yMin:Math.min.apply(null,n),xMax:Math.max.apply(null,t),yMax:Math.max.apply(null,n),leftSideBearing:this.leftSideBearing};return isFinite(s.xMin)||(s.xMin=0),isFinite(s.xMax)||(s.xMax=this.advanceWidth),isFinite(s.yMin)||(s.yMin=0),isFinite(s.yMax)||(s.yMax=0),s.rightSideBearing=this.advanceWidth-s.leftSideBearing-(s.xMax-s.xMin),s},ae.prototype.draw=function(e,t,n,s,o){this.getPath(t,n,s,o).draw(e)},ae.prototype.drawPoints=function(e,t,n,s){function o(t,n,s,o){const r=2*Math.PI;e.beginPath();for(let a=0;a<t.length;a+=1)e.moveTo(n+t[a].x*o,s+t[a].y*o),e.arc(n+t[a].x*o,s+t[a].y*o,2,0,r,!1);e.closePath(),e.fill()}t=void 0!==t?t:0,n=void 0!==n?n:0,s=void 0!==s?s:24;const r=1/this.path.unitsPerEm*s,a=[],i=[],l=this.path;for(let e=0;e<l.commands.length;e+=1){const t=l.commands[e];void 0!==t.x&&a.push({x:t.x,y:-t.y}),void 0!==t.x1&&i.push({x:t.x1,y:-t.y1}),void 0!==t.x2&&i.push({x:t.x2,y:-t.y2})}e.fillStyle="blue",o(a,t,n,r),e.fillStyle="red",o(i,t,n,r)},ae.prototype.drawMetrics=function(e,t,n,s){let o;t=void 0!==t?t:0,n=void 0!==n?n:0,s=void 0!==s?s:24,o=1/this.path.unitsPerEm*s,e.lineWidth=1,e.strokeStyle="black",$(e,t,-1e4,t,1e4),$(e,-1e4,n,1e4,n);const r=this.xMin||0;let a=this.yMin||0;const i=this.xMax||0;let l=this.yMax||0;const u=this.advanceWidth||0;e.strokeStyle="blue",$(e,t+r*o,-1e4,t+r*o,1e4),$(e,t+i*o,-1e4,t+i*o,1e4),$(e,-1e4,n+-a*o,1e4,n+-a*o),$(e,-1e4,n+-l*o,1e4,n+-l*o),e.strokeStyle="green",$(e,t+u*o,-1e4,t+u*o,1e4)};const ie=ae;function le(e,t,n){Object.defineProperty(e,t,{get:function(){return e.path,e[n]},set:function(t){e[n]=t},enumerable:!0,configurable:!0})}function ue(e,t){if(this.font=e,this.glyphs={},Array.isArray(t))for(let e=0;e<t.length;e++)this.glyphs[e]=t[e];this.length=t&&t.length||0}ue.prototype.get=function(e){return"function"==typeof this.glyphs[e]&&(this.glyphs[e]=this.glyphs[e]()),this.glyphs[e]},ue.prototype.push=function(e,t){this.glyphs[e]=t,this.length++};const ce={GlyphSet:ue,glyphLoader:function(e,t){return new ie({index:t,font:e})},ttfGlyphLoader:function(e,t,n,s,o,r){return function(){const a=new ie({index:t,font:e});return a.path=function(){n(a,s,o);const t=r(e.glyphs,a);return t.unitsPerEm=e.unitsPerEm,t},le(a,"xMin","_xMin"),le(a,"xMax","_xMax"),le(a,"yMin","_yMin"),le(a,"yMax","_yMax"),a}},cffGlyphLoader:function(e,t,n,s){return function(){const o=new ie({index:t,font:e});return o.path=function(){const t=n(e,o,s);return t.unitsPerEm=e.unitsPerEm,t},o}}};function pe(e,t){if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n+=1)if(!pe(e[n],t[n]))return!1;return!0}return!1}function fe(e){let t;return t=e.length<1240?107:e.length<33900?1131:32768,t}function he(e,t,n){const s=[],o=[],r=z.getCard16(e,t);let a,i;if(0!==r){const n=z.getByte(e,t+2);a=t+(r+1)*n+2;let o=t+3;for(let t=0;t<r+1;t+=1)s.push(z.getOffset(e,o,n)),o+=n;i=a+s[r]}else i=t+2;for(let t=0;t<s.length-1;t+=1){let r=z.getBytes(e,a+s[t],a+s[t+1]);n&&(r=n(r)),o.push(r)}return{objects:o,startOffset:t,endOffset:i}}function de(e,t){let n,s,o,r;if(28===t)return n=e.parseByte(),s=e.parseByte(),n<<8|s;if(29===t)return n=e.parseByte(),s=e.parseByte(),o=e.parseByte(),r=e.parseByte(),n<<24|s<<16|o<<8|r;if(30===t)return function(e){let t="";const n=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"];for(;;){const s=e.parseByte(),o=s>>4,r=15&s;if(15===o)break;if(t+=n[o],15===r)break;t+=n[r]}return parseFloat(t)}(e);if(t>=32&&t<=246)return t-139;if(t>=247&&t<=250)return n=e.parseByte(),256*(t-247)+n+108;if(t>=251&&t<=254)return n=e.parseByte(),256*-(t-251)-n-108;throw new Error("Invalid b0 "+t)}function ge(e,t,n){t=void 0!==t?t:0;const s=new z.Parser(e,t),o=[];let r=[];for(n=void 0!==n?n:e.length;s.relativeOffset<n;){let e=s.parseByte();e<=21?(12===e&&(e=1200+s.parseByte()),o.push([e,r]),r=[]):r.push(de(s,e))}return function(e){const t={};for(let n=0;n<e.length;n+=1){const s=e[n][0],o=e[n][1];let r;if(r=1===o.length?o[0]:o,t.hasOwnProperty(s)&&!isNaN(t[s]))throw new Error("Object "+t+" already has key "+s);t[s]=r}return t}(o)}function me(e,t){return t<=390?j[t]:e[t-391]}function ye(e,t,n){const s={};let o;for(let r=0;r<t.length;r+=1){const a=t[r];if(Array.isArray(a.type)){const t=[];t.length=a.type.length;for(let s=0;s<a.type.length;s++)o=void 0!==e[a.op]?e[a.op][s]:void 0,void 0===o&&(o=void 0!==a.value&&void 0!==a.value[s]?a.value[s]:null),"SID"===a.type[s]&&(o=me(n,o)),t[s]=o;s[a.name]=t}else o=e[a.op],void 0===o&&(o=void 0!==a.value?a.value:null),"SID"===a.type&&(o=me(n,o)),s[a.name]=o}return s}const ve=[{name:"version",op:0,type:"SID"},{name:"notice",op:1,type:"SID"},{name:"copyright",op:1200,type:"SID"},{name:"fullName",op:2,type:"SID"},{name:"familyName",op:3,type:"SID"},{name:"weight",op:4,type:"SID"},{name:"isFixedPitch",op:1201,type:"number",value:0},{name:"italicAngle",op:1202,type:"number",value:0},{name:"underlinePosition",op:1203,type:"number",value:-100},{name:"underlineThickness",op:1204,type:"number",value:50},{name:"paintType",op:1205,type:"number",value:0},{name:"charstringType",op:1206,type:"number",value:2},{name:"fontMatrix",op:1207,type:["real","real","real","real","real","real"],value:[.001,0,0,.001,0,0]},{name:"uniqueId",op:13,type:"number"},{name:"fontBBox",op:5,type:["number","number","number","number"],value:[0,0,0,0]},{name:"strokeWidth",op:1208,type:"number",value:0},{name:"xuid",op:14,type:[],value:null},{name:"charset",op:15,type:"offset",value:0},{name:"encoding",op:16,type:"offset",value:0},{name:"charStrings",op:17,type:"offset",value:0},{name:"private",op:18,type:["number","offset"],value:[0,0]},{name:"ros",op:1230,type:["SID","SID","number"]},{name:"cidFontVersion",op:1231,type:"number",value:0},{name:"cidFontRevision",op:1232,type:"number",value:0},{name:"cidFontType",op:1233,type:"number",value:0},{name:"cidCount",op:1234,type:"number",value:8720},{name:"uidBase",op:1235,type:"number"},{name:"fdArray",op:1236,type:"offset"},{name:"fdSelect",op:1237,type:"offset"},{name:"fontName",op:1238,type:"SID"}],be=[{name:"subrs",op:19,type:"offset",value:0},{name:"defaultWidthX",op:20,type:"number",value:0},{name:"nominalWidthX",op:21,type:"number",value:0}];function xe(e,t){return ye(ge(e,0,e.byteLength),ve,t)}function Se(e,t,n,s){return ye(ge(e,t,n),be,s)}function Ue(e,t,n,s){const o=[];for(let r=0;r<n.length;r+=1){const a=xe(new DataView(new Uint8Array(n[r]).buffer),s);a._subrs=[],a._subrsBias=0;const i=a.private[0],l=a.private[1];if(0!==i&&0!==l){const n=Se(e,l+t,i,s);if(a._defaultWidthX=n.defaultWidthX,a._nominalWidthX=n.nominalWidthX,0!==n.subrs){const s=he(e,l+n.subrs+t);a._subrs=s.objects,a._subrsBias=fe(a._subrs)}a._privateDict=n}o.push(a)}return o}function ke(e,t,n){let s,o,r,a;const i=new u,l=[];let c,p,f,h,d=0,g=!1,m=!1,y=0,v=0;if(e.isCIDFont){const n=e.tables.cff.topDict._fdSelect[t.index],s=e.tables.cff.topDict._fdArray[n];c=s._subrs,p=s._subrsBias,f=s._defaultWidthX,h=s._nominalWidthX}else c=e.tables.cff.topDict._subrs,p=e.tables.cff.topDict._subrsBias,f=e.tables.cff.topDict._defaultWidthX,h=e.tables.cff.topDict._nominalWidthX;let b=f;function x(e,t){m&&i.closePath(),i.moveTo(e,t),m=!0}function S(){let e;e=l.length%2!=0,e&&!g&&(b=l.shift()+h),d+=l.length>>1,l.length=0,g=!0}return function n(u){let f,U,k,T,E,w,O,I,R,L,B,C,D=0;for(;D<u.length;){let M=u[D];switch(D+=1,M){case 1:case 3:case 18:case 23:S();break;case 4:l.length>1&&!g&&(b=l.shift()+h,g=!0),v+=l.pop(),x(y,v);break;case 5:for(;l.length>0;)y+=l.shift(),v+=l.shift(),i.lineTo(y,v);break;case 6:for(;l.length>0&&(y+=l.shift(),i.lineTo(y,v),0!==l.length);)v+=l.shift(),i.lineTo(y,v);break;case 7:for(;l.length>0&&(v+=l.shift(),i.lineTo(y,v),0!==l.length);)y+=l.shift(),i.lineTo(y,v);break;case 8:for(;l.length>0;)s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a+l.shift(),i.curveTo(s,o,r,a,y,v);break;case 10:E=l.pop()+p,w=c[E],w&&n(w);break;case 11:return;case 12:switch(M=u[D],D+=1,M){case 35:s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),O=r+l.shift(),I=a+l.shift(),R=O+l.shift(),L=I+l.shift(),B=R+l.shift(),C=L+l.shift(),y=B+l.shift(),v=C+l.shift(),l.shift(),i.curveTo(s,o,r,a,O,I),i.curveTo(R,L,B,C,y,v);break;case 34:s=y+l.shift(),o=v,r=s+l.shift(),a=o+l.shift(),O=r+l.shift(),I=a,R=O+l.shift(),L=a,B=R+l.shift(),C=v,y=B+l.shift(),i.curveTo(s,o,r,a,O,I),i.curveTo(R,L,B,C,y,v);break;case 36:s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),O=r+l.shift(),I=a,R=O+l.shift(),L=a,B=R+l.shift(),C=L+l.shift(),y=B+l.shift(),i.curveTo(s,o,r,a,O,I),i.curveTo(R,L,B,C,y,v);break;case 37:s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),O=r+l.shift(),I=a+l.shift(),R=O+l.shift(),L=I+l.shift(),B=R+l.shift(),C=L+l.shift(),Math.abs(B-y)>Math.abs(C-v)?y=B+l.shift():v=C+l.shift(),i.curveTo(s,o,r,a,O,I),i.curveTo(R,L,B,C,y,v);break;default:console.log("Glyph "+t.index+": unknown operator 1200"+M),l.length=0}break;case 14:l.length>0&&!g&&(b=l.shift()+h,g=!0),m&&(i.closePath(),m=!1);break;case 19:case 20:S(),D+=d+7>>3;break;case 21:l.length>2&&!g&&(b=l.shift()+h,g=!0),v+=l.pop(),y+=l.pop(),x(y,v);break;case 22:l.length>1&&!g&&(b=l.shift()+h,g=!0),y+=l.pop(),x(y,v);break;case 24:for(;l.length>2;)s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a+l.shift(),i.curveTo(s,o,r,a,y,v);y+=l.shift(),v+=l.shift(),i.lineTo(y,v);break;case 25:for(;l.length>6;)y+=l.shift(),v+=l.shift(),i.lineTo(y,v);s=y+l.shift(),o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a+l.shift(),i.curveTo(s,o,r,a,y,v);break;case 26:for(l.length%2&&(y+=l.shift());l.length>0;)s=y,o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r,v=a+l.shift(),i.curveTo(s,o,r,a,y,v);break;case 27:for(l.length%2&&(v+=l.shift());l.length>0;)s=y+l.shift(),o=v,r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a,i.curveTo(s,o,r,a,y,v);break;case 28:f=u[D],U=u[D+1],l.push((f<<24|U<<16)>>16),D+=2;break;case 29:E=l.pop()+e.gsubrsBias,w=e.gsubrs[E],w&&n(w);break;case 30:for(;l.length>0&&(s=y,o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a+(1===l.length?l.shift():0),i.curveTo(s,o,r,a,y,v),0!==l.length);)s=y+l.shift(),o=v,r=s+l.shift(),a=o+l.shift(),v=a+l.shift(),y=r+(1===l.length?l.shift():0),i.curveTo(s,o,r,a,y,v);break;case 31:for(;l.length>0&&(s=y+l.shift(),o=v,r=s+l.shift(),a=o+l.shift(),v=a+l.shift(),y=r+(1===l.length?l.shift():0),i.curveTo(s,o,r,a,y,v),0!==l.length);)s=y,o=v+l.shift(),r=s+l.shift(),a=o+l.shift(),y=r+l.shift(),v=a+(1===l.length?l.shift():0),i.curveTo(s,o,r,a,y,v);break;default:M<32?console.log("Glyph "+t.index+": unknown operator "+M):M<247?l.push(M-139):M<251?(f=u[D],D+=1,l.push(256*(M-247)+f+108)):M<255?(f=u[D],D+=1,l.push(256*-(M-251)-f-108)):(f=u[D],U=u[D+1],k=u[D+2],T=u[D+3],D+=4,l.push((f<<24|U<<16|k<<8|T)/65536))}}}(n),t.advanceWidth=b,i}function Te(e,t){let n,s=j.indexOf(e);return s>=0&&(n=s),s=t.indexOf(e),s>=0?n=s+j.length:(n=j.length+t.length,t.push(e)),n}function Ee(e,t,n){const s={};for(let o=0;o<e.length;o+=1){const r=e[o];let a=t[r.name];void 0===a||pe(a,r.value)||("SID"===r.type&&(a=Te(a,n)),s[r.op]={name:r.name,type:r.type,value:a})}return s}function we(e,t){const n=new M.Record("Top DICT",[{name:"dict",type:"DICT",value:{}}]);return n.dict=Ee(ve,e,t),n}function Oe(e){const t=new M.Record("Top DICT INDEX",[{name:"topDicts",type:"INDEX",value:[]}]);return t.topDicts=[{name:"topDict_0",type:"TABLE",value:e}],t}function Ie(e){const t=[],n=e.path;t.push({name:"width",type:"NUMBER",value:e.advanceWidth});let s=0,o=0;for(let e=0;e<n.commands.length;e+=1){let r,a,i=n.commands[e];if("Q"===i.type){const e=1/3,t=2/3;i={type:"C",x:i.x,y:i.y,x1:e*s+t*i.x1,y1:e*o+t*i.y1,x2:e*i.x+t*i.x1,y2:e*i.y+t*i.y1}}if("M"===i.type)r=Math.round(i.x-s),a=Math.round(i.y-o),t.push({name:"dx",type:"NUMBER",value:r}),t.push({name:"dy",type:"NUMBER",value:a}),t.push({name:"rmoveto",type:"OP",value:21}),s=Math.round(i.x),o=Math.round(i.y);else if("L"===i.type)r=Math.round(i.x-s),a=Math.round(i.y-o),t.push({name:"dx",type:"NUMBER",value:r}),t.push({name:"dy",type:"NUMBER",value:a}),t.push({name:"rlineto",type:"OP",value:5}),s=Math.round(i.x),o=Math.round(i.y);else if("C"===i.type){const e=Math.round(i.x1-s),n=Math.round(i.y1-o),l=Math.round(i.x2-i.x1),u=Math.round(i.y2-i.y1);r=Math.round(i.x-i.x2),a=Math.round(i.y-i.y2),t.push({name:"dx1",type:"NUMBER",value:e}),t.push({name:"dy1",type:"NUMBER",value:n}),t.push({name:"dx2",type:"NUMBER",value:l}),t.push({name:"dy2",type:"NUMBER",value:u}),t.push({name:"dx",type:"NUMBER",value:r}),t.push({name:"dy",type:"NUMBER",value:a}),t.push({name:"rrcurveto",type:"OP",value:8}),s=Math.round(i.x),o=Math.round(i.y)}}return t.push({name:"endchar",type:"OP",value:14}),t}const Re={parse:function(e,t,n){n.tables.cff={};const s=function(e,t){const n={};return n.formatMajor=z.getCard8(e,t),n.formatMinor=z.getCard8(e,t+1),n.size=z.getCard8(e,t+2),n.offsetSize=z.getCard8(e,t+3),n.startOffset=t,n.endOffset=t+4,n}(e,t),o=he(e,s.endOffset,z.bytesToString),r=he(e,o.endOffset),a=he(e,r.endOffset,z.bytesToString),i=he(e,a.endOffset);n.gsubrs=i.objects,n.gsubrsBias=fe(n.gsubrs);const l=Ue(e,t,r.objects,a.objects);if(1!==l.length)throw new Error("CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = "+l.length);const u=l[0];if(n.tables.cff.topDict=u,u._privateDict&&(n.defaultWidthX=u._privateDict.defaultWidthX,n.nominalWidthX=u._privateDict.nominalWidthX),void 0!==u.ros[0]&&void 0!==u.ros[1]&&(n.isCIDFont=!0),n.isCIDFont){let s=u.fdArray,o=u.fdSelect;if(0===s||0===o)throw new Error("Font is marked as a CID font, but FDArray and/or FDSelect information is missing");s+=t;const r=Ue(e,t,he(e,s).objects,a.objects);u._fdArray=r,o+=t,u._fdSelect=function(e,t,n,s){const o=[];let r;const a=new z.Parser(e,t),i=a.parseCard8();if(0===i)for(let e=0;e<n;e++){if(r=a.parseCard8(),r>=s)throw new Error("CFF table CID Font FDSelect has bad FD index value "+r+" (FD count "+s+")");o.push(r)}else{if(3!==i)throw new Error("CFF Table CID Font FDSelect table has unsupported format "+i);{const e=a.parseCard16();let t,i=a.parseCard16();if(0!==i)throw new Error("CFF Table CID Font FDSelect format 3 range has bad initial GID "+i);for(let l=0;l<e;l++){if(r=a.parseCard8(),t=a.parseCard16(),r>=s)throw new Error("CFF table CID Font FDSelect has bad FD index value "+r+" (FD count "+s+")");if(t>n)throw new Error("CFF Table CID Font FDSelect format 3 range has bad GID "+t);for(;i<t;i++)o.push(r);i=t}if(t!==n)throw new Error("CFF Table CID Font FDSelect format 3 range has bad final GID "+t)}}return o}(e,o,n.numGlyphs,r.length)}const c=t+u.private[1],p=Se(e,c,u.private[0],a.objects);if(n.defaultWidthX=p.defaultWidthX,n.nominalWidthX=p.nominalWidthX,0!==p.subrs){const t=he(e,c+p.subrs);n.subrs=t.objects,n.subrsBias=fe(n.subrs)}else n.subrs=[],n.subrsBias=0;const f=he(e,t+u.charStrings);n.nGlyphs=f.objects.length;const h=function(e,t,n,s){let o,r;const a=new z.Parser(e,t);n-=1;const i=[".notdef"],l=a.parseCard8();if(0===l)for(let e=0;e<n;e+=1)o=a.parseSID(),i.push(me(s,o));else if(1===l)for(;i.length<=n;){o=a.parseSID(),r=a.parseCard8();for(let e=0;e<=r;e+=1)i.push(me(s,o)),o+=1}else{if(2!==l)throw new Error("Unknown charset format "+l);for(;i.length<=n;){o=a.parseSID(),r=a.parseCard16();for(let e=0;e<=r;e+=1)i.push(me(s,o)),o+=1}}return i}(e,t+u.charset,n.nGlyphs,a.objects);0===u.encoding?n.cffEncoding=new J(X,h):1===u.encoding?n.cffEncoding=new J(V,h):n.cffEncoding=function(e,t,n){let s;const o={},r=new z.Parser(e,t),a=r.parseCard8();if(0===a){const e=r.parseCard8();for(let t=0;t<e;t+=1)s=r.parseCard8(),o[s]=t}else{if(1!==a)throw new Error("Unknown encoding format "+a);{const e=r.parseCard8();s=1;for(let t=0;t<e;t+=1){const e=r.parseCard8(),t=r.parseCard8();for(let n=e;n<=e+t;n+=1)o[n]=s,s+=1}}}return new J(o,n)}(e,t+u.encoding,h),n.encoding=n.encoding||n.cffEncoding,n.glyphs=new ce.GlyphSet(n);for(let e=0;e<n.nGlyphs;e+=1){const t=f.objects[e];n.glyphs.push(e,ce.cffGlyphLoader(n,e,ke,t))}},make:function(e,t){const n=new M.Table("CFF ",[{name:"header",type:"RECORD"},{name:"nameIndex",type:"RECORD"},{name:"topDictIndex",type:"RECORD"},{name:"stringIndex",type:"RECORD"},{name:"globalSubrIndex",type:"RECORD"},{name:"charsets",type:"RECORD"},{name:"charStringsIndex",type:"RECORD"},{name:"privateDict",type:"RECORD"}]),s=1/t.unitsPerEm,o={version:t.version,fullName:t.fullName,familyName:t.familyName,weight:t.weightName,fontBBox:t.fontBBox||[0,0,0,0],fontMatrix:[s,0,0,s,0,0],charset:999,encoding:0,charStrings:999,private:[0,999]},r=[];let a;for(let t=1;t<e.length;t+=1)a=e.get(t),r.push(a.name);const i=[];n.header=new M.Record("Header",[{name:"major",type:"Card8",value:1},{name:"minor",type:"Card8",value:0},{name:"hdrSize",type:"Card8",value:4},{name:"major",type:"Card8",value:1}]),n.nameIndex=function(e){const t=new M.Record("Name INDEX",[{name:"names",type:"INDEX",value:[]}]);t.names=[];for(let n=0;n<e.length;n+=1)t.names.push({name:"name_"+n,type:"NAME",value:e[n]});return t}([t.postScriptName]);let l=we(o,i);n.topDictIndex=Oe(l),n.globalSubrIndex=new M.Record("Global Subr INDEX",[{name:"subrs",type:"INDEX",value:[]}]),n.charsets=function(e,t){const n=new M.Record("Charsets",[{name:"format",type:"Card8",value:0}]);for(let s=0;s<e.length;s+=1){const o=Te(e[s],t);n.fields.push({name:"glyph_"+s,type:"SID",value:o})}return n}(r,i),n.charStringsIndex=function(e){const t=new M.Record("CharStrings INDEX",[{name:"charStrings",type:"INDEX",value:[]}]);for(let n=0;n<e.length;n+=1){const s=e.get(n),o=Ie(s);t.charStrings.push({name:s.name,type:"CHARSTRING",value:o})}return t}(e),n.privateDict=function(e,t){const n=new M.Record("Private DICT",[{name:"dict",type:"DICT",value:{}}]);return n.dict=Ee(be,{},t),n}(0,i),n.stringIndex=function(e){const t=new M.Record("String INDEX",[{name:"strings",type:"INDEX",value:[]}]);t.strings=[];for(let n=0;n<e.length;n+=1)t.strings.push({name:"string_"+n,type:"STRING",value:e[n]});return t}(i);const u=n.header.sizeOf()+n.nameIndex.sizeOf()+n.topDictIndex.sizeOf()+n.stringIndex.sizeOf()+n.globalSubrIndex.sizeOf();return o.charset=u,o.encoding=0,o.charStrings=o.charset+n.charsets.sizeOf(),o.private[1]=o.charStrings+n.charStringsIndex.sizeOf(),l=we(o,i),n.topDictIndex=Oe(l),n}},Le={parse:function(e,t){const n={},s=new z.Parser(e,t);return n.version=s.parseVersion(),n.fontRevision=Math.round(1e3*s.parseFixed())/1e3,n.checkSumAdjustment=s.parseULong(),n.magicNumber=s.parseULong(),f.argument(1594834165===n.magicNumber,"Font header has wrong magic number."),n.flags=s.parseUShort(),n.unitsPerEm=s.parseUShort(),n.created=s.parseLongDateTime(),n.modified=s.parseLongDateTime(),n.xMin=s.parseShort(),n.yMin=s.parseShort(),n.xMax=s.parseShort(),n.yMax=s.parseShort(),n.macStyle=s.parseUShort(),n.lowestRecPPEM=s.parseUShort(),n.fontDirectionHint=s.parseShort(),n.indexToLocFormat=s.parseShort(),n.glyphDataFormat=s.parseShort(),n},make:function(e){const t=Math.round((new Date).getTime()/1e3)+2082844800;let n=t;return e.createdTimestamp&&(n=e.createdTimestamp+2082844800),new M.Table("head",[{name:"version",type:"FIXED",value:65536},{name:"fontRevision",type:"FIXED",value:65536},{name:"checkSumAdjustment",type:"ULONG",value:0},{name:"magicNumber",type:"ULONG",value:1594834165},{name:"flags",type:"USHORT",value:0},{name:"unitsPerEm",type:"USHORT",value:1e3},{name:"created",type:"LONGDATETIME",value:n},{name:"modified",type:"LONGDATETIME",value:t},{name:"xMin",type:"SHORT",value:0},{name:"yMin",type:"SHORT",value:0},{name:"xMax",type:"SHORT",value:0},{name:"yMax",type:"SHORT",value:0},{name:"macStyle",type:"USHORT",value:0},{name:"lowestRecPPEM",type:"USHORT",value:0},{name:"fontDirectionHint",type:"SHORT",value:2},{name:"indexToLocFormat",type:"SHORT",value:0},{name:"glyphDataFormat",type:"SHORT",value:0}],e)}},Be={parse:function(e,t){const n={},s=new z.Parser(e,t);return n.version=s.parseVersion(),n.ascender=s.parseShort(),n.descender=s.parseShort(),n.lineGap=s.parseShort(),n.advanceWidthMax=s.parseUShort(),n.minLeftSideBearing=s.parseShort(),n.minRightSideBearing=s.parseShort(),n.xMaxExtent=s.parseShort(),n.caretSlopeRise=s.parseShort(),n.caretSlopeRun=s.parseShort(),n.caretOffset=s.parseShort(),s.relativeOffset+=8,n.metricDataFormat=s.parseShort(),n.numberOfHMetrics=s.parseUShort(),n},make:function(e){return new M.Table("hhea",[{name:"version",type:"FIXED",value:65536},{name:"ascender",type:"FWORD",value:0},{name:"descender",type:"FWORD",value:0},{name:"lineGap",type:"FWORD",value:0},{name:"advanceWidthMax",type:"UFWORD",value:0},{name:"minLeftSideBearing",type:"FWORD",value:0},{name:"minRightSideBearing",type:"FWORD",value:0},{name:"xMaxExtent",type:"FWORD",value:0},{name:"caretSlopeRise",type:"SHORT",value:1},{name:"caretSlopeRun",type:"SHORT",value:0},{name:"caretOffset",type:"SHORT",value:0},{name:"reserved1",type:"SHORT",value:0},{name:"reserved2",type:"SHORT",value:0},{name:"reserved3",type:"SHORT",value:0},{name:"reserved4",type:"SHORT",value:0},{name:"metricDataFormat",type:"SHORT",value:0},{name:"numberOfHMetrics",type:"USHORT",value:0}],e)}},Ce={parse:function(e,t,n,s,o){let r,a;const i=new z.Parser(e,t);for(let e=0;e<s;e+=1){e<n&&(r=i.parseUShort(),a=i.parseShort());const t=o.get(e);t.advanceWidth=r,t.leftSideBearing=a}},make:function(e){const t=new M.Table("hmtx",[]);for(let n=0;n<e.length;n+=1){const s=e.get(n),o=s.advanceWidth||0,r=s.leftSideBearing||0;t.fields.push({name:"advanceWidth_"+n,type:"USHORT",value:o}),t.fields.push({name:"leftSideBearing_"+n,type:"SHORT",value:r})}return t}},De={make:function(e){const t=new M.Table("ltag",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"numTags",type:"ULONG",value:e.length}]);let n="";const s=12+4*e.length;for(let o=0;o<e.length;++o){let r=n.indexOf(e[o]);r<0&&(r=n.length,n+=e[o]),t.fields.push({name:"offset "+o,type:"USHORT",value:s+r}),t.fields.push({name:"length "+o,type:"USHORT",value:e[o].length})}return t.fields.push({name:"stringPool",type:"CHARARRAY",value:n}),t},parse:function(e,t){const n=new z.Parser(e,t),s=n.parseULong();f.argument(1===s,"Unsupported ltag table version."),n.skip("uLong",1);const o=n.parseULong(),r=[];for(let s=0;s<o;s++){let s="";const o=t+n.parseUShort(),a=n.parseUShort();for(let t=o;t<o+a;++t)s+=String.fromCharCode(e.getInt8(t));r.push(s)}return r}},Me={parse:function(e,t){const n={},s=new z.Parser(e,t);return n.version=s.parseVersion(),n.numGlyphs=s.parseUShort(),1===n.version&&(n.maxPoints=s.parseUShort(),n.maxContours=s.parseUShort(),n.maxCompositePoints=s.parseUShort(),n.maxCompositeContours=s.parseUShort(),n.maxZones=s.parseUShort(),n.maxTwilightPoints=s.parseUShort(),n.maxStorage=s.parseUShort(),n.maxFunctionDefs=s.parseUShort(),n.maxInstructionDefs=s.parseUShort(),n.maxStackElements=s.parseUShort(),n.maxSizeOfInstructions=s.parseUShort(),n.maxComponentElements=s.parseUShort(),n.maxComponentDepth=s.parseUShort()),n},make:function(e){return new M.Table("maxp",[{name:"version",type:"FIXED",value:20480},{name:"numGlyphs",type:"USHORT",value:e}])}},Ae=["copyright","fontFamily","fontSubfamily","uniqueID","fullName","version","postScriptName","trademark","manufacturer","designer","description","manufacturerURL","designerURL","license","licenseURL","reserved","preferredFamily","preferredSubfamily","compatibleFullName","sampleText","postScriptFindFontName","wwsFamily","wwsSubfamily"],Pe={0:"en",1:"fr",2:"de",3:"it",4:"nl",5:"sv",6:"es",7:"da",8:"pt",9:"no",10:"he",11:"ja",12:"ar",13:"fi",14:"el",15:"is",16:"mt",17:"tr",18:"hr",19:"zh-Hant",20:"ur",21:"hi",22:"th",23:"ko",24:"lt",25:"pl",26:"hu",27:"es",28:"lv",29:"se",30:"fo",31:"fa",32:"ru",33:"zh",34:"nl-BE",35:"ga",36:"sq",37:"ro",38:"cz",39:"sk",40:"si",41:"yi",42:"sr",43:"mk",44:"bg",45:"uk",46:"be",47:"uz",48:"kk",49:"az-Cyrl",50:"az-Arab",51:"hy",52:"ka",53:"mo",54:"ky",55:"tg",56:"tk",57:"mn-CN",58:"mn",59:"ps",60:"ks",61:"ku",62:"sd",63:"bo",64:"ne",65:"sa",66:"mr",67:"bn",68:"as",69:"gu",70:"pa",71:"or",72:"ml",73:"kn",74:"ta",75:"te",76:"si",77:"my",78:"km",79:"lo",80:"vi",81:"id",82:"tl",83:"ms",84:"ms-Arab",85:"am",86:"ti",87:"om",88:"so",89:"sw",90:"rw",91:"rn",92:"ny",93:"mg",94:"eo",128:"cy",129:"eu",130:"ca",131:"la",132:"qu",133:"gn",134:"ay",135:"tt",136:"ug",137:"dz",138:"jv",139:"su",140:"gl",141:"af",142:"br",143:"iu",144:"gd",145:"gv",146:"ga",147:"to",148:"el-polyton",149:"kl",150:"az",151:"nn"},Ge={0:0,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:5,11:1,12:4,13:0,14:6,15:0,16:0,17:0,18:0,19:2,20:4,21:9,22:21,23:3,24:29,25:29,26:29,27:29,28:29,29:0,30:0,31:4,32:7,33:25,34:0,35:0,36:0,37:0,38:29,39:29,40:0,41:5,42:7,43:7,44:7,45:7,46:7,47:7,48:7,49:7,50:4,51:24,52:23,53:7,54:7,55:7,56:7,57:27,58:7,59:4,60:4,61:4,62:4,63:26,64:9,65:9,66:9,67:13,68:13,69:11,70:10,71:12,72:17,73:16,74:14,75:15,76:18,77:19,78:20,79:22,80:30,81:0,82:0,83:0,84:4,85:28,86:28,87:28,88:0,89:0,90:0,91:0,92:0,93:0,94:0,128:0,129:0,130:0,131:0,132:0,133:0,134:0,135:7,136:4,137:26,138:0,139:0,140:0,141:0,142:0,143:28,144:0,145:0,146:0,147:0,148:6,149:0,150:0,151:0},Ne={1078:"af",1052:"sq",1156:"gsw",1118:"am",5121:"ar-DZ",15361:"ar-BH",3073:"ar",2049:"ar-IQ",11265:"ar-JO",13313:"ar-KW",12289:"ar-LB",4097:"ar-LY",6145:"ary",8193:"ar-OM",16385:"ar-QA",1025:"ar-SA",10241:"ar-SY",7169:"aeb",14337:"ar-AE",9217:"ar-YE",1067:"hy",1101:"as",2092:"az-Cyrl",1068:"az",1133:"ba",1069:"eu",1059:"be",2117:"bn",1093:"bn-IN",8218:"bs-Cyrl",5146:"bs",1150:"br",1026:"bg",1027:"ca",3076:"zh-HK",5124:"zh-MO",2052:"zh",4100:"zh-SG",1028:"zh-TW",1155:"co",1050:"hr",4122:"hr-BA",1029:"cs",1030:"da",1164:"prs",1125:"dv",2067:"nl-BE",1043:"nl",3081:"en-AU",10249:"en-BZ",4105:"en-CA",9225:"en-029",16393:"en-IN",6153:"en-IE",8201:"en-JM",17417:"en-MY",5129:"en-NZ",13321:"en-PH",18441:"en-SG",7177:"en-ZA",11273:"en-TT",2057:"en-GB",1033:"en",12297:"en-ZW",1061:"et",1080:"fo",1124:"fil",1035:"fi",2060:"fr-BE",3084:"fr-CA",1036:"fr",5132:"fr-LU",6156:"fr-MC",4108:"fr-CH",1122:"fy",1110:"gl",1079:"ka",3079:"de-AT",1031:"de",5127:"de-LI",4103:"de-LU",2055:"de-CH",1032:"el",1135:"kl",1095:"gu",1128:"ha",1037:"he",1081:"hi",1038:"hu",1039:"is",1136:"ig",1057:"id",1117:"iu",2141:"iu-Latn",2108:"ga",1076:"xh",1077:"zu",1040:"it",2064:"it-CH",1041:"ja",1099:"kn",1087:"kk",1107:"km",1158:"quc",1159:"rw",1089:"sw",1111:"kok",1042:"ko",1088:"ky",1108:"lo",1062:"lv",1063:"lt",2094:"dsb",1134:"lb",1071:"mk",2110:"ms-BN",1086:"ms",1100:"ml",1082:"mt",1153:"mi",1146:"arn",1102:"mr",1148:"moh",1104:"mn",2128:"mn-CN",1121:"ne",1044:"nb",2068:"nn",1154:"oc",1096:"or",1123:"ps",1045:"pl",1046:"pt",2070:"pt-PT",1094:"pa",1131:"qu-BO",2155:"qu-EC",3179:"qu",1048:"ro",1047:"rm",1049:"ru",9275:"smn",4155:"smj-NO",5179:"smj",3131:"se-FI",1083:"se",2107:"se-SE",8251:"sms",6203:"sma-NO",7227:"sms",1103:"sa",7194:"sr-Cyrl-BA",3098:"sr",6170:"sr-Latn-BA",2074:"sr-Latn",1132:"nso",1074:"tn",1115:"si",1051:"sk",1060:"sl",11274:"es-AR",16394:"es-BO",13322:"es-CL",9226:"es-CO",5130:"es-CR",7178:"es-DO",12298:"es-EC",17418:"es-SV",4106:"es-GT",18442:"es-HN",2058:"es-MX",19466:"es-NI",6154:"es-PA",15370:"es-PY",10250:"es-PE",20490:"es-PR",3082:"es",1034:"es",21514:"es-US",14346:"es-UY",8202:"es-VE",2077:"sv-FI",1053:"sv",1114:"syr",1064:"tg",2143:"tzm",1097:"ta",1092:"tt",1098:"te",1054:"th",1105:"bo",1055:"tr",1090:"tk",1152:"ug",1058:"uk",1070:"hsb",1056:"ur",2115:"uz-Cyrl",1091:"uz",1066:"vi",1106:"cy",1160:"wo",1157:"sah",1144:"ii",1130:"yo"};function Fe(e,t,n){switch(e){case 0:if(65535===t)return"und";if(n)return n[t];break;case 1:return Pe[t];case 3:return Ne[t]}}const _e="utf-16",He={0:"macintosh",1:"x-mac-japanese",2:"x-mac-chinesetrad",3:"x-mac-korean",6:"x-mac-greek",7:"x-mac-cyrillic",9:"x-mac-devanagai",10:"x-mac-gurmukhi",11:"x-mac-gujarati",12:"x-mac-oriya",13:"x-mac-bengali",14:"x-mac-tamil",15:"x-mac-telugu",16:"x-mac-kannada",17:"x-mac-malayalam",18:"x-mac-sinhalese",19:"x-mac-burmese",20:"x-mac-khmer",21:"x-mac-thai",22:"x-mac-lao",23:"x-mac-georgian",24:"x-mac-armenian",25:"x-mac-chinesesimp",26:"x-mac-tibetan",27:"x-mac-mongolian",28:"x-mac-ethiopic",29:"x-mac-ce",30:"x-mac-vietnamese",31:"x-mac-extarabic"},ze={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"};function We(e,t,n){switch(e){case 0:return _e;case 1:return ze[n]||He[t];case 3:if(1===t||10===t)return _e}}function qe(e){const t={};for(let n in e)t[e[n]]=parseInt(n);return t}function je(e,t,n,s,o,r){return new M.Record("NameRecord",[{name:"platformID",type:"USHORT",value:e},{name:"encodingID",type:"USHORT",value:t},{name:"languageID",type:"USHORT",value:n},{name:"nameID",type:"USHORT",value:s},{name:"length",type:"USHORT",value:o},{name:"offset",type:"USHORT",value:r}])}function Xe(e,t){let n=function(e,t){const n=e.length,s=t.length-n+1;e:for(let o=0;o<s;o++)for(;o<s;o++){for(let s=0;s<n;s++)if(t[o+s]!==e[s])continue e;return o}return-1}(e,t);if(n<0){n=t.length;let s=0;const o=e.length;for(;s<o;++s)t.push(e[s])}return n}const Ve={parse:function(e,t,n){const s={},o=new z.Parser(e,t),r=o.parseUShort(),a=o.parseUShort(),i=o.offset+o.parseUShort();for(let t=0;t<a;t++){const t=o.parseUShort(),r=o.parseUShort(),a=o.parseUShort(),l=o.parseUShort(),u=Ae[l]||l,c=o.parseUShort(),p=o.parseUShort(),f=Fe(t,a,n),h=We(t,r,a);if(void 0!==h&&void 0!==f){let t;if(t=h===_e?d.UTF16(e,i+p,c):d.MACSTRING(e,i+p,c,h),t){let e=s[u];void 0===e&&(e=s[u]={}),e[f]=t}}}let l=0;return 1===r&&o.parseUShort(),s},make:function(e,t){let n;const s=[],o={},r=qe(Ae);for(let t in e){let a=r[t];if(void 0===a&&(a=t),n=parseInt(a),isNaN(n))throw new Error('Name table entry "'+t+'" does not exist, see nameTableNames for complete list.');o[n]=e[t],s.push(n)}const a=qe(Pe),i=qe(Ne),l=[],u=[];for(let e=0;e<s.length;e++){n=s[e];const r=o[n];for(let e in r){const s=r[e];let o=1,c=a[e],p=Ge[c];const f=We(o,p,c);let h=g.MACSTRING(s,f);void 0===h&&(o=0,c=t.indexOf(e),c<0&&(c=t.length,t.push(e)),p=4,h=g.UTF16(s));const d=Xe(h,u);l.push(je(o,p,c,n,h.length,d));const m=i[e];if(void 0!==m){const e=g.UTF16(s),t=Xe(e,u);l.push(je(3,1,m,n,e.length,t))}}}l.sort((function(e,t){return e.platformID-t.platformID||e.encodingID-t.encodingID||e.languageID-t.languageID||e.nameID-t.nameID}));const c=new M.Table("name",[{name:"format",type:"USHORT",value:0},{name:"count",type:"USHORT",value:l.length},{name:"stringOffset",type:"USHORT",value:6+12*l.length}]);for(let e=0;e<l.length;e++)c.fields.push({name:"record_"+e,type:"RECORD",value:l[e]});return c.fields.push({name:"strings",type:"LITERAL",value:u}),c}},Ye=[{begin:0,end:127},{begin:128,end:255},{begin:256,end:383},{begin:384,end:591},{begin:592,end:687},{begin:688,end:767},{begin:768,end:879},{begin:880,end:1023},{begin:11392,end:11519},{begin:1024,end:1279},{begin:1328,end:1423},{begin:1424,end:1535},{begin:42240,end:42559},{begin:1536,end:1791},{begin:1984,end:2047},{begin:2304,end:2431},{begin:2432,end:2559},{begin:2560,end:2687},{begin:2688,end:2815},{begin:2816,end:2943},{begin:2944,end:3071},{begin:3072,end:3199},{begin:3200,end:3327},{begin:3328,end:3455},{begin:3584,end:3711},{begin:3712,end:3839},{begin:4256,end:4351},{begin:6912,end:7039},{begin:4352,end:4607},{begin:7680,end:7935},{begin:7936,end:8191},{begin:8192,end:8303},{begin:8304,end:8351},{begin:8352,end:8399},{begin:8400,end:8447},{begin:8448,end:8527},{begin:8528,end:8591},{begin:8592,end:8703},{begin:8704,end:8959},{begin:8960,end:9215},{begin:9216,end:9279},{begin:9280,end:9311},{begin:9312,end:9471},{begin:9472,end:9599},{begin:9600,end:9631},{begin:9632,end:9727},{begin:9728,end:9983},{begin:9984,end:10175},{begin:12288,end:12351},{begin:12352,end:12447},{begin:12448,end:12543},{begin:12544,end:12591},{begin:12592,end:12687},{begin:43072,end:43135},{begin:12800,end:13055},{begin:13056,end:13311},{begin:44032,end:55215},{begin:55296,end:57343},{begin:67840,end:67871},{begin:19968,end:40959},{begin:57344,end:63743},{begin:12736,end:12783},{begin:64256,end:64335},{begin:64336,end:65023},{begin:65056,end:65071},{begin:65040,end:65055},{begin:65104,end:65135},{begin:65136,end:65279},{begin:65280,end:65519},{begin:65520,end:65535},{begin:3840,end:4095},{begin:1792,end:1871},{begin:1920,end:1983},{begin:3456,end:3583},{begin:4096,end:4255},{begin:4608,end:4991},{begin:5024,end:5119},{begin:5120,end:5759},{begin:5760,end:5791},{begin:5792,end:5887},{begin:6016,end:6143},{begin:6144,end:6319},{begin:10240,end:10495},{begin:40960,end:42127},{begin:5888,end:5919},{begin:66304,end:66351},{begin:66352,end:66383},{begin:66560,end:66639},{begin:118784,end:119039},{begin:119808,end:120831},{begin:1044480,end:1048573},{begin:65024,end:65039},{begin:917504,end:917631},{begin:6400,end:6479},{begin:6480,end:6527},{begin:6528,end:6623},{begin:6656,end:6687},{begin:11264,end:11359},{begin:11568,end:11647},{begin:19904,end:19967},{begin:43008,end:43055},{begin:65536,end:65663},{begin:65856,end:65935},{begin:66432,end:66463},{begin:66464,end:66527},{begin:66640,end:66687},{begin:66688,end:66735},{begin:67584,end:67647},{begin:68096,end:68191},{begin:119552,end:119647},{begin:73728,end:74751},{begin:119648,end:119679},{begin:7040,end:7103},{begin:7168,end:7247},{begin:7248,end:7295},{begin:43136,end:43231},{begin:43264,end:43311},{begin:43312,end:43359},{begin:43520,end:43615},{begin:65936,end:65999},{begin:66e3,end:66047},{begin:66208,end:66271},{begin:127024,end:127135}],Ze={parse:function(e,t){const n={},s=new z.Parser(e,t);n.version=s.parseUShort(),n.xAvgCharWidth=s.parseShort(),n.usWeightClass=s.parseUShort(),n.usWidthClass=s.parseUShort(),n.fsType=s.parseUShort(),n.ySubscriptXSize=s.parseShort(),n.ySubscriptYSize=s.parseShort(),n.ySubscriptXOffset=s.parseShort(),n.ySubscriptYOffset=s.parseShort(),n.ySuperscriptXSize=s.parseShort(),n.ySuperscriptYSize=s.parseShort(),n.ySuperscriptXOffset=s.parseShort(),n.ySuperscriptYOffset=s.parseShort(),n.yStrikeoutSize=s.parseShort(),n.yStrikeoutPosition=s.parseShort(),n.sFamilyClass=s.parseShort(),n.panose=[];for(let e=0;e<10;e++)n.panose[e]=s.parseByte();return n.ulUnicodeRange1=s.parseULong(),n.ulUnicodeRange2=s.parseULong(),n.ulUnicodeRange3=s.parseULong(),n.ulUnicodeRange4=s.parseULong(),n.achVendID=String.fromCharCode(s.parseByte(),s.parseByte(),s.parseByte(),s.parseByte()),n.fsSelection=s.parseUShort(),n.usFirstCharIndex=s.parseUShort(),n.usLastCharIndex=s.parseUShort(),n.sTypoAscender=s.parseShort(),n.sTypoDescender=s.parseShort(),n.sTypoLineGap=s.parseShort(),n.usWinAscent=s.parseUShort(),n.usWinDescent=s.parseUShort(),n.version>=1&&(n.ulCodePageRange1=s.parseULong(),n.ulCodePageRange2=s.parseULong()),n.version>=2&&(n.sxHeight=s.parseShort(),n.sCapHeight=s.parseShort(),n.usDefaultChar=s.parseUShort(),n.usBreakChar=s.parseUShort(),n.usMaxContent=s.parseUShort()),n},make:function(e){return new M.Table("OS/2",[{name:"version",type:"USHORT",value:3},{name:"xAvgCharWidth",type:"SHORT",value:0},{name:"usWeightClass",type:"USHORT",value:0},{name:"usWidthClass",type:"USHORT",value:0},{name:"fsType",type:"USHORT",value:0},{name:"ySubscriptXSize",type:"SHORT",value:650},{name:"ySubscriptYSize",type:"SHORT",value:699},{name:"ySubscriptXOffset",type:"SHORT",value:0},{name:"ySubscriptYOffset",type:"SHORT",value:140},{name:"ySuperscriptXSize",type:"SHORT",value:650},{name:"ySuperscriptYSize",type:"SHORT",value:699},{name:"ySuperscriptXOffset",type:"SHORT",value:0},{name:"ySuperscriptYOffset",type:"SHORT",value:479},{name:"yStrikeoutSize",type:"SHORT",value:49},{name:"yStrikeoutPosition",type:"SHORT",value:258},{name:"sFamilyClass",type:"SHORT",value:0},{name:"bFamilyType",type:"BYTE",value:0},{name:"bSerifStyle",type:"BYTE",value:0},{name:"bWeight",type:"BYTE",value:0},{name:"bProportion",type:"BYTE",value:0},{name:"bContrast",type:"BYTE",value:0},{name:"bStrokeVariation",type:"BYTE",value:0},{name:"bArmStyle",type:"BYTE",value:0},{name:"bLetterform",type:"BYTE",value:0},{name:"bMidline",type:"BYTE",value:0},{name:"bXHeight",type:"BYTE",value:0},{name:"ulUnicodeRange1",type:"ULONG",value:0},{name:"ulUnicodeRange2",type:"ULONG",value:0},{name:"ulUnicodeRange3",type:"ULONG",value:0},{name:"ulUnicodeRange4",type:"ULONG",value:0},{name:"achVendID",type:"CHARARRAY",value:"XXXX"},{name:"fsSelection",type:"USHORT",value:0},{name:"usFirstCharIndex",type:"USHORT",value:0},{name:"usLastCharIndex",type:"USHORT",value:0},{name:"sTypoAscender",type:"SHORT",value:0},{name:"sTypoDescender",type:"SHORT",value:0},{name:"sTypoLineGap",type:"SHORT",value:0},{name:"usWinAscent",type:"USHORT",value:0},{name:"usWinDescent",type:"USHORT",value:0},{name:"ulCodePageRange1",type:"ULONG",value:0},{name:"ulCodePageRange2",type:"ULONG",value:0},{name:"sxHeight",type:"SHORT",value:0},{name:"sCapHeight",type:"SHORT",value:0},{name:"usDefaultChar",type:"USHORT",value:0},{name:"usBreakChar",type:"USHORT",value:0},{name:"usMaxContext",type:"USHORT",value:0}],e)},unicodeRanges:Ye,getUnicodeRange:function(e){for(let t=0;t<Ye.length;t+=1){const n=Ye[t];if(e>=n.begin&&e<n.end)return t}return-1}},Qe={parse:function(e,t){const n={},s=new z.Parser(e,t);switch(n.version=s.parseVersion(),n.italicAngle=s.parseFixed(),n.underlinePosition=s.parseShort(),n.underlineThickness=s.parseShort(),n.isFixedPitch=s.parseULong(),n.minMemType42=s.parseULong(),n.maxMemType42=s.parseULong(),n.minMemType1=s.parseULong(),n.maxMemType1=s.parseULong(),n.version){case 1:n.names=Y.slice();break;case 2:n.numberOfGlyphs=s.parseUShort(),n.glyphNameIndex=new Array(n.numberOfGlyphs);for(let e=0;e<n.numberOfGlyphs;e++)n.glyphNameIndex[e]=s.parseUShort();n.names=[];for(let e=0;e<n.numberOfGlyphs;e++)if(n.glyphNameIndex[e]>=Y.length){const e=s.parseChar();n.names.push(s.parseString(e))}break;case 2.5:n.numberOfGlyphs=s.parseUShort(),n.offset=new Array(n.numberOfGlyphs);for(let e=0;e<n.numberOfGlyphs;e++)n.offset[e]=s.parseChar()}return n},make:function(){return new M.Table("post",[{name:"version",type:"FIXED",value:196608},{name:"italicAngle",type:"FIXED",value:0},{name:"underlinePosition",type:"FWORD",value:0},{name:"underlineThickness",type:"FWORD",value:0},{name:"isFixedPitch",type:"ULONG",value:0},{name:"minMemType42",type:"ULONG",value:0},{name:"maxMemType42",type:"ULONG",value:0},{name:"minMemType1",type:"ULONG",value:0},{name:"maxMemType1",type:"ULONG",value:0}])}},Je=new Array(9);Je[1]=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(_.coverage),deltaGlyphId:this.parseUShort()}:2===t?{substFormat:2,coverage:this.parsePointer(_.coverage),substitute:this.parseOffset16List()}:void f.assert(!1,"0x"+e.toString(16)+": lookup type 1 format must be 1 or 2.")},Je[2]=function(){const e=this.parseUShort();return f.argument(1===e,"GSUB Multiple Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(_.coverage),sequences:this.parseListOfLists()}},Je[3]=function(){const e=this.parseUShort();return f.argument(1===e,"GSUB Alternate Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(_.coverage),alternateSets:this.parseListOfLists()}},Je[4]=function(){const e=this.parseUShort();return f.argument(1===e,"GSUB ligature table identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(_.coverage),ligatureSets:this.parseListOfLists((function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}}))}};const Ke={sequenceIndex:_.uShort,lookupListIndex:_.uShort};Je[5]=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{substFormat:t,coverage:this.parsePointer(_.coverage),ruleSets:this.parseListOfLists((function(){const e=this.parseUShort(),t=this.parseUShort();return{input:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,Ke)}}))};if(2===t)return{substFormat:t,coverage:this.parsePointer(_.coverage),classDef:this.parsePointer(_.classDef),classSets:this.parseListOfLists((function(){const e=this.parseUShort(),t=this.parseUShort();return{classes:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,Ke)}}))};if(3===t){const e=this.parseUShort(),n=this.parseUShort();return{substFormat:t,coverages:this.parseList(e,_.pointer(_.coverage)),lookupRecords:this.parseRecordList(n,Ke)}}f.assert(!1,"0x"+e.toString(16)+": lookup type 5 format must be 1, 2 or 3.")},Je[6]=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(_.coverage),chainRuleSets:this.parseListOfLists((function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(Ke)}}))}:2===t?{substFormat:2,coverage:this.parsePointer(_.coverage),backtrackClassDef:this.parsePointer(_.classDef),inputClassDef:this.parsePointer(_.classDef),lookaheadClassDef:this.parsePointer(_.classDef),chainClassSet:this.parseListOfLists((function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(Ke)}}))}:3===t?{substFormat:3,backtrackCoverage:this.parseList(_.pointer(_.coverage)),inputCoverage:this.parseList(_.pointer(_.coverage)),lookaheadCoverage:this.parseList(_.pointer(_.coverage)),lookupRecords:this.parseRecordList(Ke)}:void f.assert(!1,"0x"+e.toString(16)+": lookup type 6 format must be 1, 2 or 3.")},Je[7]=function(){const e=this.parseUShort();f.argument(1===e,"GSUB Extension Substitution subtable identifier-format must be 1");const t=this.parseUShort(),n=new _(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:Je[t].call(n)}},Je[8]=function(){const e=this.parseUShort();return f.argument(1===e,"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(_.coverage),backtrackCoverage:this.parseList(_.pointer(_.coverage)),lookaheadCoverage:this.parseList(_.pointer(_.coverage)),substitutes:this.parseUShortList()}};const $e=new Array(9);$e[1]=function(e){return 1===e.substFormat?new M.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new M.Coverage(e.coverage)},{name:"deltaGlyphID",type:"USHORT",value:e.deltaGlyphId}]):new M.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:2},{name:"coverage",type:"TABLE",value:new M.Coverage(e.coverage)}].concat(M.ushortList("substitute",e.substitute)))},$e[3]=function(e){return f.assert(1===e.substFormat,"Lookup type 3 substFormat must be 1."),new M.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new M.Coverage(e.coverage)}].concat(M.tableList("altSet",e.alternateSets,(function(e){return new M.Table("alternateSetTable",M.ushortList("alternate",e))}))))},$e[4]=function(e){return f.assert(1===e.substFormat,"Lookup type 4 substFormat must be 1."),new M.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new M.Coverage(e.coverage)}].concat(M.tableList("ligSet",e.ligatureSets,(function(e){return new M.Table("ligatureSetTable",M.tableList("ligature",e,(function(e){return new M.Table("ligatureTable",[{name:"ligGlyph",type:"USHORT",value:e.ligGlyph}].concat(M.ushortList("component",e.components,e.components.length+1)))})))}))))};const et={parse:function(e,t){const n=new _(e,t=t||0),s=n.parseVersion(1);return f.argument(1===s||1.1===s,"Unsupported GSUB table version."),1===s?{version:s,scripts:n.parseScriptList(),features:n.parseFeatureList(),lookups:n.parseLookupList(Je)}:{version:s,scripts:n.parseScriptList(),features:n.parseFeatureList(),lookups:n.parseLookupList(Je),variations:n.parseFeatureVariationsList()}},make:function(e){return new M.Table("GSUB",[{name:"version",type:"ULONG",value:65536},{name:"scripts",type:"TABLE",value:new M.ScriptList(e.scripts)},{name:"features",type:"TABLE",value:new M.FeatureList(e.features)},{name:"lookups",type:"TABLE",value:new M.LookupList(e.lookups,$e)}])}},tt={parse:function(e,t){const n=new z.Parser(e,t),s=n.parseULong();f.argument(1===s,"Unsupported META table version."),n.parseULong(),n.parseULong();const o=n.parseULong(),r={};for(let s=0;s<o;s++){const s=n.parseTag(),o=n.parseULong(),a=n.parseULong(),i=d.UTF8(e,t+o,a);r[s]=i}return r},make:function(e){const t=Object.keys(e).length;let n="";const s=16+12*t,o=new M.Table("meta",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"offset",type:"ULONG",value:s},{name:"numTags",type:"ULONG",value:t}]);for(let t in e){const r=n.length;n+=e[t],o.fields.push({name:"tag "+t,type:"TAG",value:t}),o.fields.push({name:"offset "+t,type:"ULONG",value:s+r}),o.fields.push({name:"length "+t,type:"ULONG",value:e[t].length})}return o.fields.push({name:"stringPool",type:"CHARARRAY",value:n}),o}};function nt(e){return Math.log(e)/Math.log(2)|0}function st(e){for(;e.length%4!=0;)e.push(0);let t=0;for(let n=0;n<e.length;n+=4)t+=(e[n]<<24)+(e[n+1]<<16)+(e[n+2]<<8)+e[n+3];return t%=Math.pow(2,32),t}function ot(e,t,n,s){return new M.Record("Table Record",[{name:"tag",type:"TAG",value:void 0!==e?e:""},{name:"checkSum",type:"ULONG",value:void 0!==t?t:0},{name:"offset",type:"ULONG",value:void 0!==n?n:0},{name:"length",type:"ULONG",value:void 0!==s?s:0}])}function rt(e){const t=new M.Table("sfnt",[{name:"version",type:"TAG",value:"OTTO"},{name:"numTables",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);t.tables=e,t.numTables=e.length;const n=Math.pow(2,nt(t.numTables));t.searchRange=16*n,t.entrySelector=nt(n),t.rangeShift=16*t.numTables-t.searchRange;const s=[],o=[];let r=t.sizeOf()+ot().sizeOf()*t.numTables;for(;r%4!=0;)r+=1,o.push({name:"padding",type:"BYTE",value:0});for(let t=0;t<e.length;t+=1){const n=e[t];f.argument(4===n.tableName.length,"Table name"+n.tableName+" is invalid.");const a=n.sizeOf(),i=ot(n.tableName,st(n.encode()),r,a);for(s.push({name:i.tag+" Table Record",type:"RECORD",value:i}),o.push({name:n.tableName+" table",type:"RECORD",value:n}),r+=a,f.argument(!isNaN(r),"Something went wrong calculating the offset.");r%4!=0;)r+=1,o.push({name:"padding",type:"BYTE",value:0})}return s.sort((function(e,t){return e.value.tag>t.value.tag?1:-1})),t.fields=t.fields.concat(s),t.fields=t.fields.concat(o),t}function at(e,t,n){for(let n=0;n<t.length;n+=1){const s=e.charToGlyphIndex(t[n]);if(s>0)return e.glyphs.get(s).getMetrics()}return n}function it(e){let t=0;for(let n=0;n<e.length;n+=1)t+=e[n];return t/e.length}const lt=function(e){const t=[],n=[],s=[],o=[],r=[],a=[],i=[];let l,u=0,c=0,p=0,f=0,h=0;for(let d=0;d<e.glyphs.length;d+=1){const g=e.glyphs.get(d),m=0|g.unicode;if(isNaN(g.advanceWidth))throw new Error("Glyph "+g.name+" ("+d+"): advanceWidth is not a number.");(l>m||void 0===l)&&m>0&&(l=m),u<m&&(u=m);const y=Ze.getUnicodeRange(m);if(y<32)c|=1<<y;else if(y<64)p|=1<<y-32;else if(y<96)f|=1<<y-64;else{if(!(y<123))throw new Error("Unicode ranges bits > 123 are reserved for internal usage");h|=1<<y-96}if(".notdef"===g.name)continue;const v=g.getMetrics();t.push(v.xMin),n.push(v.yMin),s.push(v.xMax),o.push(v.yMax),a.push(v.leftSideBearing),i.push(v.rightSideBearing),r.push(g.advanceWidth)}const d={xMin:Math.min.apply(null,t),yMin:Math.min.apply(null,n),xMax:Math.max.apply(null,s),yMax:Math.max.apply(null,o),advanceWidthMax:Math.max.apply(null,r),advanceWidthAvg:it(r),minLeftSideBearing:Math.min.apply(null,a),maxLeftSideBearing:Math.max.apply(null,a),minRightSideBearing:Math.min.apply(null,i)};d.ascender=e.ascender,d.descender=e.descender;const g=Le.make({flags:3,unitsPerEm:e.unitsPerEm,xMin:d.xMin,yMin:d.yMin,xMax:d.xMax,yMax:d.yMax,lowestRecPPEM:3,createdTimestamp:e.createdTimestamp}),m=Be.make({ascender:d.ascender,descender:d.descender,advanceWidthMax:d.advanceWidthMax,minLeftSideBearing:d.minLeftSideBearing,minRightSideBearing:d.minRightSideBearing,xMaxExtent:d.maxLeftSideBearing+(d.xMax-d.xMin),numberOfHMetrics:e.glyphs.length}),y=Me.make(e.glyphs.length),v=Ze.make({xAvgCharWidth:Math.round(d.advanceWidthAvg),usWeightClass:e.tables.os2.usWeightClass,usWidthClass:e.tables.os2.usWidthClass,usFirstCharIndex:l,usLastCharIndex:u,ulUnicodeRange1:c,ulUnicodeRange2:p,ulUnicodeRange3:f,ulUnicodeRange4:h,fsSelection:e.tables.os2.fsSelection,sTypoAscender:d.ascender,sTypoDescender:d.descender,sTypoLineGap:0,usWinAscent:d.yMax,usWinDescent:Math.abs(d.yMin),ulCodePageRange1:1,sxHeight:at(e,"xyvw",{yMax:Math.round(d.ascender/2)}).yMax,sCapHeight:at(e,"HIKLEFJMNTZBDPRAGOQSUVWXY",d).yMax,usDefaultChar:e.hasChar(" ")?32:0,usBreakChar:e.hasChar(" ")?32:0}),b=Ce.make(e.glyphs),x=q.make(e.glyphs),S=e.getEnglishName("fontFamily"),U=e.getEnglishName("fontSubfamily"),k=S+" "+U;let T=e.getEnglishName("postScriptName");T||(T=S.replace(/\s/g,"")+"-"+U);const E={};for(let t in e.names)E[t]=e.names[t];E.uniqueID||(E.uniqueID={en:e.getEnglishName("manufacturer")+":"+k}),E.postScriptName||(E.postScriptName={en:T}),E.preferredFamily||(E.preferredFamily=e.names.fontFamily),E.preferredSubfamily||(E.preferredSubfamily=e.names.fontSubfamily);const w=[],O=Ve.make(E,w),I=w.length>0?De.make(w):void 0,R=Qe.make(),L=Re.make(e.glyphs,{version:e.getEnglishName("version"),fullName:k,familyName:S,weightName:U,postScriptName:T,unitsPerEm:e.unitsPerEm,fontBBox:[0,d.yMin,d.ascender,d.advanceWidthMax]}),B=e.metas&&Object.keys(e.metas).length>0?tt.make(e.metas):void 0,C=[g,m,y,v,O,x,R,L,b];I&&C.push(I),e.tables.gsub&&C.push(et.make(e.tables.gsub)),B&&C.push(B);const D=rt(C),M=st(D.encode()),A=D.fields;let P=!1;for(let e=0;e<A.length;e+=1)if("head table"===A[e].name){A[e].value.checkSumAdjustment=2981146554-M,P=!0;break}if(!P)throw new Error("Could not find head table with checkSum to adjust.");return D};function ut(e,t){let n=0,s=e.length-1;for(;n<=s;){const o=n+s>>>1,r=e[o].tag;if(r===t)return o;r<t?n=o+1:s=o-1}return-n-1}function ct(e,t){let n=0,s=e.length-1;for(;n<=s;){const o=n+s>>>1,r=e[o];if(r===t)return o;r<t?n=o+1:s=o-1}return-n-1}function pt(e,t){let n,s=0,o=e.length-1;for(;s<=o;){const r=s+o>>>1;n=e[r];const a=n.start;if(a===t)return n;a<t?s=r+1:o=r-1}if(s>0)return n=e[s-1],t>n.end?0:n}function ft(e,t){this.font=e,this.tableName=t}ft.prototype={searchTag:ut,binSearch:ct,getTable:function(e){let t=this.font.tables[this.tableName];return!t&&e&&(t=this.font.tables[this.tableName]=this.createDefaultTable()),t},getScriptNames:function(){let e=this.getTable();return e?e.scripts.map((function(e){return e.tag})):[]},getDefaultScriptName:function(){let e=this.getTable();if(!e)return;let t=!1;for(let n=0;n<e.scripts.length;n++){const s=e.scripts[n].tag;if("DFLT"===s)return s;"latn"===s&&(t=!0)}return t?"latn":void 0},getScriptTable:function(e,t){const n=this.getTable(t);if(n){e=e||"DFLT";const s=n.scripts,o=ut(n.scripts,e);if(o>=0)return s[o].script;if(t){const t={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return s.splice(-1-o,0,t),t.script}}},getLangSysTable:function(e,t,n){const s=this.getScriptTable(e,n);if(s){if(!t||"dflt"===t||"DFLT"===t)return s.defaultLangSys;const e=ut(s.langSysRecords,t);if(e>=0)return s.langSysRecords[e].langSys;if(n){const n={tag:t,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return s.langSysRecords.splice(-1-e,0,n),n.langSys}}},getFeatureTable:function(e,t,n,s){const o=this.getLangSysTable(e,t,s);if(o){let e;const t=o.featureIndexes,r=this.font.tables[this.tableName].features;for(let s=0;s<t.length;s++)if(e=r[t[s]],e.tag===n)return e.feature;if(s){const s=r.length;return f.assert(0===s||n>=r[s-1].tag,"Features must be added in alphabetical order."),e={tag:n,feature:{params:0,lookupListIndexes:[]}},r.push(e),t.push(s),e.feature}}},getLookupTables:function(e,t,n,s,o){const r=this.getFeatureTable(e,t,n,o),a=[];if(r){let e;const t=r.lookupListIndexes,n=this.font.tables[this.tableName].lookups;for(let o=0;o<t.length;o++)e=n[t[o]],e.lookupType===s&&a.push(e);if(0===a.length&&o){e={lookupType:s,lookupFlag:0,subtables:[],markFilteringSet:void 0};const o=n.length;return n.push(e),t.push(o),[e]}}return a},getGlyphClass:function(e,t){switch(e.format){case 1:return e.startGlyph<=t&&t<e.startGlyph+e.classes.length?e.classes[t-e.startGlyph]:0;case 2:const n=pt(e.ranges,t);return n?n.classId:0}},getCoverageIndex:function(e,t){switch(e.format){case 1:const n=ct(e.glyphs,t);return n>=0?n:-1;case 2:const s=pt(e.ranges,t);return s?s.index+t-s.start:-1}},expandCoverage:function(e){if(1===e.format)return e.glyphs;{const t=[],n=e.ranges;for(let e=0;e<n.length;e++){const s=n[e],o=s.start,r=s.end;for(let e=o;e<=r;e++)t.push(e)}return t}}};const ht=ft;function dt(e){ht.call(this,e,"gpos")}dt.prototype=ht.prototype,dt.prototype.getKerningValue=function(e,t,n){for(let s=0;s<e.length;s++){const o=e[s].subtables;for(let e=0;e<o.length;e++){const s=o[e],r=this.getCoverageIndex(s.coverage,t);if(!(r<0))switch(s.posFormat){case 1:let e=s.pairSets[r];for(let t=0;t<e.length;t++){let s=e[t];if(s.secondGlyph===n)return s.value1&&s.value1.xAdvance||0}break;case 2:const o=this.getGlyphClass(s.classDef1,t),a=this.getGlyphClass(s.classDef2,n),i=s.classRecords[o][a];return i.value1&&i.value1.xAdvance||0}}}return 0},dt.prototype.getKerningTables=function(e,t){if(this.font.tables.gpos)return this.getLookupTables(e,t,"kern",2)};const gt=dt;function mt(e){ht.call(this,e,"gsub")}function yt(e,t){const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function vt(e,t,n){const s=e.subtables;for(let e=0;e<s.length;e++){const n=s[e];if(n.substFormat===t)return n}if(n)return s.push(n),n}mt.prototype=ht.prototype,mt.prototype.createDefaultTable=function(){return{version:1,scripts:[{tag:"DFLT",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}},mt.prototype.getSingle=function(e,t,n){const s=[],o=this.getLookupTables(t,n,e,1);for(let e=0;e<o.length;e++){const t=o[e].subtables;for(let e=0;e<t.length;e++){const n=t[e],o=this.expandCoverage(n.coverage);let r;if(1===n.substFormat){const e=n.deltaGlyphId;for(r=0;r<o.length;r++){const t=o[r];s.push({sub:t,by:t+e})}}else{const e=n.substitute;for(r=0;r<o.length;r++)s.push({sub:o[r],by:e[r]})}}}return s},mt.prototype.getAlternates=function(e,t,n){const s=[],o=this.getLookupTables(t,n,e,3);for(let e=0;e<o.length;e++){const t=o[e].subtables;for(let e=0;e<t.length;e++){const n=t[e],o=this.expandCoverage(n.coverage),r=n.alternateSets;for(let e=0;e<o.length;e++)s.push({sub:o[e],by:r[e]})}}return s},mt.prototype.getLigatures=function(e,t,n){const s=[],o=this.getLookupTables(t,n,e,4);for(let e=0;e<o.length;e++){const t=o[e].subtables;for(let e=0;e<t.length;e++){const n=t[e],o=this.expandCoverage(n.coverage),r=n.ligatureSets;for(let e=0;e<o.length;e++){const t=o[e],n=r[e];for(let e=0;e<n.length;e++){const o=n[e];s.push({sub:[t].concat(o.components),by:o.ligGlyph})}}}}return s},mt.prototype.addSingle=function(e,t,n,s){const o=vt(this.getLookupTables(n,s,e,1,!0)[0],2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});f.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);const r=t.sub;let a=this.binSearch(o.coverage.glyphs,r);a<0&&(a=-1-a,o.coverage.glyphs.splice(a,0,r),o.substitute.splice(a,0,0)),o.substitute[a]=t.by},mt.prototype.addAlternate=function(e,t,n,s){const o=vt(this.getLookupTables(n,s,e,3,!0)[0],1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});f.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);const r=t.sub;let a=this.binSearch(o.coverage.glyphs,r);a<0&&(a=-1-a,o.coverage.glyphs.splice(a,0,r),o.alternateSets.splice(a,0,0)),o.alternateSets[a]=t.by},mt.prototype.addLigature=function(e,t,n,s){const o=this.getLookupTables(n,s,e,4,!0)[0];let r=o.subtables[0];r||(r={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},o.subtables[0]=r),f.assert(1===r.coverage.format,"Ligature: unable to modify coverage table format "+r.coverage.format);const a=t.sub[0],i=t.sub.slice(1),l={ligGlyph:t.by,components:i};let u=this.binSearch(r.coverage.glyphs,a);if(u>=0){const e=r.ligatureSets[u];for(let t=0;t<e.length;t++)if(yt(e[t].components,i))return;e.push(l)}else u=-1-u,r.coverage.glyphs.splice(u,0,a),r.ligatureSets.splice(u,0,[l])},mt.prototype.getFeature=function(e,t,n){if(/ss\d\d/.test(e))return this.getSingle(e,t,n);switch(e){case"aalt":case"salt":return this.getSingle(e,t,n).concat(this.getAlternates(e,t,n));case"dlig":case"liga":case"rlig":return this.getLigatures(e,t,n)}},mt.prototype.add=function(e,t,n,s){if(/ss\d\d/.test(e))return this.addSingle(e,t,n,s);switch(e){case"aalt":case"salt":return"number"==typeof t.by?this.addSingle(e,t,n,s):this.addAlternate(e,t,n,s);case"dlig":case"liga":case"rlig":return this.addLigature(e,t,n,s)}};const bt=mt;function xt(e){const t=new ArrayBuffer(e.length),n=new Uint8Array(t);for(let t=0;t<e.length;++t)n[t]=e[t];return t}function St(e,t){if(!e)throw t}let Ut,kt,Tt,Et;function wt(e){this.font=e,this._fpgmState=this._prepState=void 0,this._errorState=0}function Ot(e){return e}function It(e){return Math.sign(e)*Math.round(Math.abs(e))}function Rt(e){return Math.sign(e)*Math.round(Math.abs(2*e))/2}function Lt(e){return Math.sign(e)*(Math.round(Math.abs(e)+.5)-.5)}function Bt(e){return Math.sign(e)*Math.ceil(Math.abs(e))}function Ct(e){return Math.sign(e)*Math.floor(Math.abs(e))}const Dt=function(e){const t=this.srPeriod;let n=this.srPhase,s=1;return e<0&&(e=-e,s=-1),e+=this.srThreshold-n,e=Math.trunc(e/t)*t,(e+=n)<0?n*s:e*s},Mt={x:1,y:0,axis:"x",distance:function(e,t,n,s){return(n?e.xo:e.x)-(s?t.xo:t.x)},interpolate:function(e,t,n,s){let o,r,a,i,l,u,c;if(!s||s===this)return o=e.xo-t.xo,r=e.xo-n.xo,l=t.x-t.xo,u=n.x-n.xo,a=Math.abs(o),i=Math.abs(r),c=a+i,0===c?void(e.x=e.xo+(l+u)/2):void(e.x=e.xo+(l*i+u*a)/c);o=s.distance(e,t,!0,!0),r=s.distance(e,n,!0,!0),l=s.distance(t,t,!1,!0),u=s.distance(n,n,!1,!0),a=Math.abs(o),i=Math.abs(r),c=a+i,0!==c?Mt.setRelative(e,e,(l*i+u*a)/c,s,!0):Mt.setRelative(e,e,(l+u)/2,s,!0)},normalSlope:Number.NEGATIVE_INFINITY,setRelative:function(e,t,n,s,o){if(!s||s===this)return void(e.x=(o?t.xo:t.x)+n);const r=o?t.xo:t.x,a=o?t.yo:t.y,i=r+n*s.x,l=a+n*s.y;e.x=i+(e.y-l)/s.normalSlope},slope:0,touch:function(e){e.xTouched=!0},touched:function(e){return e.xTouched},untouch:function(e){e.xTouched=!1}},At={x:0,y:1,axis:"y",distance:function(e,t,n,s){return(n?e.yo:e.y)-(s?t.yo:t.y)},interpolate:function(e,t,n,s){let o,r,a,i,l,u,c;if(!s||s===this)return o=e.yo-t.yo,r=e.yo-n.yo,l=t.y-t.yo,u=n.y-n.yo,a=Math.abs(o),i=Math.abs(r),c=a+i,0===c?void(e.y=e.yo+(l+u)/2):void(e.y=e.yo+(l*i+u*a)/c);o=s.distance(e,t,!0,!0),r=s.distance(e,n,!0,!0),l=s.distance(t,t,!1,!0),u=s.distance(n,n,!1,!0),a=Math.abs(o),i=Math.abs(r),c=a+i,0!==c?At.setRelative(e,e,(l*i+u*a)/c,s,!0):At.setRelative(e,e,(l+u)/2,s,!0)},normalSlope:0,setRelative:function(e,t,n,s,o){if(!s||s===this)return void(e.y=(o?t.yo:t.y)+n);const r=o?t.xo:t.x,a=o?t.yo:t.y,i=r+n*s.x,l=a+n*s.y;e.y=l+s.normalSlope*(e.x-i)},slope:Number.POSITIVE_INFINITY,touch:function(e){e.yTouched=!0},touched:function(e){return e.yTouched},untouch:function(e){e.yTouched=!1}};function Pt(e,t){this.x=e,this.y=t,this.axis=void 0,this.slope=t/e,this.normalSlope=-e/t,Object.freeze(this)}function Gt(e,t){const n=Math.sqrt(e*e+t*t);return t/=n,1==(e/=n)&&0===t?Mt:0===e&&1===t?At:new Pt(e,t)}function Nt(e,t,n,s){this.x=this.xo=Math.round(64*e)/64,this.y=this.yo=Math.round(64*t)/64,this.lastPointOfContour=n,this.onCurve=s,this.prevPointOnContour=void 0,this.nextPointOnContour=void 0,this.xTouched=!1,this.yTouched=!1,Object.preventExtensions(this)}Object.freeze(Mt),Object.freeze(At),Pt.prototype.distance=function(e,t,n,s){return this.x*Mt.distance(e,t,n,s)+this.y*At.distance(e,t,n,s)},Pt.prototype.interpolate=function(e,t,n,s){let o,r,a,i,l,u,c;a=s.distance(e,t,!0,!0),i=s.distance(e,n,!0,!0),o=s.distance(t,t,!1,!0),r=s.distance(n,n,!1,!0),l=Math.abs(a),u=Math.abs(i),c=l+u,0!==c?this.setRelative(e,e,(o*u+r*l)/c,s,!0):this.setRelative(e,e,(o+r)/2,s,!0)},Pt.prototype.setRelative=function(e,t,n,s,o){s=s||this;const r=o?t.xo:t.x,a=o?t.yo:t.y,i=r+n*s.x,l=a+n*s.y,u=s.normalSlope,c=this.slope,p=e.x,f=e.y;e.x=(c*p-u*i+l-f)/(c-u),e.y=c*(e.x-p)+f},Pt.prototype.touch=function(e){e.xTouched=!0,e.yTouched=!0},Nt.prototype.nextTouched=function(e){let t=this.nextPointOnContour;for(;!e.touched(t)&&t!==this;)t=t.nextPointOnContour;return t},Nt.prototype.prevTouched=function(e){let t=this.prevPointOnContour;for(;!e.touched(t)&&t!==this;)t=t.prevPointOnContour;return t};const Ft=Object.freeze(new Nt(0,0)),_t={cvCutIn:17/16,deltaBase:9,deltaShift:.125,loop:1,minDis:1,autoFlip:!0};function Ht(e,t){switch(this.env=e,this.stack=[],this.prog=t,e){case"glyf":this.zp0=this.zp1=this.zp2=1,this.rp0=this.rp1=this.rp2=0;case"prep":this.fv=this.pv=this.dpv=Mt,this.round=It}}function zt(e){const t=e.tZone=new Array(e.gZone.length);for(let e=0;e<t.length;e++)t[e]=new Nt(0,0)}function Wt(e,t){const n=e.prog;let s,o=e.ip,r=1;do{if(s=n[++o],88===s)r++;else if(89===s)r--;else if(64===s)o+=n[o+1]+1;else if(65===s)o+=2*n[o+1]+1;else if(s>=176&&s<=183)o+=s-176+1;else if(s>=184&&s<=191)o+=2*(s-184+1);else if(t&&1===r&&27===s)break}while(r>0);e.ip=o}function qt(e,t){exports.DEBUG&&console.log(t.step,"SVTCA["+e.axis+"]"),t.fv=t.pv=t.dpv=e}function jt(e,t){exports.DEBUG&&console.log(t.step,"SPVTCA["+e.axis+"]"),t.pv=t.dpv=e}function Xt(e,t){exports.DEBUG&&console.log(t.step,"SFVTCA["+e.axis+"]"),t.fv=e}function Vt(e,t){const n=t.stack,s=n.pop(),o=n.pop(),r=t.z2[s],a=t.z1[o];let i,l;exports.DEBUG&&console.log("SPVTL["+e+"]",s,o),e?(i=r.y-a.y,l=a.x-r.x):(i=a.x-r.x,l=a.y-r.y),t.pv=t.dpv=Gt(i,l)}function Yt(e,t){const n=t.stack,s=n.pop(),o=n.pop(),r=t.z2[s],a=t.z1[o];let i,l;exports.DEBUG&&console.log("SFVTL["+e+"]",s,o),e?(i=r.y-a.y,l=a.x-r.x):(i=a.x-r.x,l=a.y-r.y),t.fv=Gt(i,l)}function Zt(e){exports.DEBUG&&console.log(e.step,"POP[]"),e.stack.pop()}function Qt(e,t){const n=t.stack.pop(),s=t.z0[n],o=t.fv,r=t.pv;exports.DEBUG&&console.log(t.step,"MDAP["+e+"]",n);let a=r.distance(s,Ft);e&&(a=t.round(a)),o.setRelative(s,Ft,a,r),o.touch(s),t.rp0=t.rp1=n}function Jt(e,t){const n=t.z2,s=n.length-2;let o,r,a;exports.DEBUG&&console.log(t.step,"IUP["+e.axis+"]");for(let t=0;t<s;t++)o=n[t],e.touched(o)||(r=o.prevTouched(e),r!==o&&(a=o.nextTouched(e),r===a&&e.setRelative(o,o,e.distance(r,r,!1,!0),e,!0),e.interpolate(o,r,a,e)))}function Kt(e,t){const n=t.stack,s=e?t.rp1:t.rp2,o=(e?t.z0:t.z1)[s],r=t.fv,a=t.pv;let i=t.loop;const l=t.z2;for(;i--;){const s=n.pop(),u=l[s],c=a.distance(o,o,!1,!0);r.setRelative(u,u,c,a),r.touch(u),exports.DEBUG&&console.log(t.step,(t.loop>1?"loop "+(t.loop-i)+": ":"")+"SHP["+(e?"rp1":"rp2")+"]",s)}t.loop=1}function $t(e,t){const n=t.stack,s=e?t.rp1:t.rp2,o=(e?t.z0:t.z1)[s],r=t.fv,a=t.pv,i=n.pop(),l=t.z2[t.contours[i]];let u=l;exports.DEBUG&&console.log(t.step,"SHC["+e+"]",i);const c=a.distance(o,o,!1,!0);do{u!==o&&r.setRelative(u,u,c,a),u=u.nextPointOnContour}while(u!==l)}function en(e,t){const n=t.stack,s=e?t.rp1:t.rp2,o=(e?t.z0:t.z1)[s],r=t.fv,a=t.pv,i=n.pop();let l,u;switch(exports.DEBUG&&console.log(t.step,"SHZ["+e+"]",i),i){case 0:l=t.tZone;break;case 1:l=t.gZone;break;default:throw new Error("Invalid zone")}const c=a.distance(o,o,!1,!0),p=l.length-2;for(let e=0;e<p;e++)u=l[e],r.setRelative(u,u,c,a)}function tn(e,t){const n=t.stack,s=n.pop()/64,o=n.pop(),r=t.z1[o],a=t.z0[t.rp0],i=t.fv,l=t.pv;i.setRelative(r,a,s,l),i.touch(r),exports.DEBUG&&console.log(t.step,"MSIRP["+e+"]",s,o),t.rp1=t.rp0,t.rp2=o,e&&(t.rp0=o)}function nn(e,t){const n=t.stack,s=n.pop(),o=n.pop(),r=t.z0[o],a=t.fv,i=t.pv;let l=t.cvt[s];exports.DEBUG&&console.log(t.step,"MIAP["+e+"]",s,"(",l,")",o);let u=i.distance(r,Ft);e&&(Math.abs(u-l)<t.cvCutIn&&(u=l),u=t.round(u)),a.setRelative(r,Ft,u,i),0===t.zp0&&(r.xo=r.x,r.yo=r.y),a.touch(r),t.rp0=t.rp1=o}function sn(e,t){const n=t.stack,s=n.pop(),o=t.z2[s];exports.DEBUG&&console.log(t.step,"GC["+e+"]",s),n.push(64*t.dpv.distance(o,Ft,e,!1))}function on(e,t){const n=t.stack,s=n.pop(),o=n.pop(),r=t.z1[s],a=t.z0[o],i=t.dpv.distance(a,r,e,e);exports.DEBUG&&console.log(t.step,"MD["+e+"]",s,o,"->",i),t.stack.push(Math.round(64*i))}function rn(e,t){const n=t.stack,s=n.pop(),o=t.fv,r=t.pv,a=t.ppem,i=t.deltaBase+16*(e-1),l=t.deltaShift,u=t.z0;exports.DEBUG&&console.log(t.step,"DELTAP["+e+"]",s,n);for(let e=0;e<s;e++){const e=n.pop(),s=n.pop();if(i+((240&s)>>4)!==a)continue;let c=(15&s)-8;c>=0&&c++,exports.DEBUG&&console.log(t.step,"DELTAPFIX",e,"by",c*l);const p=u[e];o.setRelative(p,p,c*l,r)}}function an(e,t){const n=t.stack,s=n.pop();exports.DEBUG&&console.log(t.step,"ROUND[]"),n.push(64*t.round(s/64))}function ln(e,t){const n=t.stack,s=n.pop(),o=t.ppem,r=t.deltaBase+16*(e-1),a=t.deltaShift;exports.DEBUG&&console.log(t.step,"DELTAC["+e+"]",s,n);for(let e=0;e<s;e++){const e=n.pop(),s=n.pop();if(r+((240&s)>>4)!==o)continue;let i=(15&s)-8;i>=0&&i++;const l=i*a;exports.DEBUG&&console.log(t.step,"DELTACFIX",e,"by",l),t.cvt[e]+=l}}function un(e,t){const n=t.stack,s=n.pop(),o=n.pop(),r=t.z2[s],a=t.z1[o];let i,l;exports.DEBUG&&console.log(t.step,"SDPVTL["+e+"]",s,o),e?(i=r.y-a.y,l=a.x-r.x):(i=a.x-r.x,l=a.y-r.y),t.dpv=Gt(i,l)}function cn(e,t){const n=t.stack,s=t.prog;let o=t.ip;exports.DEBUG&&console.log(t.step,"PUSHB["+e+"]");for(let t=0;t<e;t++)n.push(s[++o]);t.ip=o}function pn(e,t){let n=t.ip;const s=t.prog,o=t.stack;exports.DEBUG&&console.log(t.ip,"PUSHW["+e+"]");for(let t=0;t<e;t++){let e=s[++n]<<8|s[++n];32768&e&&(e=-(1+(65535^e))),o.push(e)}t.ip=n}function fn(e,t,n,s,o,r){const a=r.stack,i=e&&a.pop(),l=a.pop(),u=r.rp0,c=r.z0[u],p=r.z1[l],f=r.minDis,h=r.fv,d=r.dpv;let g,m,y,v;m=g=d.distance(p,c,!0,!0),y=m>=0?1:-1,m=Math.abs(m),e&&(v=r.cvt[i],s&&Math.abs(m-v)<r.cvCutIn&&(m=v)),n&&m<f&&(m=f),s&&(m=r.round(m)),h.setRelative(p,c,y*m,d),h.touch(p),exports.DEBUG&&console.log(r.step,(e?"MIRP[":"MDRP[")+(t?"M":"m")+(n?">":"_")+(s?"R":"_")+(0===o?"Gr":1===o?"Bl":2===o?"Wh":"")+"]",e?i+"("+r.cvt[i]+","+v+")":"",l,"(d =",g,"->",y*m,")"),r.rp1=r.rp0,r.rp2=l,t&&(r.rp0=l)}wt.prototype.exec=function(e,t){if("number"!=typeof t)throw new Error("Point size is not a number!");if(this._errorState>2)return;const n=this.font;let s=this._prepState;if(!s||s.ppem!==t){let e=this._fpgmState;if(!e){Ht.prototype=_t,e=this._fpgmState=new Ht("fpgm",n.tables.fpgm),e.funcs=[],e.font=n,exports.DEBUG&&(console.log("---EXEC FPGM---"),e.step=-1);try{kt(e)}catch(e){return console.log("Hinting error in FPGM:"+e),void(this._errorState=3)}}Ht.prototype=e,s=this._prepState=new Ht("prep",n.tables.prep),s.ppem=t;const o=n.tables.cvt;if(o){const e=s.cvt=new Array(o.length),r=t/n.unitsPerEm;for(let t=0;t<o.length;t++)e[t]=o[t]*r}else s.cvt=[];exports.DEBUG&&(console.log("---EXEC PREP---"),s.step=-1);try{kt(s)}catch(e){this._errorState<2&&console.log("Hinting error in PREP:"+e),this._errorState=2}}if(!(this._errorState>1))try{return Tt(e,s)}catch(e){return this._errorState<1&&(console.log("Hinting error:"+e),console.log("Note: further hinting errors are silenced")),void(this._errorState=1)}},Tt=function(e,t){const n=t.ppem/t.font.unitsPerEm,s=n;let o,r,a,i=e.components;if(Ht.prototype=t,i){const l=t.font;r=[],o=[];for(let e=0;e<i.length;e++){const t=i[e],u=l.glyphs.get(t.glyphIndex);a=new Ht("glyf",u.instructions),exports.DEBUG&&(console.log("---EXEC COMP "+e+"---"),a.step=-1),Et(u,a,n,s);const c=Math.round(t.dx*n),p=Math.round(t.dy*s),f=a.gZone,h=a.contours;for(let e=0;e<f.length;e++){const t=f[e];t.xTouched=t.yTouched=!1,t.xo=t.x=t.x+c,t.yo=t.y=t.y+p}const d=r.length;r.push.apply(r,f);for(let e=0;e<h.length;e++)o.push(h[e]+d)}e.instructions&&!a.inhibitGridFit&&(a=new Ht("glyf",e.instructions),a.gZone=a.z0=a.z1=a.z2=r,a.contours=o,r.push(new Nt(0,0),new Nt(Math.round(e.advanceWidth*n),0)),exports.DEBUG&&(console.log("---EXEC COMPOSITE---"),a.step=-1),kt(a),r.length-=2)}else a=new Ht("glyf",e.instructions),exports.DEBUG&&(console.log("---EXEC GLYPH---"),a.step=-1),Et(e,a,n,s),r=a.gZone;return r},Et=function(e,t,n,s){const o=e.points||[],r=o.length,a=t.gZone=t.z0=t.z1=t.z2=[],i=t.contours=[];let l,u,c;for(let e=0;e<r;e++)l=o[e],a[e]=new Nt(l.x*n,l.y*s,l.lastPointOfContour,l.onCurve);for(let e=0;e<r;e++)l=a[e],u||(u=l,i.push(e)),l.lastPointOfContour?(l.nextPointOnContour=u,u.prevPointOnContour=l,u=void 0):(c=a[e+1],l.nextPointOnContour=c,c.prevPointOnContour=l);if(!t.inhibitGridFit){if(exports.DEBUG){console.log("PROCESSING GLYPH",t.stack);for(let e=0;e<r;e++)console.log(e,a[e].x,a[e].y)}if(a.push(new Nt(0,0),new Nt(Math.round(e.advanceWidth*n),0)),kt(t),a.length-=2,exports.DEBUG){console.log("FINISHED GLYPH",t.stack);for(let e=0;e<r;e++)console.log(e,a[e].x,a[e].y)}}},kt=function(e){let t=e.prog;if(!t)return;const n=t.length;let s;for(e.ip=0;e.ip<n;e.ip++){if(exports.DEBUG&&e.step++,s=Ut[t[e.ip]],!s)throw new Error("unknown instruction: 0x"+Number(t[e.ip]).toString(16));s(e)}},Ut=[qt.bind(void 0,At),qt.bind(void 0,Mt),jt.bind(void 0,At),jt.bind(void 0,Mt),Xt.bind(void 0,At),Xt.bind(void 0,Mt),Vt.bind(void 0,0),Vt.bind(void 0,1),Yt.bind(void 0,0),Yt.bind(void 0,1),function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"SPVFS[]",n,s),e.pv=e.dpv=Gt(s,n)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"SPVFS[]",n,s),e.fv=Gt(s,n)},function(e){const t=e.stack,n=e.pv;exports.DEBUG&&console.log(e.step,"GPV[]"),t.push(16384*n.x),t.push(16384*n.y)},function(e){const t=e.stack,n=e.fv;exports.DEBUG&&console.log(e.step,"GFV[]"),t.push(16384*n.x),t.push(16384*n.y)},function(e){e.fv=e.pv,exports.DEBUG&&console.log(e.step,"SFVTPV[]")},function(e){const t=e.stack,n=t.pop(),s=t.pop(),o=t.pop(),r=t.pop(),a=t.pop(),i=e.z0,l=e.z1,u=i[n],c=i[s],p=l[o],f=l[r],h=e.z2[a];exports.DEBUG&&console.log("ISECT[], ",n,s,o,r,a);const d=u.x,g=u.y,m=c.x,y=c.y,v=p.x,b=p.y,x=f.x,S=f.y,U=(d-m)*(b-S)-(g-y)*(v-x),k=d*y-g*m,T=v*S-b*x;h.x=(k*(v-x)-T*(d-m))/U,h.y=(k*(b-S)-T*(g-y))/U},function(e){e.rp0=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP0[]",e.rp0)},function(e){e.rp1=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP1[]",e.rp1)},function(e){e.rp2=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP2[]",e.rp2)},function(e){const t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP0[]",t),e.zp0=t,t){case 0:e.tZone||zt(e),e.z0=e.tZone;break;case 1:e.z0=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){const t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP1[]",t),e.zp1=t,t){case 0:e.tZone||zt(e),e.z1=e.tZone;break;case 1:e.z1=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){const t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP2[]",t),e.zp2=t,t){case 0:e.tZone||zt(e),e.z2=e.tZone;break;case 1:e.z2=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){const t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZPS[]",t),e.zp0=e.zp1=e.zp2=t,t){case 0:e.tZone||zt(e),e.z0=e.z1=e.z2=e.tZone;break;case 1:e.z0=e.z1=e.z2=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){e.loop=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SLOOP[]",e.loop)},function(e){exports.DEBUG&&console.log(e.step,"RTG[]"),e.round=It},function(e){exports.DEBUG&&console.log(e.step,"RTHG[]"),e.round=Lt},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SMD[]",t),e.minDis=t/64},function(e){exports.DEBUG&&console.log(e.step,"ELSE[]"),Wt(e,!1)},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"JMPR[]",t),e.ip+=t-1},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCVTCI[]",t),e.cvCutIn=t/64},void 0,void 0,function(e){const t=e.stack;exports.DEBUG&&console.log(e.step,"DUP[]"),t.push(t[t.length-1])},Zt,function(e){exports.DEBUG&&console.log(e.step,"CLEAR[]"),e.stack.length=0},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"SWAP[]"),t.push(n),t.push(s)},function(e){const t=e.stack;exports.DEBUG&&console.log(e.step,"DEPTH[]"),t.push(t.length)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"CINDEX[]",n),t.push(t[t.length-n])},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"MINDEX[]",n),t.push(t.splice(t.length-n,1)[0])},void 0,void 0,void 0,function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"LOOPCALL[]",n,s);const o=e.ip,r=e.prog;e.prog=e.funcs[n];for(let t=0;t<s;t++)kt(e),exports.DEBUG&&console.log(++e.step,t+1<s?"next loopcall":"done loopcall",t);e.ip=o,e.prog=r},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"CALL[]",t);const n=e.ip,s=e.prog;e.prog=e.funcs[t],kt(e),e.ip=n,e.prog=s,exports.DEBUG&&console.log(++e.step,"returning from",t)},function(e){if("fpgm"!==e.env)throw new Error("FDEF not allowed here");const t=e.stack,n=e.prog;let s=e.ip;const o=t.pop(),r=s;for(exports.DEBUG&&console.log(e.step,"FDEF[]",o);45!==n[++s];);e.ip=s,e.funcs[o]=n.slice(r+1,s)},void 0,Qt.bind(void 0,0),Qt.bind(void 0,1),Jt.bind(void 0,At),Jt.bind(void 0,Mt),Kt.bind(void 0,0),Kt.bind(void 0,1),$t.bind(void 0,0),$t.bind(void 0,1),en.bind(void 0,0),en.bind(void 0,1),function(e){const t=e.stack;let n=e.loop;const s=e.fv,o=t.pop()/64,r=e.z2;for(;n--;){const a=t.pop(),i=r[a];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-n)+": ":"")+"SHPIX[]",a,o),s.setRelative(i,i,o),s.touch(i)}e.loop=1},function(e){const t=e.stack,n=e.rp1,s=e.rp2;let o=e.loop;const r=e.z0[n],a=e.z1[s],i=e.fv,l=e.dpv,u=e.z2;for(;o--;){const c=t.pop(),p=u[c];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-o)+": ":"")+"IP[]",c,n,"<->",s),i.interpolate(p,r,a,l),i.touch(p)}e.loop=1},tn.bind(void 0,0),tn.bind(void 0,1),function(e){const t=e.stack,n=e.rp0,s=e.z0[n];let o=e.loop;const r=e.fv,a=e.pv,i=e.z1;for(;o--;){const n=t.pop(),l=i[n];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-o)+": ":"")+"ALIGNRP[]",n),r.setRelative(l,s,0,a),r.touch(l)}e.loop=1},function(e){exports.DEBUG&&console.log(e.step,"RTDG[]"),e.round=Rt},nn.bind(void 0,0),nn.bind(void 0,1),function(e){const t=e.prog;let n=e.ip;const s=e.stack,o=t[++n];exports.DEBUG&&console.log(e.step,"NPUSHB[]",o);for(let e=0;e<o;e++)s.push(t[++n]);e.ip=n},function(e){let t=e.ip;const n=e.prog,s=e.stack,o=n[++t];exports.DEBUG&&console.log(e.step,"NPUSHW[]",o);for(let e=0;e<o;e++){let e=n[++t]<<8|n[++t];32768&e&&(e=-(1+(65535^e))),s.push(e)}e.ip=t},function(e){const t=e.stack;let n=e.store;n||(n=e.store=[]);const s=t.pop(),o=t.pop();exports.DEBUG&&console.log(e.step,"WS",s,o),n[o]=s},function(e){const t=e.stack,n=e.store,s=t.pop();exports.DEBUG&&console.log(e.step,"RS",s);const o=n&&n[s]||0;t.push(o)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"WCVTP",n,s),e.cvt[s]=n/64},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"RCVT",n),t.push(64*e.cvt[n])},sn.bind(void 0,0),sn.bind(void 0,1),void 0,on.bind(void 0,0),on.bind(void 0,1),function(e){exports.DEBUG&&console.log(e.step,"MPPEM[]"),e.stack.push(e.ppem)},void 0,function(e){exports.DEBUG&&console.log(e.step,"FLIPON[]"),e.autoFlip=!0},void 0,void 0,function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"LT[]",n,s),t.push(s<n?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"LTEQ[]",n,s),t.push(s<=n?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"GT[]",n,s),t.push(s>n?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"GTEQ[]",n,s),t.push(s>=n?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"EQ[]",n,s),t.push(n===s?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"NEQ[]",n,s),t.push(n!==s?1:0)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"ODD[]",n),t.push(Math.trunc(n)%2?1:0)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"EVEN[]",n),t.push(Math.trunc(n)%2?0:1)},function(e){let t=e.stack.pop();exports.DEBUG&&console.log(e.step,"IF[]",t),t||(Wt(e,!0),exports.DEBUG&&console.log(e.step,"EIF[]"))},function(e){exports.DEBUG&&console.log(e.step,"EIF[]")},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"AND[]",n,s),t.push(n&&s?1:0)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"OR[]",n,s),t.push(n||s?1:0)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"NOT[]",n),t.push(n?0:1)},rn.bind(void 0,1),function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SDB[]",t),e.deltaBase=t},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SDS[]",t),e.deltaShift=Math.pow(.5,t)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"ADD[]",n,s),t.push(s+n)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"SUB[]",n,s),t.push(s-n)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"DIV[]",n,s),t.push(64*s/n)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"MUL[]",n,s),t.push(s*n/64)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"ABS[]",n),t.push(Math.abs(n))},function(e){const t=e.stack;let n=t.pop();exports.DEBUG&&console.log(e.step,"NEG[]",n),t.push(-n)},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"FLOOR[]",n),t.push(64*Math.floor(n/64))},function(e){const t=e.stack,n=t.pop();exports.DEBUG&&console.log(e.step,"CEILING[]",n),t.push(64*Math.ceil(n/64))},an.bind(void 0,0),an.bind(void 0,1),an.bind(void 0,2),an.bind(void 0,3),void 0,void 0,void 0,void 0,function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"WCVTF[]",n,s),e.cvt[s]=n*e.ppem/e.font.unitsPerEm},rn.bind(void 0,2),rn.bind(void 0,3),ln.bind(void 0,1),ln.bind(void 0,2),ln.bind(void 0,3),function(e){let t,n=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SROUND[]",n),e.round=Dt,192&n){case 0:t=.5;break;case 64:t=1;break;case 128:t=2;break;default:throw new Error("invalid SROUND value")}switch(e.srPeriod=t,48&n){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error("invalid SROUND value")}n&=15,e.srThreshold=0===n?0:(n/8-.5)*t},function(e){let t,n=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"S45ROUND[]",n),e.round=Dt,192&n){case 0:t=Math.sqrt(2)/2;break;case 64:t=Math.sqrt(2);break;case 128:t=2*Math.sqrt(2);break;default:throw new Error("invalid S45ROUND value")}switch(e.srPeriod=t,48&n){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error("invalid S45ROUND value")}n&=15,e.srThreshold=0===n?0:(n/8-.5)*t},void 0,void 0,function(e){exports.DEBUG&&console.log(e.step,"ROFF[]"),e.round=Ot},void 0,function(e){exports.DEBUG&&console.log(e.step,"RUTG[]"),e.round=Bt},function(e){exports.DEBUG&&console.log(e.step,"RDTG[]"),e.round=Ct},Zt,Zt,void 0,void 0,void 0,void 0,void 0,function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCANCTRL[]",t)},un.bind(void 0,0),un.bind(void 0,1),function(e){const t=e.stack,n=t.pop();let s=0;exports.DEBUG&&console.log(e.step,"GETINFO[]",n),1&n&&(s=35),32&n&&(s|=4096),t.push(s)},void 0,function(e){const t=e.stack,n=t.pop(),s=t.pop(),o=t.pop();exports.DEBUG&&console.log(e.step,"ROLL[]"),t.push(s),t.push(n),t.push(o)},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"MAX[]",n,s),t.push(Math.max(s,n))},function(e){const t=e.stack,n=t.pop(),s=t.pop();exports.DEBUG&&console.log(e.step,"MIN[]",n,s),t.push(Math.min(s,n))},function(e){const t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCANTYPE[]",t)},function(e){const t=e.stack.pop();let n=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"INSTCTRL[]",t,n),t){case 1:return void(e.inhibitGridFit=!!n);case 2:return void(e.ignoreCvt=!!n);default:throw new Error("invalid INSTCTRL[] selector")}},void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,cn.bind(void 0,1),cn.bind(void 0,2),cn.bind(void 0,3),cn.bind(void 0,4),cn.bind(void 0,5),cn.bind(void 0,6),cn.bind(void 0,7),cn.bind(void 0,8),pn.bind(void 0,1),pn.bind(void 0,2),pn.bind(void 0,3),pn.bind(void 0,4),pn.bind(void 0,5),pn.bind(void 0,6),pn.bind(void 0,7),pn.bind(void 0,8),fn.bind(void 0,0,0,0,0,0),fn.bind(void 0,0,0,0,0,1),fn.bind(void 0,0,0,0,0,2),fn.bind(void 0,0,0,0,0,3),fn.bind(void 0,0,0,0,1,0),fn.bind(void 0,0,0,0,1,1),fn.bind(void 0,0,0,0,1,2),fn.bind(void 0,0,0,0,1,3),fn.bind(void 0,0,0,1,0,0),fn.bind(void 0,0,0,1,0,1),fn.bind(void 0,0,0,1,0,2),fn.bind(void 0,0,0,1,0,3),fn.bind(void 0,0,0,1,1,0),fn.bind(void 0,0,0,1,1,1),fn.bind(void 0,0,0,1,1,2),fn.bind(void 0,0,0,1,1,3),fn.bind(void 0,0,1,0,0,0),fn.bind(void 0,0,1,0,0,1),fn.bind(void 0,0,1,0,0,2),fn.bind(void 0,0,1,0,0,3),fn.bind(void 0,0,1,0,1,0),fn.bind(void 0,0,1,0,1,1),fn.bind(void 0,0,1,0,1,2),fn.bind(void 0,0,1,0,1,3),fn.bind(void 0,0,1,1,0,0),fn.bind(void 0,0,1,1,0,1),fn.bind(void 0,0,1,1,0,2),fn.bind(void 0,0,1,1,0,3),fn.bind(void 0,0,1,1,1,0),fn.bind(void 0,0,1,1,1,1),fn.bind(void 0,0,1,1,1,2),fn.bind(void 0,0,1,1,1,3),fn.bind(void 0,1,0,0,0,0),fn.bind(void 0,1,0,0,0,1),fn.bind(void 0,1,0,0,0,2),fn.bind(void 0,1,0,0,0,3),fn.bind(void 0,1,0,0,1,0),fn.bind(void 0,1,0,0,1,1),fn.bind(void 0,1,0,0,1,2),fn.bind(void 0,1,0,0,1,3),fn.bind(void 0,1,0,1,0,0),fn.bind(void 0,1,0,1,0,1),fn.bind(void 0,1,0,1,0,2),fn.bind(void 0,1,0,1,0,3),fn.bind(void 0,1,0,1,1,0),fn.bind(void 0,1,0,1,1,1),fn.bind(void 0,1,0,1,1,2),fn.bind(void 0,1,0,1,1,3),fn.bind(void 0,1,1,0,0,0),fn.bind(void 0,1,1,0,0,1),fn.bind(void 0,1,1,0,0,2),fn.bind(void 0,1,1,0,0,3),fn.bind(void 0,1,1,0,1,0),fn.bind(void 0,1,1,0,1,1),fn.bind(void 0,1,1,0,1,2),fn.bind(void 0,1,1,0,1,3),fn.bind(void 0,1,1,1,0,0),fn.bind(void 0,1,1,1,0,1),fn.bind(void 0,1,1,1,0,2),fn.bind(void 0,1,1,1,0,3),fn.bind(void 0,1,1,1,1,0),fn.bind(void 0,1,1,1,1,1),fn.bind(void 0,1,1,1,1,2),fn.bind(void 0,1,1,1,1,3)];const hn=wt;function dn(e){(e=e||{}).empty||(St(e.familyName,"When creating a new Font object, familyName is required."),St(e.styleName,"When creating a new Font object, styleName is required."),St(e.unitsPerEm,"When creating a new Font object, unitsPerEm is required."),St(e.ascender,"When creating a new Font object, ascender is required."),St(e.descender,"When creating a new Font object, descender is required."),St(e.descender<0,"Descender should be negative (e.g. -512)."),this.names={fontFamily:{en:e.familyName||" "},fontSubfamily:{en:e.styleName||" "},fullName:{en:e.fullName||e.familyName+" "+e.styleName},postScriptName:{en:e.postScriptName||e.familyName+e.styleName},designer:{en:e.designer||" "},designerURL:{en:e.designerURL||" "},manufacturer:{en:e.manufacturer||" "},manufacturerURL:{en:e.manufacturerURL||" "},license:{en:e.license||" "},licenseURL:{en:e.licenseURL||" "},version:{en:e.version||"Version 0.1"},description:{en:e.description||" "},copyright:{en:e.copyright||" "},trademark:{en:e.trademark||" "}},this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables={os2:{usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR}}),this.supported=!0,this.glyphs=new ce.GlyphSet(this,e.glyphs||[]),this.encoding=new Z(this),this.position=new gt(this),this.substitution=new bt(this),this.tables=this.tables||{},Object.defineProperty(this,"hinting",{get:function(){return this._hinting?this._hinting:"truetype"===this.outlinesFormat?this._hinting=new hn(this):void 0}})}dn.prototype.hasChar=function(e){return null!==this.encoding.charToGlyphIndex(e)},dn.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)},dn.prototype.charToGlyph=function(e){const t=this.charToGlyphIndex(e);let n=this.glyphs.get(t);return n||(n=this.glyphs.get(0)),n},dn.prototype.stringToGlyphs=function(e,t){t=t||this.defaultRenderOptions;const n=[];for(let t=0;t<e.length;t+=1){const s=e[t];n.push(this.charToGlyphIndex(s))}let s=n.length;if(t.features){const e=t.script||this.substitution.getDefaultScriptName();let o=[];t.features.liga&&(o=o.concat(this.substitution.getFeature("liga",e,t.language))),t.features.rlig&&(o=o.concat(this.substitution.getFeature("rlig",e,t.language)));for(let e=0;e<s;e+=1)for(let t=0;t<o.length;t++){const r=o[t],a=r.sub,i=a.length;let l=0;for(;l<i&&a[l]===n[e+l];)l++;l===i&&(n.splice(e,i,r.by),s=s-i+1)}}const o=new Array(s),r=this.glyphs.get(0);for(let e=0;e<s;e+=1)o[e]=this.glyphs.get(n[e])||r;return o},dn.prototype.nameToGlyphIndex=function(e){return this.glyphNames.nameToGlyphIndex(e)},dn.prototype.nameToGlyph=function(e){const t=this.nameToGlyphIndex(e);let n=this.glyphs.get(t);return n||(n=this.glyphs.get(0)),n},dn.prototype.glyphIndexToName=function(e){return this.glyphNames.glyphIndexToName?this.glyphNames.glyphIndexToName(e):""},dn.prototype.getKerningValue=function(e,t){return e=e.index||e,t=t.index||t,this.kerningPairs[e+","+t]||0},dn.prototype.defaultRenderOptions={kerning:!0,features:{liga:!0,rlig:!0}},dn.prototype.forEachGlyph=function(e,t,n,s,o,r){t=void 0!==t?t:0,n=void 0!==n?n:0,s=void 0!==s?s:72,o=o||this.defaultRenderOptions;const a=1/this.unitsPerEm*s,i=this.stringToGlyphs(e,o);let l;if(o.kerning){const e=o.script||this.position.getDefaultScriptName();l=this.position.getKerningTables(e,o.language)}for(let e=0;e<i.length;e+=1){const u=i[e];r.call(this,u,t,n,s,o),u.advanceWidth&&(t+=u.advanceWidth*a),o.kerning&&e<i.length-1&&(t+=(l?this.position.getKerningValue(l,u.index,i[e+1].index):this.getKerningValue(u,i[e+1]))*a),o.letterSpacing?t+=o.letterSpacing*s:o.tracking&&(t+=o.tracking/1e3*s)}return t},dn.prototype.getPath=function(e,t,n,s,o){const r=new u;return this.forEachGlyph(e,t,n,s,o,(function(e,t,n,s){const a=e.getPath(t,n,s,o,this);r.extend(a)})),r},dn.prototype.getPaths=function(e,t,n,s,o){const r=[];return this.forEachGlyph(e,t,n,s,o,(function(e,t,n,s){const a=e.getPath(t,n,s,o,this);r.push(a)})),r},dn.prototype.getAdvanceWidth=function(e,t,n){return this.forEachGlyph(e,0,0,t,n,(function(){}))},dn.prototype.draw=function(e,t,n,s,o,r){this.getPath(t,n,s,o,r).draw(e)},dn.prototype.drawPoints=function(e,t,n,s,o,r){this.forEachGlyph(t,n,s,o,r,(function(t,n,s,o){t.drawPoints(e,n,s,o)}))},dn.prototype.drawMetrics=function(e,t,n,s,o,r){this.forEachGlyph(t,n,s,o,r,(function(t,n,s,o){t.drawMetrics(e,n,s,o)}))},dn.prototype.getEnglishName=function(e){const t=this.names[e];if(t)return t.en},dn.prototype.validate=function(){const e=[],t=this;function n(t,n){t||e.push(n)}function s(e){const s=t.getEnglishName(e);n(s&&s.trim().length>0,"No English "+e+" specified.")}s("fontFamily"),s("weightName"),s("manufacturer"),s("copyright"),s("version"),n(this.unitsPerEm>0,"No unitsPerEm specified.")},dn.prototype.toTables=function(){return lt(this)},dn.prototype.toBuffer=function(){return console.warn("Font.toBuffer is deprecated. Use Font.toArrayBuffer instead."),this.toArrayBuffer()},dn.prototype.toArrayBuffer=function(){const e=this.toTables().encode(),t=new ArrayBuffer(e.length),n=new Uint8Array(t);for(let t=0;t<e.length;t++)n[t]=e[t];return t},dn.prototype.download=function(e){const t=this.getEnglishName("fontFamily"),s=this.getEnglishName("fontSubfamily");e=e||t.replace(/\s/g,"")+"-"+s+".otf";const o=this.toArrayBuffer();if("undefined"!=typeof window)window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem,window.requestFileSystem(window.TEMPORARY,o.byteLength,(function(t){t.root.getFile(e,{create:!0},(function(e){e.createWriter((function(t){const n=new DataView(o),s=new Blob([n],{type:"font/opentype"});t.write(s),t.addEventListener("writeend",(function(){location.href=e.toURL()}),!1)}))}))}),(function(e){throw new Error(e.name+": "+e.message)}));else{const t=n(383),s=function(e){const t=new Buffer(e.byteLength),n=new Uint8Array(e);for(let e=0;e<t.length;++e)t[e]=n[e];return t}(o);t.writeFileSync(e,s)}},dn.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512},dn.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9},dn.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900};const gn=dn;function mn(e,t){const n=JSON.stringify(e);let s=256;for(let e in t){let o=parseInt(e);if(o&&!(o<256)){if(JSON.stringify(t[e])===n)return o;s<=o&&(s=o+1)}}return t[s]=e,s}function yn(e,t,n){const s=mn(t.name,n);return[{name:"tag_"+e,type:"TAG",value:t.tag},{name:"minValue_"+e,type:"FIXED",value:t.minValue<<16},{name:"defaultValue_"+e,type:"FIXED",value:t.defaultValue<<16},{name:"maxValue_"+e,type:"FIXED",value:t.maxValue<<16},{name:"flags_"+e,type:"USHORT",value:0},{name:"nameID_"+e,type:"USHORT",value:s}]}function vn(e,t,n){const s={},o=new z.Parser(e,t);return s.tag=o.parseTag(),s.minValue=o.parseFixed(),s.defaultValue=o.parseFixed(),s.maxValue=o.parseFixed(),o.skip("uShort",1),s.name=n[o.parseUShort()]||{},s}function bn(e,t,n,s){const o=[{name:"nameID_"+e,type:"USHORT",value:mn(t.name,s)},{name:"flags_"+e,type:"USHORT",value:0}];for(let s=0;s<n.length;++s){const r=n[s].tag;o.push({name:"axis_"+e+" "+r,type:"FIXED",value:t.coordinates[r]<<16})}return o}function xn(e,t,n,s){const o={},r=new z.Parser(e,t);o.name=s[r.parseUShort()]||{},r.skip("uShort",1),o.coordinates={};for(let e=0;e<n.length;++e)o.coordinates[n[e].tag]=r.parseFixed();return o}const Sn={make:function(e,t){const n=new M.Table("fvar",[{name:"version",type:"ULONG",value:65536},{name:"offsetToData",type:"USHORT",value:0},{name:"countSizePairs",type:"USHORT",value:2},{name:"axisCount",type:"USHORT",value:e.axes.length},{name:"axisSize",type:"USHORT",value:20},{name:"instanceCount",type:"USHORT",value:e.instances.length},{name:"instanceSize",type:"USHORT",value:4+4*e.axes.length}]);n.offsetToData=n.sizeOf();for(let s=0;s<e.axes.length;s++)n.fields=n.fields.concat(yn(s,e.axes[s],t));for(let s=0;s<e.instances.length;s++)n.fields=n.fields.concat(bn(s,e.instances[s],e.axes,t));return n},parse:function(e,t,n){const s=new z.Parser(e,t),o=s.parseULong();f.argument(65536===o,"Unsupported fvar table version.");const r=s.parseOffset16();s.skip("uShort",1);const a=s.parseUShort(),i=s.parseUShort(),l=s.parseUShort(),u=s.parseUShort(),c=[];for(let s=0;s<a;s++)c.push(vn(e,t+r+s*i,n));const p=[],h=t+r+a*i;for(let t=0;t<l;t++)p.push(xn(e,h+t*u,c,n));return{axes:c,instances:p}}},Un=new Array(10);Un[1]=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{posFormat:1,coverage:this.parsePointer(_.coverage),value:this.parseValueRecord()}:2===t?{posFormat:2,coverage:this.parsePointer(_.coverage),values:this.parseValueRecordList()}:void f.assert(!1,"0x"+e.toString(16)+": GPOS lookup type 1 format must be 1 or 2.")},Un[2]=function(){const e=this.offset+this.relativeOffset,t=this.parseUShort(),n=this.parsePointer(_.coverage),s=this.parseUShort(),o=this.parseUShort();if(1===t)return{posFormat:t,coverage:n,valueFormat1:s,valueFormat2:o,pairSets:this.parseList(_.pointer(_.list((function(){return{secondGlyph:this.parseUShort(),value1:this.parseValueRecord(s),value2:this.parseValueRecord(o)}}))))};if(2===t){const e=this.parsePointer(_.classDef),r=this.parsePointer(_.classDef),a=this.parseUShort(),i=this.parseUShort();return{posFormat:t,coverage:n,valueFormat1:s,valueFormat2:o,classDef1:e,classDef2:r,class1Count:a,class2Count:i,classRecords:this.parseList(a,_.list(i,(function(){return{value1:this.parseValueRecord(s),value2:this.parseValueRecord(o)}})))}}f.assert(!1,"0x"+e.toString(16)+": GPOS lookup type 2 format must be 1 or 2.")},Un[3]=function(){return{error:"GPOS Lookup 3 not supported"}},Un[4]=function(){return{error:"GPOS Lookup 4 not supported"}},Un[5]=function(){return{error:"GPOS Lookup 5 not supported"}},Un[6]=function(){return{error:"GPOS Lookup 6 not supported"}},Un[7]=function(){return{error:"GPOS Lookup 7 not supported"}},Un[8]=function(){return{error:"GPOS Lookup 8 not supported"}},Un[9]=function(){return{error:"GPOS Lookup 9 not supported"}};const kn=new Array(10),Tn={parse:function(e,t){const n=new _(e,t=t||0),s=n.parseVersion(1);return f.argument(1===s||1.1===s,"Unsupported GPOS table version "+s),1===s?{version:s,scripts:n.parseScriptList(),features:n.parseFeatureList(),lookups:n.parseLookupList(Un)}:{version:s,scripts:n.parseScriptList(),features:n.parseFeatureList(),lookups:n.parseLookupList(Un),variations:n.parseFeatureVariationsList()}},make:function(e){return new M.Table("GPOS",[{name:"version",type:"ULONG",value:65536},{name:"scripts",type:"TABLE",value:new M.ScriptList(e.scripts)},{name:"features",type:"TABLE",value:new M.FeatureList(e.features)},{name:"lookups",type:"TABLE",value:new M.LookupList(e.lookups,kn)}])}},En={parse:function(e,t){const n=new z.Parser(e,t),s=n.parseUShort();if(0===s)return function(e){const t={};e.skip("uShort");const n=e.parseUShort();f.argument(0===n,"Unsupported kern sub-table version."),e.skip("uShort",2);const s=e.parseUShort();e.skip("uShort",3);for(let n=0;n<s;n+=1){const n=e.parseUShort(),s=e.parseUShort(),o=e.parseShort();t[n+","+s]=o}return t}(n);if(1===s)return function(e){const t={};e.skip("uShort"),e.parseULong()>1&&console.warn("Only the first kern subtable is supported."),e.skip("uLong");const n=255&e.parseUShort();if(e.skip("uShort"),0===n){const n=e.parseUShort();e.skip("uShort",3);for(let s=0;s<n;s+=1){const n=e.parseUShort(),s=e.parseUShort(),o=e.parseShort();t[n+","+s]=o}}return t}(n);throw new Error("Unsupported kern table version ("+s+").")}},wn={parse:function(e,t,n,s){const o=new z.Parser(e,t),r=s?o.parseUShort:o.parseULong,a=[];for(let e=0;e<n+1;e+=1){let e=r.call(o);s&&(e*=2),a.push(e)}return a}};function On(e,t){n(383).readFile(e,(function(e,n){if(e)return t(e.message);t(null,xt(n))}))}function In(e,t){const n=new XMLHttpRequest;n.open("get",e,!0),n.responseType="arraybuffer",n.onload=function(){return n.response?t(null,n.response):t("Font could not be loaded: "+n.statusText)},n.onerror=function(){t("Font could not be loaded")},n.send()}function Rn(e,t){const n=[];let s=12;for(let o=0;o<t;o+=1){const t=z.getTag(e,s),o=z.getULong(e,s+4),r=z.getULong(e,s+8),a=z.getULong(e,s+12);n.push({tag:t,checksum:o,offset:r,length:a,compression:!1}),s+=16}return n}function Ln(e,t){if("WOFF"===t.compression){const n=new Uint8Array(e.buffer,t.offset+2,t.compressedLength-2),s=new Uint8Array(t.length);if(o()(n,s),s.byteLength!==t.length)throw new Error("Decompression error: "+t.tag+" decompressed length doesn't match recorded length");return{data:new DataView(s.buffer,0),offset:0}}return{data:e,offset:t.offset}}function Bn(e){let t,n;const s=new gn({empty:!0}),o=new DataView(e,0);let r,a=[];const i=z.getTag(o,0);if(i===String.fromCharCode(0,1,0,0)||"true"===i||"typ1"===i)s.outlinesFormat="truetype",r=z.getUShort(o,4),a=Rn(o,r);else if("OTTO"===i)s.outlinesFormat="cff",r=z.getUShort(o,4),a=Rn(o,r);else{if("wOFF"!==i)throw new Error("Unsupported OpenType signature "+i);{const e=z.getTag(o,4);if(e===String.fromCharCode(0,1,0,0))s.outlinesFormat="truetype";else{if("OTTO"!==e)throw new Error("Unsupported OpenType flavor "+i);s.outlinesFormat="cff"}r=z.getUShort(o,12),a=function(e,t){const n=[];let s=44;for(let o=0;o<t;o+=1){const t=z.getTag(e,s),o=z.getULong(e,s+4),r=z.getULong(e,s+8),a=z.getULong(e,s+12);let i;i=r<a&&"WOFF",n.push({tag:t,offset:o,compression:i,compressedLength:r,length:a}),s+=20}return n}(o,r)}}let l,u,c,p,f,h,d,g,m,y,v;for(let e=0;e<r;e+=1){const r=a[e];let i;switch(r.tag){case"cmap":i=Ln(o,r),s.tables.cmap=q.parse(i.data,i.offset),s.encoding=new Q(s.tables.cmap);break;case"cvt ":i=Ln(o,r),v=new z.Parser(i.data,i.offset),s.tables.cvt=v.parseShortList(r.length/2);break;case"fvar":u=r;break;case"fpgm":i=Ln(o,r),v=new z.Parser(i.data,i.offset),s.tables.fpgm=v.parseByteList(r.length);break;case"head":i=Ln(o,r),s.tables.head=Le.parse(i.data,i.offset),s.unitsPerEm=s.tables.head.unitsPerEm,t=s.tables.head.indexToLocFormat;break;case"hhea":i=Ln(o,r),s.tables.hhea=Be.parse(i.data,i.offset),s.ascender=s.tables.hhea.ascender,s.descender=s.tables.hhea.descender,s.numberOfHMetrics=s.tables.hhea.numberOfHMetrics;break;case"hmtx":h=r;break;case"ltag":i=Ln(o,r),n=De.parse(i.data,i.offset);break;case"maxp":i=Ln(o,r),s.tables.maxp=Me.parse(i.data,i.offset),s.numGlyphs=s.tables.maxp.numGlyphs;break;case"name":m=r;break;case"OS/2":i=Ln(o,r),s.tables.os2=Ze.parse(i.data,i.offset);break;case"post":i=Ln(o,r),s.tables.post=Qe.parse(i.data,i.offset),s.glyphNames=new K(s.tables.post);break;case"prep":i=Ln(o,r),v=new z.Parser(i.data,i.offset),s.tables.prep=v.parseByteList(r.length);break;case"glyf":c=r;break;case"loca":g=r;break;case"CFF ":l=r;break;case"kern":d=r;break;case"GPOS":p=r;break;case"GSUB":f=r;break;case"meta":y=r}}const b=Ln(o,m);if(s.tables.name=Ve.parse(b.data,b.offset,n),s.names=s.tables.name,c&&g){const e=0===t,n=Ln(o,g),r=wn.parse(n.data,n.offset,s.numGlyphs,e),a=Ln(o,c);s.glyphs=re.parse(a.data,a.offset,r,s)}else{if(!l)throw new Error("Font doesn't contain TrueType or CFF outlines.");{const e=Ln(o,l);Re.parse(e.data,e.offset,s)}}const x=Ln(o,h);if(Ce.parse(x.data,x.offset,s.numberOfHMetrics,s.numGlyphs,s.glyphs),function(e){let t;const n=e.tables.cmap.glyphIndexMap,s=Object.keys(n);for(let o=0;o<s.length;o+=1){const r=s[o],a=n[r];t=e.glyphs.get(a),t.addUnicode(parseInt(r))}for(let n=0;n<e.glyphs.length;n+=1)t=e.glyphs.get(n),e.cffEncoding?e.isCIDFont?t.name="gid"+n:t.name=e.cffEncoding.charset[n]:e.glyphNames.names&&(t.name=e.glyphNames.glyphIndexToName(n))}(s),d){const e=Ln(o,d);s.kerningPairs=En.parse(e.data,e.offset)}else s.kerningPairs={};if(p){const e=Ln(o,p);s.tables.gpos=Tn.parse(e.data,e.offset)}if(f){const e=Ln(o,f);s.tables.gsub=et.parse(e.data,e.offset)}if(u){const e=Ln(o,u);s.tables.fvar=Sn.parse(e.data,e.offset,s.names)}if(y){const e=Ln(o,y);s.tables.meta=tt.parse(e.data,e.offset),s.metas=s.tables.meta}return s}function Cn(e,t){("undefined"==typeof window?On:In)(e,(function(e,n){if(e)return t(e);let s;try{s=Bn(n)}catch(e){return t(e,null)}return t(null,s)}))}function Dn(e){return Bn(xt(n(383).readFileSync(e)))}},596:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(272);class o extends s.Writable{constructor(){super(...arguments),this.offset=0,this._waiters=[],this._closed=!1}_write(e,t,n){let s=0;const o=()=>{for(;this._waiters.length>0;){const t=this._waiters[0];if(this._buffer){const o=this._buffer.size-this._buffer.offset;if(!(o+e.length>=t.size)){if(!t.skip){const t=Buffer.alloc(o+e.length);this._buffer.buf.copy(t,0,this._buffer.offset,this._buffer.size),e.copy(t,o,0,e.length),this._buffer.buf=t}this._buffer.offset=0,this._buffer.size=o+e.length,n();break}if(t.skip)this._buffer=void 0,t.resolve();else{const n=Math.min(o,t.size),s=Buffer.alloc(t.size);this._buffer.buf.copy(s,0,this._buffer.offset,this._buffer.offset+n),e.copy(s,n,0,t.size-n),t.resolve(s)}if(this.offset+=t.size,this._waiters.shift(),this._buffer=void 0,o+e.length===t.size){n();break}s+=t.size-o}else{if(!(e.length-s>=t.size)){this._buffer={buf:t.skip?void 0:e.slice(s),offset:0,size:e.length-s},s=e.length,n();break}if(t.skip?t.resolve():t.resolve(e.slice(s,s+t.size)),this.offset+=t.size,this._waiters.shift(),s+=t.size,e.length===s){n();break}}}this._processTrigger=e.length-s>0?o:void 0};o()}_destroy(e,t){this._processTrigger=void 0;for(const t of this._waiters)t.reject(e||new Error("stream destroyed"));this._waiters=[],this._closed=!0}_final(e){this._processTrigger=void 0;for(const e of this._waiters)e.reject(new Error("not enough data in stream"));this._waiters=[],this._closed=!0}read(e){return new Promise(((t,n)=>{this._closed&&n(new Error("stream is closed")),this._waiters.push({resolve:t,reject:n,size:e,skip:!1}),this._processTrigger&&this._processTrigger()}))}skip(e){return new Promise(((t,n)=>{this._closed&&n(new Error("stream is closed")),this._waiters.push({resolve:t,reject:n,size:e,skip:!0}),this._processTrigger&&this._processTrigger()}))}}function r(){return new o}e.exports=Object.assign(r,{default:r}),t.default=r},741:e=>{function t(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function n(e,n){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=n,this.destLen=0,this.ltree=new t,this.dtree=new t}var s=new t,o=new t,r=new Uint8Array(30),a=new Uint16Array(30),i=new Uint8Array(30),l=new Uint16Array(30),u=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),c=new t,p=new Uint8Array(320);function f(e,t,n,s){var o,r;for(o=0;o<n;++o)e[o]=0;for(o=0;o<30-n;++o)e[o+n]=o/n|0;for(r=s,o=0;o<30;++o)t[o]=r,r+=1<<e[o]}var h=new Uint16Array(16);function d(e,t,n,s){var o,r;for(o=0;o<16;++o)e.table[o]=0;for(o=0;o<s;++o)e.table[t[n+o]]++;for(e.table[0]=0,r=0,o=0;o<16;++o)h[o]=r,r+=e.table[o];for(o=0;o<s;++o)t[n+o]&&(e.trans[h[t[n+o]]++]=o)}function g(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var t=1&e.tag;return e.tag>>>=1,t}function m(e,t,n){if(!t)return n;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var s=e.tag&65535>>>16-t;return e.tag>>>=t,e.bitcount-=t,s+n}function y(e,t){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var n=0,s=0,o=0,r=e.tag;do{s=2*s+(1&r),r>>>=1,++o,n+=t.table[o],s-=t.table[o]}while(s>=0);return e.tag=r,e.bitcount-=o,t.trans[n+s]}function v(e,t,n){var s,o,r,a,i,l;for(s=m(e,5,257),o=m(e,5,1),r=m(e,4,4),a=0;a<19;++a)p[a]=0;for(a=0;a<r;++a){var f=m(e,3,0);p[u[a]]=f}for(d(c,p,0,19),i=0;i<s+o;){var h=y(e,c);switch(h){case 16:var g=p[i-1];for(l=m(e,2,3);l;--l)p[i++]=g;break;case 17:for(l=m(e,3,3);l;--l)p[i++]=0;break;case 18:for(l=m(e,7,11);l;--l)p[i++]=0;break;default:p[i++]=h}}d(t,p,0,s),d(n,p,s,o)}function b(e,t,n){for(;;){var s,o,u,c,p=y(e,t);if(256===p)return 0;if(p<256)e.dest[e.destLen++]=p;else for(s=m(e,r[p-=257],a[p]),o=y(e,n),c=u=e.destLen-m(e,i[o],l[o]);c<u+s;++c)e.dest[e.destLen++]=e.dest[c]}}function x(e){for(var t,n;e.bitcount>8;)e.sourceIndex--,e.bitcount-=8;if((t=256*(t=e.source[e.sourceIndex+1])+e.source[e.sourceIndex])!==(65535&~(256*e.source[e.sourceIndex+3]+e.source[e.sourceIndex+2])))return-3;for(e.sourceIndex+=4,n=t;n;--n)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,0}!function(e,t){var n;for(n=0;n<7;++n)e.table[n]=0;for(e.table[7]=24,e.table[8]=152,e.table[9]=112,n=0;n<24;++n)e.trans[n]=256+n;for(n=0;n<144;++n)e.trans[24+n]=n;for(n=0;n<8;++n)e.trans[168+n]=280+n;for(n=0;n<112;++n)e.trans[176+n]=144+n;for(n=0;n<5;++n)t.table[n]=0;for(t.table[5]=32,n=0;n<32;++n)t.trans[n]=n}(s,o),f(r,a,4,3),f(i,l,2,1),r[28]=0,a[28]=258,e.exports=function(e,t){var r,a,i=new n(e,t);do{switch(r=g(i),m(i,2,0)){case 0:a=x(i);break;case 1:a=b(i,s,o);break;case 2:v(i,i.ltree,i.dtree),a=b(i,i.ltree,i.dtree);break;default:a=-3}if(0!==a)throw new Error("Data error")}while(!r);return i.destLen<i.dest.length?"function"==typeof i.dest.slice?i.dest.slice(0,i.destLen):i.dest.subarray(0,i.destLen):i.dest}},259:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},799:(e,t,n)=>{"use strict";function s(e){var t=this;if(t instanceof s||(t=new s),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach((function(e){t.push(e)}));else if(arguments.length>0)for(var n=0,o=arguments.length;n<o;n++)t.push(arguments[n]);return t}function o(e,t,n,s){if(!(this instanceof o))return new o(e,t,n,s);this.list=s,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}e.exports=s,s.Node=o,s.create=s,s.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t},s.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},s.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},s.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)s=arguments[e],(n=this).tail=new o(s,n.tail,null,n),n.head||(n.head=n.tail),n.length++;var n,s;return this.length},s.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)s=arguments[e],(n=this).head=new o(s,null,n.head,n),n.tail||(n.tail=n.head),n.length++;var n,s;return this.length},s.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},s.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},s.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,s=0;null!==n;s++)e.call(t,n.value,s,this),n=n.next},s.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,s=this.length-1;null!==n;s--)e.call(t,n.value,s,this),n=n.prev},s.prototype.get=function(e){for(var t=0,n=this.head;null!==n&&t<e;t++)n=n.next;if(t===e&&null!==n)return n.value},s.prototype.getReverse=function(e){for(var t=0,n=this.tail;null!==n&&t<e;t++)n=n.prev;if(t===e&&null!==n)return n.value},s.prototype.map=function(e,t){t=t||this;for(var n=new s,o=this.head;null!==o;)n.push(e.call(t,o.value,this)),o=o.next;return n},s.prototype.mapReverse=function(e,t){t=t||this;for(var n=new s,o=this.tail;null!==o;)n.push(e.call(t,o.value,this)),o=o.prev;return n},s.prototype.reduce=function(e,t){var n,s=this.head;if(arguments.length>1)n=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");s=this.head.next,n=this.head.value}for(var o=0;null!==s;o++)n=e(n,s.value,o),s=s.next;return n},s.prototype.reduceReverse=function(e,t){var n,s=this.tail;if(arguments.length>1)n=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");s=this.tail.prev,n=this.tail.value}for(var o=this.length-1;null!==s;o--)n=e(n,s.value,o),s=s.prev;return n},s.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;null!==n;t++)e[t]=n.value,n=n.next;return e},s.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;null!==n;t++)e[t]=n.value,n=n.prev;return e},s.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var n=new s;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=0,r=this.head;null!==r&&o<e;o++)r=r.next;for(;null!==r&&o<t;o++,r=r.next)n.push(r.value);return n},s.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var n=new s;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=this.length,r=this.tail;null!==r&&o>t;o--)r=r.prev;for(;null!==r&&o>e;o--,r=r.prev)n.push(r.value);return n},s.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var s=0,r=this.head;null!==r&&s<e;s++)r=r.next;var a,i,l,u,c=[];for(s=0;r&&s<t;s++)c.push(r.value),r=this.removeNode(r);for(null===r&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev),s=0;s<n.length;s++)a=this,i=r,l=n[s],u=void 0,null===(u=i===a.head?new o(l,null,i,a):new o(l,i,i.next,a)).next&&(a.tail=u),null===u.prev&&(a.head=u),a.length++,r=u;return c},s.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;null!==n;n=n.prev){var s=n.prev;n.prev=n.next,n.next=s}return this.head=t,this.tail=e,this};try{n(259)(s)}catch(e){}},416:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=async function(e,t){var n,i;if(!r){if("undefined"!=typeof navigator&&"fonts"in navigator){try{const e=await(null===(i=(n=navigator.permissions).request)||void 0===i?void 0:i.call(n,{name:"local-fonts"}));if(e&&"granted"!==e.state)throw new Error("Permission to access local fonts not granted.")}catch(e){if("TypeError"!==e.name)throw e}const e={};try{const t=await navigator.fonts.query();for(const n of t)e.hasOwnProperty(n.family)||(e[n.family]=[]),e[n.family].push(n);r=Promise.resolve(e)}catch(e){console.error(e.name,e.message)}}else if("undefined"!=typeof window&&"queryLocalFonts"in window){const e={};try{const t=await window.queryLocalFonts();for(const n of t)e.hasOwnProperty(n.family)||(e[n.family]=[]),e[n.family].push(n);r=Promise.resolve(e)}catch(e){console.error(e.name,e.message)}}r||(r=Promise.resolve({}))}const l=await r;for(const n of(0,o.default)(e)){if(a.includes(n))return;if(l.hasOwnProperty(n)&&l[n].length>0){const e=l[n][0];if("blob"in e){const n=await e.blob(),o=await n.arrayBuffer();return(0,s.loadBuffer)(o,{cacheSize:t})}return}}};const s=n(768),o=n(838);let r;const a=["serif","sans-serif","cursive","fantasy","monospace","system-ui","emoji","math","fangsong"]},241:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.enableLigatures=function(e,t=[]){let n,r,a,i=0;return e.registerCharacterJoiner((l=>{const u=e.options.fontFamily;if(u&&(0===i||n!==u)){r=void 0,i=1,n=u;const t=n;(0,s.default)(t,o).then((n=>{t===e.options.fontFamily&&(i=2,r=n,n&&e.refresh(0,e.rows-1))})).catch((n=>{t===e.options.fontFamily&&(i=3,"debug"===e.options.logLevel&&console.debug(a,new Error("Failure while loading font")),r=void 0,a=n)}))}return r&&2===i?r.findLigatureRanges(l).map((e=>[e[0],e[1]])):function(e,t){const n=[];for(let s=0;s<e.length;s++)for(let o=0;o<t.length;o++)if(e.startsWith(t[o],s)){n.push([s,s+t[o].length]),s+=t[o].length-1;break}return n}(l,t)}))};const s=n(416),o=1e5},838:(e,t)=>{"use strict";function n(e,t){let n="",s=!1;for(;e.offset<e.input.length;){const r=e.input[e.offset++];if(s)/[\dA-Fa-f]/.test(r)?(e.offset--,n+=o(e)):"\n"!==r&&(n+=r),s=!1;else switch(r){case t:return n;case"\\":s=!0;break;default:n+=r}}throw new Error("Unterminated string")}function s(e){let t="",n=!1;for(;e.offset<e.input.length;){const s=e.input[e.offset++];if(n)/[\dA-Fa-f]/.test(s)?(e.offset--,t+=o(e)):t+=s,n=!1;else switch(s){case"\\":n=!0;break;case",":return t;default:/\s/.test(s)?t.endsWith(" ")||(t+=" "):t+=s}}return t}function o(e){let t="";for(;e.offset<e.input.length;){const n=e.input[e.offset++];if(/\s/.test(n))return r(t);if(t.length>=6||!/[\dA-Fa-f]/.test(n))return e.offset--,r(t);t+=n}return r(t)}function r(e){return String.fromCodePoint(parseInt(e,16))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("string"!=typeof e)throw new Error("Font family must be a string");const t={input:e,offset:0},o=[];let r="";for(;t.offset<t.input.length;){const e=t.input[t.offset++];switch(e){case"'":case'"':r+=n(t,e);break;case",":o.push(r),r="";break;default:/\s/.test(e)||(t.offset--,r+=s(t),o.push(r),r="")}}return o}},383:e=>{"use strict";e.exports=require("fs")},3:e=>{"use strict";e.exports=require("path")},272:e=>{"use strict";e.exports=require("stream")},460:e=>{"use strict";e.exports=require("util")},486:()=>{},621:()=>{}},t={};function n(s){var o=t[s];if(void 0!==o)return o.exports;var r=t[s]={exports:{}};return e[s].call(r.exports,r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return(()=>{"use strict";var e=s;Object.defineProperty(e,"__esModule",{value:!0}),e.LigaturesAddon=void 0;const t=n(241);e.LigaturesAddon=class{constructor(e){this._fallbackLigatures=((null==e?void 0:e.fallbackLigatures)||["<--","<---","<<-","<-","->","->>","--\x3e","---\x3e","<==","<===","<<=","<=","=>","=>>","==>","===>",">=",">>=","<->","<--\x3e","<---\x3e","<----\x3e","<=>","<==>","<===>","<====>","::",":::","<~~","</","</>","/>","~~>","==","!=","/=","~=","<>","===","!==","!===","<:",":=","*=","*+","<*","<*>","*>","<|","<|>","|>","+*","=*","=:",":>","/*","*/","+++","\x3c!--","\x3c!---"]).sort(((e,t)=>t.length-e.length)),this._fontFeatureSettings=null==e?void 0:e.fontFeatureSettings}activate(e){var n;if(!e.element)throw new Error("Cannot activate LigaturesAddon before open is called");this._terminal=e,this._characterJoinerId=(0,t.enableLigatures)(e,this._fallbackLigatures),e.element.style.fontFeatureSettings=null!==(n=this._fontFeatureSettings)&&void 0!==n?n:'"calt" on'}dispose(){var e,t;void 0!==this._characterJoinerId&&(null===(e=this._terminal)||void 0===e||e.deregisterCharacterJoiner(this._characterJoinerId),this._characterJoinerId=void 0),(null===(t=this._terminal)||void 0===t?void 0:t.element)&&(this._terminal.element.style.fontFeatureSettings="")}}})(),s})()));