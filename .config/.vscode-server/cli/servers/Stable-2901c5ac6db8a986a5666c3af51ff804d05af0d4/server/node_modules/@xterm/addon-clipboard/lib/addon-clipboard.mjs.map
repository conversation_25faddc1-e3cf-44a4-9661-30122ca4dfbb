{"version": 3, "sources": ["../node_modules/js-base64/base64.mjs", "../src/ClipboardAddon.ts"], "sourcesContent": ["/**\n *  base64.ts\n *\n *  Licensed under the BSD 3-Clause License.\n *    http://opensource.org/licenses/BSD-3-Clause\n *\n *  References:\n *    http://en.wikipedia.org/wiki/Base64\n *\n * <AUTHOR> (https://github.com/dankogai)\n */\nconst version = '3.7.7';\n/**\n * @deprecated use lowercase `version`.\n */\nconst VERSION = version;\nconst _hasBuffer = typeof Buffer === 'function';\nconst _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;\nconst _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;\nconst b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nconst b64chs = Array.prototype.slice.call(b64ch);\nconst b64tab = ((a) => {\n    let tab = {};\n    a.forEach((c, i) => tab[c] = i);\n    return tab;\n})(b64chs);\nconst b64re = /^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;\nconst _fromCC = String.fromCharCode.bind(String);\nconst _U8Afrom = typeof Uint8Array.from === 'function'\n    ? Uint8Array.from.bind(Uint8Array)\n    : (it) => new Uint8Array(Array.prototype.slice.call(it, 0));\nconst _mkUriSafe = (src) => src\n    .replace(/=/g, '').replace(/[+\\/]/g, (m0) => m0 == '+' ? '-' : '_');\nconst _tidyB64 = (s) => s.replace(/[^A-Za-z0-9\\+\\/]/g, '');\n/**\n * polyfill version of `btoa`\n */\nconst btoaPolyfill = (bin) => {\n    // console.log('polyfilled');\n    let u32, c0, c1, c2, asc = '';\n    const pad = bin.length % 3;\n    for (let i = 0; i < bin.length;) {\n        if ((c0 = bin.charCodeAt(i++)) > 255 ||\n            (c1 = bin.charCodeAt(i++)) > 255 ||\n            (c2 = bin.charCodeAt(i++)) > 255)\n            throw new TypeError('invalid character found');\n        u32 = (c0 << 16) | (c1 << 8) | c2;\n        asc += b64chs[u32 >> 18 & 63]\n            + b64chs[u32 >> 12 & 63]\n            + b64chs[u32 >> 6 & 63]\n            + b64chs[u32 & 63];\n    }\n    return pad ? asc.slice(0, pad - 3) + \"===\".substring(pad) : asc;\n};\n/**\n * does what `window.btoa` of web browsers do.\n * @param {String} bin binary string\n * @returns {string} Base64-encoded string\n */\nconst _btoa = typeof btoa === 'function' ? (bin) => btoa(bin)\n    : _hasBuffer ? (bin) => Buffer.from(bin, 'binary').toString('base64')\n        : btoaPolyfill;\nconst _fromUint8Array = _hasBuffer\n    ? (u8a) => Buffer.from(u8a).toString('base64')\n    : (u8a) => {\n        // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326\n        const maxargs = 0x1000;\n        let strs = [];\n        for (let i = 0, l = u8a.length; i < l; i += maxargs) {\n            strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));\n        }\n        return _btoa(strs.join(''));\n    };\n/**\n * converts a Uint8Array to a Base64 string.\n * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5\n * @returns {string} Base64 string\n */\nconst fromUint8Array = (u8a, urlsafe = false) => urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);\n// This trick is found broken https://github.com/dankogai/js-base64/issues/130\n// const utob = (src: string) => unescape(encodeURIComponent(src));\n// reverting good old fationed regexp\nconst cb_utob = (c) => {\n    if (c.length < 2) {\n        var cc = c.charCodeAt(0);\n        return cc < 0x80 ? c\n            : cc < 0x800 ? (_fromCC(0xc0 | (cc >>> 6))\n                + _fromCC(0x80 | (cc & 0x3f)))\n                : (_fromCC(0xe0 | ((cc >>> 12) & 0x0f))\n                    + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                    + _fromCC(0x80 | (cc & 0x3f)));\n    }\n    else {\n        var cc = 0x10000\n            + (c.charCodeAt(0) - 0xD800) * 0x400\n            + (c.charCodeAt(1) - 0xDC00);\n        return (_fromCC(0xf0 | ((cc >>> 18) & 0x07))\n            + _fromCC(0x80 | ((cc >>> 12) & 0x3f))\n            + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n            + _fromCC(0x80 | (cc & 0x3f)));\n    }\n};\nconst re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;\n/**\n * @deprecated should have been internal use only.\n * @param {string} src UTF-8 string\n * @returns {string} UTF-16 string\n */\nconst utob = (u) => u.replace(re_utob, cb_utob);\n//\nconst _encode = _hasBuffer\n    ? (s) => Buffer.from(s, 'utf8').toString('base64')\n    : _TE\n        ? (s) => _fromUint8Array(_TE.encode(s))\n        : (s) => _btoa(utob(s));\n/**\n * converts a UTF-8-encoded string to a Base64 string.\n * @param {boolean} [urlsafe] if `true` make the result URL-safe\n * @returns {string} Base64 string\n */\nconst encode = (src, urlsafe = false) => urlsafe\n    ? _mkUriSafe(_encode(src))\n    : _encode(src);\n/**\n * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.\n * @returns {string} Base64 string\n */\nconst encodeURI = (src) => encode(src, true);\n// This trick is found broken https://github.com/dankogai/js-base64/issues/130\n// const btou = (src: string) => decodeURIComponent(escape(src));\n// reverting good old fationed regexp\nconst re_btou = /[\\xC0-\\xDF][\\x80-\\xBF]|[\\xE0-\\xEF][\\x80-\\xBF]{2}|[\\xF0-\\xF7][\\x80-\\xBF]{3}/g;\nconst cb_btou = (cccc) => {\n    switch (cccc.length) {\n        case 4:\n            var cp = ((0x07 & cccc.charCodeAt(0)) << 18)\n                | ((0x3f & cccc.charCodeAt(1)) << 12)\n                | ((0x3f & cccc.charCodeAt(2)) << 6)\n                | (0x3f & cccc.charCodeAt(3)), offset = cp - 0x10000;\n            return (_fromCC((offset >>> 10) + 0xD800)\n                + _fromCC((offset & 0x3FF) + 0xDC00));\n        case 3:\n            return _fromCC(((0x0f & cccc.charCodeAt(0)) << 12)\n                | ((0x3f & cccc.charCodeAt(1)) << 6)\n                | (0x3f & cccc.charCodeAt(2)));\n        default:\n            return _fromCC(((0x1f & cccc.charCodeAt(0)) << 6)\n                | (0x3f & cccc.charCodeAt(1)));\n    }\n};\n/**\n * @deprecated should have been internal use only.\n * @param {string} src UTF-16 string\n * @returns {string} UTF-8 string\n */\nconst btou = (b) => b.replace(re_btou, cb_btou);\n/**\n * polyfill version of `atob`\n */\nconst atobPolyfill = (asc) => {\n    // console.log('polyfilled');\n    asc = asc.replace(/\\s+/g, '');\n    if (!b64re.test(asc))\n        throw new TypeError('malformed base64.');\n    asc += '=='.slice(2 - (asc.length & 3));\n    let u24, bin = '', r1, r2;\n    for (let i = 0; i < asc.length;) {\n        u24 = b64tab[asc.charAt(i++)] << 18\n            | b64tab[asc.charAt(i++)] << 12\n            | (r1 = b64tab[asc.charAt(i++)]) << 6\n            | (r2 = b64tab[asc.charAt(i++)]);\n        bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)\n            : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)\n                : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);\n    }\n    return bin;\n};\n/**\n * does what `window.atob` of web browsers do.\n * @param {String} asc Base64-encoded string\n * @returns {string} binary string\n */\nconst _atob = typeof atob === 'function' ? (asc) => atob(_tidyB64(asc))\n    : _hasBuffer ? (asc) => Buffer.from(asc, 'base64').toString('binary')\n        : atobPolyfill;\n//\nconst _toUint8Array = _hasBuffer\n    ? (a) => _U8Afrom(Buffer.from(a, 'base64'))\n    : (a) => _U8Afrom(_atob(a).split('').map(c => c.charCodeAt(0)));\n/**\n * converts a Base64 string to a Uint8Array.\n */\nconst toUint8Array = (a) => _toUint8Array(_unURI(a));\n//\nconst _decode = _hasBuffer\n    ? (a) => Buffer.from(a, 'base64').toString('utf8')\n    : _TD\n        ? (a) => _TD.decode(_toUint8Array(a))\n        : (a) => btou(_atob(a));\nconst _unURI = (a) => _tidyB64(a.replace(/[-_]/g, (m0) => m0 == '-' ? '+' : '/'));\n/**\n * converts a Base64 string to a UTF-8 string.\n * @param {String} src Base64 string.  Both normal and URL-safe are supported\n * @returns {string} UTF-8 string\n */\nconst decode = (src) => _decode(_unURI(src));\n/**\n * check if a value is a valid Base64 string\n * @param {String} src a value to check\n  */\nconst isValid = (src) => {\n    if (typeof src !== 'string')\n        return false;\n    const s = src.replace(/\\s+/g, '').replace(/={0,2}$/, '');\n    return !/[^\\s0-9a-zA-Z\\+/]/.test(s) || !/[^\\s0-9a-zA-Z\\-_]/.test(s);\n};\n//\nconst _noEnum = (v) => {\n    return {\n        value: v, enumerable: false, writable: true, configurable: true\n    };\n};\n/**\n * extend String.prototype with relevant methods\n */\nconst extendString = function () {\n    const _add = (name, body) => Object.defineProperty(String.prototype, name, _noEnum(body));\n    _add('fromBase64', function () { return decode(this); });\n    _add('toBase64', function (urlsafe) { return encode(this, urlsafe); });\n    _add('toBase64URI', function () { return encode(this, true); });\n    _add('toBase64URL', function () { return encode(this, true); });\n    _add('toUint8Array', function () { return toUint8Array(this); });\n};\n/**\n * extend Uint8Array.prototype with relevant methods\n */\nconst extendUint8Array = function () {\n    const _add = (name, body) => Object.defineProperty(Uint8Array.prototype, name, _noEnum(body));\n    _add('toBase64', function (urlsafe) { return fromUint8Array(this, urlsafe); });\n    _add('toBase64URI', function () { return fromUint8Array(this, true); });\n    _add('toBase64URL', function () { return fromUint8Array(this, true); });\n};\n/**\n * extend Builtin prototypes with relevant methods\n */\nconst extendBuiltins = () => {\n    extendString();\n    extendUint8Array();\n};\nconst gBase64 = {\n    version: version,\n    VERSION: VERSION,\n    atob: _atob,\n    atobPolyfill: atobPolyfill,\n    btoa: _btoa,\n    btoaPolyfill: btoaPolyfill,\n    fromBase64: decode,\n    toBase64: encode,\n    encode: encode,\n    encodeURI: encodeURI,\n    encodeURL: encodeURI,\n    utob: utob,\n    btou: btou,\n    decode: decode,\n    isValid: isValid,\n    fromUint8Array: fromUint8Array,\n    toUint8Array: toUint8Array,\n    extendString: extendString,\n    extendUint8Array: extendUint8Array,\n    extendBuiltins: extendBuiltins\n};\n// makecjs:CUT //\nexport { version };\nexport { VERSION };\nexport { _atob as atob };\nexport { atobPolyfill };\nexport { _btoa as btoa };\nexport { btoaPolyfill };\nexport { decode as fromBase64 };\nexport { encode as toBase64 };\nexport { utob };\nexport { encode };\nexport { encodeURI };\nexport { encodeURI as encodeURL };\nexport { btou };\nexport { decode };\nexport { isValid };\nexport { fromUint8Array };\nexport { toUint8Array };\nexport { extendString };\nexport { extendUint8Array };\nexport { extendBuiltins };\n// and finally,\nexport { gBase64 as Base64 };\n", "/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { IDisposable, ITerminalAddon, Terminal } from '@xterm/xterm';\nimport { type IClipboardProvider, ClipboardSelectionType, type IBase64 } from '@xterm/addon-clipboard';\nimport { Base64 as JSBase64 } from 'js-base64';\n\nexport class ClipboardAddon implements ITerminalAddon {\n  private _terminal?: Terminal;\n  private _disposable?: IDisposable;\n\n  constructor(\n    private _base64: IBase64 = new Base64(),\n    private _provider: IClipboardProvider = new BrowserClipboardProvider()\n  ) {}\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this._disposable = terminal.parser.registerOscHandler(52, data => this._setOrReportClipboard(data));\n  }\n\n  public dispose(): void {\n    return this._disposable?.dispose();\n  }\n\n  private _readText(sel: ClipboardSelectionType, data: string): void {\n    const b64 = this._base64.encodeText(data);\n    this._terminal?.input(`\\x1b]52;${sel};${b64}\\x07`, false);\n  }\n\n  private _setOrReportClipboard(data: string): boolean | Promise<boolean> {\n    const args = data.split(';');\n    if (args.length < 2) {\n      return true;\n    }\n\n    const pc = args[0] as ClipboardSelectionType;\n    const pd = args[1];\n    if (pd === '?') {\n      const text = this._provider.readText(pc);\n\n      // Report clipboard\n      if (text instanceof Promise) {\n        return text.then((data) => {\n          this._readText(pc, data);\n          return true;\n        });\n      }\n\n      this._readText(pc, text);\n      return true;\n    }\n\n    // Clear clipboard if text is not a base64 encoded string.\n    let text = '';\n    try {\n      text = this._base64.decodeText(pd);\n    } catch {}\n\n\n    const result = this._provider.writeText(pc, text);\n    if (result instanceof Promise) {\n      return result.then(() => true);\n    }\n\n    return true;\n  }\n}\n\nexport class BrowserClipboardProvider implements IClipboardProvider {\n  public async readText(selection: ClipboardSelectionType): Promise<string> {\n    if (selection !== 'c') {\n      return Promise.resolve('');\n    }\n    return navigator.clipboard.readText();\n  }\n\n  public async writeText(selection: ClipboardSelectionType, text: string): Promise<void> {\n    if (selection !== 'c') {\n      return Promise.resolve();\n    }\n    return navigator.clipboard.writeText(text);\n  }\n}\n\nexport class Base64 implements IBase64 {\n  public encodeText(data: string): string {\n    return JSBase64.encode(data);\n  }\n  public decodeText(data: string): string {\n    const text = JSBase64.decode(data);\n    if (!JSBase64.isValid(data) || JSBase64.encode(text) !== data) {\n      return '';\n    }\n    return text;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAWA,IAAMA,EAAU,QAIVC,EAAUD,EACVE,EAAa,OAAO,QAAW,WAC/BC,EAAM,OAAO,aAAgB,WAAa,IAAI,YAAgB,OAC9DC,EAAM,OAAO,aAAgB,WAAa,IAAI,YAAgB,OAC9DC,EAAQ,oEACRC,EAAS,MAAM,UAAU,MAAM,KAAKD,CAAK,EACzCE,GAAWC,GAAM,CACnB,IAAIC,EAAM,CAAC,EACX,OAAAD,EAAE,QAAQ,CAACE,EAAGC,IAAMF,EAAIC,CAAC,EAAIC,CAAC,EACvBF,CACX,GAAGH,CAAM,EACHM,EAAQ,0EACRC,EAAU,OAAO,aAAa,KAAK,MAAM,EACzCC,EAAW,OAAO,WAAW,MAAS,WACtC,WAAW,KAAK,KAAK,UAAU,EAC9BC,GAAO,IAAI,WAAW,MAAM,UAAU,MAAM,KAAKA,EAAI,CAAC,CAAC,EACxDC,EAAcC,GAAQA,EACvB,QAAQ,KAAM,EAAE,EAAE,QAAQ,SAAWC,GAAOA,GAAM,IAAM,IAAM,GAAG,EAChEC,EAAYC,GAAMA,EAAE,QAAQ,oBAAqB,EAAE,EAInDC,EAAgBC,GAAQ,CAE1B,IAAIC,EAAKC,EAAIC,EAAIC,EAAIC,EAAM,GACrBC,EAAMN,EAAI,OAAS,EACzB,QAASX,EAAI,EAAGA,EAAIW,EAAI,QAAS,CAC7B,IAAKE,EAAKF,EAAI,WAAWX,GAAG,GAAK,MAC5Bc,EAAKH,EAAI,WAAWX,GAAG,GAAK,MAC5Be,EAAKJ,EAAI,WAAWX,GAAG,GAAK,IAC7B,MAAM,IAAI,UAAU,yBAAyB,EACjDY,EAAOC,GAAM,GAAOC,GAAM,EAAKC,EAC/BC,GAAOrB,EAAOiB,GAAO,GAAK,EAAE,EACtBjB,EAAOiB,GAAO,GAAK,EAAE,EACrBjB,EAAOiB,GAAO,EAAI,EAAE,EACpBjB,EAAOiB,EAAM,EAAE,CACzB,CACA,OAAOK,EAAMD,EAAI,MAAM,EAAGC,EAAM,CAAC,EAAI,MAAM,UAAUA,CAAG,EAAID,CAChE,EAMME,EAAQ,OAAO,MAAS,WAAcP,GAAQ,KAAKA,CAAG,EACtDpB,EAAcoB,GAAQ,OAAO,KAAKA,EAAK,QAAQ,EAAE,SAAS,QAAQ,EAC9DD,EACJS,EAAkB5B,EACjB6B,GAAQ,OAAO,KAAKA,CAAG,EAAE,SAAS,QAAQ,EAC1CA,GAAQ,CAGP,IAAIC,EAAO,CAAC,EACZ,QAASrB,EAAI,EAAGsB,EAAIF,EAAI,OAAQpB,EAAIsB,EAAGtB,GAAK,KACxCqB,EAAK,KAAKnB,EAAQ,MAAM,KAAMkB,EAAI,SAASpB,EAAGA,EAAI,IAAO,CAAC,CAAC,EAE/D,OAAOkB,EAAMG,EAAK,KAAK,EAAE,CAAC,CAC9B,EAMEE,EAAiB,CAACH,EAAKI,EAAU,KAAUA,EAAUnB,EAAWc,EAAgBC,CAAG,CAAC,EAAID,EAAgBC,CAAG,EAI3GK,EAAW1B,GAAM,CACnB,GAAIA,EAAE,OAAS,EAAG,CACd,IAAI2B,EAAK3B,EAAE,WAAW,CAAC,EACvB,OAAO2B,EAAK,IAAO3B,EACb2B,EAAK,KAASxB,EAAQ,IAAQwB,IAAO,CAAE,EACnCxB,EAAQ,IAAQwB,EAAK,EAAK,EACzBxB,EAAQ,IAASwB,IAAO,GAAM,EAAK,EAChCxB,EAAQ,IAASwB,IAAO,EAAK,EAAK,EAClCxB,EAAQ,IAAQwB,EAAK,EAAK,CAC5C,KACK,CACD,IAAIA,EAAK,OACF3B,EAAE,WAAW,CAAC,EAAI,OAAU,MAC5BA,EAAE,WAAW,CAAC,EAAI,OACzB,OAAQG,EAAQ,IAASwB,IAAO,GAAM,CAAK,EACrCxB,EAAQ,IAASwB,IAAO,GAAM,EAAK,EACnCxB,EAAQ,IAASwB,IAAO,EAAK,EAAK,EAClCxB,EAAQ,IAAQwB,EAAK,EAAK,CACpC,CACJ,EACMC,EAAU,gDAMVC,EAAQC,GAAMA,EAAE,QAAQF,EAASF,CAAO,EAExCK,EAAUvC,EACTkB,GAAM,OAAO,KAAKA,EAAG,MAAM,EAAE,SAAS,QAAQ,EAC/ChB,EACKgB,GAAMU,EAAgB1B,EAAI,OAAOgB,CAAC,CAAC,EACnCA,GAAMS,EAAMU,EAAKnB,CAAC,CAAC,EAMxBsB,EAAS,CAACzB,EAAKkB,EAAU,KAAUA,EACnCnB,EAAWyB,EAAQxB,CAAG,CAAC,EACvBwB,EAAQxB,CAAG,EAKX0B,EAAa1B,GAAQyB,EAAOzB,EAAK,EAAI,EAIrC2B,EAAU,8EACVC,EAAWC,GAAS,CACtB,OAAQA,EAAK,OAAQ,CACjB,IAAK,GACD,IAAIC,GAAO,EAAOD,EAAK,WAAW,CAAC,IAAM,IACjC,GAAOA,EAAK,WAAW,CAAC,IAAM,IAC9B,GAAOA,EAAK,WAAW,CAAC,IAAM,EAC/B,GAAOA,EAAK,WAAW,CAAC,EAAIE,EAASD,EAAK,MACjD,OAAQlC,GAASmC,IAAW,IAAM,KAAM,EAClCnC,GAASmC,EAAS,MAAS,KAAM,EAC3C,IAAK,GACD,OAAOnC,GAAU,GAAOiC,EAAK,WAAW,CAAC,IAAM,IACvC,GAAOA,EAAK,WAAW,CAAC,IAAM,EAC/B,GAAOA,EAAK,WAAW,CAAC,CAAE,EACrC,QACI,OAAOjC,GAAU,GAAOiC,EAAK,WAAW,CAAC,IAAM,EACxC,GAAOA,EAAK,WAAW,CAAC,CAAE,CACzC,CACJ,EAMMG,EAAQC,GAAMA,EAAE,QAAQN,EAASC,CAAO,EAIxCM,EAAgBxB,GAAQ,CAG1B,GADAA,EAAMA,EAAI,QAAQ,OAAQ,EAAE,EACxB,CAACf,EAAM,KAAKe,CAAG,EACf,MAAM,IAAI,UAAU,mBAAmB,EAC3CA,GAAO,KAAK,MAAM,GAAKA,EAAI,OAAS,EAAE,EACtC,IAAIyB,EAAK9B,EAAM,GAAI+B,EAAIC,EACvB,QAAS,EAAI,EAAG,EAAI3B,EAAI,QACpByB,EAAM7C,EAAOoB,EAAI,OAAO,GAAG,CAAC,GAAK,GAC3BpB,EAAOoB,EAAI,OAAO,GAAG,CAAC,GAAK,IAC1B0B,EAAK9C,EAAOoB,EAAI,OAAO,GAAG,CAAC,IAAM,GACjC2B,EAAK/C,EAAOoB,EAAI,OAAO,GAAG,CAAC,GAClCL,GAAO+B,IAAO,GAAKxC,EAAQuC,GAAO,GAAK,GAAG,EACpCE,IAAO,GAAKzC,EAAQuC,GAAO,GAAK,IAAKA,GAAO,EAAI,GAAG,EAC/CvC,EAAQuC,GAAO,GAAK,IAAKA,GAAO,EAAI,IAAKA,EAAM,GAAG,EAEhE,OAAO9B,CACX,EAMMiC,EAAQ,OAAO,MAAS,WAAc5B,GAAQ,KAAKR,EAASQ,CAAG,CAAC,EAChEzB,EAAcyB,GAAQ,OAAO,KAAKA,EAAK,QAAQ,EAAE,SAAS,QAAQ,EAC9DwB,EAEJK,EAAgBtD,EACfM,GAAMM,EAAS,OAAO,KAAKN,EAAG,QAAQ,CAAC,EACvCA,GAAMM,EAASyC,EAAM/C,CAAC,EAAE,MAAM,EAAE,EAAE,IAAIE,GAAKA,EAAE,WAAW,CAAC,CAAC,CAAC,EAI5D+C,EAAgBjD,GAAMgD,EAAcE,EAAOlD,CAAC,CAAC,EAE7CmD,EAAUzD,EACTM,GAAM,OAAO,KAAKA,EAAG,QAAQ,EAAE,SAAS,MAAM,EAC/CL,EACKK,GAAML,EAAI,OAAOqD,EAAchD,CAAC,CAAC,EACjCA,GAAMyC,EAAKM,EAAM/C,CAAC,CAAC,EACxBkD,EAAUlD,GAAMW,EAASX,EAAE,QAAQ,QAAUU,GAAOA,GAAM,IAAM,IAAM,GAAG,CAAC,EAM1E0C,EAAU3C,GAAQ0C,EAAQD,EAAOzC,CAAG,CAAC,EAKrC4C,EAAW5C,GAAQ,CACrB,GAAI,OAAOA,GAAQ,SACf,MAAO,GACX,IAAMG,EAAIH,EAAI,QAAQ,OAAQ,EAAE,EAAE,QAAQ,UAAW,EAAE,EACvD,MAAO,CAAC,oBAAoB,KAAKG,CAAC,GAAK,CAAC,oBAAoB,KAAKA,CAAC,CACtE,EAEM0C,EAAWC,IACN,CACH,MAAOA,EAAG,WAAY,GAAO,SAAU,GAAM,aAAc,EAC/D,GAKEC,EAAe,UAAY,CAC7B,IAAMC,EAAO,CAACC,EAAMC,IAAS,OAAO,eAAe,OAAO,UAAWD,EAAMJ,EAAQK,CAAI,CAAC,EACxFF,EAAK,aAAc,UAAY,CAAE,OAAOL,EAAO,IAAI,CAAG,CAAC,EACvDK,EAAK,WAAY,SAAU9B,EAAS,CAAE,OAAOO,EAAO,KAAMP,CAAO,CAAG,CAAC,EACrE8B,EAAK,cAAe,UAAY,CAAE,OAAOvB,EAAO,KAAM,EAAI,CAAG,CAAC,EAC9DuB,EAAK,cAAe,UAAY,CAAE,OAAOvB,EAAO,KAAM,EAAI,CAAG,CAAC,EAC9DuB,EAAK,eAAgB,UAAY,CAAE,OAAOR,EAAa,IAAI,CAAG,CAAC,CACnE,EAIMW,EAAmB,UAAY,CACjC,IAAMH,EAAO,CAACC,EAAMC,IAAS,OAAO,eAAe,WAAW,UAAWD,EAAMJ,EAAQK,CAAI,CAAC,EAC5FF,EAAK,WAAY,SAAU9B,EAAS,CAAE,OAAOD,EAAe,KAAMC,CAAO,CAAG,CAAC,EAC7E8B,EAAK,cAAe,UAAY,CAAE,OAAO/B,EAAe,KAAM,EAAI,CAAG,CAAC,EACtE+B,EAAK,cAAe,UAAY,CAAE,OAAO/B,EAAe,KAAM,EAAI,CAAG,CAAC,CAC1E,EAIMmC,EAAiB,IAAM,CACzBL,EAAa,EACbI,EAAiB,CACrB,EACME,EAAU,CACZ,QAAStE,EACT,QAASC,EACT,KAAMsD,EACN,aAAcJ,EACd,KAAMtB,EACN,aAAcR,EACd,WAAYuC,EACZ,SAAUlB,EACV,OAAQA,EACR,UAAWC,EACX,UAAWA,EACX,KAAMJ,EACN,KAAMU,EACN,OAAQW,EACR,QAASC,EACT,eAAgB3B,EAChB,aAAcuB,EACd,aAAcO,EACd,iBAAkBI,EAClB,eAAgBC,CACpB,ECrQO,IAAME,EAAN,KAA+C,CAIpD,YACUC,EAAmB,IAAIC,EACvBC,EAAgC,IAAIC,EAC5C,CAFQ,aAAAH,EACA,eAAAE,CACP,CAEI,SAASE,EAA0B,CACxC,KAAK,UAAYA,EACjB,KAAK,YAAcA,EAAS,OAAO,mBAAmB,GAAIC,GAAQ,KAAK,sBAAsBA,CAAI,CAAC,CACpG,CAEO,SAAgB,CACrB,OAAO,KAAK,aAAa,QAAQ,CACnC,CAEQ,UAAUC,EAA6BD,EAAoB,CACjE,IAAME,EAAM,KAAK,QAAQ,WAAWF,CAAI,EACxC,KAAK,WAAW,MAAM,WAAWC,CAAG,IAAIC,CAAG,OAAQ,EAAK,CAC1D,CAEQ,sBAAsBF,EAA0C,CACtE,IAAMG,EAAOH,EAAK,MAAM,GAAG,EAC3B,GAAIG,EAAK,OAAS,EAChB,MAAO,GAGT,IAAMC,EAAKD,EAAK,CAAC,EACXE,EAAKF,EAAK,CAAC,EACjB,GAAIE,IAAO,IAAK,CACd,IAAMC,EAAO,KAAK,UAAU,SAASF,CAAE,EAGvC,OAAIE,aAAgB,QACXA,EAAK,KAAMN,IAChB,KAAK,UAAUI,EAAIJ,CAAI,EAChB,GACR,GAGH,KAAK,UAAUI,EAAIE,CAAI,EAChB,GACT,CAGA,IAAIA,EAAO,GACX,GAAI,CACFA,EAAO,KAAK,QAAQ,WAAWD,CAAE,CACnC,MAAQ,CAAC,CAGT,IAAME,EAAS,KAAK,UAAU,UAAUH,EAAIE,CAAI,EAChD,OAAIC,aAAkB,QACbA,EAAO,KAAK,IAAM,EAAI,EAGxB,EACT,CACF,EAEaT,EAAN,KAA6D,CAClE,MAAa,SAASU,EAAoD,CACxE,OAAIA,IAAc,IACT,QAAQ,QAAQ,EAAE,EAEpB,UAAU,UAAU,SAAS,CACtC,CAEA,MAAa,UAAUA,EAAmCF,EAA6B,CACrF,OAAIE,IAAc,IACT,QAAQ,QAAQ,EAElB,UAAU,UAAU,UAAUF,CAAI,CAC3C,CACF,EAEaV,EAAN,KAAgC,CAC9B,WAAWI,EAAsB,CACtC,OAAOS,EAAS,OAAOT,CAAI,CAC7B,CACO,WAAWA,EAAsB,CACtC,IAAMM,EAAOG,EAAS,OAAOT,CAAI,EACjC,MAAI,CAACS,EAAS,QAAQT,CAAI,GAAKS,EAAS,OAAOH,CAAI,IAAMN,EAChD,GAEFM,CACT,CACF", "names": ["version", "VERSION", "_hasBuffer", "_TD", "_TE", "b64ch", "b64chs", "b64tab", "a", "tab", "c", "i", "b64re", "_fromCC", "_U8Afrom", "it", "_mkUriSafe", "src", "m0", "_tidyB64", "s", "btoaPolyfill", "bin", "u32", "c0", "c1", "c2", "asc", "pad", "_btoa", "_fromUint8Array", "u8a", "strs", "l", "fromUint8Array", "urlsafe", "cb_utob", "cc", "re_utob", "utob", "u", "_encode", "encode", "encodeURI", "re_btou", "cb_btou", "cccc", "cp", "offset", "btou", "b", "atobPolyfill", "u24", "r1", "r2", "_atob", "_toUint8Array", "toUint8Array", "_unURI", "_decode", "decode", "<PERSON><PERSON><PERSON><PERSON>", "_noEnum", "v", "extendString", "_add", "name", "body", "extendUint8Array", "extendBuiltins", "gBase64", "ClipboardAddon", "_base64", "Base64", "_provider", "BrowserClipboardProvider", "terminal", "data", "sel", "b64", "args", "pc", "pd", "text", "result", "selection", "gBase64"]}