/*!
 * 1DS JS SDK Core, 3.2.13
 * Copyright (c) Microsoft and contributors. All rights reserved.
 * (Microsoft Internal Only)
 */
var e=this,n=function(e,c,f,n){"use strict";function L(e){return e&&"object"==typeof e&&"default"in e?e["default"]:e}var o=L(n),n=f.createEnumStyle({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,CustomerContent_GenericContent:32}),V=f.createEnumStyle({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}),x=f.createEnumStyle({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),M=f.createEnumStyle({Normal:1,Critical:2}),R=f.createEnumStyle({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),H=f.objFreeze(c.__assignFn(c.__assignFn({},f._InternalMessageId),f.createEnumStyle({AuthHandShakeError:501,AuthRedirectFail:502,BrowserCannotReadLocalStorage:503,BrowserCannotWriteLocalStorage:504,BrowserDoesNotSupportLocalStorage:505,CannotParseBiBlobValue:506,CannotParseDataAttribute:507,CVPluginNotAvailable:508,DroppedEvent:509,ErrorParsingAISessionCookie:510,ErrorProvidedChannels:511,FailedToGetCookies:512,FailedToInitializeCorrelationVector:513,FailedToInitializeSDK:514,InvalidContentBlob:515,InvalidCorrelationValue:516,SessionRenewalDateIsZero:517,SendPostOnCompleteFailure:518,PostResponseHandler:519,SDKNotInitialized:520}))),g="",d="https://browser.events.data.microsoft.com/OneCollector/1.0/",r="version",i="properties",t="3.2.13",s="1DS-Web-JS-"+t,B="withCredentials",G=((u={})[0]=0,u[2]=6,u[1]=1,u[3]=7,u[4098]=6,u[4097]=1,u[4099]=7,u),a=null,l=!!f.getDocument(),u=!!f.getWindow();function m(e){return!(e===g||f.isNullOrUndefined(e))}function W(e){if(e){var n=e.indexOf("-");if(-1<n)return e.substring(0,n)}return g}function z(){return a=null===a?!(f.isUndefined(Uint8Array)||(e=f.getNavigator(),f.isUndefined(e)||!e.userAgent||!~(e=e.userAgent.toLowerCase()).indexOf("safari")&&!~e.indexOf("firefox")||~e.indexOf("chrome")?void 0:1)||f.isReactNative()):a;var e}function v(e){return!!(e&&f.isNumber(e)&&1<=e&&e<=4)}function K(e,n,t){if(!n&&!m(n)||"string"!=typeof e)return null;e=typeof n;if("string"==e||"number"==e||"boolean"==e||f.isArray(n))n={value:n};else if("object"!=e||c.ObjHasOwnProperty.call(n,"value")){if(f.isNullOrUndefined(n.value)||n.value===g||!f.isString(n.value)&&!f.isNumber(n.value)&&!f.isBoolean(n.value)&&!f.isArray(n.value))return null}else n={value:t?JSON.stringify(n):n};if(f.isArray(n.value)&&!S(n.value))return null;if(!f.isNullOrUndefined(n.kind)){if(f.isArray(n.value)||!y(n.kind))return null;n.value=n.value.toString()}return n}function q(e,n,t){var r=-1;return f.isUndefined(e)||(0<n&&(32===n?r=8192:n<=13&&(r=n<<5)),0<=t&&t<=9?(-1===r&&(r=0),r|=t):(n=G[b(e)]||-1,-1!==r&&-1!==n?r|=n:6===n&&(r=n))),r}function J(){f.safeGetCookieMgr(null).setEnabled(!1)}function X(e,n,t){f.areCookiesSupported(null)&&f.safeGetCookieMgr(null).set(e,n,86400*t,null,"/")}function Q(e){f.areCookiesSupported(null)&&f.safeGetCookieMgr(null).del(e)}function Z(e){return f.areCookiesSupported(null)?Y(f.safeGetCookieMgr(null),e):g}function Y(e,n,t){var r;return void 0===t&&(t=!0),(r=e&&(r=e.get(n),t)&&r&&decodeURIComponent?decodeURIComponent(r):r)||g}function $(e){void 0===e&&(e="D");var n=f.newGuid();return"B"===e?n="{"+n+"}":"P"===e?n="("+n+")":"N"===e&&(n=n.replace(/-/g,g)),n}function p(e,n,t,r,i){var o={},a=!1,s=0,l=arguments.length,u=Object[c.strShimPrototype],d=arguments;for("[object Boolean]"===u.toString.call(d[0])&&(a=d[0],s++);s<l;s++)f.objForEachKey(d[s],function(t,e){a&&e&&f.isObject(e)?f.isArray(e)?(o[t]=o[t]||[],f.arrForEach(e,function(e,n){e&&f.isObject(e)?o[t][n]=p(!0,o[t][n],e):o[t][n]=e})):o[t]=p(!0,o[t],e):o[t]=e});return o}var h=f.perfNow;function y(e){return 0===e||0<e&&e<=13||32===e}function S(e){return 0<e.length}function ee(e,n){e.timings=e.timings||{},e.timings.processTelemetryStart=e.timings.processTelemetryStart||{},e.timings.processTelemetryStart[n]=h()}function b(e){var n,t=0;return null!==e&&e!==undefined&&("string"==(n=typeof e)?t=1:"number"==n?t=2:"boolean"==n?t=3:n===c.strShimObject&&(t=4,f.isArray(e)?(t=4096,0<e.length&&(t|=b(e[0]))):c.ObjHasOwnProperty.call(e,"value")&&(t=8192|b(e.value)))),t}var ne={Version:t,FullVersionString:s,strUndefined:f.strUndefined,strObject:f.strObject,Undefined:f.strUndefined,arrForEach:f.arrForEach,arrIndexOf:f.arrIndexOf,arrMap:f.arrMap,arrReduce:f.arrReduce,objKeys:f.objKeys,toISOString:f.toISOString,isReactNative:f.isReactNative,isString:f.isString,isNumber:f.isNumber,isBoolean:f.isBoolean,isFunction:f.isFunction,isArray:f.isArray,isObject:f.isObject,strTrim:f.strTrim,isDocumentObjectAvailable:l,isWindowObjectAvailable:u,isValueAssigned:m,getTenantId:W,isBeaconsSupported:f.isBeaconsSupported,isUint8ArrayAvailable:z,isLatency:v,sanitizeProperty:K,getISOString:f.toISOString,useXDomainRequest:f.useXDomainRequest,getCommonSchemaMetaData:q,cookieAvailable:f.areCookiesSupported,disallowsSameSiteNone:f.uaDisallowsSameSiteNone,setCookie:X,deleteCookie:Q,getCookie:Z,createGuid:$,extend:p,getTime:h,isValueKind:y,isArrayValid:S,objDefineAccessors:f.objDefineAccessors,addPageUnloadEventListener:f.addPageUnloadEventListener,setProcessTelemetryTimings:ee,addEventHandler:f.addEventHandler,getFieldValueType:b,strEndsWith:f.strEndsWith,objForEachKey:f.objForEachKey},E={_canUseCookies:undefined,isTypeof:f.isTypeof,isUndefined:f.isUndefined,isNullOrUndefined:f.isNullOrUndefined,hasOwnProperty:f.hasOwnProperty,isFunction:f.isFunction,isObject:f.isObject,isDate:f.isDate,isArray:f.isArray,isError:f.isError,isString:f.isString,isNumber:f.isNumber,isBoolean:f.isBoolean,toISOString:f.toISOString,arrForEach:f.arrForEach,arrIndexOf:f.arrIndexOf,arrMap:f.arrMap,arrReduce:f.arrReduce,strTrim:f.strTrim,objCreate:c.objCreateFn,objKeys:f.objKeys,objDefineAccessors:f.objDefineAccessors,addEventHandler:f.addEventHandler,dateNow:f.dateNow,isIE:f.isIE,disableCookies:J,newGuid:f.newGuid,perfNow:f.perfNow,newId:f.newId,randomValue:f.randomValue,random32:f.random32,mwcRandomSeed:f.mwcRandomSeed,mwcRandom32:f.mwcRandom32,generateW3CId:f.generateW3CId},te=f.AppInsightsCore;c.__extendsFn(C,te),C.__ieDyn=1;var P=C;function C(){var e=te.call(this)||this;return e.pluginVersionStringArr=[],o(C,e,function(l,u){l.logger&&l.logger.queue||(l.logger=new f.DiagnosticLogger({loggingLevelConsole:1})),l.initialize=function(i,o,a,s){f.doPerf(l,function(){return"AppInsightsCore.initialize"},function(){var n=l.pluginVersionStringArr;i&&(i.endpointUrl||(i.endpointUrl=d),!(e=i.propertyStorageOverride)||e.getProperty&&e.setProperty||f.throwError("Invalid property storage override passed."),i.channels)&&f.arrForEach(i.channels,function(e){e&&f.arrForEach(e,function(e){e.identifier&&e.version&&(e=e.identifier+"="+e.version,n.push(e))})}),l.getWParam=function(){return"undefined"!=typeof document||i.enableWParam?0:-1},o&&f.arrForEach(o,function(e){e&&e.identifier&&e.version&&(e=e.identifier+"="+e.version,n.push(e))}),l.pluginVersionString=n.join(";"),l.pluginVersionStringArr=n;try{u.initialize(i,o,a,s),l.pollInternalLogs("InternalLog")}catch(r){var e=l.logger,t=f.dumpObj(r);~t.indexOf("channels")&&(t+="\n - Channels must be provided through config.channels only!"),f._throwInternal(e,1,514,"SDK Initialization Failed - no telemetry will be sent: "+t)}},function(){return{config:i,extensions:o,logger:a,notificationManager:s}})},l.track=function(t){f.doPerf(l,function(){return"AppInsightsCore.track"},function(){var e,n=t;n&&(n.timings=n.timings||{},n.timings.trackStart=h(),v(n.latency)||(n.latency=1),(e=n.ext=n.ext||{}).sdk=e.sdk||{},e.sdk.ver=s,(e=n.baseData=n.baseData||{})[i]=e[i]||{},(e=e[i])[r]=e[r]||l.pluginVersionString||g),u.track(n)},function(){return{item:t}},!t.sync)}}),e}I=f.BaseCore,c.__extendsFn(O,I),O.__ieDyn=1;var I,w=O;function O(){var e=I.call(this)||this;return o(O,e,function(o,a){o.initialize=function(e,n,t,r){e&&!e.endpointUrl&&(e.endpointUrl=d),o.getWParam=function(){return l||e.enableWParam?0:-1};try{a.initialize(e,n,t,r)}catch(i){f._throwInternal(o.logger,1,514,"Initialization Failed: "+f.dumpObj(i)+"\n - Note: Channels must be provided through config.channels only")}},o.track=function(e){var n;e&&((n=e.ext=e.ext||{}).sdk=n.sdk||{},n.sdk.ver=s),a.track(e)}}),e}var N=f.isFunction,T=(A.resolve=function(r){return r instanceof A?r:r&&N(r.then)?new A(function(e,n){try{r.then(e,n)}catch(t){n(t)}}):new A(function(e){e(r)})},A.reject=function(t){return new A(function(e,n){n(t)})},A.all=function(s){if(s&&s.length)return new A(function(r,e){try{for(var i=[],o=0,n=0;n<s.length;n++){var t=s[n];t&&N(t.then)?(o++,t.then(function(n,t){return function(e){n[t]=e,0==--o&&r(i)}}(i,n),e)):i[n]=t}0===o&&setTimeout(function(){r(i)},0)}catch(a){e(a)}})},A.race=function(i){return new A(function(n,t){if(i&&i.length)try{for(var r=0;r<i.length;r++)!function(){var e=i[r];e&&N(e.then)?e.then(n,t):setTimeout(function(){n(e)},0)}()}catch(e){t(e)}})},A);function A(e){var a=0,s=null,n=[];function i(t,r,i,o){n.push(function(){var e;try{(e=1===a?N(t)?t(s):s:N(r)?r(s):s)instanceof A?e.then(i,o):(2!==a||N(r)?i:o)(e)}catch(n){o(n)}}),0!==a&&l()}function l(){var r;0<n.length&&(r=n.slice(),n=[],setTimeout(function(){for(var e=0,n=r.length;e<n;++e)try{r[e]()}catch(t){}},0))}function t(e){0===a&&(s=e,a=2,l())}if(o(A,this,function(n){n.then=function(t,r){return new A(function(e,n){i(t,r,e,n)})},n["catch"]=function(e){return n.then(null,e)}}),!N(e))throw new TypeError("ESPromise: resolvedFunc argument is not a Function");try{e(function(e){0===a&&(s=e,a=1,l())},t)}catch(r){t(r)}}var re=0,j=[],F=[],U=[];function k(){return(new Date).getTime()}_.incomplete=function(){return j},_.waitingToStart=function(){return F};var D=_;function _(e,n){var p=0,h=(e||"<unnamed>")+"."+re;function y(e){var n=f.getGlobal();n&&n.QUnit&&console&&console.log("ESPromiseScheduler["+h+"] "+e)}function S(e){f._warnToConsole(n,"ESPromiseScheduler["+h+"] "+e)}re++,o(_,this,function(e){var g=null,m=0;function v(e,n){for(var t=0;t<e.length;t++)if(e[t].id===n)return e.splice(t,1)[0];return null}e.scheduleEvent=function(t,e,s){var i,o,n,a=h+"."+m,e=(m++,e&&(a+="-("+e+")"),a+"{"+p+"}"),r=(p++,{evt:null,tm:k(),id:e,isRunning:!1,isAborted:!1});return r.evt=g?(i=r,o=g,n=new T(function(n,t){var e=k()-o.tm,r=o.id;y("["+a+"] is waiting for ["+r+":"+e+" ms] to complete before starting -- ["+F.length+"] waiting and ["+j.length+"] running"),i.abort=function(e){i.abort=null,v(F,a),i.isAborted=!0,t(Error(e))},o.evt.then(function(e){v(F,a),f(i).then(n,t)},function(e){v(F,a),f(i).then(n,t)})}),F.push(i),n):f(r),(g=r).evt._schId=e,r.evt;function l(e){for(var n=k(),t=n-6e5,r=e.length,i=0;i<r;){var o,a=e[i];a&&a.tm<t?(o=null,a.abort?(o="Aborting ["+a.id+"] due to Excessive runtime ("+(n-a.tm)+" ms)",a.abort(o)):o="Removing ["+a.id+"] due to Excessive runtime ("+(n-a.tm)+" ms)",S(o),e.splice(i,1),r--):i++}}function u(e,n){var t,r=!1,i=v(j,e);i||(i=v(U,e),r=!0),i?(i.to&&(clearTimeout(i.to),i.to=null),t=k()-i.tm,n?r?S("Timed out event ["+e+"] finally complete -- "+t+" ms"):y("Promise ["+e+"] Complete -- "+t+" ms"):(U.push(i),S("Event ["+e+"] Timed out and removed -- "+t+" ms"))):y("Failed to remove ["+e+"] from running queue"),g&&g.id===e&&(g=null),l(j),l(F),l(U)}function d(n,t){return function(e){return u(n,!0),t&&t(e),e}}function c(r,o){var a=r.id;return new T(function(n,t){y("Event ["+a+"] Starting -- waited for "+(r.wTm||"--")+" ms"),r.isRunning=!0,r.abort=function(e){r.abort=null,r.isAborted=!0,u(a,!1),t(Error(e))};var e=o(a);e instanceof T?(s&&(r.to=setTimeout(function(){u(a,!1),t(Error("Timed out after ["+s+"] ms"))},s)),function i(n,e,t,r){e.then(function(e){return e instanceof T?(y("Event ["+n+"] returned a promise -- waiting"),i(n,e,t,r),e):d(n,t)(e)},d(n,r))}(a,e,function(e){y("Event ["+a+"] Resolving after "+(k()-r.tm)+" ms"),n(e)},t)):(y("Promise ["+a+"] Auto completed as the start action did not return a promise"),n())})}function f(e){var n=k();return e.wTm=n-e.tm,e.tm=n,e.isAborted?T.reject(Error("["+a+"] was aborted")):(j.push(e),c(e,t))}}})}oe.getFieldType=b;var ie=oe;function oe(e){var u=this,o={},a=[],s=[];function l(e,n){var t=o[e];if(!(i=t?t[n]:i)&&null!==i){if(f.isString(e)&&f.isString(n))if(0<s.length){for(var r=0;r<s.length;r++)if(s[r].handleField(e,n)){i={canHandle:!0,fieldHandler:s[r]};break}}else 0===a.length&&(i={canHandle:!0});if(!i&&null!==i)for(var i=null,r=0;r<a.length;r++)if(a[r].handleField(e,n)){i={canHandle:!0,handler:a[r],fieldHandler:null};break}(t=t||(o[e]={}))[n]=i}return i}function d(e,n,t,r,i,o){if(e.handler)return e.handler.property(n,t,i,o);if(!f.isNullOrUndefined(i.kind)){if(4096==(4096&r)||!y(i.kind))return null;i.value=i.value.toString()}return function l(i,o,a,e,n){var t,s,r;return n&&i&&(t=i.getSanitizer(o,a,e,n.kind,n.propertyType))&&(4===e?(s={},r=n.value,f.objForEachKey(r,function(e,n){var t,r=o+"."+a;m(n)&&(t=c(0,0,n),t=l(i,r,e,b(n),t))&&(s[e]=t.value)}),n.value=s):n=t.call(u,{path:o,name:a,type:e,prop:n,sanitizer:u})),n}(e.fieldHandler,n,t,r,i)}function c(e,n,t){return m(t)?{value:t}:null}e&&s.push(e),u.addSanitizer=function(e){e&&(a.push(e),o={})},u.addFieldSanitizer=function(e){e&&(s.push(e),o={})},u.handleField=function(e,n){e=l(e,n);return!!e&&e.canHandle},u.value=function(e,n,t,r){var i=l(e,n);if(i&&i.canHandle){if(!i||!i.canHandle)return null;if(i.handler)return i.handler.value(e,n,t,r);if(!f.isString(n)||f.isNullOrUndefined(t)||t===g)return null;var o=null,a=b(t);if(8192==(8192&a)){var s=-8193&a;if(!m((o=t).value)||1!=s&&2!=s&&3!=s&&4096!=(4096&s))return null}else 1===a||2===a||3===a||4096==(4096&a)?o=c(0,0,t):4===a&&(o=c(0,0,r?JSON.stringify(t):t));if(o)return d(i,e,n,a,o,r)}return null},u.property=function(e,n,t,r){var i,o=l(e,n);return o&&o.canHandle&&f.isString(n)&&!f.isNullOrUndefined(t)&&m(t.value)&&0!==(i=b(t.value))?d(o,e,n,i,t,r):null}}e.BaseTelemetryPlugin=f.BaseTelemetryPlugin,e.DiagnosticLogger=f.DiagnosticLogger,e.EventHelper=f.EventHelper,e.EventsDiscardedReason=f.EventsDiscardedReason,e.InternalAppInsightsCore=f.AppInsightsCore,e.InternalBaseCore=f.BaseCore,e.LoggingSeverity=f.LoggingSeverity,e.MinChannelPriorty=f.MinChannelPriorty,e.NotificationManager=f.NotificationManager,e.PerfEvent=f.PerfEvent,e.PerfManager=f.PerfManager,e.ProcessTelemetryContext=f.ProcessTelemetryContext,e.Undefined=f.strUndefined,e._InternalLogMessage=f._InternalLogMessage,e._InternalMessageId=f._InternalMessageId,e.__getRegisteredEvents=f.__getRegisteredEvents,e._logInternalMessage=f._logInternalMessage,e._throwInternal=f._throwInternal,e._warnToConsole=f._warnToConsole,e.addEventHandler=f.addEventHandler,e.addEventListeners=f.addEventListeners,e.addPageHideEventListener=f.addPageHideEventListener,e.addPageShowEventListener=f.addPageShowEventListener,e.addPageUnloadEventListener=f.addPageUnloadEventListener,e.areCookiesSupported=f.areCookiesSupported,e.arrForEach=f.arrForEach,e.arrIndexOf=f.arrIndexOf,e.arrMap=f.arrMap,e.arrReduce=f.arrReduce,e.attachEvent=f.attachEvent,e.cookieAvailable=f.areCookiesSupported,e.createCookieMgr=f.createCookieMgr,e.createEnumStyle=f.createEnumStyle,e.createProcessTelemetryContext=f.createProcessTelemetryContext,e.createTraceParent=f.createTraceParent,e.createUniqueNamespace=f.createUniqueNamespace,e.createUnloadHandlerContainer=f.createUnloadHandlerContainer,e.dateNow=f.dateNow,e.detachEvent=f.detachEvent,e.disallowsSameSiteNone=f.uaDisallowsSameSiteNone,e.doPerf=f.doPerf,e.dumpObj=f.dumpObj,e.eventOff=f.eventOff,e.eventOn=f.eventOn,e.findW3cTraceParent=f.findW3cTraceParent,e.formatTraceParent=f.formatTraceParent,e.generateW3CId=f.generateW3CId,e.getConsole=f.getConsole,e.getCrypto=f.getCrypto,e.getDocument=f.getDocument,e.getExceptionName=f.getExceptionName,e.getGlobal=f.getGlobal,e.getGlobalInst=f.getGlobalInst,e.getHistory=f.getHistory,e.getIEVersion=f.getIEVersion,e.getISOString=f.toISOString,e.getJSON=f.getJSON,e.getLocation=f.getLocation,e.getMsCrypto=f.getMsCrypto,e.getNavigator=f.getNavigator,e.getPerformance=f.getPerformance,e.getSetValue=f.getSetValue,e.getWindow=f.getWindow,e.hasDocument=f.hasDocument,e.hasHistory=f.hasHistory,e.hasJSON=f.hasJSON,e.hasNavigator=f.hasNavigator,e.hasOwnProperty=f.hasOwnProperty,e.hasWindow=f.hasWindow,e.isArray=f.isArray,e.isBeaconsSupported=f.isBeaconsSupported,e.isBoolean=f.isBoolean,e.isDate=f.isDate,e.isError=f.isError,e.isFetchSupported=f.isFetchSupported,e.isFunction=f.isFunction,e.isIE=f.isIE,e.isNotTruthy=f.isNotTruthy,e.isNullOrUndefined=f.isNullOrUndefined,e.isNumber=f.isNumber,e.isObject=f.isObject,e.isReactNative=f.isReactNative,e.isSampledFlag=f.isSampledFlag,e.isString=f.isString,e.isTruthy=f.isTruthy,e.isTypeof=f.isTypeof,e.isUndefined=f.isUndefined,e.isValidSpanId=f.isValidSpanId,e.isValidTraceId=f.isValidTraceId,e.isValidTraceParent=f.isValidTraceParent,e.isXhrSupported=f.isXhrSupported,e.mergeEvtNamespace=f.mergeEvtNamespace,e.newGuid=f.newGuid,e.newId=f.newId,e.normalizeJsName=f.normalizeJsName,e.objCreate=f.objCreate,e.objDefineAccessors=f.objDefineAccessors,e.objForEachKey=f.objForEachKey,e.objFreeze=f.objFreeze,e.objKeys=f.objKeys,e.objSeal=f.objSeal,e.optimizeObject=f.optimizeObject,e.parseTraceParent=f.parseTraceParent,e.perfNow=f.perfNow,e.proxyAssign=f.proxyAssign,e.proxyFunctionAs=f.proxyFunctionAs,e.proxyFunctions=f.proxyFunctions,e.random32=f.random32,e.randomValue=f.randomValue,e.removeEventHandler=f.removeEventHandler,e.removeEventListeners=f.removeEventListeners,e.removePageHideEventListener=f.removePageHideEventListener,e.removePageShowEventListener=f.removePageShowEventListener,e.removePageUnloadEventListener=f.removePageUnloadEventListener,e.safeGetCookieMgr=f.safeGetCookieMgr,e.safeGetLogger=f.safeGetLogger,e.setEnableEnvMocks=f.setEnableEnvMocks,e.setValue=f.setValue,e.strContains=f.strContains,e.strEndsWith=f.strEndsWith,e.strFunction=f.strFunction,e.strObject=f.strObject,e.strPrototype=f.strPrototype,e.strStartsWith=f.strStartsWith,e.strTrim=f.strTrim,e.strUndefined=f.strUndefined,e.throwError=f.throwError,e.toISOString=f.toISOString,e.useXDomainRequest=f.useXDomainRequest,e.AppInsightsCore=P,e.BaseCore=w,e.CoreUtils=E,e.ESPromise=T,e.ESPromiseScheduler=D,e.EventLatency=V,e.EventPersistence=M,e.EventPropertyType=x,e.FullVersionString=s,e.TraceLevel=R,e.Utils=ne,e.ValueKind=n,e.ValueSanitizer=ie,e.Version=t,e._ExtendedInternalMessageId=H,e.createGuid=$,e.deleteCookie=Q,e.disableCookies=J,e.extend=p,e.getCommonSchemaMetaData=q,e.getCookie=Z,e.getCookieValue=Y,e.getFieldValueType=b,e.getTenantId=W,e.getTime=h,e.isArrayValid=S,e.isChromium=function(){return!!f.getGlobalInst("chrome")},e.isDocumentObjectAvailable=l,e.isLatency=v,e.isUint8ArrayAvailable=z,e.isValueAssigned=m,e.isValueKind=y,e.isWindowObjectAvailable=u,e.openXhr=function(e,n,t,r,i,o){function a(e,n,t){try{e[n]=t}catch(r){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var s=new XMLHttpRequest;return r&&a(s,"Microsoft_ApplicationInsights_BypassAjaxInstrumentation",r),t&&a(s,B,t),s.open(e,n,!i),t&&a(s,B,t),!i&&o&&a(s,"timeout",o),s},e.sanitizeProperty=K,e.setCookie=X,e.setProcessTelemetryTimings=ee;P=e,w="__esModule",E={value:!0},D=Object.defineProperty;if(D)try{return void D(P,w,E)}catch(ae){}typeof E.value!==undefined&&(P[w]=E.value)};"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("@microsoft/applicationinsights-shims"),require("@microsoft/applicationinsights-core-js"),require("@microsoft/dynamicproto-js")):"function"==typeof define&&define.amd?define(["exports","@microsoft/applicationinsights-shims","@microsoft/applicationinsights-core-js","@microsoft/dynamicproto-js"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).oneDS=e.oneDS||{},e.applicationinsightsShims,e.applicationinsightsCoreJs,e.dynamicProto);
