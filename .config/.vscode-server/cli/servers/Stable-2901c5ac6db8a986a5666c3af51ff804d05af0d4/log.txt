*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[15:05:35] 




[15:05:35] Extension host agent started.
[15:05:35] [<unknown>][1171114c][ManagementConnection] New connection established.
[15:05:35] [<unknown>][ecf1f7b5][ExtensionHostConnection] New connection established.
[15:05:35] [<unknown>][ecf1f7b5][ExtensionHostConnection] <3800> Launched Extension Host Process.
[15:05:35] ComputeTargetPlatform: linux-x64
[15:05:37] ComputeTargetPlatform: linux-x64
[15:05:38] Getting Manifest... augment.vscode-augment
[15:05:38] Getting Manifest... github.copilot
[15:05:38] Getting Manifest... github.copilot-chat
[15:05:38] Installing extension: github.copilot {
  productVersion: { version: '1.101.2', date: '2025-06-24T20:27:15.391Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[15:05:38] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.101.2', date: '2025-06-24T20:27:15.391Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[15:05:38] Installing extension: github.copilot-chat {
  productVersion: { version: '1.101.2', date: '2025-06-24T20:27:15.391Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[15:05:40] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1825ms.
[15:05:40] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1758ms.
[15:05:40] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1843ms.
[15:05:41] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3: github.copilot-chat
[15:05:41] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3
[15:05:41] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:05:41] Marked extension as removed github.copilot-chat-0.28.1
[15:05:42] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.338.0: github.copilot
[15:05:42] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.338.0
[15:05:42] Marked extension as removed github.copilot-1.336.0
[15:05:42] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:05:42] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1: augment.vscode-augment
[15:05:42] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1
[15:05:42] Marked extension as removed augment.vscode-augment-0.482.1
[15:05:42] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:06:01] [<unknown>][1171114c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[15:06:01] [<unknown>][ecf1f7b5][ExtensionHostConnection] <3800> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[15:06:01] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[15:06:01] Last EH closed, waiting before shutting down
[15:06:03] [<unknown>][30df160a][ManagementConnection] New connection established.
[15:06:03] [<unknown>][d25cc1ab][ExtensionHostConnection] New connection established.
[15:06:03] [<unknown>][d25cc1ab][ExtensionHostConnection] <4313> Launched Extension Host Process.
New EH opened, aborting shutdown
[15:11:01] New EH opened, aborting shutdown
