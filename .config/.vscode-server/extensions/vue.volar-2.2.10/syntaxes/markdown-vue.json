{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "fileTypes": [], "injectionSelector": "L:text.html.markdown", "patterns": [{"include": "#vue-code-block"}], "repository": {"vue-code-block": {"begin": "(^|\\G)(\\s*)(`{3,}|~{3,})\\s*(?i:(vue)((\\s+|:|,|\\{|\\?)[^`~]*)?$)", "name": "markup.fenced_code.block.markdown", "end": "(^|\\G)(\\2|\\s{0,3})(\\3)\\s*$", "beginCaptures": {"3": {"name": "punctuation.definition.markdown"}, "4": {"name": "fenced_code.block.language.markdown"}, "5": {"name": "fenced_code.block.language.attributes.markdown", "patterns": []}}, "endCaptures": {"3": {"name": "punctuation.definition.markdown"}}, "patterns": [{"include": "source.vue"}]}}, "scopeName": "markdown.vue.codeblock"}