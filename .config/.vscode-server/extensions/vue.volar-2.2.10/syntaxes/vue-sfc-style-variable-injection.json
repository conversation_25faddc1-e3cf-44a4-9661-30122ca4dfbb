{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "fileTypes": [], "injectionSelector": "L:source.css -comment, L:source.postcss -comment, L:source.sass -comment, L:source.stylus -comment", "patterns": [{"include": "#vue-sfc-style-variable-injection"}], "repository": {"vue-sfc-style-variable-injection": {"begin": "\\b(v-bind)\\s*\\(", "name": "vue.sfc.style.variable.injection.v-bind", "end": "\\)", "beginCaptures": {"1": {"name": "entity.name.function"}}, "patterns": [{"begin": "('|\")", "beginCaptures": {"1": {"name": "punctuation.definition.tag.begin.html"}}, "end": "(\\1)", "endCaptures": {"1": {"name": "punctuation.definition.tag.end.html"}}, "name": "source.ts.embedded.html.vue", "patterns": [{"include": "source.js"}]}, {"include": "source.js"}]}}, "scopeName": "vue.sfc.style.variable.injection"}