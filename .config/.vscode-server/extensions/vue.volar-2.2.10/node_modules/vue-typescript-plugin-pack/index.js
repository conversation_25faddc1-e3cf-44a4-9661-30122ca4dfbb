"use strict";var Yr=Object.create;var Ne=Object.defineProperty;var Qr=Object.getOwnPropertyDescriptor;var Xr=Object.getOwnPropertyNames;var Zr=Object.getPrototypeOf,ei=Object.prototype.hasOwnProperty;var J=(e,t)=>()=>(e&&(t=e(e=0)),t);var q=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),tt=(e,t)=>{for(var n in t)Ne(e,n,{get:t[n],enumerable:!0})},Yt=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Xr(t))!ei.call(e,i)&&i!==n&&Ne(e,i,{get:()=>t[i],enumerable:!(r=Qr(t,i))||r.enumerable});return e};var te=(e,t,n)=>(n=e!=null?Yr(Zr(e)):{},Yt(t||!e||!e.__esModule?Ne(n,"default",{value:e,enumerable:!0}):n,e)),nt=e=>Yt(Ne({},"__esModule",{value:!0}),e);var Qt=q(rt=>{"use strict";Object.defineProperty(rt,"__esModule",{value:!0});rt.binarySearch=ti;function ti(e,t){let n=0,r=e.length-1,i;for(;n<=r;){let c=Math.floor((n+r)/2),s=e[c];if(s<t)n=c+1;else if(s>t)r=c-1;else{n=c,r=c,i=c;break}}let a=Math.max(Math.min(n,r,e.length-1),0),l=Math.min(Math.max(n,r,0),e.length-1);return{low:a,high:l,match:i}}});var st=q(it=>{"use strict";Object.defineProperty(it,"__esModule",{value:!0});it.translateOffset=ni;var Xt=!1;function ni(e,t,n,r,i=r){if(!t.every((s,o)=>o===0||t[o-1]<=s)){for(let s=0;s<t.length;s++){let o=t[s],f=r[s];if(e>=o&&e<=o+f){let u=i[s],d=n[s],p=Math.min(e-o,u);return d+p}}Xt||(Xt=!0,console.warn("fromOffsets should be sorted in ascending order"))}let l=0,c=t.length-1;for(;l<=c;){let s=Math.floor((l+c)/2),o=t[s],f=r[s];if(e>=o&&e<=o+f){let u=i[s],d=n[s],p=Math.min(e-o,u);return d+p}else e<o?c=s-1:l=s+1}}});var en=q(De=>{"use strict";Object.defineProperty(De,"__esModule",{value:!0});De.SourceMap=void 0;var ot=Qt(),Zt=st(),at=class{constructor(t){this.mappings=t}toSourceRange(t,n,r,i){return this.findMatchingStartEnd(t,n,r,"generatedOffsets",i)}toGeneratedRange(t,n,r,i){return this.findMatchingStartEnd(t,n,r,"sourceOffsets",i)}toSourceLocation(t,n){return this.findMatchingOffsets(t,"generatedOffsets",n)}toGeneratedLocation(t,n){return this.findMatchingOffsets(t,"sourceOffsets",n)}*findMatchingOffsets(t,n,r){let i=this.getMemoBasedOnRange(n);if(i.offsets.length===0)return;let{low:a,high:l}=(0,ot.binarySearch)(i.offsets,t),c=new Set,s=n=="sourceOffsets"?"generatedOffsets":"sourceOffsets";for(let o=a;o<=l;o++)for(let f of i.mappings[o]){if(c.has(f)||(c.add(f),r&&!r(f.data)))continue;let u=(0,Zt.translateOffset)(t,f[n],f[s],ve(f,n),ve(f,s));u!==void 0&&(yield[u,f])}}*findMatchingStartEnd(t,n,r,i,a){let l=i=="sourceOffsets"?"generatedOffsets":"sourceOffsets",c=[],s=!1;for(let[o,f]of this.findMatchingOffsets(t,i)){if(a&&!a(f.data))continue;c.push([o,f]);let u=(0,Zt.translateOffset)(n,f[i],f[l],ve(f,i),ve(f,l));u!==void 0&&(s=!0,yield[o,u,f,f])}if(!s&&r){for(let[o,f]of c)for(let[u,d]of this.findMatchingOffsets(n,i))if(!(a&&!a(d.data)||u<o)){yield[o,u,f,d];break}}}getMemoBasedOnRange(t){return t==="sourceOffsets"?this.sourceCodeOffsetsMemo??=this.createMemo("sourceOffsets"):this.generatedCodeOffsetsMemo??=this.createMemo("generatedOffsets")}createMemo(t){let n=new Set;for(let a of this.mappings)for(let l=0;l<a[t].length;l++)n.add(a[t][l]),n.add(a[t][l]+ve(a,t)[l]);let r=[...n].sort((a,l)=>a-l),i=r.map(()=>new Set);for(let a of this.mappings)for(let l=0;l<a[t].length;l++){let c=(0,ot.binarySearch)(r,a[t][l]).match,s=(0,ot.binarySearch)(r,a[t][l]+ve(a,t)[l]).match;for(let o=c;o<=s;o++)i[o].add(a)}return{offsets:r,mappings:i}}};De.SourceMap=at;function ve(e,t){return t=="sourceOffsets"?e.lengths:e.generatedLengths??e.lengths}});var Me=q(fe=>{"use strict";var ri=fe&&fe.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),tn=fe&&fe.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&ri(t,e,n)};Object.defineProperty(fe,"__esModule",{value:!0});tn(en(),fe);tn(st(),fe)});var nn=q(I=>{"use strict";Object.defineProperty(I,"__esModule",{value:!0});I.isHoverEnabled=ii;I.isInlayHintsEnabled=si;I.isCodeLensEnabled=oi;I.isMonikerEnabled=ai;I.isInlineValueEnabled=ci;I.isSemanticTokensEnabled=li;I.isCallHierarchyEnabled=fi;I.isTypeHierarchyEnabled=ui;I.isRenameEnabled=di;I.isDefinitionEnabled=pi;I.isTypeDefinitionEnabled=gi;I.isReferencesEnabled=mi;I.isImplementationEnabled=hi;I.isHighlightEnabled=Si;I.isSymbolsEnabled=yi;I.isFoldingRangesEnabled=vi;I.isSelectionRangesEnabled=bi;I.isLinkedEditingEnabled=Ci;I.isColorEnabled=xi;I.isDocumentLinkEnabled=Ei;I.isDiagnosticsEnabled=Pi;I.isCodeActionsEnabled=Fi;I.isFormattingEnabled=Oi;I.isCompletionEnabled=Ai;I.isAutoInsertEnabled=Ti;I.isSignatureHelpEnabled=_i;I.shouldReportDiagnostics=wi;I.resolveRenameNewName=Ni;I.resolveRenameEditText=Di;function ii(e){return!!e.semantic}function si(e){return!!e.semantic}function oi(e){return!!e.semantic}function ai(e){return!!e.semantic}function ci(e){return!!e.semantic}function li(e){return typeof e.semantic=="object"?e.semantic.shouldHighlight?.()??!0:!!e.semantic}function fi(e){return!!e.navigation}function ui(e){return!!e.navigation}function di(e){return typeof e.navigation=="object"?e.navigation.shouldRename?.()??!0:!!e.navigation}function pi(e){return!!e.navigation}function gi(e){return!!e.navigation}function mi(e){return!!e.navigation}function hi(e){return!!e.navigation}function Si(e){return!!e.navigation}function yi(e){return!!e.structure}function vi(e){return!!e.structure}function bi(e){return!!e.structure}function Ci(e){return!!e.structure}function xi(e){return!!e.structure}function Ei(e){return!!e.structure}function Pi(e){return!!e.verification}function Fi(e){return!!e.verification}function Oi(e){return!!e.format}function Ai(e){return!!e.completion}function Ti(e){return!!e.completion}function _i(e){return!!e.completion}function wi(e,t,n){return typeof e.verification=="object"?e.verification.shouldReport?.(t,n)??!0:!!e.verification}function Ni(e,t){return typeof t.navigation=="object"?t.navigation.resolveRenameNewName?.(e)??e:e}function Di(e,t){return typeof t.navigation=="object"?t.navigation.resolveRenameEditText?.(e)??e:e}});var lt=q(Le=>{"use strict";Object.defineProperty(Le,"__esModule",{value:!0});Le.LinkedCodeMap=void 0;var Mi=Me(),ct=class extends Mi.SourceMap{*getLinkedOffsets(t){for(let n of this.toGeneratedLocation(t))yield n[0];for(let n of this.toSourceLocation(t))yield n[0]}};Le.LinkedCodeMap=ct});var sn=q(rn=>{"use strict";Object.defineProperty(rn,"__esModule",{value:!0})});var on=q(Re=>{"use strict";Object.defineProperty(Re,"__esModule",{value:!0});Re.FileMap=void 0;var ft=class extends Map{constructor(t){super(),this.caseSensitive=t,this.originalFileNames=new Map}keys(){return this.originalFileNames.values()}get(t){return super.get(this.normalizeId(t))}has(t){return super.has(this.normalizeId(t))}set(t,n){return this.originalFileNames.set(this.normalizeId(t),t),super.set(this.normalizeId(t),n)}delete(t){return this.originalFileNames.delete(this.normalizeId(t)),super.delete(this.normalizeId(t))}clear(){return this.originalFileNames.clear(),super.clear()}normalizeId(t){return this.caseSensitive?t:t.toLowerCase()}};Re.FileMap=ft});var be=q(K=>{"use strict";var Li=K&&K.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),Ie=K&&K.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&Li(t,e,n)};Object.defineProperty(K,"__esModule",{value:!0});K.defaultMapperFactory=K.SourceMap=void 0;K.createLanguage=qi;K.forEachEmbeddedCode=ke;var Ri=Me();Object.defineProperty(K,"SourceMap",{enumerable:!0,get:function(){return Ri.SourceMap}});Ie(nn(),K);Ie(lt(),K);Ie(sn(),K);Ie(on(),K);var ki=Me(),Ii=lt(),ji=e=>new ki.SourceMap(e);K.defaultMapperFactory=ji;function qi(e,t,n){let r=new WeakMap,i=new WeakMap,a=new WeakMap,l={mapperFactory:K.defaultMapperFactory,plugins:e,scripts:{fromVirtualCode(o){return r.get(o)},get(o,f=!0,u=!1){n(o,f,u);let d=t.get(o);return d?.isAssociationDirty&&this.set(o,d.snapshot,d.languageId),t.get(o)},set(o,f,u,d=e){if(!u){for(let g of e)if(u=g.getLanguageId?.(o),u)break}if(!u){console.warn(`languageId not found for ${o}`);return}let p=!1;for(let g of e)if(g.isAssociatedFileOnly?.(o,u)){p=!0;break}if(t.has(o)){let g=t.get(o);if(g.languageId!==u||g.associatedOnly!==p)return this.delete(o),this.set(o,f,u);if(p)g.snapshot=f;else if(g.isAssociationDirty||g.snapshot!==f){g.snapshot=f;let m=s(g);if(g.generated){let{updateVirtualCode:S,createVirtualCode:h}=g.generated.languagePlugin,y=S?S(o,g.generated.root,f,m):h?.(o,u,f,m);if(y){g.generated.root=y,g.generated.embeddedCodes.clear();for(let b of ke(g.generated.root))r.set(b,g),g.generated.embeddedCodes.set(b.id,b);return g}else{this.delete(o);return}}c(g)}else return g}else{let g={id:o,languageId:u,snapshot:f,associatedIds:new Set,targetIds:new Set,associatedOnly:p};if(t.set(o,g),p)return g;for(let m of d){let S=m.createVirtualCode?.(o,u,f,s(g));if(S){g.generated={root:S,languagePlugin:m,embeddedCodes:new Map};for(let h of ke(S))r.set(h,g),g.generated.embeddedCodes.set(h.id,h);break}}return g}},delete(o){let f=t.get(o);f&&(f.generated?.languagePlugin.disposeVirtualCode?.(o,f.generated.root),t.delete(o),c(f))}},maps:{get(o,f){let u=i.get(o.snapshot);if(u||i.set(o.snapshot,u=new WeakMap),!u.has(f.snapshot)){let d=o.associatedScriptMappings?.get(f.id)??o.mappings;u.set(f.snapshot,l.mapperFactory(d))}return u.get(f.snapshot)},*forEach(o){let f=r.get(o);if(yield[f,this.get(o,f)],o.associatedScriptMappings)for(let[u]of o.associatedScriptMappings){let d=t.get(u);d&&(yield[d,this.get(o,d)])}}},linkedCodeMaps:{get(o){let f=r.get(o),u=a.get(o.snapshot);return u?.[0]!==f.snapshot&&a.set(o.snapshot,u=[f.snapshot,o.linkedCodeMappings?new Ii.LinkedCodeMap(o.linkedCodeMappings):void 0]),u[1]}}};return l;function c(o){o.targetIds.forEach(f=>{let u=t.get(f);u&&(u.isAssociationDirty=!0)})}function s(o){for(let f of o.associatedIds)t.get(f)?.targetIds.delete(o.id);return o.associatedIds.clear(),o.isAssociationDirty=!1,{getAssociatedScript(f){n(f,!0,!0);let u=t.get(f);return u&&(u.targetIds.add(o.id),o.associatedIds.add(u.id)),u}}}}function*ke(e){if(yield e,e.embeddedCodes)for(let t of e.embeddedCodes)yield*ke(t)}});var je=q(ut=>{"use strict";Object.defineProperty(ut,"__esModule",{value:!0});ut.resolveFileLanguageId=Vi;function Vi(e){switch(e.split(".").pop()){case"js":return"javascript";case"cjs":return"javascript";case"mjs":return"javascript";case"ts":return"typescript";case"cts":return"typescript";case"mts":return"typescript";case"jsx":return"javascriptreact";case"tsx":return"typescriptreact";case"json":return"json"}}});var an=q(dt=>{"use strict";Object.defineProperty(dt,"__esModule",{value:!0});dt.dedupeDocumentSpans=zi;function zi(e){return Hi(e,t=>[t.fileName,t.textSpan.start,t.textSpan.length].join(":"))}function Hi(e,t){let n=new Map;for(let r of e.reverse())n.set(t(r),r);return[...n.values()]}});var qe=q(pt=>{"use strict";Object.defineProperty(pt,"__esModule",{value:!0});pt.getServiceScript=Ui;function Ui(e,t){let n=e.scripts.get(t);if(n?.targetIds.size)for(let r of n.targetIds){let i=e.scripts.get(r);if(i?.generated){let a=i.generated.languagePlugin.typescript?.getServiceScript(i.generated.root);if(a)return[a,i,n]}}if(n?.associatedOnly)return[void 0,n,n];if(n?.generated){let r=n.generated.languagePlugin.typescript?.getServiceScript(n.generated.root);if(r)return[r,n,n]}return[void 0,void 0,void 0]}});var gt=q(W=>{"use strict";Object.defineProperty(W,"__esModule",{value:!0});W.transformCallHierarchyItem=Gi;W.transformDiagnostic=ln;W.fillSourceFileText=fn;W.transformFileTextChanges=Bi;W.transformDocumentSpan=Ki;W.transformSpan=ue;W.transformTextChange=Wi;W.transformTextSpan=ze;W.toSourceOffset=Ji;W.toSourceRanges=un;W.toSourceOffsets=dn;W.toGeneratedRanges=Yi;W.toGeneratedOffset=Qi;W.toGeneratedOffsets=pn;W.getMappingOffset=ne;var $i=be(),Ve=qe(),Ce=new WeakMap,cn=new WeakSet;function Gi(e,t,n,r){let i=ue(e,t.file,t.span,n,r),a=ue(e,t.file,t.selectionSpan,n,r);return{...t,file:i?.fileName??t.file,span:i?.textSpan??{start:0,length:0},selectionSpan:a?.textSpan??{start:0,length:0}}}function ln(e,t,n,r){if(!Ce.has(t)){Ce.set(t,void 0);let{relatedInformation:i}=t;if(i&&(t.relatedInformation=i.map(a=>ln(e,a,n,r)).filter(a=>!!a)),t.file!==void 0&&t.start!==void 0&&t.length!==void 0){let[a]=(0,Ve.getServiceScript)(e,t.file.fileName);if(a){let[l,c]=ze(void 0,e,a,{start:t.start,length:t.length},!0,o=>(0,$i.shouldReportDiagnostics)(o,String(t.source),String(t.code)))??[],s=l?t.file.fileName===l?t.file:n?.getSourceFile(l):void 0;c&&s&&(r&&fn(e,t.file),Ce.set(t,{...t,file:s,start:c.start,length:c.length}))}else Ce.set(t,t)}else Ce.set(t,t)}return Ce.get(t)}function fn(e,t){if(cn.has(t))return;cn.add(t);let[n]=(0,Ve.getServiceScript)(e,t.fileName);if(n&&!n.preventLeadingOffset){let r=e.scripts.fromVirtualCode(n.code);t.text=r.snapshot.getText(0,r.snapshot.getLength())+t.text.substring(r.snapshot.getLength())}}function Bi(e,t,n,r){let i={},a=new Set;for(let c of t){let[s,o]=(0,Ve.getServiceScript)(e,c.fileName);if(o)c.textChanges.forEach(f=>{let{fileName:u,textSpan:d}=ue(e,c.fileName,f.span,n,r)??{};u&&d&&(i[u]??(i[u]=[])).push({...f,span:d})});else{let f=i[c.fileName]??(i[c.fileName]=[]);c.textChanges.forEach(u=>{f.push(u)}),c.isNewFile&&a.add(c.fileName)}}let l=[];for(let c in i)l.push({fileName:c,isNewFile:a.has(c),textChanges:i[c]});return l}function Ki(e,t,n,r,i){let a=ue(e,t.fileName,t.textSpan,n,r);if(!a&&i&&(a={fileName:t.fileName,textSpan:{start:0,length:0}}),!a)return;let l=ue(e,t.fileName,t.contextSpan,n,r),c=ue(e,t.originalFileName,t.originalTextSpan,n,r),s=ue(e,t.originalFileName,t.originalContextSpan,n,r);return{...t,fileName:a.fileName,textSpan:a.textSpan,contextSpan:l?.textSpan,originalFileName:c?.fileName,originalTextSpan:c?.textSpan,originalContextSpan:s?.textSpan}}function ue(e,t,n,r,i){if(!t||!n)return;let[a]=(0,Ve.getServiceScript)(e,t);if(a){let[l,c]=ze(void 0,e,a,n,r,i)??[];if(c&&l)return{fileName:l,textSpan:c}}else return{fileName:t,textSpan:n}}function Wi(e,t,n,r,i,a){let[l,c]=ze(e,t,n,r.span,i,a)??[];if(c&&l)return[l,{newText:r.newText,span:c}]}function ze(e,t,n,r,i,a){let l=r.start,c=r.start+r.length;for(let[s,o,f]of un(e,t,n,l,c,i,a))return[s,{start:o,length:f-o}]}function Ji(e,t,n,r,i){for(let a of dn(e,t,n,r,i))return a}function*un(e,t,n,r,i,a,l){if(e){let c=t.maps.get(n.code,e);for(let[s,o]of c.toSourceRange(r-ne(t,n),i-ne(t,n),a,l))yield[e.id,s,o]}else for(let[c,s]of t.maps.forEach(n.code))for(let[o,f]of s.toSourceRange(r-ne(t,n),i-ne(t,n),a,l))yield[c.id,o,f]}function*dn(e,t,n,r,i){if(e){let a=t.maps.get(n.code,e);for(let[l,c]of a.toSourceLocation(r-ne(t,n)))i(c.data)&&(yield[e.id,l])}else for(let[a,l]of t.maps.forEach(n.code))for(let[c,s]of l.toSourceLocation(r-ne(t,n)))i(s.data)&&(yield[a.id,c])}function*Yi(e,t,n,r,i,a){let l=e.maps.get(t.code,n);for(let[c,s]of l.toGeneratedRange(r,i,!0,a))yield[c+ne(e,t),s+ne(e,t)]}function Qi(e,t,n,r,i){for(let[a]of pn(e,t,n,r,i))return a}function*pn(e,t,n,r,i){let a=e.maps.get(t.code,n);for(let[l,c]of a.toGeneratedLocation(r))i(c.data)&&(yield[l+ne(e,t),c])}function ne(e,t){return t.preventLeadingOffset?0:e.scripts.fromVirtualCode(t.code).snapshot.getLength()}});var ht=q(mt=>{"use strict";Object.defineProperty(mt,"__esModule",{value:!0});mt.createProxyLanguageService=Xi;var w=be(),de=an(),N=gt(),V=qe(),j=/\\/g;function Xi(e){let t=new Map,n;return{initialize(r){n=(i,a)=>{switch(a){case"getNavigationTree":return Zi(r,i[a]);case"getOutliningSpans":return es(r,i[a]);case"getFormattingEditsForDocument":return ts(r,i[a]);case"getFormattingEditsForRange":return ns(r,i[a]);case"getFormattingEditsAfterKeystroke":return rs(r,i[a]);case"getEditsForFileRename":return is(r,i[a]);case"getLinkedEditingRangeAtPosition":return ss(r,i[a]);case"prepareCallHierarchy":return os(r,i[a]);case"provideCallHierarchyIncomingCalls":return as(r,i[a]);case"provideCallHierarchyOutgoingCalls":return cs(r,i[a]);case"organizeImports":return ls(r,i[a]);case"getQuickInfoAtPosition":return fs(r,i[a]);case"getSignatureHelpItems":return us(r,i[a]);case"getDocumentHighlights":return ds(r,i[a]);case"getApplicableRefactors":return ps(r,i[a]);case"getEditsForRefactor":return gs(r,i[a]);case"getCombinedCodeFix":return ms(r,i[a]);case"getRenameInfo":return hs(r,i[a]);case"getCodeFixesAtPosition":return Ss(r,i[a]);case"getEncodedSemanticClassifications":return ys(r,i[a]);case"getSyntacticDiagnostics":return vs(r,e,i[a]);case"getSemanticDiagnostics":return bs(r,e,i[a]);case"getSuggestionDiagnostics":return Cs(r,e,i[a]);case"getDefinitionAndBoundSpan":return xs(r,i[a]);case"findReferences":return Es(r,i[a]);case"getDefinitionAtPosition":return Ps(r,i[a]);case"getTypeDefinitionAtPosition":return Fs(r,i[a]);case"getImplementationAtPosition":return Os(r,i[a]);case"findRenameLocations":return As(r,i[a]);case"getReferencesAtPosition":return Ts(r,i[a]);case"getCompletionsAtPosition":return _s(r,i[a]);case"getCompletionEntryDetails":return ws(r,i[a]);case"provideInlayHints":return Ns(r,i[a]);case"getFileReferences":return Ds(r,i[a]);case"getNavigateToItems":return Ms(r,i[a])}}},proxy:new Proxy(e,{get(r,i,a){if(n){t.has(i)||t.set(i,n(r,i));let l=t.get(i);if(l)return l}return Reflect.get(r,i,a)},set(r,i,a,l){return Reflect.set(r,i,a,l)}})}}function Zi(e,t){return n=>{let r=n.replace(j,"/"),[i,a]=(0,V.getServiceScript)(e,r);if(i||a?.associatedOnly){let l=t(a.id);return l.childItems=void 0,l}else return t(r)}}function es(e,t){return n=>{let r=n.replace(j,"/"),[i,a]=(0,V.getServiceScript)(e,r);return i||a?.associatedOnly?[]:t(r)}}function ts(e,t){return(n,r)=>{let i=n.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);return l?.associatedOnly?[]:a?e.maps.get(a.code,l).mappings.some(f=>(0,w.isFormattingEnabled)(f.data))?t(l.id,r).map(f=>(0,N.transformTextChange)(c,e,a,f,!1,w.isFormattingEnabled)?.[1]).filter(f=>!!f):[]:t(i,r)}}function ns(e,t){return(n,r,i,a)=>{let l=n.replace(j,"/"),[c,s,o]=(0,V.getServiceScript)(e,l);if(s?.associatedOnly)return[];if(c){let f=(0,N.toGeneratedOffset)(e,c,o,r,w.isFormattingEnabled),u=(0,N.toGeneratedOffset)(e,c,o,i,w.isFormattingEnabled);return f!==void 0&&u!==void 0?t(s.id,f,u,a).map(p=>(0,N.transformTextChange)(o,e,c,p,!1,w.isFormattingEnabled)?.[1]).filter(p=>!!p):[]}else return t(l,r,i,a)}}function rs(e,t){return(n,r,i,a)=>{let l=n.replace(j,"/"),[c,s,o]=(0,V.getServiceScript)(e,l);if(s?.associatedOnly)return[];if(c){let f=(0,N.toGeneratedOffset)(e,c,o,r,w.isFormattingEnabled);return f!==void 0?t(s.id,f,i,a).map(d=>(0,N.transformTextChange)(o,e,c,d,!1,w.isFormattingEnabled)?.[1]).filter(d=>!!d):[]}else return t(l,r,i,a)}}function is(e,t){return(n,r,i,a)=>{let l=t(n,r,i,a);return(0,N.transformFileTextChanges)(e,l,!1,w.isRenameEnabled)}}function ss(e,t){return(n,r)=>{let i=n.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);if(!l?.associatedOnly)if(a){let s=(0,N.toGeneratedOffset)(e,a,c,r,w.isLinkedEditingEnabled);if(s!==void 0){let o=t(l.id,s);if(o)return{ranges:o.ranges.map(f=>(0,N.transformTextSpan)(c,e,a,f,!1,w.isLinkedEditingEnabled)?.[1]).filter(f=>!!f),wordPattern:o.wordPattern}}}else return t(i,r)}}function os(e,t){return(n,r)=>{let i=n.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);if(!l?.associatedOnly)if(a){let s=(0,N.toGeneratedOffset)(e,a,c,r,w.isCallHierarchyEnabled);if(s!==void 0){let o=t(l.id,s);if(Array.isArray(o))return o.map(f=>(0,N.transformCallHierarchyItem)(e,f,!0,w.isCallHierarchyEnabled));if(o)return(0,N.transformCallHierarchyItem)(e,o,!0,w.isCallHierarchyEnabled)}}else return t(i,r)}}function as(e,t){return(n,r)=>{let i=[],a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(c?.associatedOnly)return[];if(l){let o=(0,N.toGeneratedOffset)(e,l,s,r,w.isCallHierarchyEnabled);o!==void 0&&(i=t(c.id,o))}else i=t(a,r);return i.map(o=>{let f=(0,N.transformCallHierarchyItem)(e,o.from,!0,w.isCallHierarchyEnabled),u=o.fromSpans.map(d=>(0,N.transformSpan)(e,o.from.file,d,!0,w.isCallHierarchyEnabled)?.textSpan).filter(d=>!!d);return{from:f,fromSpans:u}})}}function cs(e,t){return(n,r)=>{let i=[],a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(c?.associatedOnly)return[];if(l){let o=(0,N.toGeneratedOffset)(e,l,s,r,w.isCallHierarchyEnabled);o!==void 0&&(i=t(c.id,o))}else i=t(a,r);return i.map(o=>{let f=(0,N.transformCallHierarchyItem)(e,o.to,!0,w.isCallHierarchyEnabled),u=o.fromSpans.map(d=>l?(0,N.transformTextSpan)(s,e,l,d,!0,w.isCallHierarchyEnabled)?.[1]:d).filter(d=>!!d);return{to:f,fromSpans:u}})}}function ls(e,t){return(n,r,i)=>{let a=t(n,r,i);return(0,N.transformFileTextChanges)(e,a,!1,w.isCodeActionsEnabled)}}function fs(e,t){return(n,r)=>{let i=n.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);if(!l?.associatedOnly)if(a){let s=[];for(let[o]of(0,N.toGeneratedOffsets)(e,a,c,r,w.isHoverEnabled)){let f=t(l.id,o);if(f){let u=(0,N.transformTextSpan)(c,e,a,f.textSpan,!0,w.isHoverEnabled)?.[1];u&&s.push({...f,textSpan:u})}}if(s.length===1)return s[0];if(s.length>=2){let o={...s[0]};o.displayParts=o.displayParts?.slice(),o.documentation=o.documentation?.slice(),o.tags=o.tags?.slice();let f=new Set([oe(s[0].displayParts)]),u=new Set([oe(s[0].documentation)]),d=new Set;for(let p of s[0].tags??[])d.add(p.name+"__volar__"+oe(p.text));for(let p=1;p<s.length;p++){let{displayParts:g,documentation:m,tags:S}=s[p];g?.length&&!f.has(oe(g))&&(f.add(oe(g)),o.displayParts??=[],o.displayParts.push({...g[0],text:`

`+g[0].text}),o.displayParts.push(...g.slice(1))),m?.length&&!u.has(oe(m))&&(u.add(oe(m)),o.documentation??=[],o.documentation.push({...m[0],text:`

`+m[0].text}),o.documentation.push(...m.slice(1)));for(let h of S??[])d.has(h.name+"__volar__"+oe(h.text))||(d.add(h.name+"__volar__"+oe(h.text)),o.tags??=[],o.tags.push(h))}return o}}else return t(i,r)}}function us(e,t){return(n,r,i)=>{let a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(!c?.associatedOnly)if(l){let o=(0,N.toGeneratedOffset)(e,l,s,r,w.isSignatureHelpEnabled);if(o!==void 0){let f=t(c.id,o,i);if(f){let u=(0,N.transformTextSpan)(s,e,l,f.applicableSpan,!0,w.isSignatureHelpEnabled)?.[1];if(u)return{...f,applicableSpan:u}}}}else return t(a,r,i)}}function ds(e,t){return(n,r,i)=>{let a=n.replace(j,"/");return pe(e,a,r,w.isHighlightEnabled,(s,o)=>t(s,o,i),function*(s){for(let o of s)for(let f of o.highlightSpans)yield[f.fileName??o.fileName,f.textSpan.start]}).flat().map(s=>({...s,highlightSpans:s.highlightSpans.map(o=>{let{textSpan:f}=(0,N.transformSpan)(e,o.fileName??s.fileName,o.textSpan,!1,w.isHighlightEnabled)??{};if(f)return{...o,contextSpan:(0,N.transformSpan)(e,o.fileName??s.fileName,o.contextSpan,!1,w.isHighlightEnabled)?.textSpan,textSpan:f}}).filter(o=>!!o)}))}}function ps(e,t){return(n,r,i,a,l,c)=>{let s=n.replace(j,"/"),[o,f,u]=(0,V.getServiceScript)(e,s);if(f?.associatedOnly)return[];if(o){if(typeof r=="number"){let d=(0,N.toGeneratedOffset)(e,o,u,r,w.isCodeActionsEnabled);if(d!==void 0)return t(f.id,d,i,a,l,c)}else for(let[d,p]of(0,N.toGeneratedRanges)(e,o,u,r.pos,r.end,w.isCodeActionsEnabled))return t(f.id,{pos:d,end:p},i,a,l,c);return[]}else return t(s,r,i,a,l,c)}}function gs(e,t){return(n,r,i,a,l,c,s)=>{let o,f=n.replace(j,"/"),[u,d,p]=(0,V.getServiceScript)(e,f);if(!d?.associatedOnly){if(u)if(typeof i=="number"){let g=(0,N.toGeneratedOffset)(e,u,p,i,w.isCodeActionsEnabled);g!==void 0&&(o=t(d.id,r,g,a,l,c,s))}else for(let[g,m]of(0,N.toGeneratedRanges)(e,u,p,i.pos,i.end,w.isCodeActionsEnabled))o=t(d.id,r,{pos:g,end:m},a,l,c,s);else o=t(f,r,i,a,l,c,s);if(o)return o.edits=(0,N.transformFileTextChanges)(e,o.edits,!1,w.isCodeActionsEnabled),o}}}function ms(e,t){return(...n)=>{let r=t(...n);return r.changes=(0,N.transformFileTextChanges)(e,r.changes,!1,w.isCodeActionsEnabled),r}}function hs(e,t){return(n,r,i)=>{let a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(c?.associatedOnly)return{canRename:!1,localizedErrorMessage:"Cannot rename"};if(l){let o;for(let[f]of(0,N.toGeneratedOffsets)(e,l,s,r,w.isRenameEnabled)){let u=t(c.id,f,i);if(u.canRename){let d=(0,N.transformTextSpan)(s,e,l,u.triggerSpan,!1,w.isRenameEnabled)?.[1];if(d)return u.triggerSpan=d,u}else o=u}return o||{canRename:!1,localizedErrorMessage:"Failed to get rename locations"}}else return t(a,r,i)}}function Ss(e,t){return(n,r,i,a,l,c)=>{let s=[],o=n.replace(j,"/"),[f,u,d]=(0,V.getServiceScript)(e,o);if(u?.associatedOnly)return[];if(f){let p=(0,N.toGeneratedOffset)(e,f,d,r,w.isCodeActionsEnabled),g=(0,N.toGeneratedOffset)(e,f,d,i,w.isCodeActionsEnabled);p!==void 0&&g!==void 0&&(s=t(u.id,p,g,a,l,c))}else s=t(o,r,i,a,l,c);return s=s.map(p=>(p.changes=(0,N.transformFileTextChanges)(e,p.changes,!1,w.isCodeActionsEnabled),p)),s}}function ys(e,t){return(n,r,i)=>{let a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(c?.associatedOnly)return{spans:[],endOfLineState:0};if(l){let o,f,u=e.maps.get(l.code,c);for(let m of u.mappings)(0,w.isSemanticTokensEnabled)(m.data)&&m.sourceOffsets[0]>=r.start&&m.sourceOffsets[0]<=r.start+r.length&&(o??=m.generatedOffsets[0],f??=m.generatedOffsets[m.generatedOffsets.length-1]+(m.generatedLengths??m.lengths)[m.lengths.length-1],o=Math.min(o,m.generatedOffsets[0]),f=Math.max(f,m.generatedOffsets[m.generatedOffsets.length-1]+(m.generatedLengths??m.lengths)[m.lengths.length-1]));o??=0,f??=c.snapshot.getLength();let d=(0,N.getMappingOffset)(e,l);o+=d,f+=d;let p=t(c.id,{start:o,length:f-o},i),g=[];for(let m=0;m<p.spans.length;m+=3)for(let[S,h,y]of(0,N.toSourceRanges)(s,e,l,p.spans[m],p.spans[m]+p.spans[m+1],!1,w.isSemanticTokensEnabled)){g.push(h,y-h,p.spans[m+2]);break}return p.spans=g,p}else return t(a,r,i)}}function vs(e,t,n){return r=>{let i=r.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);return l?.associatedOnly?[]:n(l?.id??i).map(s=>(0,N.transformDiagnostic)(e,s,t.getProgram(),!1)).filter(s=>!!s).filter(s=>!a||e.scripts.get(s.file.fileName)===c)}}function bs(e,t,n){return r=>{let i=r.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);return l?.associatedOnly?[]:n(l?.id??i).map(s=>(0,N.transformDiagnostic)(e,s,t.getProgram(),!1)).filter(s=>!!s).filter(s=>!a||!s.file||e.scripts.get(s.file.fileName)===c)}}function Cs(e,t,n){return r=>{let i=r.replace(j,"/"),[a,l,c]=(0,V.getServiceScript)(e,i);return l?.associatedOnly?[]:n(l?.id??i).map(s=>(0,N.transformDiagnostic)(e,s,t.getProgram(),!1)).filter(s=>!!s).filter(s=>!a||!s.file||e.scripts.get(s.file.fileName)===c)}}function xs(e,t){return(n,r)=>{let i=n.replace(j,"/"),a=pe(e,i,r,w.isDefinitionEnabled,(s,o)=>t(s,o),function*(s){for(let o of s.definitions??[])yield[o.fileName,o.textSpan.start]}),l=a.map(s=>(0,N.transformSpan)(e,i,s.textSpan,!0,w.isDefinitionEnabled)?.textSpan).filter(s=>!!s)[0];if(!l)return;let c=a.map(s=>s.definitions?.map(o=>(0,N.transformDocumentSpan)(e,o,!0,w.isDefinitionEnabled,o.fileName!==i)).filter(o=>!!o)??[]).flat();return{textSpan:l,definitions:(0,de.dedupeDocumentSpans)(c)}}}function Es(e,t){return(n,r)=>{let i=n.replace(j,"/");return pe(e,i,r,w.isReferencesEnabled,(c,s)=>t(c,s),function*(c){for(let s of c)for(let o of s.references)yield[o.fileName,o.textSpan.start]}).flat().map(c=>({definition:(0,N.transformDocumentSpan)(e,c.definition,!0,w.isDefinitionEnabled,!0),references:c.references.map(o=>(0,N.transformDocumentSpan)(e,o,!0,w.isReferencesEnabled)).filter(o=>!!o)}))}}function Ps(e,t){return(n,r)=>{let i=n.replace(j,"/"),l=pe(e,i,r,w.isDefinitionEnabled,(c,s)=>t(c,s),function*(c){for(let s of c)yield[s.fileName,s.textSpan.start]}).flat().map(c=>(0,N.transformDocumentSpan)(e,c,!0,w.isDefinitionEnabled,c.fileName!==i)).filter(c=>!!c);return(0,de.dedupeDocumentSpans)(l)}}function Fs(e,t){return(n,r)=>{let i=n.replace(j,"/"),l=pe(e,i,r,w.isTypeDefinitionEnabled,(c,s)=>t(c,s),function*(c){for(let s of c)yield[s.fileName,s.textSpan.start]}).flat().map(c=>(0,N.transformDocumentSpan)(e,c,!0,w.isTypeDefinitionEnabled)).filter(c=>!!c);return(0,de.dedupeDocumentSpans)(l)}}function Os(e,t){return(n,r)=>{let i=n.replace(j,"/"),l=pe(e,i,r,w.isImplementationEnabled,(c,s)=>t(c,s),function*(c){for(let s of c)yield[s.fileName,s.textSpan.start]}).flat().map(c=>(0,N.transformDocumentSpan)(e,c,!0,w.isImplementationEnabled)).filter(c=>!!c);return(0,de.dedupeDocumentSpans)(l)}}function As(e,t){return(n,r,i,a,l)=>{let c=n.replace(j,"/"),o=pe(e,c,r,w.isRenameEnabled,(f,u)=>t(f,u,i,a,l),function*(f){for(let u of f)yield[u.fileName,u.textSpan.start]}).flat().map(f=>(0,N.transformDocumentSpan)(e,f,!1,w.isRenameEnabled)).filter(f=>!!f);return(0,de.dedupeDocumentSpans)(o)}}function Ts(e,t){return(n,r)=>{let i=n.replace(j,"/"),l=pe(e,i,r,w.isReferencesEnabled,(c,s)=>t(c,s),function*(c){for(let s of c)yield[s.fileName,s.textSpan.start]}).flat().map(c=>(0,N.transformDocumentSpan)(e,c,!0,w.isReferencesEnabled)).filter(c=>!!c);return(0,de.dedupeDocumentSpans)(l)}}function _s(e,t){return(n,r,i,a)=>{let l=n.replace(j,"/"),[c,s,o]=(0,V.getServiceScript)(e,l);if(!s?.associatedOnly)if(c){let f,u=[];for(let[p,g]of(0,N.toGeneratedOffsets)(e,c,o,r,w.isCompletionEnabled)){let m=typeof g.data.completion=="object"&&g.data.completion.isAdditional;if(!m&&f?.entries.length)continue;let S=t(s.id,p,i,a);if(S){typeof g.data.completion=="object"&&g.data.completion.onlyImport&&(S.entries=S.entries.filter(h=>!!h.sourceDisplay));for(let h of S.entries)h.replacementSpan=h.replacementSpan&&(0,N.transformTextSpan)(o,e,c,h.replacementSpan,!1,w.isCompletionEnabled)?.[1];S.optionalReplacementSpan=S.optionalReplacementSpan&&(0,N.transformTextSpan)(o,e,c,S.optionalReplacementSpan,!1,w.isCompletionEnabled)?.[1],m?u.push(S):f=S}}let d=u;if(f&&d.unshift(f),d.length)return{...d[0],entries:d.map(p=>p.entries).flat()}}else return t(l,r,i,a)}}function ws(e,t){return(n,r,i,a,l,c,s)=>{let o,f=n.replace(j,"/"),[u,d,p]=(0,V.getServiceScript)(e,f);if(!d?.associatedOnly){if(u){let g=(0,N.toGeneratedOffset)(e,u,p,r,w.isCompletionEnabled);g!==void 0&&(o=t(d.id,g,i,a,l,c,s))}else return t(f,r,i,a,l,c,s);if(o?.codeActions)for(let g of o.codeActions)g.changes=(0,N.transformFileTextChanges)(e,g.changes,!1,w.isCompletionEnabled);return o}}}function Ns(e,t){return(n,r,i)=>{let a=n.replace(j,"/"),[l,c,s]=(0,V.getServiceScript)(e,a);if(c?.associatedOnly)return[];if(l){let o,f,u=e.maps.get(l.code,s);for(let m of u.mappings){if(!(0,w.isInlayHintsEnabled)(m.data))continue;let S=m.sourceOffsets[0],h,y;if(S>=r.start&&S<=r.start+r.length)h=m.generatedOffsets[0],y=m.generatedOffsets[m.generatedOffsets.length-1]+(m.generatedLengths??m.lengths)[m.generatedOffsets.length-1];else if(S<r.start&&r.start<S+m.lengths[0]&&m.sourceOffsets.length==1&&(!m.generatedLengths||m.generatedLengths[0]===m.lengths[0]))h=m.generatedOffsets[0]+r.start-S,y=Math.min(h+r.length,m.generatedOffsets[0]+m.lengths[0]);else continue;o=Math.min(o??h,h),f=Math.max(f??y,y)}(o===void 0||f===void 0)&&(o=0,f=0);let d=(0,N.getMappingOffset)(e,l);o+=d,f+=d;let p=t(c.id,{start:o,length:f-o},i),g=[];for(let m of p){let S=(0,N.toSourceOffset)(s,e,l,m.position,w.isInlayHintsEnabled);S!==void 0&&g.push({...m,position:S[1]})}return g}else return t(a,r,i)}}function Ds(e,t){return n=>{let r=n.replace(j,"/"),a=t(r).map(l=>(0,N.transformDocumentSpan)(e,l,!0,w.isReferencesEnabled)).filter(l=>!!l);return(0,de.dedupeDocumentSpans)(a)}}function Ms(e,t){return(...n)=>{let i=t(...n).map(a=>(0,N.transformDocumentSpan)(e,a,!0,w.isReferencesEnabled)).filter(a=>!!a);return(0,de.dedupeDocumentSpans)(i)}}function pe(e,t,n,r,i,a){let l=[],c=new Set,[s,o,f]=(0,V.getServiceScript)(e,t);if(s)for(let[d]of(0,N.toGeneratedOffsets)(e,s,f,n,r))u(o.id,d);else u(t,n);return l;function u(d,p){if(c.has(d+":"+p))return;c.add(d+":"+p);let g=i(d,p);if(g){l.push(g);for(let m of a(g)){c.add(m[0]+":"+m[1]);let[S]=(0,V.getServiceScript)(e,m[0]);if(!S)continue;let h=e.linkedCodeMaps.get(S.code);if(!h)continue;let y=(0,N.getMappingOffset)(e,S);for(let b of h.getLinkedOffsets(m[1]-y))u(m[0],b+y)}}}}function oe(e){return e?e.map(t=>t.text).join(""):""}});var He=q(St=>{"use strict";Object.defineProperty(St,"__esModule",{value:!0});St.createResolveModuleName=Ls;function Ls(e,t,n,r,i){let a=new Map,l={readFile:n.readFile.bind(n),directoryExists:n.directoryExists?.bind(n),realpath:n.realpath?.bind(n),getCurrentDirectory:n.getCurrentDirectory?.bind(n),getDirectories:n.getDirectories?.bind(n),useCaseSensitiveFileNames:typeof n.useCaseSensitiveFileNames=="function"?n.useCaseSensitiveFileNames.bind(n):n.useCaseSensitiveFileNames,fileExists(s){for(let{typescript:o}of r)if(o){for(let{extension:f}of o.extraFileExtensions)if(s.endsWith(`.d.${f}.ts`)){let u=s.slice(0,-`.d.${f}.ts`.length)+`.${f}`;if(c(u)){let d=i(u);if(d?.generated){let p=d.generated.languagePlugin.typescript?.getServiceScript(d.generated.root);if(p){let g=u+".d.ts";return(p.extension===".js"||p.extension===".jsx")&&c(g)?a.set(s,{sourceFileName:g,extension:".ts"}):a.set(s,{sourceFileName:u,extension:p.extension}),!0}}}}if(o.resolveHiddenExtensions&&s.endsWith(".d.ts"))for(let{extension:f}of o.extraFileExtensions){let u=s.slice(0,-5)+`.${f}`;if(c(u)){let d=i(u);if(d?.generated){let p=d.generated.languagePlugin.typescript?.getServiceScript(d.generated.root);if(p)return a.set(s,{sourceFileName:u,extension:p.extension}),!0}}}}return n.fileExists(s)}};return(s,o,f,u,d,p)=>{let g=e.resolveModuleName(s,o,f,l,u,d,p);if(g.resolvedModule){let m=a.get(g.resolvedModule.resolvedFileName);m&&(g.resolvedModule.resolvedFileName=m.sourceFileName,g.resolvedModule.extension=m.extension)}return a.clear(),g};function c(s){return n.fileExists(s)?(t?.(s)??n.readFile(s)?.length??0)<4*1024*1024:!1}}});var yt=q(Ue=>{"use strict";Object.defineProperty(Ue,"__esModule",{value:!0});Ue.decorateLanguageServiceHost=ks;Ue.searchExternalFiles=Is;var Rs=He();function ks(e,t,n){let r=t.plugins.map(d=>d.typescript?.extraFileExtensions.map(p=>"."+p.extension)??[]).flat(),i=new Map,a=new Set,l=n.readDirectory?.bind(n),c=n.resolveModuleNameLiterals?.bind(n),s=n.resolveModuleNames?.bind(n),o=n.getScriptSnapshot.bind(n),f=n.getScriptKind?.bind(n);if(l&&(n.readDirectory=(d,p,g,m,S)=>{if(p)for(let h of r)p.includes(h)||(p=[...p,h]);return l(d,p,g,m,S)}),r.length){let d=(0,Rs.createResolveModuleName)(e,e.sys.getFileSize,n,t.plugins,m=>t.scripts.get(m)),p=n.useCaseSensitiveFileNames?.()?m=>m:m=>m.toLowerCase(),g=e.createModuleResolutionCache(n.getCurrentDirectory(),p,n.getCompilationSettings());c&&(n.resolveModuleNameLiterals=(m,S,h,y,...b)=>m.every(P=>!r.some(O=>P.text.endsWith(O)))?c(m,S,h,y,...b):m.map(P=>d(P.text,S,y,g,h))),s&&(n.resolveModuleNames=(m,S,h,y,b,P)=>m.every(O=>!r.some(L=>O.endsWith(L)))?s(m,S,h,y,b,P):m.map(O=>d(O,S,b,g,y).resolvedModule))}n.getScriptSnapshot=d=>{let p=u(d,!0);return p?p.snapshot:o(d)},f&&(n.getScriptKind=d=>{let p=u(d,!1);return p?p.scriptKind:f(d)});function u(d,p){if(a.has(d))return;let g;try{g=n.getScriptVersion(d)}catch{a.add(d)}if(g===void 0)return;let m=i.get(d);if(!m||m[0]!==g){m=[g];let S=t.scripts.get(d,void 0,p);if(S?.generated){let h=S.generated.languagePlugin.typescript?.getServiceScript(S.generated.root);if(h)if(h.preventLeadingOffset)m[1]={extension:h.extension,scriptKind:h.scriptKind,snapshot:h.code.snapshot};else{let b=S.snapshot.getText(0,S.snapshot.getLength()).split(`
`).map(P=>" ".repeat(P.length)).join(`
`)+h.code.snapshot.getText(0,h.code.snapshot.getLength());m[1]={extension:h.extension,scriptKind:h.scriptKind,snapshot:e.ScriptSnapshot.fromString(b)}}S.generated.languagePlugin.typescript?.getExtraServiceScripts&&console.warn("getExtraServiceScripts() is not available in TS plugin.")}i.set(d,m)}return m[1]}}function Is(e,t,n){if(t.projectKind!==e.server.ProjectKind.Configured)return[];let r=t.getProjectName(),i=e.readJsonConfigFile(r,t.readFile.bind(t)),a={useCaseSensitiveFileNames:t.useCaseSensitiveFileNames(),fileExists:t.fileExists.bind(t),readFile:t.readFile.bind(t),readDirectory:(...c)=>(c[1]=n,t.readDirectory(...c))};return e.parseJsonSourceFileConfigFileContent(i,a,t.getCurrentDirectory()).fileNames}});var Sn=q(z=>{"use strict";Object.defineProperty(z,"__esModule",{value:!0});z.decoratedLanguageServiceHosts=z.decoratedLanguageServices=z.projectExternalFileExtensions=z.externalFiles=void 0;z.createLanguageServicePlugin=Vs;z.arrayItemsEqual=hn;var gn=be(),js=je(),qs=ht(),mn=yt();z.externalFiles=new WeakMap;z.projectExternalFileExtensions=new WeakMap;z.decoratedLanguageServices=new WeakSet;z.decoratedLanguageServiceHosts=new WeakSet;function Vs(e){return t=>{let{typescript:n}=t;return{create(i){if(!z.decoratedLanguageServices.has(i.languageService)&&!z.decoratedLanguageServiceHosts.has(i.languageServiceHost)){z.decoratedLanguageServices.add(i.languageService),z.decoratedLanguageServiceHosts.add(i.languageServiceHost);let{languagePlugins:l,setup:c}=e(n,i),s=l.map(p=>p.typescript?.extraFileExtensions.map(g=>"."+g.extension)??[]).flat();z.projectExternalFileExtensions.set(i.project,s);let o=i.languageServiceHost.getScriptSnapshot.bind(i.languageServiceHost),f=(0,gn.createLanguage)([...l,{getLanguageId:js.resolveFileLanguageId}],new gn.FileMap(n.sys.useCaseSensitiveFileNames),(p,g,m)=>{let S;m?S=o(p):(S=a(p)?.getSnapshot(),S||(i.project.getScriptVersion(p),S=a(p)?.getSnapshot())),S?f.scripts.set(p,S):f.scripts.delete(p)}),{proxy:u,initialize:d}=(0,qs.createProxyLanguageService)(i.languageService);i.languageService=u,d(f),(0,mn.decorateLanguageServiceHost)(n,f,i.languageServiceHost),c?.(f)}return i.languageService;function a(l){try{return i.project.getScriptInfo(l)}catch{}}},getExternalFiles(i,a=0){if(a>=1||!z.externalFiles.has(i)){let l=z.externalFiles.get(i),c=z.projectExternalFileExtensions.get(i),s=c?.length?(0,mn.searchExternalFiles)(n,i,c):[];z.externalFiles.set(i,s),l&&!hn(l,s)&&i.refreshDiagnostics()}return z.externalFiles.get(i)}}}}function hn(e,t){if(e.length!==t.length)return!1;let n=new Set(e);for(let r of t)if(!n.has(r))return!1;return!0}});var Dn=q(T=>{"use strict";Object.defineProperty(T,"__esModule",{value:!0});function Y(e){let t=Object.create(null);for(let n of e.split(","))t[n]=1;return n=>n in t}var zs={},Hs=[],Us=()=>{},$s=()=>!1,Gs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Bs=e=>e.startsWith("onUpdate:"),Ks=Object.assign,Ws=(e,t)=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)},Js=Object.prototype.hasOwnProperty,Ys=(e,t)=>Js.call(e,t),me=Array.isArray,bn=e=>he(e)==="[object Map]",Cn=e=>he(e)==="[object Set]",Ct=e=>he(e)==="[object Date]",Qs=e=>he(e)==="[object RegExp]",Ee=e=>typeof e=="function",re=e=>typeof e=="string",Pe=e=>typeof e=="symbol",ge=e=>e!==null&&typeof e=="object",Xs=e=>(ge(e)||Ee(e))&&Ee(e.then)&&Ee(e.catch),xt=Object.prototype.toString,he=e=>xt.call(e),Zs=e=>he(e).slice(8,-1),xn=e=>he(e)==="[object Object]",eo=e=>re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,to=Y(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),no=Y("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),$e=e=>{let t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ro=/-(\w)/g,io=$e(e=>e.replace(ro,(t,n)=>n?n.toUpperCase():"")),so=/\B([A-Z])/g,En=$e(e=>e.replace(so,"-$1").toLowerCase()),Pn=$e(e=>e.charAt(0).toUpperCase()+e.slice(1)),oo=$e(e=>e?`on${Pn(e)}`:""),ao=(e,t)=>!Object.is(e,t),co=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},lo=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},fo=e=>{let t=parseFloat(e);return isNaN(t)?e:t},uo=e=>{let t=re(e)?Number(e):NaN;return isNaN(t)?e:t},yn,po=()=>yn||(yn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),go=/^[_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*$/;function mo(e){return go.test(e)?`__props.${e}`:`__props[${JSON.stringify(e)}]`}function ho(e,t){return e+JSON.stringify(t,(n,r)=>typeof r=="function"?r.toString():r)}var So={TEXT:1,1:"TEXT",CLASS:2,2:"CLASS",STYLE:4,4:"STYLE",PROPS:8,8:"PROPS",FULL_PROPS:16,16:"FULL_PROPS",NEED_HYDRATION:32,32:"NEED_HYDRATION",STABLE_FRAGMENT:64,64:"STABLE_FRAGMENT",KEYED_FRAGMENT:128,128:"KEYED_FRAGMENT",UNKEYED_FRAGMENT:256,256:"UNKEYED_FRAGMENT",NEED_PATCH:512,512:"NEED_PATCH",DYNAMIC_SLOTS:1024,1024:"DYNAMIC_SLOTS",DEV_ROOT_FRAGMENT:2048,2048:"DEV_ROOT_FRAGMENT",CACHED:-1,"-1":"CACHED",BAIL:-2,"-2":"BAIL"},yo={1:"TEXT",2:"CLASS",4:"STYLE",8:"PROPS",16:"FULL_PROPS",32:"NEED_HYDRATION",64:"STABLE_FRAGMENT",128:"KEYED_FRAGMENT",256:"UNKEYED_FRAGMENT",512:"NEED_PATCH",1024:"DYNAMIC_SLOTS",2048:"DEV_ROOT_FRAGMENT",[-1]:"HOISTED",[-2]:"BAIL"},vo={ELEMENT:1,1:"ELEMENT",FUNCTIONAL_COMPONENT:2,2:"FUNCTIONAL_COMPONENT",STATEFUL_COMPONENT:4,4:"STATEFUL_COMPONENT",TEXT_CHILDREN:8,8:"TEXT_CHILDREN",ARRAY_CHILDREN:16,16:"ARRAY_CHILDREN",SLOTS_CHILDREN:32,32:"SLOTS_CHILDREN",TELEPORT:64,64:"TELEPORT",SUSPENSE:128,128:"SUSPENSE",COMPONENT_SHOULD_KEEP_ALIVE:256,256:"COMPONENT_SHOULD_KEEP_ALIVE",COMPONENT_KEPT_ALIVE:512,512:"COMPONENT_KEPT_ALIVE",COMPONENT:6,6:"COMPONENT"},bo={STABLE:1,1:"STABLE",DYNAMIC:2,2:"DYNAMIC",FORWARDED:3,3:"FORWARDED"},Co={1:"STABLE",2:"DYNAMIC",3:"FORWARDED"},xo="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",Fn=Y(xo),Eo=Fn,vn=2;function Po(e,t=0,n=e.length){if(t=Math.max(0,Math.min(t,e.length)),n=Math.max(0,Math.min(n,e.length)),t>n)return"";let r=e.split(/(\r?\n)/),i=r.filter((c,s)=>s%2===1);r=r.filter((c,s)=>s%2===0);let a=0,l=[];for(let c=0;c<r.length;c++)if(a+=r[c].length+(i[c]&&i[c].length||0),a>=t){for(let s=c-vn;s<=c+vn||n>a;s++){if(s<0||s>=r.length)continue;let o=s+1;l.push(`${o}${" ".repeat(Math.max(3-String(o).length,0))}|  ${r[s]}`);let f=r[s].length,u=i[s]&&i[s].length||0;if(s===c){let d=t-(a-(f+u)),p=Math.max(1,n>a?f-d:n-t);l.push("   |  "+" ".repeat(d)+"^".repeat(p))}else if(s>c){if(n>a){let d=Math.max(Math.min(n-a,f),1);l.push("   |  "+"^".repeat(d))}a+=f+u}}break}return l.join(`
`)}function Et(e){if(me(e)){let t={};for(let n=0;n<e.length;n++){let r=e[n],i=re(r)?On(r):Et(r);if(i)for(let a in i)t[a]=i[a]}return t}else if(re(e)||ge(e))return e}var Fo=/;(?![^(]*\))/g,Oo=/:([^]+)/,Ao=/\/\*[^]*?\*\//g;function On(e){let t={};return e.replace(Ao,"").split(Fo).forEach(n=>{if(n){let r=n.split(Oo);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function To(e){if(!e)return"";if(re(e))return e;let t="";for(let n in e){let r=e[n];if(re(r)||typeof r=="number"){let i=n.startsWith("--")?n:En(n);t+=`${i}:${r};`}}return t}function Pt(e){let t="";if(re(e))t=e;else if(me(e))for(let n=0;n<e.length;n++){let r=Pt(e[n]);r&&(t+=r+" ")}else if(ge(e))for(let n in e)e[n]&&(t+=n+" ");return t.trim()}function _o(e){if(!e)return null;let{class:t,style:n}=e;return t&&!re(t)&&(e.class=Pt(t)),n&&(e.style=Et(n)),e}var wo="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",No="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Do="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Mo="area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr",Lo=Y(wo),Ro=Y(No),ko=Y(Do),Io=Y(Mo),An="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",jo=Y(An),qo=Y(An+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,inert,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected");function Vo(e){return!!e||e===""}var zo=/[>/="'\u0009\u000a\u000c\u0020]/,vt={};function Ho(e){if(vt.hasOwnProperty(e))return vt[e];let t=zo.test(e);return t&&console.error(`unsafe attribute name: ${e}`),vt[e]=!t}var Uo={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},$o=Y("accept,accept-charset,accesskey,action,align,allow,alt,async,autocapitalize,autocomplete,autofocus,autoplay,background,bgcolor,border,buffered,capture,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,contenteditable,contextmenu,controls,coords,crossorigin,csp,data,datetime,decoding,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,enterkeyhint,for,form,formaction,formenctype,formmethod,formnovalidate,formtarget,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,importance,inert,integrity,ismap,itemprop,keytype,kind,label,lang,language,loading,list,loop,low,manifest,max,maxlength,minlength,media,min,multiple,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,referrerpolicy,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,selected,shape,size,sizes,slot,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,translate,type,usemap,value,width,wrap"),Go=Y("xmlns,accent-height,accumulate,additive,alignment-baseline,alphabetic,amplitude,arabic-form,ascent,attributeName,attributeType,azimuth,baseFrequency,baseline-shift,baseProfile,bbox,begin,bias,by,calcMode,cap-height,class,clip,clipPathUnits,clip-path,clip-rule,color,color-interpolation,color-interpolation-filters,color-profile,color-rendering,contentScriptType,contentStyleType,crossorigin,cursor,cx,cy,d,decelerate,descent,diffuseConstant,direction,display,divisor,dominant-baseline,dur,dx,dy,edgeMode,elevation,enable-background,end,exponent,fill,fill-opacity,fill-rule,filter,filterRes,filterUnits,flood-color,flood-opacity,font-family,font-size,font-size-adjust,font-stretch,font-style,font-variant,font-weight,format,from,fr,fx,fy,g1,g2,glyph-name,glyph-orientation-horizontal,glyph-orientation-vertical,glyphRef,gradientTransform,gradientUnits,hanging,height,href,hreflang,horiz-adv-x,horiz-origin-x,id,ideographic,image-rendering,in,in2,intercept,k,k1,k2,k3,k4,kernelMatrix,kernelUnitLength,kerning,keyPoints,keySplines,keyTimes,lang,lengthAdjust,letter-spacing,lighting-color,limitingConeAngle,local,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mask,maskContentUnits,maskUnits,mathematical,max,media,method,min,mode,name,numOctaves,offset,opacity,operator,order,orient,orientation,origin,overflow,overline-position,overline-thickness,panose-1,paint-order,path,pathLength,patternContentUnits,patternTransform,patternUnits,ping,pointer-events,points,pointsAtX,pointsAtY,pointsAtZ,preserveAlpha,preserveAspectRatio,primitiveUnits,r,radius,referrerPolicy,refX,refY,rel,rendering-intent,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,result,rotate,rx,ry,scale,seed,shape-rendering,slope,spacing,specularConstant,specularExponent,speed,spreadMethod,startOffset,stdDeviation,stemh,stemv,stitchTiles,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,string,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,style,surfaceScale,systemLanguage,tabindex,tableValues,target,targetX,targetY,text-anchor,text-decoration,text-rendering,textLength,to,transform,transform-origin,type,u1,u2,underline-position,underline-thickness,unicode,unicode-bidi,unicode-range,units-per-em,v-alphabetic,v-hanging,v-ideographic,v-mathematical,values,vector-effect,version,vert-adv-y,vert-origin-x,vert-origin-y,viewBox,viewTarget,visibility,width,widths,word-spacing,writing-mode,x,x-height,x1,x2,xChannelSelector,xlink:actuate,xlink:arcrole,xlink:href,xlink:role,xlink:show,xlink:title,xlink:type,xmlns:xlink,xml:base,xml:lang,xml:space,y,y1,y2,yChannelSelector,z,zoomAndPan"),Bo=Y("accent,accentunder,actiontype,align,alignmentscope,altimg,altimg-height,altimg-valign,altimg-width,alttext,bevelled,close,columnsalign,columnlines,columnspan,denomalign,depth,dir,display,displaystyle,encoding,equalcolumns,equalrows,fence,fontstyle,fontweight,form,frame,framespacing,groupalign,height,href,id,indentalign,indentalignfirst,indentalignlast,indentshift,indentshiftfirst,indentshiftlast,indextype,justify,largetop,largeop,lquote,lspace,mathbackground,mathcolor,mathsize,mathvariant,maxsize,minlabelspacing,mode,other,overflow,position,rowalign,rowlines,rowspan,rquote,rspace,scriptlevel,scriptminsize,scriptsizemultiplier,selection,separator,separators,shift,side,src,stackalign,stretchy,subscriptshift,superscriptshift,symmetric,voffset,width,widths,xlink:href,xlink:show,xlink:type,xmlns");function Ko(e){if(e==null)return!1;let t=typeof e;return t==="string"||t==="number"||t==="boolean"}var Wo=/["'&<>]/;function Jo(e){let t=""+e,n=Wo.exec(t);if(!n)return t;let r="",i,a,l=0;for(a=n.index;a<t.length;a++){switch(t.charCodeAt(a)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 39:i="&#39;";break;case 60:i="&lt;";break;case 62:i="&gt;";break;default:continue}l!==a&&(r+=t.slice(l,a)),l=a+1,r+=i}return l!==a?r+t.slice(l,a):r}var Yo=/^-?>|<!--|-->|--!>|<!-$/g;function Qo(e){return e.replace(Yo,"")}var Tn=/[ !"#$%&'()*+,./:;<=>?@[\\\]^`{|}~]/g;function Xo(e,t){return e.replace(Tn,n=>t?n==='"'?'\\\\\\"':`\\\\${n}`:`\\${n}`)}function Zo(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=Ge(e[r],t[r]);return n}function Ge(e,t){if(e===t)return!0;let n=Ct(e),r=Ct(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=Pe(e),r=Pe(t),n||r)return e===t;if(n=me(e),r=me(t),n||r)return n&&r?Zo(e,t):!1;if(n=ge(e),r=ge(t),n||r){if(!n||!r)return!1;let i=Object.keys(e).length,a=Object.keys(t).length;if(i!==a)return!1;for(let l in e){let c=e.hasOwnProperty(l),s=t.hasOwnProperty(l);if(c&&!s||!c&&s||!Ge(e[l],t[l]))return!1}}return String(e)===String(t)}function ea(e,t){return e.findIndex(n=>Ge(n,t))}var _n=e=>!!(e&&e.__v_isRef===!0),wn=e=>re(e)?e:e==null?"":me(e)||ge(e)&&(e.toString===xt||!Ee(e.toString))?_n(e)?wn(e.value):JSON.stringify(e,Nn,2):String(e),Nn=(e,t)=>_n(t)?Nn(e,t.value):bn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,i],a)=>(n[bt(r,a)+" =>"]=i,n),{})}:Cn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>bt(n))}:Pe(t)?bt(t):ge(t)&&!me(t)&&!xn(t)?String(t):t,bt=(e,t="")=>{var n;return Pe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};T.EMPTY_ARR=Hs;T.EMPTY_OBJ=zs;T.NO=$s;T.NOOP=Us;T.PatchFlagNames=yo;T.PatchFlags=So;T.ShapeFlags=vo;T.SlotFlags=bo;T.camelize=io;T.capitalize=Pn;T.cssVarNameEscapeSymbolsRE=Tn;T.def=lo;T.escapeHtml=Jo;T.escapeHtmlComment=Qo;T.extend=Ks;T.genCacheKey=ho;T.genPropsAccessExp=mo;T.generateCodeFrame=Po;T.getEscapedCssVarName=Xo;T.getGlobalThis=po;T.hasChanged=ao;T.hasOwn=Ys;T.hyphenate=En;T.includeBooleanAttr=Vo;T.invokeArrayFns=co;T.isArray=me;T.isBooleanAttr=qo;T.isBuiltInDirective=no;T.isDate=Ct;T.isFunction=Ee;T.isGloballyAllowed=Fn;T.isGloballyWhitelisted=Eo;T.isHTMLTag=Lo;T.isIntegerKey=eo;T.isKnownHtmlAttr=$o;T.isKnownMathMLAttr=Bo;T.isKnownSvgAttr=Go;T.isMap=bn;T.isMathMLTag=ko;T.isModelListener=Bs;T.isObject=ge;T.isOn=Gs;T.isPlainObject=xn;T.isPromise=Xs;T.isRegExp=Qs;T.isRenderableAttrValue=Ko;T.isReservedProp=to;T.isSSRSafeAttrName=Ho;T.isSVGTag=Ro;T.isSet=Cn;T.isSpecialBooleanAttr=jo;T.isString=re;T.isSymbol=Pe;T.isVoidTag=Io;T.looseEqual=Ge;T.looseIndexOf=ea;T.looseToNumber=fo;T.makeMap=Y;T.normalizeClass=Pt;T.normalizeProps=_o;T.normalizeStyle=Et;T.objectToString=xt;T.parseStringStyle=On;T.propsToAttrMap=Uo;T.remove=Ws;T.slotFlagsText=Co;T.stringifyStyle=To;T.toDisplayString=wn;T.toHandlerKey=oo;T.toNumber=uo;T.toRawType=Zs;T.toTypeString=he});var Be=q((rl,Mn)=>{"use strict";Mn.exports=Dn()});function Ke(e,t,n,r,i,a){let c=t.getProgram().getTypeChecker(),s=a.split("."),o=r.type.getProperty(s[0])??r.type.getProperty((0,ae.camelize)(s[0]))??r.type.getProperty((0,ae.capitalize)((0,ae.camelize)(s[0]))),f;if(!o)Ft(i)===(0,ae.capitalize)((0,ae.camelize)(a))&&(f=ce(e,t,n,"__VLS_self")?.type);else{f=c.getTypeOfSymbolAtLocation(o,r.node);for(let u=1;u<s.length;u++)o=f.getProperty(s[u]),o&&(f=c.getTypeOfSymbolAtLocation(o,r.node))}return f}function Ft(e){let t=Ln.basename(e);return(0,ae.capitalize)((0,ae.camelize)(t.slice(0,t.lastIndexOf("."))))}function ce(e,t,n,r){let i=t.getProgram(),a=i.getSourceFile(n.fileName);if(a){let l=i.getTypeChecker(),c=ta(e,a,r);if(c)return{node:c,type:l.getTypeAtLocation(c)}}}function ta(e,t,n){let r;return i(t),r;function i(a){r||(e.isVariableDeclaration(a)&&a.name.getText()===n?r=a:a.forEachChild(i))}}var ae,Ln,Fe=J(()=>{"use strict";ae=te(Be()),Ln=te(require("node:path"))});function kn(e){let{typescript:t,language:n,languageService:r,getFileId:i}=this,a=n.scripts.get(i(e));if(!(a?.generated?.root instanceof Rn.VueVirtualCode))return;let l=a.generated.root;return Ot(t,r,l)}function Ot(e,t,n){let r=ce(e,t,n,"__VLS_components")?.type?.getProperties().map(i=>i.name).filter(i=>!i.includes("$")&&!i.startsWith("_"))??[];return r.push(Ft(n.fileName)),r}var Rn,At=J(()=>{"use strict";Rn=require("vue-language-core-pack");Fe()});function jn(e,t){let{typescript:n,language:r,languageService:i,getFileId:a}=this;if(!(r.scripts.get(a(e))?.generated?.root instanceof In.VueVirtualCode))return;let c=i.getProgram(),s=c.getSourceFile(e);if(s){let o=c.getTypeChecker(),f=s.statements.filter(n.isTypeAliasDeclaration).find(u=>u.name.getText()==="__VLS_IntrinsicElementsCompletion");if(f){let d=o.getTypeFromTypeNode(f.type).getProperty(t);if(d)return o.getTypeOfSymbolAtLocation(d,f).getProperties().map(g=>g.name)}}return[]}function qn(e,t,n){let r=t.getProgram(),i=r.getSourceFile(n.fileName);if(i){let a=r.getTypeChecker(),l=i.statements.filter(e.isTypeAliasDeclaration).find(c=>c.name.getText()==="__VLS_IntrinsicElementsCompletion");if(l)return a.getTypeFromTypeNode(l.type).getProperties().map(s=>s.name)}return[]}var In,Tt=J(()=>{"use strict";In=require("vue-language-core-pack")});var Hn={};tt(Hn,{getComponentSpans:()=>zn,proxyLanguageServiceForVue:()=>na});function na(e,t,n,r,i){let a=new Map,l=(c,s)=>{switch(s){case"getCompletionsAtPosition":return ra(r,c[s]);case"getCompletionEntryDetails":return ia(t,i,c[s]);case"getCodeFixesAtPosition":return sa(c[s]);case"getQuickInfoAtPosition":return oa(e,c,c[s]);case"getEncodedSemanticClassifications":return aa(e,t,c,i,c[s])}};return new Proxy(n,{get(c,s,o){if(l){a.has(s)||a.set(s,l(c,s));let f=a.get(s);if(f)return f}return Reflect.get(c,s,o)},set(c,s,o,f){return Reflect.set(c,s,o,f)}})}function ra(e,t){return(n,r,i,a)=>{let l=n.replace(Vn,"/"),c=t(l,r,i,a);if(c){c.entries=c.entries.filter(s=>!s.name.includes("__VLS_")&&!s.labelDetails?.description?.includes("__VLS_"));for(let s of c.entries)if(s.source){let o=s.name;for(let f of e.extensions){let u=(0,_t.capitalize)(f.slice(1));if(s.source.endsWith(f)&&s.name.endsWith(u)){s.name=(0,_t.capitalize)(s.name.slice(0,-u.length)),s.insertText&&(s.insertText=s.insertText.replace(`${u}$1`,"$1")),s.data&&(s.data.__isComponentAutoImport={ext:f,suffix:u,originalName:o,newName:s.insertText});break}}s.data&&(s.data.__isAutoImport={fileName:l})}}return c}}function ia(e,t,n){return(...r)=>{let i=n(...r);if(r[6]?.__isComponentAutoImport){let{ext:a,suffix:l,originalName:c,newName:s}=r[6]?.__isComponentAutoImport;for(let o of i?.codeActions??[])for(let f of o.changes)for(let u of f.textChanges)u.newText=u.newText.replace("import "+c+" from ","import "+s+" from ")}if(r[6]?.__isAutoImport){let{fileName:a}=r[6]?.__isAutoImport,l=e.scripts.get(t(a));if(l?.generated?.root instanceof Se.VueVirtualCode){let c=l.generated.root.vueSfc;if(!c?.descriptor.script&&!c?.descriptor.scriptSetup)for(let s of i?.codeActions??[]){for(let o of s.changes){for(let f of o.textChanges){f.newText=`<script setup lang="ts">${f.newText}</script>

`;break}break}break}}}return i}}function sa(e){return(...t)=>{let n=e(...t);return n=n.filter(r=>!r.description.includes("__VLS_")),n}}function oa(e,t,n){return(...r)=>{let i=n(...r);if(i&&i.documentation?.length===1&&i.documentation[0].text.startsWith("__VLS_emit,")){let[a,l,c]=i.documentation[0].text.split(","),s=t.getProgram(),o=s.getTypeChecker(),f=s.getSourceFile(r[0]);i.documentation=void 0;let u;if(f?.forEachChild(function d(p){e.isIdentifier(p)&&p.text===l&&(u=p),!u&&e.forEachChild(p,d)}),u){let d=o.getSymbolAtLocation(u);if(d){let g=o.getTypeOfSymbolAtLocation(d,u).getCallSignatures();for(let m of g){let S=o.getTypeOfSymbolAtLocation(m.parameters[0],u).value;m.getJsDocTags(),S===c&&(i.documentation=m.getDocumentationComment(o),i.tags=m.getJsDocTags())}}}}return i}}function aa(e,t,n,r,i){return(a,l,c)=>{let s=a.replace(Vn,"/"),o=i(s,l,c),u=t.scripts.get(r(s))?.generated?.root;if(u instanceof Se.VueVirtualCode){let{template:d}=u.sfc;if(d)for(let p of zn.call({typescript:e,languageService:n},u,d,{start:l.start-d.startTagEnd,length:l.length}))o.spans.push(p.start+d.startTagEnd,p.length,256)}return o}}function zn(e,t,n){let{typescript:r,languageService:i}=this,a=[],l=Ot(r,i,e),c=new Set(qn(r,i,e)),s=new Set([...l,...l.map(Se.hyphenateTag)]);if(t.ast){for(let o of(0,Se.forEachElementNode)(t.ast))if(!(o.loc.end.offset<=n.start||o.loc.start.offset>=n.start+n.length)&&s.has(o.tag)&&!c.has(o.tag)){let f=o.loc.start.offset;t.lang==="html"&&(f+=1),a.push({start:f,length:o.tag.length}),t.lang==="html"&&!o.isSelfClosing&&a.push({start:o.loc.start.offset+o.loc.source.lastIndexOf(o.tag),length:o.tag.length})}}return a}var Se,_t,Vn,Un=J(()=>{"use strict";Se=require("vue-language-core-pack"),_t=te(Be());At();Tt();Vn=/\\/g});function $n(e,t){let{typescript:n,languageService:r,language:i,isTsPlugin:a,getFileId:l}=this,c=i.scripts.get(l(e));if(!c?.generated)return;let s=c.generated.root;if(!(s instanceof We.VueVirtualCode))return;let o=new Map,f=r.getProgram(),u=f.getSourceFile(e),d=f.getTypeChecker(),p=c.generated?.languagePlugin.typescript?.getServiceScript(s),g=p?[...i.maps.forEach(p.code)].map(([S,h])=>h):[],{sfc:m}=s;return u.forEachChild(function S(h){if(n.isPropertyAccessExpression(h)&&n.isIdentifier(h.expression)&&h.expression.text==="__VLS_ctx"&&n.isIdentifier(h.name)){let{name:y}=h;for(let b of g){let P=!1;for(let O of b.toSourceLocation(y.getEnd()-(a?c.snapshot.getLength():0)))if(O[0]>=m.template.startTagEnd+t[0]&&O[0]<=m.template.startTagEnd+t[1]&&(0,We.isSemanticTokensEnabled)(O[1].data)){if(P=!0,!o.has(y.text)){let R=d.getTypeAtLocation(h),C=d.typeToString(R,h,n.TypeFormatFlags.NoTruncation);o.set(y.text,{name:y.text,type:C.includes("__VLS_")?"any":C,model:!1})}(n.isPostfixUnaryExpression(h.parent)||n.isBinaryExpression(h.parent))&&(o.get(y.text).model=!0);break}if(P)break}}h.forEachChild(S)}),[...o.values()]}var We,Gn=J(()=>{"use strict";We=require("vue-language-core-pack")});function Kn(e){let{typescript:t,language:n,languageService:r,getFileId:i}=this,a=n.scripts.get(i(e));if(!(a?.generated?.root instanceof Bn.VueVirtualCode))return;let l=a.generated.root,c=ce(t,r,l,"__VLS_directives");return c?c.type.getProperties().map(({name:s})=>s).filter(s=>s.startsWith("v")&&s.length>=2&&s[1]===s[1].toUpperCase()).filter(s=>!["vBind","vIf","vOn","VOnce","vShow","VSlot"].includes(s)):[]}var Bn,Wn=J(()=>{"use strict";Bn=require("vue-language-core-pack");Fe()});function Yn(e,t){let{typescript:n,language:r,languageService:i,getFileId:a}=this,l=r.scripts.get(a(e));if(!(l?.generated?.root instanceof Jn.VueVirtualCode))return;let c=l.generated.root,o=i.getProgram().getTypeChecker(),f=ce(n,i,c,"__VLS_components");if(!f)return[];let u=Ke(n,i,c,f,e,t);if(!u)return[];let d=new Set;for(let p of u.getConstructSignatures()){let m=p.getReturnType().getProperty("$emit");if(m){let S=o.getTypeOfSymbolAtLocation(m,f.node);for(let h of S.getCallSignatures()){let y=h.parameters[0];if(y){let b=o.getTypeOfSymbolAtLocation(y,f.node);b.isStringLiteral()&&d.add(b.value)}}}}return[...d]}var Jn,Qn=J(()=>{"use strict";Jn=require("vue-language-core-pack");Fe()});function wt(e,t){let{typescript:n,language:r,languageService:i,getFileId:a}=this,l=r.scripts.get(a(e));if(!(l?.generated?.root instanceof Zn.VueVirtualCode))return;let c=l.generated.root,o=i.getProgram().getTypeChecker(),f=ce(n,i,c,"__VLS_components");if(!f)return[];let u=Ke(n,i,c,f,e,t);if(!u)return[];let d=new Map;for(let p of u.getCallSignatures()){let g=p.parameters[0];if(g){let S=o.getTypeOfSymbolAtLocation(g,f.node).getProperties();for(let h of S){let y=h.name,b=!(h.flags&n.SymbolFlags.Optional)||void 0,{content:P,deprecated:O}=Xn(h.getDocumentationComment(o),h.getJsDocTags());d.set(y,{name:y,required:b,deprecated:O,commentMarkdown:P})}}}for(let p of u.getConstructSignatures()){let m=p.getReturnType().getProperty("$props");if(m){let h=o.getTypeOfSymbolAtLocation(m,f.node).getProperties();for(let y of h){if(y.flags&n.SymbolFlags.Method)continue;let b=y.name,P=!(y.flags&n.SymbolFlags.Optional)||void 0,{content:O,deprecated:L}=Xn(y.getDocumentationComment(o),y.getJsDocTags());d.set(b,{name:b,required:P,deprecated:L,commentMarkdown:O})}}}return[...d.values()]}function Xn(e,t){let n=ca(e),r=la(t),i=[n,r].filter(l=>!!l).join(`

`),a=t.some(l=>l.name==="deprecated");return{content:i,deprecated:a}}function ca(e){return e.map(t=>{switch(t.kind){case"keyword":return`\`${t.text}\``;case"functionName":return`**${t.text}**`;default:return t.text}}).join("")}function la(e){return e.map(t=>{let n=`*@${t.name}*`,r=t.text?.map(i=>i.kind==="parameterName"?`\`${i.text}\``:i.text).join("")||"";return`${n} ${r}`}).join(`

`)}var Zn,er=J(()=>{"use strict";Zn=require("vue-language-core-pack");Fe()});function tr(e,t,n){let{typescript:r,languageService:i,languageServiceHost:a}=this,l=i.getProgram(),c=l?.getSourceFile(t),s=l?.getSourceFile(e);if(!l||!s||!c)return;let o=r.moduleSpecifiers.getModuleSpecifiersWithCacheInfo,f=r.createModuleSpecifierResolutionHost(l,a),u=o(c.symbol,l.getTypeChecker(),a.getCompilationSettings(),s,f,n);for(let d of u.moduleSpecifiers)return d}var nr=J(()=>{"use strict"});function ir(e,t){let{languageService:n,language:r,typescript:i,isTsPlugin:a,getFileId:l}=this,c=r.scripts.get(l(e));if(c?.generated){let m=c.generated.languagePlugin.typescript?.getServiceScript(c.generated.root);if(!m)return;let S=!1;for(let[h,y]of r.maps.forEach(m.code)){for(let[b,P]of y.toGeneratedLocation(t))if((0,rr.isCompletionEnabled)(P.data)){t=b,S=!0;break}if(S)break}if(!S)return;a&&(t+=c.snapshot.getLength())}let s=n.getProgram(),o=s.getSourceFile(e);if(!o)return;let f=g(o,o,t);if(!f)return;return s.getTypeChecker().getTypeAtLocation(f).getProperties().map(m=>m.name);function g(m,S,h){let y;return S.forEachChild(b=>{y||(b.end===h&&i.isIdentifier(b)?y=b:b.end>=h&&b.getStart(m)<h&&(y=g(m,b,h)))}),y}}var rr,sr=J(()=>{"use strict";rr=require("vue-language-core-pack")});function or(e,t){let{typescript:n,languageService:r}=this;return n.displayPartsToString(r.getQuickInfoAtPosition(e,t)?.displayParts??[])}var ar=J(()=>{"use strict"});var Mt=q(Dt=>{"use strict";Object.defineProperty(Dt,"__esModule",{value:!0});Dt.decorateProgram=fa;var le=gt(),Nt=qe();function fa(e,t){let n=t.emit,r=t.getSyntacticDiagnostics,i=t.getSemanticDiagnostics,a=t.getGlobalDiagnostics,l=t.getSourceFileByPath,c=t.getBindAndCheckDiagnostics;t.emit=(...s)=>{let o=n(...s);return{...o,diagnostics:o.diagnostics.map(f=>(0,le.transformDiagnostic)(e,f,t,!0)).filter(f=>!!f)}},t.getSyntacticDiagnostics=(s,o)=>{if(s){let[f,u,d]=(0,Nt.getServiceScript)(e,s.fileName),p=u?t.getSourceFile(u.id):s;return r(p,o).map(g=>(0,le.transformDiagnostic)(e,g,t,!0)).filter(g=>!!g).filter(g=>!f||!g.file||e.scripts.get(g.file.fileName)===d)}else return r(void 0,o).map(f=>(0,le.transformDiagnostic)(e,f,t,!0)).filter(f=>!!f)},t.getSemanticDiagnostics=(s,o)=>{if(s){let[f,u,d]=(0,Nt.getServiceScript)(e,s.fileName),p=u?t.getSourceFile(u.id):s;return i(p,o).map(g=>(0,le.transformDiagnostic)(e,g,t,!0)).filter(g=>!!g).filter(g=>!f||!g.file||e.scripts.get(g.file.fileName)===d)}else return i(void 0,o).map(f=>(0,le.transformDiagnostic)(e,f,t,!0)).filter(f=>!!f)},t.getGlobalDiagnostics=s=>a(s).map(o=>(0,le.transformDiagnostic)(e,o,t,!0)).filter(o=>!!o),t.getBindAndCheckDiagnostics=(s,o)=>{if(s){let[f,u,d]=(0,Nt.getServiceScript)(e,s.fileName),p=u?t.getSourceFile(u.id):s;return c(p,o).map(g=>(0,le.transformDiagnostic)(e,g,t,!0)).filter(g=>!!g).filter(g=>!f||e.scripts.get(g.file.fileName)===d)}else return c(void 0,o).map(f=>(0,le.transformDiagnostic)(e,f,t,!0)).filter(f=>!!f)},t.getSourceFileByPath=s=>{let o=l(s);return o&&(0,le.fillSourceFileText)(e,o),o}}});var lr=q(Rt=>{"use strict";Object.defineProperty(Rt,"__esModule",{value:!0});Rt.proxyCreateProgram=ha;var Lt=be(),ua=He(),da=Mt(),pa=je(),ga=(e,t)=>{if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0},ma=(e,t)=>{let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let i of n)if(e[i]!==t[i])return!1;return!0};function ha(e,t,n){let r=new Lt.FileMap(e.sys.useCaseSensitiveFileNames),i=new WeakMap,a,l,c,s;return new Proxy(t,{apply:(o,f,u)=>{let d=u[0];if(cr(!!d.host,"!!options.host"),!a||!l||!c||!ga(d.rootNames,a.rootNames)||!ma(d.options,a.options)){s=e.createModuleResolutionCache(d.host.getCurrentDirectory(),d.host.getCanonicalFileName,d.options),a=d;let S=n(e,d);Array.isArray(S)?l=S:l=S.languagePlugins,c=(0,Lt.createLanguage)([...l,{getLanguageId:pa.resolveFileLanguageId}],new Lt.FileMap(e.sys.useCaseSensitiveFileNames),(h,y)=>{if(y&&!r.has(h)){let P=p.readFile(h);P!==void 0?r.set(h,[void 0,{getChangeRange(){},getLength(){return P.length},getText(O,L){return P.substring(O,L)}}]):r.set(h,[void 0,void 0])}let b=r.get(h)?.[1];b?c.scripts.set(h,b):c.scripts.delete(h)}),"setup"in S&&S.setup?.(c)}let p=d.host,g=l.map(S=>S.typescript?.extraFileExtensions.map(({extension:h})=>`.${h}`)??[]).flat();if(d.host={...p},d.host.getSourceFile=(S,h,y,b)=>{let P=p.getSourceFile(S,h,y,b);if((!r.has(S)||r.get(S)?.[0]!==P)&&(P?r.set(S,[P,{getChangeRange(){},getLength(){return P.text.length},getText(O,L){return P.text.substring(O,L)}}]):r.set(S,[void 0,void 0])),!!P){if(!i.has(P)){let O=c.scripts.get(S);if(cr(!!O,"!!sourceScript"),i.set(P,void 0),O.generated?.languagePlugin.typescript){let{getServiceScript:L,getExtraServiceScripts:R}=O.generated.languagePlugin.typescript,C=L(O.generated.root);if(C){let E;C.preventLeadingOffset?E=C.code.snapshot.getText(0,C.code.snapshot.getLength()):E=P.text.split(`
`).map(x=>" ".repeat(x.length)).join(`
`)+C.code.snapshot.getText(0,C.code.snapshot.getLength());let A=e.createSourceFile(S,E,h,void 0,C.scriptKind);A.version=P.version,i.set(P,A)}R&&console.warn("getExtraServiceScripts() is not available in this use case.")}}return i.get(P)??P}},g.length){d.options.allowArbitraryExtensions=!0;let S=(0,ua.createResolveModuleName)(e,e.sys.getFileSize,p,c.plugins,b=>c.scripts.get(b)),h=p.resolveModuleNameLiterals,y=p.resolveModuleNames;d.host.resolveModuleNameLiterals=(b,P,O,L,...R)=>h&&b.every(C=>!g.some(E=>C.text.endsWith(E)))?h(b,P,O,L,...R):b.map(C=>S(C.text,P,L,s,O)),d.host.resolveModuleNames=(b,P,O,L,R,C)=>y&&b.every(E=>!g.some(A=>E.endsWith(A)))?y(b,P,O,L,R,C):b.map(E=>S(E,P,R,s,L).resolvedModule)}let m=Reflect.apply(o,f,u);return(0,da.decorateProgram)(c,m),m.__volar__={language:c},m}})}function cr(e,t){if(!e)throw console.error(t),new Error(t)}});var kt=q((Pl,ur)=>{"use strict";function ie(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function fr(e,t){for(var n="",r=0,i=-1,a=0,l,c=0;c<=e.length;++c){if(c<e.length)l=e.charCodeAt(c);else{if(l===47)break;l=47}if(l===47){if(!(i===c-1||a===1))if(i!==c-1&&a===2){if(n.length<2||r!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){var s=n.lastIndexOf("/");if(s!==n.length-1){s===-1?(n="",r=0):(n=n.slice(0,s),r=n.length-1-n.lastIndexOf("/")),i=c,a=0;continue}}else if(n.length===2||n.length===1){n="",r=0,i=c,a=0;continue}}t&&(n.length>0?n+="/..":n="..",r=2)}else n.length>0?n+="/"+e.slice(i+1,c):n=e.slice(i+1,c),r=c-i-1;i=c,a=0}else l===46&&a!==-1?++a:a=-1}return n}function Sa(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+e+r:r}var xe={resolve:function(){for(var t="",n=!1,r,i=arguments.length-1;i>=-1&&!n;i--){var a;i>=0?a=arguments[i]:(r===void 0&&(r=process.cwd()),a=r),ie(a),a.length!==0&&(t=a+"/"+t,n=a.charCodeAt(0)===47)}return t=fr(t,!n),n?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(t){if(ie(t),t.length===0)return".";var n=t.charCodeAt(0)===47,r=t.charCodeAt(t.length-1)===47;return t=fr(t,!n),t.length===0&&!n&&(t="."),t.length>0&&r&&(t+="/"),n?"/"+t:t},isAbsolute:function(t){return ie(t),t.length>0&&t.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var t,n=0;n<arguments.length;++n){var r=arguments[n];ie(r),r.length>0&&(t===void 0?t=r:t+="/"+r)}return t===void 0?".":xe.normalize(t)},relative:function(t,n){if(ie(t),ie(n),t===n||(t=xe.resolve(t),n=xe.resolve(n),t===n))return"";for(var r=1;r<t.length&&t.charCodeAt(r)===47;++r);for(var i=t.length,a=i-r,l=1;l<n.length&&n.charCodeAt(l)===47;++l);for(var c=n.length,s=c-l,o=a<s?a:s,f=-1,u=0;u<=o;++u){if(u===o){if(s>o){if(n.charCodeAt(l+u)===47)return n.slice(l+u+1);if(u===0)return n.slice(l+u)}else a>o&&(t.charCodeAt(r+u)===47?f=u:u===0&&(f=0));break}var d=t.charCodeAt(r+u),p=n.charCodeAt(l+u);if(d!==p)break;d===47&&(f=u)}var g="";for(u=r+f+1;u<=i;++u)(u===i||t.charCodeAt(u)===47)&&(g.length===0?g+="..":g+="/..");return g.length>0?g+n.slice(l+f):(l+=f,n.charCodeAt(l)===47&&++l,n.slice(l))},_makeLong:function(t){return t},dirname:function(t){if(ie(t),t.length===0)return".";for(var n=t.charCodeAt(0),r=n===47,i=-1,a=!0,l=t.length-1;l>=1;--l)if(n=t.charCodeAt(l),n===47){if(!a){i=l;break}}else a=!1;return i===-1?r?"/":".":r&&i===1?"//":t.slice(0,i)},basename:function(t,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');ie(t);var r=0,i=-1,a=!0,l;if(n!==void 0&&n.length>0&&n.length<=t.length){if(n.length===t.length&&n===t)return"";var c=n.length-1,s=-1;for(l=t.length-1;l>=0;--l){var o=t.charCodeAt(l);if(o===47){if(!a){r=l+1;break}}else s===-1&&(a=!1,s=l+1),c>=0&&(o===n.charCodeAt(c)?--c===-1&&(i=l):(c=-1,i=s))}return r===i?i=s:i===-1&&(i=t.length),t.slice(r,i)}else{for(l=t.length-1;l>=0;--l)if(t.charCodeAt(l)===47){if(!a){r=l+1;break}}else i===-1&&(a=!1,i=l+1);return i===-1?"":t.slice(r,i)}},extname:function(t){ie(t);for(var n=-1,r=0,i=-1,a=!0,l=0,c=t.length-1;c>=0;--c){var s=t.charCodeAt(c);if(s===47){if(!a){r=c+1;break}continue}i===-1&&(a=!1,i=c+1),s===46?n===-1?n=c:l!==1&&(l=1):n!==-1&&(l=-1)}return n===-1||i===-1||l===0||l===1&&n===i-1&&n===r+1?"":t.slice(n,i)},format:function(t){if(t===null||typeof t!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return Sa("/",t)},parse:function(t){ie(t);var n={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return n;var r=t.charCodeAt(0),i=r===47,a;i?(n.root="/",a=1):a=0;for(var l=-1,c=0,s=-1,o=!0,f=t.length-1,u=0;f>=a;--f){if(r=t.charCodeAt(f),r===47){if(!o){c=f+1;break}continue}s===-1&&(o=!1,s=f+1),r===46?l===-1?l=f:u!==1&&(u=1):l!==-1&&(u=-1)}return l===-1||s===-1||u===0||u===1&&l===s-1&&l===c+1?s!==-1&&(c===0&&i?n.base=n.name=t.slice(1,s):n.base=n.name=t.slice(c,s)):(c===0&&i?(n.name=t.slice(1,l),n.base=t.slice(1,s)):(n.name=t.slice(c,l),n.base=t.slice(c,s)),n.ext=t.slice(l,s)),c>0?n.dir=t.slice(0,c-1):i&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};xe.posix=xe;ur.exports=xe});var pr=q(It=>{"use strict";Object.defineProperty(It,"__esModule",{value:!0});It.createLanguageServiceHost=ba;var Oe=be(),ya=kt(),va=He();function ba(e,t,n,r,i){let a=new Oe.FileMap(t.useCaseSensitiveFileNames),l,c=0,s=new Oe.FileMap(t.useCaseSensitiveFileNames),o=new Oe.FileMap(t.useCaseSensitiveFileNames),f=new Oe.FileMap(t.useCaseSensitiveFileNames),u=new Set,d=new Set,p={...t,getCurrentDirectory(){return i.getCurrentDirectory()},useCaseSensitiveFileNames(){return t.useCaseSensitiveFileNames},getNewLine(){return t.newLine},getTypeRootsVersion:()=>"version"in t?t.version:-1,getDirectories(y){return t.getDirectories(y)},readDirectory(y,b,P,O,L){let R=new Set(b);for(let C of n.plugins)for(let E of C.typescript?.extraFileExtensions??[])R.add("."+E.extension);return b=[...R],t.readDirectory(y,b,P,O,L)},getCompilationSettings(){let y=i.getCompilationSettings();return n.plugins.some(b=>b.typescript?.extraFileExtensions.length)&&(y.allowNonTsExtensions??=!0,y.allowNonTsExtensions||console.warn("`allowNonTsExtensions` must be `true`.")),y},getLocalizedDiagnosticMessages:i.getLocalizedDiagnosticMessages,getProjectReferences:i.getProjectReferences,getDefaultLibFileName:y=>{try{return e.getDefaultLibFilePath(y)}catch{return`/node_modules/typescript/lib/${e.getDefaultLibFileName(y)}`}},readFile(y){let b=S(y);if(b)return b.getText(0,b.getLength())},directoryExists(y){return m(),o.has(y)?!0:t.directoryExists(y)},fileExists(y){return h(y)!==""},getProjectVersion(){return m(),c+("version"in t?`:${t.version}`:"")},getScriptFileNames(){return m(),[...s.keys()]},getScriptKind(y){if(m(),f.has(y))return f.get(y).scriptKind;let b=n.scripts.get(r(y));if(b?.generated){let P=b.generated.languagePlugin.typescript?.getServiceScript(b.generated.root);if(P)return P.scriptKind}switch(ya.extname(y)){case".js":case".cjs":case".mjs":return e.ScriptKind.JS;case".jsx":return e.ScriptKind.JSX;case".ts":case".cts":case".mts":return e.ScriptKind.TS;case".tsx":return e.ScriptKind.TSX;case".json":return e.ScriptKind.JSON;default:return e.ScriptKind.Unknown}},getScriptVersion:h,getScriptSnapshot:S};for(let y of n.plugins)y.typescript?.resolveLanguageServiceHost&&(p=y.typescript.resolveLanguageServiceHost(p));if(n.plugins.some(y=>y.typescript?.extraFileExtensions.length)){let y=e.createModuleResolutionCache(p.getCurrentDirectory(),p.useCaseSensitiveFileNames?.()?O=>O:O=>O.toLowerCase(),p.getCompilationSettings()),b=(0,va.createResolveModuleName)(e,t.getFileSize,p,n.plugins,O=>n.scripts.get(r(O))),P="version"in t?t.version:void 0;p.resolveModuleNameLiterals=(O,L,R,C,E)=>("version"in t&&P!==t.version&&(P=t.version,y.clear()),O.map(A=>b(A.text,L,C,y,R,E.impliedNodeFormat))),p.resolveModuleNames=(O,L,R,C,E)=>("version"in t&&P!==t.version&&(P=t.version,y.clear()),O.map(A=>b(A,L,E,y,C).resolvedModule)),p.getModuleResolutionCache=()=>y}return{languageServiceHost:p,getExtraServiceScript:g};function g(y){return m(),f.get(y)}function m(){let y=i.getProjectVersion?.();if(!(y===void 0||y!==l))return;l=y,f.clear();let P=new Set,O=new Set,L=new Set;for(let R of i.getScriptFileNames()){let C=n.scripts.get(r(R));if(C?.generated){let E=C.generated.languagePlugin.typescript?.getServiceScript(C.generated.root);E&&(P.add(E.code.snapshot),L.add(R));for(let A of C.generated.languagePlugin.typescript?.getExtraServiceScripts?.(R,C.generated.root)??[])P.add(A.code.snapshot),L.add(A.fileName),f.set(A.fileName,A);for(let A of(0,Oe.forEachEmbeddedCode)(C.generated.root))O.add(A.snapshot)}else L.add(R)}dr(u,P)?dr(d,O)&&c++:c++,u=P,d=O,s.clear(),o.clear();for(let R of L){s.set(R,!0);let C=R.split("/");for(let E=1;E<C.length;E++){let A=C.slice(0,E).join("/");o.set(A,!0)}}}function S(y){if(m(),f.has(y))return f.get(y).code.snapshot;let b=n.scripts.get(r(y));if(b?.generated){let P=b.generated.languagePlugin.typescript?.getServiceScript(b.generated.root);if(P)return P.code.snapshot}else if(b)return b.snapshot}function h(y){m(),a.has(y)||a.set(y,{lastVersion:0,map:new WeakMap});let b=a.get(y);if(f.has(y)){let L=f.get(y).code.snapshot;return b.map.has(L)||b.map.set(L,b.lastVersion++),b.map.get(L).toString()}let P=n.scripts.get(r(y));if(P?.generated){let L=P.generated.languagePlugin.typescript?.getServiceScript(P.generated.root);if(L)return b.map.has(L.code.snapshot)||b.map.set(L.code.snapshot,b.lastVersion++),b.map.get(L.code.snapshot).toString()}let O=n.scripts.get(r(y),!1);return O&&!O.generated?(b.map.has(O.snapshot)||b.map.set(O.snapshot,b.lastVersion++),b.map.get(O.snapshot).toString()):t.fileExists(y)?t.getModifiedTime?.(y)?.valueOf().toString()??"0":""}}function dr(e,t){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}});var jt=q(G=>{"use strict";Object.defineProperty(G,"__esModule",{value:!0});G.every=xa;G.findIndex=Ea;G.indexOfAnyCharCode=Fa;G.map=Oa;G.flatten=Aa;G.flatMap=Ta;G.some=_a;G.sort=Na;G.lastOrUndefined=Da;G.last=Ma;G.equateStringsCaseInsensitive=Ia;G.equateStringsCaseSensitive=ja;G.compareStringsCaseSensitive=vr;G.getStringComparer=za;G.endsWith=Ha;G.stringContains=Ua;G.createGetCanonicalFileName=$a;G.startsWith=Ga;var Ca=[];function xa(e,t){if(e){for(let n=0;n<e.length;n++)if(!t(e[n],n))return!1}return!0}function Ea(e,t,n){if(e===void 0)return-1;for(let r=n??0;r<e.length;r++)if(t(e[r],r))return r;return-1}function Pa(e,t,n=yr){if(e){for(let r of e)if(n(r,t))return!0}return!1}function Fa(e,t,n){for(let r=n||0;r<e.length;r++)if(Pa(t,e.charCodeAt(r)))return r;return-1}function Oa(e,t){let n;if(e){n=[];for(let r=0;r<e.length;r++)n.push(t(e[r],r))}return n}function Aa(e){let t=[];for(let n of e)n&&(Sr(n)?hr(t,n):t.push(n));return t}function Ta(e,t){let n;if(e)for(let r=0;r<e.length;r++){let i=t(e[r],r);i&&(Sr(i)?n=hr(n,i):n=wa(n,i))}return n||Ca}function _a(e,t){if(e)if(t){for(let n of e)if(t(n))return!0}else return e.length>0;return!1}function wa(e,t){return t===void 0?e:e===void 0?[t]:(e.push(t),e)}function gr(e,t){return t<0?e.length+t:t}function hr(e,t,n,r){if(t===void 0||t.length===0)return e;if(e===void 0)return t.slice(n,r);n=n===void 0?0:gr(t,n),r=r===void 0?t.length:gr(t,r);for(let i=n;i<r&&i<t.length;i++)t[i]!==void 0&&e.push(t[i]);return e}function Na(e,t){return e.length===0?e:e.slice().sort(t)}function Da(e){return e===void 0||e.length===0?void 0:e[e.length-1]}function Ma(e){return e[e.length-1]}function Sr(e){return Array.isArray?Array.isArray(e):e instanceof Array}function La(e){return e}function Ra(e){return e.toLowerCase()}var mr=/[^\u0130\u0131\u00DFa-z0-9\\/:\-_\. ]+/g;function ka(e){return mr.test(e)?e.replace(mr,Ra):e}function yr(e,t){return e===t}function Ia(e,t){return e===t||e!==void 0&&t!==void 0&&e.toUpperCase()===t.toUpperCase()}function ja(e,t){return yr(e,t)}function qa(e,t){return e===t?0:e===void 0?-1:t===void 0?1:e<t?-1:1}function Va(e,t){return e===t?0:e===void 0?-1:t===void 0?1:(e=e.toUpperCase(),t=t.toUpperCase(),e<t?-1:e>t?1:0)}function vr(e,t){return qa(e,t)}function za(e){return e?Va:vr}function Ha(e,t){let n=e.length-t.length;return n>=0&&e.indexOf(t,n)===n}function Ua(e,t){return e.indexOf(t)!==-1}function $a(e){return e?La:ka}function Ga(e,t){return e.lastIndexOf(t,0)===0}});var Or=q(U=>{"use strict";Object.defineProperty(U,"__esModule",{value:!0});U.directorySeparator=void 0;U.isRootedDiskPath=Ja;U.hasExtension=Ya;U.fileExtensionIsOneOf=Xa;U.getDirectoryPath=ec;U.combinePaths=Qe;U.getNormalizedPathComponents=sc;U.normalizePath=oc;U.removeTrailingDirectorySeparator=Xe;U.containsPath=ac;var se=jt();U.directorySeparator="/";var Ba="\\",br="://",Ka=/\\/g;function Wa(e){return e===47||e===92}function Ja(e){return Pr(e)>0}function Ya(e){return(0,se.stringContains)(Fr(e),".")}function Qa(e,t){return e.length>t.length&&(0,se.endsWith)(e,t)}function Xa(e,t){for(let n of t)if(Qa(e,n))return!0;return!1}function qt(e){return e.length>0&&Wa(e.charCodeAt(e.length-1))}function Cr(e){return e>=97&&e<=122||e>=65&&e<=90}function Za(e,t){let n=e.charCodeAt(t);if(n===58)return t+1;if(n===37&&e.charCodeAt(t+1)===51){let r=e.charCodeAt(t+2);if(r===97||r===65)return t+3}return-1}function Pr(e){if(!e)return 0;let t=e.charCodeAt(0);if(t===47||t===92){if(e.charCodeAt(1)!==t)return 1;let r=e.indexOf(t===47?U.directorySeparator:Ba,2);return r<0?e.length:r+1}if(Cr(t)&&e.charCodeAt(1)===58){let r=e.charCodeAt(2);if(r===47||r===92)return 3;if(e.length===2)return 2}let n=e.indexOf(br);if(n!==-1){let r=n+br.length,i=e.indexOf(U.directorySeparator,r);if(i!==-1){let a=e.slice(0,n),l=e.slice(r,i);if(a==="file"&&(l===""||l==="localhost")&&Cr(e.charCodeAt(i+1))){let c=Za(e,i+2);if(c!==-1){if(e.charCodeAt(c)===47)return~(c+1);if(c===e.length)return~c}}return~(i+1)}return~e.length}return 0}function Ae(e){let t=Pr(e);return t<0?~t:t}function ec(e){e=Te(e);let t=Ae(e);return t===e.length?e:(e=Xe(e),e.slice(0,Math.max(t,e.lastIndexOf(U.directorySeparator))))}function Fr(e,t,n){if(e=Te(e),Ae(e)===e.length)return"";e=Xe(e);let i=e.slice(Math.max(Ae(e),e.lastIndexOf(U.directorySeparator)+1)),a=t!==void 0&&n!==void 0?nc(i,t,n):void 0;return a?i.slice(0,i.length-a.length):i}function xr(e,t,n){if((0,se.startsWith)(t,".")||(t="."+t),e.length>=t.length&&e.charCodeAt(e.length-t.length)===46){let r=e.slice(e.length-t.length);if(n(r,t))return r}}function tc(e,t,n){if(typeof t=="string")return xr(e,t,n)||"";for(let r of t){let i=xr(e,r,n);if(i)return i}return""}function nc(e,t,n){if(t)return tc(Xe(e),t,n?se.equateStringsCaseInsensitive:se.equateStringsCaseSensitive);let r=Fr(e),i=r.lastIndexOf(".");return i>=0?r.substring(i):""}function rc(e,t){let n=e.substring(0,t),r=e.substring(t).split(U.directorySeparator);return r.length&&!(0,se.lastOrUndefined)(r)&&r.pop(),[n,...r]}function Je(e,t=""){return e=Qe(t,e),rc(e,Ae(e))}function ic(e){return e.length===0?"":(e[0]&&Vt(e[0]))+e.slice(1).join(U.directorySeparator)}function Te(e){return e.indexOf("\\")!==-1?e.replace(Ka,U.directorySeparator):e}function Ye(e){if(!(0,se.some)(e))return[];let t=[e[0]];for(let n=1;n<e.length;n++){let r=e[n];if(r&&r!=="."){if(r===".."){if(t.length>1){if(t[t.length-1]!==".."){t.pop();continue}}else if(t[0])continue}t.push(r)}}return t}function Qe(e,...t){e&&(e=Te(e));for(let n of t)n&&(n=Te(n),!e||Ae(n)!==0?e=n:e=Vt(e)+n);return e}function sc(e,t){return Ye(Je(e,t))}function oc(e){if(e=Te(e),!Er.test(e))return e;let t=e.replace(/\/\.\//g,"/").replace(/^\.\//,"");if(t!==e&&(e=t,!Er.test(e)))return e;let n=ic(Ye(Je(e)));return n&&qt(e)?Vt(n):n}function Xe(e){return qt(e)?e.substr(0,e.length-1):e}function Vt(e){return qt(e)?e:e+U.directorySeparator}var Er=/(?:\/\/)|(?:^|\/)\.\.?(?:$|\/)/;function ac(e,t,n,r){if(typeof n=="string"?(e=Qe(n,e),t=Qe(n,t)):typeof n=="boolean"&&(r=n),e===void 0||t===void 0)return!1;if(e===t)return!0;let i=Ye(Je(e)),a=Ye(Je(t));if(a.length<i.length)return!1;let l=r?se.equateStringsCaseInsensitive:se.equateStringsCaseSensitive;for(let c=0;c<i.length;c++)if(!(c===0?se.equateStringsCaseInsensitive:l)(i[c],a[c]))return!1;return!0}});var Dr=q(Gt=>{"use strict";Object.defineProperty(Gt,"__esModule",{value:!0});Gt.matchFiles=gc;var Q=jt(),H=Or(),Ar=/[^\w\s\/]/g,cc=[42,63],lc=["node_modules","bower_components","jspm_packages"],Ut=`(?!(${lc.join("|")})(/|$))`,Tr={singleAsteriskRegexFragment:"([^./]|(\\.(?!min\\.js$))?)*",doubleAsteriskRegexFragment:`(/${Ut}[^/.][^/]*)*?`,replaceWildcardCharacter:e=>$t(e,Tr.singleAsteriskRegexFragment)},_r={singleAsteriskRegexFragment:"[^/]*",doubleAsteriskRegexFragment:`(/${Ut}[^/.][^/]*)*?`,replaceWildcardCharacter:e=>$t(e,_r.singleAsteriskRegexFragment)},wr={singleAsteriskRegexFragment:"[^/]*",doubleAsteriskRegexFragment:"(/.+?)?",replaceWildcardCharacter:e=>$t(e,wr.singleAsteriskRegexFragment)},fc={files:Tr,directories:_r,exclude:wr};function zt(e,t,n){let r=Nr(e,t,n);return!r||!r.length?void 0:`^(${r.map(l=>`(${l})`).join("|")})${n==="exclude"?"($|/)":"$"}`}function Nr(e,t,n){if(!(e===void 0||e.length===0))return(0,Q.flatMap)(e,r=>r&&dc(r,t,n,fc[n]))}function uc(e){return!/[.*?]/.test(e)}function dc(e,t,n,{singleAsteriskRegexFragment:r,doubleAsteriskRegexFragment:i,replaceWildcardCharacter:a}){let l="",c=!1,s=(0,H.getNormalizedPathComponents)(e,t),o=(0,Q.last)(s);if(n!=="exclude"&&o==="**")return;s[0]=(0,H.removeTrailingDirectorySeparator)(s[0]),uc(o)&&s.push("**","*");let f=0;for(let u of s){if(u==="**")l+=i;else if(n==="directories"&&(l+="(",f++),c&&(l+=H.directorySeparator),n!=="exclude"){let d="";u.charCodeAt(0)===42?(d+="([^./]"+r+")?",u=u.substr(1)):u.charCodeAt(0)===63&&(d+="[^./]",u=u.substr(1)),d+=u.replace(Ar,a),d!==u&&(l+=Ut),l+=d}else l+=u.replace(Ar,a);c=!0}for(;f>0;)l+=")?",f--;return l}function $t(e,t){return e==="*"?t:e==="?"?"[^/]":"\\"+e}function pc(e,t,n,r,i){e=(0,H.normalizePath)(e),i=(0,H.normalizePath)(i);let a=(0,H.combinePaths)(i,e);return{includeFilePatterns:(0,Q.map)(Nr(n,a,"files"),l=>`^${l}$`),includeFilePattern:zt(n,a,"files"),includeDirectoryPattern:zt(n,a,"directories"),excludePattern:zt(t,a,"exclude"),basePaths:mc(e,n,r)}}function Ht(e,t){return new RegExp(e,t?"":"i")}function gc(e,t,n,r,i,a,l,c,s){e=(0,H.normalizePath)(e),a=(0,H.normalizePath)(a);let o=pc(e,n,r,i,a),f=o.includeFilePatterns&&o.includeFilePatterns.map(h=>Ht(h,i)),u=o.includeDirectoryPattern&&Ht(o.includeDirectoryPattern,i),d=o.excludePattern&&Ht(o.excludePattern,i),p=f?f.map(()=>[]):[[]],g=new Map,m=(0,Q.createGetCanonicalFileName)(i);for(let h of o.basePaths)S(h,(0,H.combinePaths)(a,h),l);return(0,Q.flatten)(p);function S(h,y,b){let P=m(s(y));if(g.has(P))return;g.set(P,!0);let{files:O,directories:L}=c(h);for(let R of(0,Q.sort)(O,Q.compareStringsCaseSensitive)){let C=(0,H.combinePaths)(h,R),E=(0,H.combinePaths)(y,R);if(!(t&&!(0,H.fileExtensionIsOneOf)(C,t))&&!(d&&d.test(E)))if(!f)p[0].push(C);else{let A=(0,Q.findIndex)(f,x=>x.test(E));A!==-1&&p[A].push(C)}}if(!(b!==void 0&&(b--,b===0)))for(let R of(0,Q.sort)(L,Q.compareStringsCaseSensitive)){let C=(0,H.combinePaths)(h,R),E=(0,H.combinePaths)(y,R);(!u||u.test(E))&&(!d||!d.test(E))&&S(C,E,b)}}}function mc(e,t,n){let r=[e];if(t){let i=[];for(let a of t){let l=(0,H.isRootedDiskPath)(a)?a:(0,H.normalizePath)((0,H.combinePaths)(e,a));i.push(hc(l))}i.sort((0,Q.getStringComparer)(!n));for(let a of i)(0,Q.every)(r,l=>!(0,H.containsPath)(l,a,e,!n))&&r.push(a)}return r}function hc(e){let t=(0,Q.indexOfAnyCharCode)(e,cc);return t<0?(0,H.hasExtension)(e)?(0,H.removeTrailingDirectorySeparator)((0,H.getDirectoryPath)(e)):e:e.substring(0,e.lastIndexOf(H.directorySeparator,t))}});var Lr={};tt(Lr,{URI:()=>Sc,Utils:()=>yc});var Mr,Sc,yc,Rr=J(()=>{"use strict";(()=>{"use strict";var e={470:i=>{function a(s){if(typeof s!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(s))}function l(s,o){for(var f,u="",d=0,p=-1,g=0,m=0;m<=s.length;++m){if(m<s.length)f=s.charCodeAt(m);else{if(f===47)break;f=47}if(f===47){if(!(p===m-1||g===1))if(p!==m-1&&g===2){if(u.length<2||d!==2||u.charCodeAt(u.length-1)!==46||u.charCodeAt(u.length-2)!==46){if(u.length>2){var S=u.lastIndexOf("/");if(S!==u.length-1){S===-1?(u="",d=0):d=(u=u.slice(0,S)).length-1-u.lastIndexOf("/"),p=m,g=0;continue}}else if(u.length===2||u.length===1){u="",d=0,p=m,g=0;continue}}o&&(u.length>0?u+="/..":u="..",d=2)}else u.length>0?u+="/"+s.slice(p+1,m):u=s.slice(p+1,m),d=m-p-1;p=m,g=0}else f===46&&g!==-1?++g:g=-1}return u}var c={resolve:function(){for(var s,o="",f=!1,u=arguments.length-1;u>=-1&&!f;u--){var d;u>=0?d=arguments[u]:(s===void 0&&(s=process.cwd()),d=s),a(d),d.length!==0&&(o=d+"/"+o,f=d.charCodeAt(0)===47)}return o=l(o,!f),f?o.length>0?"/"+o:"/":o.length>0?o:"."},normalize:function(s){if(a(s),s.length===0)return".";var o=s.charCodeAt(0)===47,f=s.charCodeAt(s.length-1)===47;return(s=l(s,!o)).length!==0||o||(s="."),s.length>0&&f&&(s+="/"),o?"/"+s:s},isAbsolute:function(s){return a(s),s.length>0&&s.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var s,o=0;o<arguments.length;++o){var f=arguments[o];a(f),f.length>0&&(s===void 0?s=f:s+="/"+f)}return s===void 0?".":c.normalize(s)},relative:function(s,o){if(a(s),a(o),s===o||(s=c.resolve(s))===(o=c.resolve(o)))return"";for(var f=1;f<s.length&&s.charCodeAt(f)===47;++f);for(var u=s.length,d=u-f,p=1;p<o.length&&o.charCodeAt(p)===47;++p);for(var g=o.length-p,m=d<g?d:g,S=-1,h=0;h<=m;++h){if(h===m){if(g>m){if(o.charCodeAt(p+h)===47)return o.slice(p+h+1);if(h===0)return o.slice(p+h)}else d>m&&(s.charCodeAt(f+h)===47?S=h:h===0&&(S=0));break}var y=s.charCodeAt(f+h);if(y!==o.charCodeAt(p+h))break;y===47&&(S=h)}var b="";for(h=f+S+1;h<=u;++h)h!==u&&s.charCodeAt(h)!==47||(b.length===0?b+="..":b+="/..");return b.length>0?b+o.slice(p+S):(p+=S,o.charCodeAt(p)===47&&++p,o.slice(p))},_makeLong:function(s){return s},dirname:function(s){if(a(s),s.length===0)return".";for(var o=s.charCodeAt(0),f=o===47,u=-1,d=!0,p=s.length-1;p>=1;--p)if((o=s.charCodeAt(p))===47){if(!d){u=p;break}}else d=!1;return u===-1?f?"/":".":f&&u===1?"//":s.slice(0,u)},basename:function(s,o){if(o!==void 0&&typeof o!="string")throw new TypeError('"ext" argument must be a string');a(s);var f,u=0,d=-1,p=!0;if(o!==void 0&&o.length>0&&o.length<=s.length){if(o.length===s.length&&o===s)return"";var g=o.length-1,m=-1;for(f=s.length-1;f>=0;--f){var S=s.charCodeAt(f);if(S===47){if(!p){u=f+1;break}}else m===-1&&(p=!1,m=f+1),g>=0&&(S===o.charCodeAt(g)?--g==-1&&(d=f):(g=-1,d=m))}return u===d?d=m:d===-1&&(d=s.length),s.slice(u,d)}for(f=s.length-1;f>=0;--f)if(s.charCodeAt(f)===47){if(!p){u=f+1;break}}else d===-1&&(p=!1,d=f+1);return d===-1?"":s.slice(u,d)},extname:function(s){a(s);for(var o=-1,f=0,u=-1,d=!0,p=0,g=s.length-1;g>=0;--g){var m=s.charCodeAt(g);if(m!==47)u===-1&&(d=!1,u=g+1),m===46?o===-1?o=g:p!==1&&(p=1):o!==-1&&(p=-1);else if(!d){f=g+1;break}}return o===-1||u===-1||p===0||p===1&&o===u-1&&o===f+1?"":s.slice(o,u)},format:function(s){if(s===null||typeof s!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof s);return function(o,f){var u=f.dir||f.root,d=f.base||(f.name||"")+(f.ext||"");return u?u===f.root?u+d:u+"/"+d:d}(0,s)},parse:function(s){a(s);var o={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return o;var f,u=s.charCodeAt(0),d=u===47;d?(o.root="/",f=1):f=0;for(var p=-1,g=0,m=-1,S=!0,h=s.length-1,y=0;h>=f;--h)if((u=s.charCodeAt(h))!==47)m===-1&&(S=!1,m=h+1),u===46?p===-1?p=h:y!==1&&(y=1):p!==-1&&(y=-1);else if(!S){g=h+1;break}return p===-1||m===-1||y===0||y===1&&p===m-1&&p===g+1?m!==-1&&(o.base=o.name=g===0&&d?s.slice(1,m):s.slice(g,m)):(g===0&&d?(o.name=s.slice(1,p),o.base=s.slice(1,m)):(o.name=s.slice(g,p),o.base=s.slice(g,m)),o.ext=s.slice(p,m)),g>0?o.dir=s.slice(0,g-1):d&&(o.dir="/"),o},sep:"/",delimiter:":",win32:null,posix:null};c.posix=c,i.exports=c}},t={};function n(i){var a=t[i];if(a!==void 0)return a.exports;var l=t[i]={exports:{}};return e[i](l,l.exports,n),l.exports}n.d=(i,a)=>{for(var l in a)n.o(a,l)&&!n.o(i,l)&&Object.defineProperty(i,l,{enumerable:!0,get:a[l]})},n.o=(i,a)=>Object.prototype.hasOwnProperty.call(i,a),n.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;n.r(r),n.d(r,{URI:()=>d,Utils:()=>A}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);let a=/^\w[\w\d+.-]*$/,l=/^\//,c=/^\/\//;function s(x,v){if(!x.scheme&&v)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${x.authority}", path: "${x.path}", query: "${x.query}", fragment: "${x.fragment}"}`);if(x.scheme&&!a.test(x.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(x.path){if(x.authority){if(!l.test(x.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(c.test(x.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}let o="",f="/",u=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{static isUri(v){return v instanceof d||!!v&&typeof v.authority=="string"&&typeof v.fragment=="string"&&typeof v.path=="string"&&typeof v.query=="string"&&typeof v.scheme=="string"&&typeof v.fsPath=="string"&&typeof v.with=="function"&&typeof v.toString=="function"}scheme;authority;path;query;fragment;constructor(v,_,F,D,k,M=!1){typeof v=="object"?(this.scheme=v.scheme||o,this.authority=v.authority||o,this.path=v.path||o,this.query=v.query||o,this.fragment=v.fragment||o):(this.scheme=function($,Z){return $||Z?$:"file"}(v,M),this.authority=_||o,this.path=function($,Z){switch($){case"https":case"http":case"file":Z?Z[0]!==f&&(Z=f+Z):Z=f}return Z}(this.scheme,F||o),this.query=D||o,this.fragment=k||o,s(this,M))}get fsPath(){return y(this,!1)}with(v){if(!v)return this;let{scheme:_,authority:F,path:D,query:k,fragment:M}=v;return _===void 0?_=this.scheme:_===null&&(_=o),F===void 0?F=this.authority:F===null&&(F=o),D===void 0?D=this.path:D===null&&(D=o),k===void 0?k=this.query:k===null&&(k=o),M===void 0?M=this.fragment:M===null&&(M=o),_===this.scheme&&F===this.authority&&D===this.path&&k===this.query&&M===this.fragment?this:new g(_,F,D,k,M)}static parse(v,_=!1){let F=u.exec(v);return F?new g(F[2]||o,L(F[4]||o),L(F[5]||o),L(F[7]||o),L(F[9]||o),_):new g(o,o,o,o,o)}static file(v){let _=o;if(i&&(v=v.replace(/\\/g,f)),v[0]===f&&v[1]===f){let F=v.indexOf(f,2);F===-1?(_=v.substring(2),v=f):(_=v.substring(2,F),v=v.substring(F)||f)}return new g("file",_,v,o,o)}static from(v){let _=new g(v.scheme,v.authority,v.path,v.query,v.fragment);return s(_,!0),_}toString(v=!1){return b(this,v)}toJSON(){return this}static revive(v){if(v){if(v instanceof d)return v;{let _=new g(v);return _._formatted=v.external,_._fsPath=v._sep===p?v.fsPath:null,_}}return v}}let p=i?1:void 0;class g extends d{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=y(this,!1)),this._fsPath}toString(v=!1){return v?b(this,!0):(this._formatted||(this._formatted=b(this,!1)),this._formatted)}toJSON(){let v={$mid:1};return this._fsPath&&(v.fsPath=this._fsPath,v._sep=p),this._formatted&&(v.external=this._formatted),this.path&&(v.path=this.path),this.scheme&&(v.scheme=this.scheme),this.authority&&(v.authority=this.authority),this.query&&(v.query=this.query),this.fragment&&(v.fragment=this.fragment),v}}let m={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function S(x,v,_){let F,D=-1;for(let k=0;k<x.length;k++){let M=x.charCodeAt(k);if(M>=97&&M<=122||M>=65&&M<=90||M>=48&&M<=57||M===45||M===46||M===95||M===126||v&&M===47||_&&M===91||_&&M===93||_&&M===58)D!==-1&&(F+=encodeURIComponent(x.substring(D,k)),D=-1),F!==void 0&&(F+=x.charAt(k));else{F===void 0&&(F=x.substr(0,k));let $=m[M];$!==void 0?(D!==-1&&(F+=encodeURIComponent(x.substring(D,k)),D=-1),F+=$):D===-1&&(D=k)}}return D!==-1&&(F+=encodeURIComponent(x.substring(D))),F!==void 0?F:x}function h(x){let v;for(let _=0;_<x.length;_++){let F=x.charCodeAt(_);F===35||F===63?(v===void 0&&(v=x.substr(0,_)),v+=m[F]):v!==void 0&&(v+=x[_])}return v!==void 0?v:x}function y(x,v){let _;return _=x.authority&&x.path.length>1&&x.scheme==="file"?`//${x.authority}${x.path}`:x.path.charCodeAt(0)===47&&(x.path.charCodeAt(1)>=65&&x.path.charCodeAt(1)<=90||x.path.charCodeAt(1)>=97&&x.path.charCodeAt(1)<=122)&&x.path.charCodeAt(2)===58?v?x.path.substr(1):x.path[1].toLowerCase()+x.path.substr(2):x.path,i&&(_=_.replace(/\//g,"\\")),_}function b(x,v){let _=v?h:S,F="",{scheme:D,authority:k,path:M,query:$,fragment:Z}=x;if(D&&(F+=D,F+=":"),(k||D==="file")&&(F+=f,F+=f),k){let B=k.indexOf("@");if(B!==-1){let we=k.substr(0,B);k=k.substr(B+1),B=we.lastIndexOf(":"),B===-1?F+=_(we,!1,!1):(F+=_(we.substr(0,B),!1,!1),F+=":",F+=_(we.substr(B+1),!1,!0)),F+="@"}k=k.toLowerCase(),B=k.lastIndexOf(":"),B===-1?F+=_(k,!1,!0):(F+=_(k.substr(0,B),!1,!0),F+=k.substr(B))}if(M){if(M.length>=3&&M.charCodeAt(0)===47&&M.charCodeAt(2)===58){let B=M.charCodeAt(1);B>=65&&B<=90&&(M=`/${String.fromCharCode(B+32)}:${M.substr(3)}`)}else if(M.length>=2&&M.charCodeAt(1)===58){let B=M.charCodeAt(0);B>=65&&B<=90&&(M=`${String.fromCharCode(B+32)}:${M.substr(2)}`)}F+=_(M,!0,!1)}return $&&(F+="?",F+=_($,!1,!1)),Z&&(F+="#",F+=v?Z:S(Z,!1,!1)),F}function P(x){try{return decodeURIComponent(x)}catch{return x.length>3?x.substr(0,3)+P(x.substr(3)):x}}let O=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function L(x){return x.match(O)?x.replace(O,v=>P(v)):x}var R=n(470);let C=R.posix||R,E="/";var A;(function(x){x.joinPath=function(v,..._){return v.with({path:C.join(v.path,..._)})},x.resolvePath=function(v,..._){let F=v.path,D=!1;F[0]!==E&&(F=E+F,D=!0);let k=C.resolve(F,..._);return D&&k[0]===E&&!v.authority&&(k=k.substring(1)),v.with({path:k})},x.dirname=function(v){if(v.path.length===0||v.path===E)return v;let _=C.dirname(v.path);return _.length===1&&_.charCodeAt(0)===46&&(_=""),v.with({path:_})},x.basename=function(v){return C.basename(v.path)},x.extname=function(v){return C.extname(v.path)}})(A||(A={}))})(),Mr=r})();({URI:Sc,Utils:yc}=Mr)});var Ir=q(Bt=>{"use strict";Object.defineProperty(Bt,"__esModule",{value:!0});Bt.createSys=Cc;var ee=kt(),vc=Dr(),bc=(Rr(),nt(Lr)),kr="";function Cc(e,t,n,r){let i=0,a=e?.useCaseSensitiveFileNames??!1,l={name:"",dirs:new Map,files:new Map,requestedRead:!1},c=new Set,s=t.onDidChangeWatchedFiles?.(({changes:C})=>{i++;for(let E of C){let A=bc.URI.parse(E.uri),x=r.asFileName(A),v=ee.dirname(x),_=ee.basename(x),F=E.type===1||E.type===2;O(v,F).files.set(R(_),F?{name:_,stat:{type:1,ctime:Date.now(),mtime:Date.now(),size:-1},requestedStat:!1,requestedText:!1}:{name:_,stat:void 0,text:void 0,requestedStat:!0,requestedText:!0})}});return{dispose(){s?.dispose()},args:e?.args??[],newLine:e?.newLine??`
`,useCaseSensitiveFileNames:a,realpath:e?.realpath,write:e?.write??(()=>{}),writeFile:e?.writeFile??(()=>{}),createDirectory:e?.createDirectory??(()=>{}),exit:e?.exit??(()=>{}),getExecutingFilePath:e?.getExecutingFilePath??(()=>n+"/__fake__.js"),getCurrentDirectory:n,getModifiedTime:d,readFile:f,readDirectory:h,getDirectories:S,resolvePath:o,fileExists:p,directoryExists:u,get version(){return i},async sync(){for(;c.size;)await Promise.all(c);return i}};function o(C){if(e){let E=n();if(kr!==E&&(kr=E,e.directoryExists(E)))try{process.chdir(E)}catch{}return e.resolvePath(C).replace(/\\/g,"/")}return ee.resolve(C).replace(/\\/g,"/")}function f(C,E){C=o(C);let A=ee.dirname(C),x=O(A),v=ee.basename(C);return y(C,E,x),x.files.get(R(v))?.text}function u(C){C=o(C);let E=O(C);if(E.exists===void 0){E.exists=!1;let A=t.fs?.stat(r.asUri(C));if(typeof A=="object"&&"then"in A){let x=A;c.add(x),A.then(v=>{c.delete(x),E.exists=v?.type===2,E.exists&&i++})}else E.exists=A?.type===2}return E.exists}function d(C){C=o(C);let E=m(C);return E.requestedStat||(E.requestedStat=!0,g(C,E)),E.stat?new Date(E.stat.mtime):new Date(0)}function p(C){C=o(C);let E=m(C),A=()=>E.text!==void 0||E.stat?.type===1;return A()?!0:(E.requestedStat||(E.requestedStat=!0,g(C,E)),A())}function g(C,E){let A=t.fs?.stat(r.asUri(C));if(typeof A=="object"&&"then"in A){let x=A;c.add(x),A.then(v=>{c.delete(x),(E.stat?.type!==v?.type||E.stat?.mtime!==v?.mtime)&&i++,E.stat=v})}else E.stat=A}function m(C){C=o(C);let E=ee.dirname(C),A=ee.basename(C),x=O(E),v=x.files.get(R(A));return v||x.files.set(R(A),v={name:A,requestedStat:!1,requestedText:!1}),v}function S(C){return C=o(C),b(C),[...O(C).dirs.values()].filter(A=>A.exists).map(A=>A.name)}function h(C,E,A,x,v){C=o(C);let _=n(),F=(0,vc.matchFiles)(C,E,A,x,a,_,v,D=>{D=o(D),b(D);let k=O(D);return{files:[...k.files.values()].filter(M=>M.stat?.type===1).map(M=>M.name),directories:[...k.dirs.values()].filter(M=>M.exists).map(M=>M.name)}},e?.realpath?D=>e.realpath(D):D=>D);return[...new Set(F)]}function y(C,E,A){let x=ee.basename(C),v=A.files.get(R(x));if(v||A.files.set(R(x),v={name:x,requestedStat:!1,requestedText:!1}),v.requestedText)return;v.requestedText=!0;let _=r.asUri(C),F=t.fs?.readFile(_,E);if(typeof F=="object"&&"then"in F){let D=F;c.add(D),F.then(k=>{c.delete(D),k!==void 0&&(v.text=k,v.stat&&v.stat.mtime++,i++)})}else F!==void 0&&(v.text=F)}function b(C){let E=O(C);if(E.requestedRead)return;E.requestedRead=!0;let A=t.fs?.readDirectory(r.asUri(C||"."));if(typeof A=="object"&&"then"in A){let x=A;c.add(x),A.then(v=>{c.delete(x),P(C,E,v)&&i++})}else P(C,E,A??[])}function P(C,E,A){A=A.filter(([v])=>v!=="."&&v!=="..");let x=!1;for(let[v,_]of A){let F=_;if(F===64){let D=t.fs?.stat(r.asUri(C+"/"+v));if(typeof D=="object"&&"then"in D){let k=D;c.add(k),D.then(M=>{if(c.delete(k),M?.type===1){let $=E.files.get(R(v));$||E.files.set(R(v),$={name:v,requestedStat:!1,requestedText:!1}),(M.type!==$.stat?.type||M.mtime!==$.stat?.mtime)&&i++,$.stat=M,$.requestedStat=!0}else if(M?.type===2){let $=L(E,v);$.exists||($.exists=!0,i++)}})}else D&&(F=D.type)}if(F===1){let D=E.files.get(R(v));D||E.files.set(R(v),D={name:v,requestedStat:!1,requestedText:!1}),D.stat||(D.stat={type:1,mtime:0,ctime:0,size:0},x=!0)}else if(F===2){let D=L(E,v);D.exists||(D.exists=!0,x=!0)}}return x}function O(C,E=!1){let A=[],x=C,v=ee.basename(x),_;for(;_!==x;)_=x,A.push(v),x=ee.dirname(x),v=ee.basename(x);let F=l;for(let D=A.length-1;D>=0;D--){let k=A[D];F=L(F,k),E&&!F.exists&&(F.exists=!0,i++)}return F}function L(C,E){let A=C.dirs.get(R(E));return A||C.dirs.set(R(E),A={name:E,dirs:new Map,files:new Map}),A}function R(C){return a?C:C.toLowerCase()}}});var jr=q(X=>{"use strict";var xc=X&&X.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),ye=X&&X.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&xc(t,e,n)};Object.defineProperty(X,"__esModule",{value:!0});ye(je(),X);ye(ht(),X);ye(yt(),X);ye(Mt(),X);ye(lr(),X);ye(pr(),X);ye(Ir(),X)});var qr=q((Nl,Ec)=>{Ec.exports={name:"@vue/typescript-plugin",version:"2.2.10",license:"MIT",files:["**/*.js","**/*.d.ts"],sideEffects:!1,repository:{type:"git",url:"https://github.com/vuejs/language-tools.git",directory:"packages/typescript-plugin"},dependencies:{"@volar/typescript":"~2.4.11","@vue/language-core":"2.2.10","@vue/shared":"^3.5.0"},devDependencies:{"@types/node":"^22.10.4"}}});function Kt(e,t){return e===1?`${zr}vue-named-pipe-${Vr}-configured-${t}`:`${zr}vue-named-pipe-${Vr}-inferred-${t}`}var Hr,_e,Ur,$r,Gr,Tc,Vr,Pc,zr,Ze,Fc,Oc,Ac,Br=J(()=>{"use strict";Hr=require("vue-language-core-pack"),_e=te(Be()),Ur=te(require("node:fs")),$r=te(require("node:net")),Gr=te(require("node:os")),Tc=te(jr()),{version:Vr}=qr(),Pc=Gr.platform(),zr=Pc==="win32"?"\\\\.\\pipe\\":"/tmp/";Ze=class{constructor(t,n){this.connecting=!1;this.containsFileCache=new Map;this.componentNamesAndProps=new Hr.FileMap(!1);this.seq=0;this.dataChunks=[];this.requestHandlers=new Map;this.path=Kt(t,n)}containsFile(t){if(this.projectInfo)return this.containsFileCache.has(t)||this.containsFileCache.set(t,(async()=>{let n=await this.sendRequest("containsFile",t);return typeof n!="boolean"&&this.containsFileCache.delete(t),n})()),this.containsFileCache.get(t)}async getComponentProps(t,n){let r=this.componentNamesAndProps.get(t);if(!r)return;let i=r[n]??r[(0,_e.camelize)(n)]??r[(0,_e.capitalize)((0,_e.camelize)(n))];return i||await this.sendRequest("subscribeComponentProps",t,n)}update(){!this.connecting&&!this.projectInfo&&(this.connecting=!0,this.connect())}connect(){this.socket=$r.connect(this.path),this.socket.on("data",this.onData.bind(this)),this.socket.on("connect",async()=>{let t=await this.sendRequest("projectInfo","");t?(console.log("TSServer project ready:",t.name),this.projectInfo=t,this.containsFileCache.clear(),Ac.forEach(n=>n())):this.close()}),this.socket.on("error",t=>{if(t.code==="ECONNREFUSED")try{console.log("Deleteing invalid named pipe file:",this.path),Ur.promises.unlink(this.path)}catch{}this.close()}),this.socket.on("timeout",()=>{this.close()})}close(){this.connecting=!1,this.projectInfo=void 0,this.socket?.end()}onData(t){this.dataChunks.push(t);let r=Buffer.concat(this.dataChunks).toString();if(r.endsWith(`

`)){this.dataChunks.length=0;let i=r.split(`

`);for(let a of i)if(a=a.trim(),!!a)try{let l=JSON.parse(a.trim());if(typeof l[0]=="number"){let[c,s]=l;this.requestHandlers.get(c)?.(s)}else{let[c,s,o]=l;this.onNotification(c,s,o)}}catch(l){console.error("JSON parse error:",l)}}}onNotification(t,n,r){if(t==="componentNamesUpdated"){let i=this.componentNamesAndProps.get(n);i||(i={},this.componentNamesAndProps.set(n,i));let a=r,l=new Set(a);for(let c in i)l.has(c)||delete i[c];for(let c of a)i[c]||(i[c]=null)}else if(t==="componentPropsUpdated"){let i=this.componentNamesAndProps.get(n)??{},[a,l]=r;a in i&&(i[a]=l)}else{console.error("Unknown notification type:",t);debugger}}sendRequest(t,n,...r){return new Promise(i=>{let a=this.seq++;this.requestHandlers.set(a,s=>{this.requestHandlers.delete(a),i(s),clearInterval(c)});let l=()=>{let s=[a,t,n,...r];this.socket.write(JSON.stringify(s)+`

`)};l();let c=setInterval(l,1e3)})}},Fc=[],Oc=[],Ac=[];for(let e=0;e<10;e++)Fc.push(new Ze(1,e)),Oc.push(new Ze(0,e))});var Wr={};tt(Wr,{startNamedPipeServer:()=>_c});async function _c(e,t,n,r){let i,a={typescript:e,languageService:t.languageService,languageServiceHost:t.languageServiceHost,language:n,isTsPlugin:!0,getFileId:S=>S},l=[],c=new Kr.FileMap(!1),s=new Set,o=new Set,f=et.createServer(S=>{s.add(S),S.on("end",()=>{s.delete(S)}),S.on("data",h=>{l.push(h);let y=l.toString();if(y.endsWith(`

`)){l.length=0;let b=y.split(`

`);for(let P of b)if(P=P.trim(),!!P)try{g(S,JSON.parse(P))}catch(O){console.error("[Vue Named Pipe Server] JSON parse error:",O)}}}),S.on("error",h=>console.error("[Vue Named Pipe Server]",h.message));for(let[h,[y,b]]of c){p(S,"componentNamesUpdated",h,y);for(let[P,O]of Object.entries(b))p(S,"componentPropsUpdated",h,[P,O])}});for(let S=0;S<10;S++){let h=Kt(r,S),y=await wc(h,100);if(typeof y=="object"&&y.end(),typeof y=="object"||y==="timeout")continue;if(await Nc(f,h))break}u();async function u(){for(;;){await d(500);let S=t.project.getProjectVersion();if(i===S)continue;let h=[...s].filter(P=>!P.destroyed);if(!h.length)continue;let y=t.languageServiceHost.getCancellationToken?.(),b=t.project.getRootScriptInfos().filter(P=>P.isScriptOpen());if(b.length){for(let P of b){if(await d(10),y?.isCancellationRequested())break;let O=c.get(P.fileName);O||(O=[[],{}],c.set(P.fileName,O));let[L,R]=O,C=kn.apply(a,[P.fileName])??[];if(JSON.stringify(L)!==JSON.stringify(C)){O[0]=C;for(let E of h)p(E,"componentNamesUpdated",P.fileName,C)}for(let[E,A]of Object.entries(R)){if(await d(10),y?.isCancellationRequested())break;let x=wt.apply(a,[P.fileName,E])??[];if(JSON.stringify(A)!==JSON.stringify(x)){R[E]=x;for(let v of h)p(v,"componentPropsUpdated",P.fileName,[E,x])}}}i=S}}}function d(S){return new Promise(h=>setTimeout(h,S))}function p(S,h,y,b){S.write(JSON.stringify([h,y,b])+`

`)}function g(S,[h,y,...b]){if(o.has(h))return;setTimeout(()=>o.delete(h),500),o.add(h);let P;try{P=m(y,...b)}catch{P=null}S.write(JSON.stringify([h,P??null])+`

`)}function m(S,...h){let y=h[0];if(S==="projectInfo")return{name:t.project.getProjectName(),kind:t.project.projectKind,currentDirectory:t.project.getCurrentDirectory()};if(S==="containsFile")return t.project.containsFile(e.server.toNormalizedPath(y));if(S==="collectExtractProps")return $n.apply(a,h);if(S==="getImportPathForFile")return tr.apply(a,h);if(S==="getPropertiesAtLocation")return ir.apply(a,h);if(S==="getQuickInfoAtPosition")return or.apply(a,h);if(S==="subscribeComponentProps"){let b=h[1],P=wt.apply(a,[y,b])??[],O=c.get(y);return O||(O=[[],{}],c.set(y,O)),O[1][b]=P,P}else{if(S==="getComponentEvents")return Yn.apply(a,h);if(S==="getComponentDirectives")return Kn.apply(a,h);if(S==="getElementAttrs")return jn.apply(a,h)}console.warn("[Vue Named Pipe Server] Unknown request:",S);debugger}}function wc(e,t){return new Promise(n=>{let r=et.connect(e);t&&r.setTimeout(t);let i=()=>{c(),n(r)},a=s=>{if(s.code==="ECONNREFUSED")try{console.log("[Vue Named Pipe Client] Deleting:",e),Wt.promises.unlink(e)}catch{}c(),n("error"),r.end()},l=()=>{c(),n("timeout"),r.end()},c=()=>{r.off("connect",i),r.off("error",a),r.off("timeout",l)};r.on("connect",i),r.on("error",a),r.on("timeout",l)})}function Nc(e,t){return new Promise(n=>{let r=()=>{e.off("error",i),n(!0)},i=a=>{if(a.code==="ECONNREFUSED")try{console.log("[Vue Named Pipe Client] Deleting:",t),Wt.promises.unlink(t)}catch{}e.off("error",i),e.close(),n(!1)};e.listen(t,r),e.on("error",i)})}var Kr,Wt,et,Jr=J(()=>{"use strict";Kr=require("vue-language-core-pack"),Wt=te(require("node:fs")),et=te(require("node:net"));Gn();Wn();Qn();At();er();Tt();nr();sr();ar();Br()});var Dc=Sn(),Jt=require("vue-language-core-pack"),Mc=(Un(),nt(Hn)),Lc=(Jr(),nt(Wr)),Rc=/\\/g,kc=new WeakMap,Ic=(0,Dc.createLanguageServicePlugin)((e,t)=>{let n=i(),r=Jt.createVueLanguagePlugin(e,t.languageServiceHost.getCompilationSettings(),n,a=>a);return kc.set(t.project,n),{languagePlugins:[r],setup:a=>{t.languageService=(0,Mc.proxyLanguageServiceForVue)(e,a,t.languageService,n,c=>c),(t.project.projectKind===e.server.ProjectKind.Configured||t.project.projectKind===e.server.ProjectKind.Inferred)&&(0,Lc.startNamedPipeServer)(e,t,a,t.project.projectKind);let l=setInterval(()=>{t.project.program&&(clearInterval(l),t.project.program.__vue__={language:a})},50)}};function i(){if(t.project.projectKind===e.server.ProjectKind.Configured){let a=t.project.getProjectName();return Jt.createParsedCommandLine(e,e.sys,a.replace(Rc,"/")).vueOptions}else return Jt.createParsedCommandLineByJson(e,e.sys,t.languageServiceHost.getCurrentDirectory(),{}).vueOptions}});module.exports=Ic;
/*! Bundled license information:

@vue/shared/dist/shared.cjs.prod.js:
  (**
  * @vue/shared v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
  (*! #__NO_SIDE_EFFECTS__ *)
*/
