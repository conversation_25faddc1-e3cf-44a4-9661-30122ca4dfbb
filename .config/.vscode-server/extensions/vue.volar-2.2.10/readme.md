# Vue - Official

## Quick Start

- [create-vue](https://github.com/vuejs/create-vue)
- [Vitesse](https://github.com/antfu/vitesse)
- [petite](https://github.com/JessicaSachs/petite)
- [volar-starter](https://github.com/johnsoncodehk/volar-starter) (For bug report and experiment features testing)

## Insiders Program 🚀

This project is community-driven. If you would like to support this project, consider joining the [Insiders Program](https://github.com/vuejs/language-tools/wiki/Get-Insiders-Edition) to improve the sustainability of this project and unlock more features.

<!-- 

## Usage

<details>
<summary>Setup for Vue 2</summary>

1. Add `@vue/runtime-dom`

This extension requires Vue 3 types from the `@vue/runtime-dom`.

Vue 3 and Vue 2.7 has built-in JSX types. For Vue version \<= 2.6.14 you need to add JSX types by install `@vue/runtime-dom`:

```jsonc
// package.json
{
  "devDependencies": {
    "@vue/runtime-dom": "latest"
  }
}
```

2. Remove `Vue.extend`

Template type-checking is not supported with `Vue.extend`. You can use [composition-api](https://github.com/vuejs/composition-api), [vue-class-component](https://github.com/vuejs/vue-class-component), or `export default { ... }` instead of `export default Vue.extend`.

Here is a compatibility table for different ways of writing the script blocks:

|                                          | Component options type-checking in `<script>` | Interpolation type-checking in `<template>` | Cross-component props type-checking |
|:-----------------------------------------|:----------------------------------------------|:--------------------------------------------|:------------------------------------|
| `export default { ... }` with JS         | Not supported                                 | Not supported                               | Not supported                       |
| `export default { ... }` with TS         | Not supported                                 | Supported but deprecated                    | Supported but deprecated            |
| `export default Vue.extend({ ... })` with JS | Not supported                             | Not supported                               | Not supported                       |
| `export default Vue.extend({ ... })` with TS | Limited (supports `data` types but not `props` types) | Limited                         | Not supported                       |
| `export default defineComponent({ ... })` | Supported                                    | Supported                                   | Supported                           |
| Class component                          | Supported                                     | Supported with additional code ([#21](https://github.com/vuejs/language-tools/issues/21)) |  Supported with [additional code](https://github.com/vuejs/language-tools/pull/750#issuecomment-1023947885)     |

Note that you can use `defineComponent` even for components that are using the `Options API`.

3. Support for Vue 2 template

Volar preferentially supports Vue 3. Vue 3 and Vue 2 templates have some differences. You need to set the `target` option to support the Vue 2 templates.

```jsonc
// tsconfig.json
{
  "compilerOptions": {
    // ...
  },
  "vueCompilerOptions": {
    "target": 2.7,
    // "target": 2, // For Vue version <= 2.6.14
  }
}
```

4. remove `.d.ts` files if they exist.

For projects generated by the [Vue CLI](https://cli.vuejs.org/), `.d.ts` files are included. Remove these files.

```
rm src/shims-tsx.d.ts src/shims-vue.d.ts
```

</details>

<details>
<summary>Define Global Components</summary>

PR: https://github.com/vuejs/vue-next/pull/3399

Local components, Built-in components, native HTML elements Type-Checking is available with no configuration.

For Global components, you need to define `GlobalComponents` interface, for example:

```typescript
// components.d.ts
declare module 'vue' {  // Vue >= 2.7
// declare module '@vue/runtime-dom' {  // Vue <= 2.6.14
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}

export {}
```

</details>

## Notes

### Vetur

You need to disable Vetur to avoid conflicts.

Recommended use css / less / scss as `<style>` language, because these base on [vscode-css-languageservice](https://github.com/microsoft/vscode-css-languageservice) to provide reliable language support.

If use postcss / stylus / sass, you need to install additional extension for syntax highlighting. I tried these and it works, you can also choose others.

- postcss: [language-postcss](https://marketplace.visualstudio.com/items?itemName=cpylua.language-postcss).
- stylus: [language-stylus](https://marketplace.visualstudio.com/items?itemName=sysoev.language-stylus)
- sass: [Sass](https://marketplace.visualstudio.com/items?itemName=Syler.sass-indented)

Volar does not include ESLint and Prettier, but the official [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) and [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) extensions support Vue, so you could install these yourself if needed.

If using Vetur's [Customizable Scaffold Snippets](https://vuejs.github.io/vetur/guide/snippet.html#customizable-scaffold-snippets), recommend use [Snippet Generator](https://marketplace.visualstudio.com/items?itemName=wenfangdu.snippet-generator) convert to VSCode Snippets. There are also snippets on the VSCode Marketplace, such as Sarah Drasner's [Vue VSCode Snippets](https://marketplace.visualstudio.com/items?itemName=sdras.vue-vscode-snippets), if you prefer ready-made snippets without customization.

If VSCode gives an error for `class` and `slot` like this:

<kbd><img width="483" src="https://user-images.githubusercontent.com/3253920/145134536-7bb090e9-9dcd-4a61-8096-3c47d6c1a699.png" /></kbd>

This is because one of the packages installed in your project uses `@types/react` which breaks some parts of Volar.

Please see the following solutions:
- https://github.com/vuejs/language-tools/discussions/592
- https://github.com/vuejs/language-tools/discussions/592#discussioncomment-1763880

### Recursive components

Volar can't typecheck recursive components out of the box due to TS limitation.
But there's a workaround, you can explicitly specify component's props like so:

`Bar.vue`

```vue
<template>
  <Bar :a="'wrong'" />
</template>

<script setup lang="ts">
import { defineAsyncComponent, type DefineComponent } from 'vue'

interface Props {
  a: number
}

const Bar = defineAsyncComponent<DefineComponent<Props>>(
  () => import('./Bar.vue') as any
)
defineProps<Props>()
</script>
```

### Custom File Extensions

Syntax highlighting and intellisense can be applied to additional file extensions beyond just `vue`. This will need to be configured in three different places for full support.

In VS Code settings for the Volar extension add any additional extensions you need to `Additional Extensions`. Such as `ext`.

In your tsconfig.json file you will need to make sure your custom extension is included by TypeScript. If you have an include value for `./src/*.vue` then you would want to add an include for `./src/*.ext` as well.

```json
"include": [
  "./src/*.ts",
  "./src/*.vue",
  "./src/*.ext",
]
```

Finally you need to make VS Code recognize your new extension and automatically associate it with the Vue language format. To do this you need to configure your File Associations setting to map `*.ext` to the language value `vue`. This can be done under Text Editor &gt; Files, or with the key `files.associations`.

 -->

## Sponsors

<!-- <table>
  <tbody>
    <tr>
      <td align="center" valign="middle" colspan="2">
        <b>Special Sponsor</b>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle" colspan="2">
        <a href="https://stackblitz.com/">
          <img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/StackBlitz.svg" height="80" />
        </a>
        <p>Stay in the flow with instant dev experiences.<br>No more hours stashing/pulling/installing locally</p>
        <p><b> — just click, and start coding.</b></p>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle" colspan="2">
        <b>Platinum Sponsors</b>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle" width="50%">
        <a href="https://vuejs.org/">
          <img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/Vue.svg" height="80" />
        </a>
        <p>An approachable, performant and versatile framework for building web user interfaces.</p>
      </td>
      <td align="center" valign="middle" width="50%">
        <a href="https://astro.build/">
          <img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/Astro.svg" height="80" />
        </a>
        <p>Astro powers the world's fastest websites, client-side web apps, dynamic API endpoints, and everything in-between.</p>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <a href="https://www.jetbrains.com/">
          <img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/JetBrains.svg" height="80" />
        </a>
        <p>Essential tools for software developers and teams.</p>
      </td>
      <td align="center" valign="middle">
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle" colspan="2">
        <b>Silver Sponsors</b>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <a href="https://www.prefect.io/"><img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/Prefect.svg" height="50" /></a>
      </td>
      <td align="center" valign="middle">
        <a href="https://www.techjobasia.com/"><img src="https://raw.githubusercontent.com/johnsoncodehk/sponsors/master/logos/TechJobAsia.svg" height="50" /></a>
      </td>
    </tr>
  </tbody>
</table> -->

<p align="center">
	<a href="https://cdn.jsdelivr.net/gh/johnsoncodehk/sponsors/sponsors.svg">
		<img src="https://cdn.jsdelivr.net/gh/johnsoncodehk/sponsors/sponsors.png"/>
	</a>
</p>

<p align="center">
	<a href="https://github.com/sponsors/johnsoncodehk">Become a sponsor</a>
</p>

## Credits

- [vscode-extension-samples](https://github.com/microsoft/vscode-extension-samples) shows all the knowledge required to develop the extension.
- [angular](https://github.com/angular/angular) shows how TS server plugin working with language service.
- Syntax highlight is rewritten base on [vue-syntax-highlight](https://github.com/vuejs/vue-syntax-highlight).
- [vscode-fenced-code-block-grammar-injection-example](https://github.com/mjbvz/vscode-fenced-code-block-grammar-injection-example) shows how to inject vue syntax highlight to markdown.
