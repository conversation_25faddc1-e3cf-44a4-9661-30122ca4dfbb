"use strict";var Eu=Object.defineProperty;var lT=Object.getOwnPropertyDescriptor;var dT=Object.getOwnPropertyNames;var fT=Object.prototype.hasOwnProperty;var pT=(t,e)=>()=>(t&&(e=t(t=0)),e);var b=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),hT=(t,e)=>{for(var n in e)Eu(t,n,{get:e[n],enumerable:!0})},gT=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of dT(e))!fT.call(t,i)&&i!==n&&Eu(t,i,{get:()=>e[i],enumerable:!(r=lT(e,i))||r.enumerable});return t};var xu=t=>gT(Eu({},"__esModule",{value:!0}),t);var ki=b(gt=>{"use strict";Object.defineProperty(gt,"__esModule",{value:!0});gt.stringArray=gt.array=gt.func=gt.error=gt.number=gt.string=gt.boolean=void 0;function mT(t){return t===!0||t===!1}gt.boolean=mT;function Lm(t){return typeof t=="string"||t instanceof String}gt.string=Lm;function vT(t){return typeof t=="number"||t instanceof Number}gt.number=vT;function yT(t){return t instanceof Error}gt.error=yT;function wT(t){return typeof t=="function"}gt.func=wT;function jm(t){return Array.isArray(t)}gt.array=jm;function bT(t){return jm(t)&&t.every(e=>Lm(e))}gt.stringArray=bT});var el=b(K=>{"use strict";Object.defineProperty(K,"__esModule",{value:!0});K.Message=K.NotificationType9=K.NotificationType8=K.NotificationType7=K.NotificationType6=K.NotificationType5=K.NotificationType4=K.NotificationType3=K.NotificationType2=K.NotificationType1=K.NotificationType0=K.NotificationType=K.RequestType9=K.RequestType8=K.RequestType7=K.RequestType6=K.RequestType5=K.RequestType4=K.RequestType3=K.RequestType2=K.RequestType1=K.RequestType=K.RequestType0=K.AbstractMessageSignature=K.ParameterStructures=K.ResponseError=K.ErrorCodes=void 0;var ei=ki(),qu;(function(t){t.ParseError=-32700,t.InvalidRequest=-32600,t.MethodNotFound=-32601,t.InvalidParams=-32602,t.InternalError=-32603,t.jsonrpcReservedErrorRangeStart=-32099,t.serverErrorStart=-32099,t.MessageWriteError=-32099,t.MessageReadError=-32098,t.PendingResponseRejected=-32097,t.ConnectionInactive=-32096,t.ServerNotInitialized=-32002,t.UnknownErrorCode=-32001,t.jsonrpcReservedErrorRangeEnd=-32e3,t.serverErrorEnd=-32e3})(qu||(K.ErrorCodes=qu={}));var ku=class t extends Error{constructor(e,n,r){super(n),this.code=ei.number(e)?e:qu.UnknownErrorCode,this.data=r,Object.setPrototypeOf(this,t.prototype)}toJson(){let e={code:this.code,message:this.message};return this.data!==void 0&&(e.data=this.data),e}};K.ResponseError=ku;var Ft=class t{constructor(e){this.kind=e}static is(e){return e===t.auto||e===t.byName||e===t.byPosition}toString(){return this.kind}};K.ParameterStructures=Ft;Ft.auto=new Ft("auto");Ft.byPosition=new Ft("byPosition");Ft.byName=new Ft("byName");var Ne=class{constructor(e,n){this.method=e,this.numberOfParams=n}get parameterStructures(){return Ft.auto}};K.AbstractMessageSignature=Ne;var Ou=class extends Ne{constructor(e){super(e,0)}};K.RequestType0=Ou;var Iu=class extends Ne{constructor(e,n=Ft.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};K.RequestType=Iu;var Nu=class extends Ne{constructor(e,n=Ft.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};K.RequestType1=Nu;var Fu=class extends Ne{constructor(e){super(e,2)}};K.RequestType2=Fu;var Mu=class extends Ne{constructor(e){super(e,3)}};K.RequestType3=Mu;var Au=class extends Ne{constructor(e){super(e,4)}};K.RequestType4=Au;var Lu=class extends Ne{constructor(e){super(e,5)}};K.RequestType5=Lu;var ju=class extends Ne{constructor(e){super(e,6)}};K.RequestType6=ju;var $u=class extends Ne{constructor(e){super(e,7)}};K.RequestType7=$u;var Hu=class extends Ne{constructor(e){super(e,8)}};K.RequestType8=Hu;var Uu=class extends Ne{constructor(e){super(e,9)}};K.RequestType9=Uu;var Vu=class extends Ne{constructor(e,n=Ft.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};K.NotificationType=Vu;var Wu=class extends Ne{constructor(e){super(e,0)}};K.NotificationType0=Wu;var Ku=class extends Ne{constructor(e,n=Ft.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};K.NotificationType1=Ku;var zu=class extends Ne{constructor(e){super(e,2)}};K.NotificationType2=zu;var Bu=class extends Ne{constructor(e){super(e,3)}};K.NotificationType3=Bu;var Gu=class extends Ne{constructor(e){super(e,4)}};K.NotificationType4=Gu;var Xu=class extends Ne{constructor(e){super(e,5)}};K.NotificationType5=Xu;var Ju=class extends Ne{constructor(e){super(e,6)}};K.NotificationType6=Ju;var Yu=class extends Ne{constructor(e){super(e,7)}};K.NotificationType7=Yu;var Qu=class extends Ne{constructor(e){super(e,8)}};K.NotificationType8=Qu;var Zu=class extends Ne{constructor(e){super(e,9)}};K.NotificationType9=Zu;var $m;(function(t){function e(i){let s=i;return s&&ei.string(s.method)&&(ei.string(s.id)||ei.number(s.id))}t.isRequest=e;function n(i){let s=i;return s&&ei.string(s.method)&&i.id===void 0}t.isNotification=n;function r(i){let s=i;return s&&(s.result!==void 0||!!s.error)&&(ei.string(s.id)||ei.number(s.id)||s.id===null)}t.isResponse=r})($m||(K.Message=$m={}))});var nl=b(Cr=>{"use strict";var Hm;Object.defineProperty(Cr,"__esModule",{value:!0});Cr.LRUCache=Cr.LinkedMap=Cr.Touch=void 0;var mt;(function(t){t.None=0,t.First=1,t.AsOld=t.First,t.Last=2,t.AsNew=t.Last})(mt||(Cr.Touch=mt={}));var Wo=class{constructor(){this[Hm]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,n=mt.None){let r=this._map.get(e);if(r)return n!==mt.None&&this.touch(r,n),r.value}set(e,n,r=mt.None){let i=this._map.get(e);if(i)i.value=n,r!==mt.None&&this.touch(i,r);else{switch(i={key:e,value:n,next:void 0,previous:void 0},r){case mt.None:this.addItemLast(i);break;case mt.First:this.addItemFirst(i);break;case mt.Last:this.addItemLast(i);break;default:this.addItemLast(i);break}this._map.set(e,i),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){let n=this._map.get(e);if(n)return this._map.delete(e),this.removeItem(n),this._size--,n.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");let e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,n){let r=this._state,i=this._head;for(;i;){if(n?e.bind(n)(i.value,i.key,this):e(i.value,i.key,this),this._state!==r)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let i={value:n.key,done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}values(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let i={value:n.value,done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}entries(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let i={value:[n.key,n.value],done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}[(Hm=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let n=this._head,r=this.size;for(;n&&r>e;)this._map.delete(n.key),n=n.next,r--;this._head=n,this._size=r,n&&(n.previous=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw new Error("Invalid list");this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw new Error("Invalid list");this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{let n=e.next,r=e.previous;if(!n||!r)throw new Error("Invalid list");n.previous=r,r.next=n}e.next=void 0,e.previous=void 0,this._state++}touch(e,n){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(n!==mt.First&&n!==mt.Last)){if(n===mt.First){if(e===this._head)return;let r=e.next,i=e.previous;e===this._tail?(i.next=void 0,this._tail=i):(r.previous=i,i.next=r),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(n===mt.Last){if(e===this._tail)return;let r=e.next,i=e.previous;e===this._head?(r.previous=void 0,this._head=r):(r.previous=i,i.next=r),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){let e=[];return this.forEach((n,r)=>{e.push([r,n])}),e}fromJSON(e){this.clear();for(let[n,r]of e)this.set(n,r)}};Cr.LinkedMap=Wo;var tl=class extends Wo{constructor(e,n=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,n),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,n=mt.AsNew){return super.get(e,n)}peek(e){return super.get(e,mt.None)}set(e,n){return super.set(e,n,mt.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}};Cr.LRUCache=tl});var Vm=b(Ko=>{"use strict";Object.defineProperty(Ko,"__esModule",{value:!0});Ko.Disposable=void 0;var Um;(function(t){function e(n){return{dispose:n}}t.create=e})(Um||(Ko.Disposable=Um={}))});var _r=b(sl=>{"use strict";Object.defineProperty(sl,"__esModule",{value:!0});var rl;function il(){if(rl===void 0)throw new Error("No runtime abstraction layer installed");return rl}(function(t){function e(n){if(n===void 0)throw new Error("No runtime abstraction layer provided");rl=n}t.install=e})(il||(il={}));sl.default=il});var Ii=b(Oi=>{"use strict";Object.defineProperty(Oi,"__esModule",{value:!0});Oi.Emitter=Oi.Event=void 0;var CT=_r(),Wm;(function(t){let e={dispose(){}};t.None=function(){return e}})(Wm||(Oi.Event=Wm={}));var ol=class{add(e,n=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(n),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,n)})}remove(e,n=null){if(!this._callbacks)return;let r=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e)if(this._contexts[i]===n){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else r=!0;if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let n=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,o=r.length;s<o;s++)try{n.push(r[s].apply(i[s],e))}catch(a){(0,CT.default)().console.error(a)}return n}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},zo=class t{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,n,r)=>{this._callbacks||(this._callbacks=new ol),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,n);let i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,n),i.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};Oi.Emitter=zo;zo._noop=function(){}});var Xo=b(Ni=>{"use strict";Object.defineProperty(Ni,"__esModule",{value:!0});Ni.CancellationTokenSource=Ni.CancellationToken=void 0;var _T=_r(),ST=ki(),al=Ii(),Bo;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:al.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:al.Event.None});function e(n){let r=n;return r&&(r===t.None||r===t.Cancelled||ST.boolean(r.isCancellationRequested)&&!!r.onCancellationRequested)}t.is=e})(Bo||(Ni.CancellationToken=Bo={}));var DT=Object.freeze(function(t,e){let n=(0,_T.default)().timer.setTimeout(t.bind(e),0);return{dispose(){n.dispose()}}}),Go=class{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?DT:(this._emitter||(this._emitter=new al.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},cl=class{get token(){return this._token||(this._token=new Go),this._token}cancel(){this._token?this._token.cancel():this._token=Bo.Cancelled}dispose(){this._token?this._token instanceof Go&&this._token.dispose():this._token=Bo.None}};Ni.CancellationTokenSource=cl});var Km=b(Fi=>{"use strict";Object.defineProperty(Fi,"__esModule",{value:!0});Fi.SharedArrayReceiverStrategy=Fi.SharedArraySenderStrategy=void 0;var RT=Xo(),Ns;(function(t){t.Continue=0,t.Cancelled=1})(Ns||(Ns={}));var ul=class{constructor(){this.buffers=new Map}enableCancellation(e){if(e.id===null)return;let n=new SharedArrayBuffer(4),r=new Int32Array(n,0,1);r[0]=Ns.Continue,this.buffers.set(e.id,n),e.$cancellationData=n}async sendCancellation(e,n){let r=this.buffers.get(n);if(r===void 0)return;let i=new Int32Array(r,0,1);Atomics.store(i,0,Ns.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};Fi.SharedArraySenderStrategy=ul;var ll=class{constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===Ns.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}},dl=class{constructor(e){this.token=new ll(e)}cancel(){}dispose(){}},fl=class{constructor(){this.kind="request"}createCancellationTokenSource(e){let n=e.$cancellationData;return n===void 0?new RT.CancellationTokenSource:new dl(n)}};Fi.SharedArrayReceiverStrategy=fl});var hl=b(Jo=>{"use strict";Object.defineProperty(Jo,"__esModule",{value:!0});Jo.Semaphore=void 0;var TT=_r(),pl=class{constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((n,r)=>{this._waiting.push({thunk:e,resolve:n,reject:r}),this.runNext()})}get active(){return this._active}runNext(){this._waiting.length===0||this._active===this._capacity||(0,TT.default)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(this._waiting.length===0||this._active===this._capacity)return;let e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{let n=e.thunk();n instanceof Promise?n.then(r=>{this._active--,e.resolve(r),this.runNext()},r=>{this._active--,e.reject(r),this.runNext()}):(this._active--,e.resolve(n),this.runNext())}catch(n){this._active--,e.reject(n),this.runNext()}}};Jo.Semaphore=pl});var Bm=b(Sr=>{"use strict";Object.defineProperty(Sr,"__esModule",{value:!0});Sr.ReadableStreamMessageReader=Sr.AbstractMessageReader=Sr.MessageReader=void 0;var ml=_r(),Mi=ki(),gl=Ii(),PT=hl(),zm;(function(t){function e(n){let r=n;return r&&Mi.func(r.listen)&&Mi.func(r.dispose)&&Mi.func(r.onError)&&Mi.func(r.onClose)&&Mi.func(r.onPartialMessage)}t.is=e})(zm||(Sr.MessageReader=zm={}));var Yo=class{constructor(){this.errorEmitter=new gl.Emitter,this.closeEmitter=new gl.Emitter,this.partialMessageEmitter=new gl.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${Mi.string(e.message)?e.message:"unknown"}`)}};Sr.AbstractMessageReader=Yo;var vl;(function(t){function e(n){let r,i,s,o=new Map,a,c=new Map;if(n===void 0||typeof n=="string")r=n??"utf-8";else{if(r=n.charset??"utf-8",n.contentDecoder!==void 0&&(s=n.contentDecoder,o.set(s.name,s)),n.contentDecoders!==void 0)for(let u of n.contentDecoders)o.set(u.name,u);if(n.contentTypeDecoder!==void 0&&(a=n.contentTypeDecoder,c.set(a.name,a)),n.contentTypeDecoders!==void 0)for(let u of n.contentTypeDecoders)c.set(u.name,u)}return a===void 0&&(a=(0,ml.default)().applicationJson.decoder,c.set(a.name,a)),{charset:r,contentDecoder:s,contentDecoders:o,contentTypeDecoder:a,contentTypeDecoders:c}}t.fromOptions=e})(vl||(vl={}));var yl=class extends Yo{constructor(e,n){super(),this.readable=e,this.options=vl.fromOptions(n),this.buffer=(0,ml.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new PT.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;let n=this.readable.onData(r=>{this.onData(r)});return this.readable.onError(r=>this.fireError(r)),this.readable.onClose(()=>this.fireClose()),n}onData(e){try{for(this.buffer.append(e);;){if(this.nextMessageLength===-1){let r=this.buffer.tryReadHeaders(!0);if(!r)return;let i=r.get("content-length");if(!i){this.fireError(new Error(`Header must provide a Content-Length property.
${JSON.stringify(Object.fromEntries(r))}`));return}let s=parseInt(i);if(isNaN(s)){this.fireError(new Error(`Content-Length value must be a number. Got ${i}`));return}this.nextMessageLength=s}let n=this.buffer.tryReadBody(this.nextMessageLength);if(n===void 0){this.setPartialMessageTimer();return}this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock(async()=>{let r=this.options.contentDecoder!==void 0?await this.options.contentDecoder.decode(n):n,i=await this.options.contentTypeDecoder.decode(r,this.options);this.callback(i)}).catch(r=>{this.fireError(r)})}}catch(n){this.fireError(n)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),!(this._partialMessageTimeout<=0)&&(this.partialMessageTimer=(0,ml.default)().timer.setTimeout((e,n)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:n}),this.setPartialMessageTimer())},this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}};Sr.ReadableStreamMessageReader=yl});var Qm=b(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.WriteableStreamMessageWriter=Dr.AbstractMessageWriter=Dr.MessageWriter=void 0;var Gm=_r(),Fs=ki(),ET=hl(),Xm=Ii(),xT="Content-Length: ",Jm=`\r
`,Ym;(function(t){function e(n){let r=n;return r&&Fs.func(r.dispose)&&Fs.func(r.onClose)&&Fs.func(r.onError)&&Fs.func(r.write)}t.is=e})(Ym||(Dr.MessageWriter=Ym={}));var Qo=class{constructor(){this.errorEmitter=new Xm.Emitter,this.closeEmitter=new Xm.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,n,r){this.errorEmitter.fire([this.asError(e),n,r])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${Fs.string(e.message)?e.message:"unknown"}`)}};Dr.AbstractMessageWriter=Qo;var wl;(function(t){function e(n){return n===void 0||typeof n=="string"?{charset:n??"utf-8",contentTypeEncoder:(0,Gm.default)().applicationJson.encoder}:{charset:n.charset??"utf-8",contentEncoder:n.contentEncoder,contentTypeEncoder:n.contentTypeEncoder??(0,Gm.default)().applicationJson.encoder}}t.fromOptions=e})(wl||(wl={}));var bl=class extends Qo{constructor(e,n){super(),this.writable=e,this.options=wl.fromOptions(n),this.errorCount=0,this.writeSemaphore=new ET.Semaphore(1),this.writable.onError(r=>this.fireError(r)),this.writable.onClose(()=>this.fireClose())}async write(e){return this.writeSemaphore.lock(async()=>this.options.contentTypeEncoder.encode(e,this.options).then(r=>this.options.contentEncoder!==void 0?this.options.contentEncoder.encode(r):r).then(r=>{let i=[];return i.push(xT,r.byteLength.toString(),Jm),i.push(Jm),this.doWrite(e,i,r)},r=>{throw this.fireError(r),r}))}async doWrite(e,n,r){try{return await this.writable.write(n.join(""),"ascii"),this.writable.write(r)}catch(i){return this.handleError(i,e),Promise.reject(i)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){this.writable.end()}};Dr.WriteableStreamMessageWriter=bl});var Zm=b(Zo=>{"use strict";Object.defineProperty(Zo,"__esModule",{value:!0});Zo.AbstractMessageBuffer=void 0;var qT=13,kT=10,OT=`\r
`,Cl=class{constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){let n=typeof e=="string"?this.fromString(e,this._encoding):e;this._chunks.push(n),this._totalLength+=n.byteLength}tryReadHeaders(e=!1){if(this._chunks.length===0)return;let n=0,r=0,i=0,s=0;e:for(;r<this._chunks.length;){let u=this._chunks[r];for(i=0;i<u.length;){switch(u[i]){case qT:switch(n){case 0:n=1;break;case 2:n=3;break;default:n=0}break;case kT:switch(n){case 1:n=2;break;case 3:n=4,i++;break e;default:n=0}break;default:n=0}i++}s+=u.byteLength,r++}if(n!==4)return;let o=this._read(s+i),a=new Map,c=this.toString(o,"ascii").split(OT);if(c.length<2)return a;for(let u=0;u<c.length-2;u++){let f=c[u],d=f.indexOf(":");if(d===-1)throw new Error(`Message header must separate key and value using ':'
${f}`);let p=f.substr(0,d),g=f.substr(d+1).trim();a.set(e?p.toLowerCase():p,g)}return a}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(e===0)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){let s=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(s)}if(this._chunks[0].byteLength>e){let s=this._chunks[0],o=this.asNative(s,e);return this._chunks[0]=s.slice(e),this._totalLength-=e,o}let n=this.allocNative(e),r=0,i=0;for(;e>0;){let s=this._chunks[i];if(s.byteLength>e){let o=s.slice(0,e);n.set(o,r),r+=e,this._chunks[i]=s.slice(e),this._totalLength-=e,e-=e}else n.set(s,r),r+=s.byteLength,this._chunks.shift(),this._totalLength-=s.byteLength,e-=s.byteLength}return n}};Zo.AbstractMessageBuffer=Cl});var iv=b(ee=>{"use strict";Object.defineProperty(ee,"__esModule",{value:!0});ee.createMessageConnection=ee.ConnectionOptions=ee.MessageStrategy=ee.CancellationStrategy=ee.CancellationSenderStrategy=ee.CancellationReceiverStrategy=ee.RequestCancellationReceiverStrategy=ee.IdCancellationReceiverStrategy=ee.ConnectionStrategy=ee.ConnectionError=ee.ConnectionErrors=ee.LogTraceNotification=ee.SetTraceNotification=ee.TraceFormat=ee.TraceValues=ee.Trace=ee.NullLogger=ee.ProgressType=ee.ProgressToken=void 0;var ev=_r(),He=ki(),G=el(),tv=nl(),Ms=Ii(),_l=Xo(),js;(function(t){t.type=new G.NotificationType("$/cancelRequest")})(js||(js={}));var Sl;(function(t){function e(n){return typeof n=="string"||typeof n=="number"}t.is=e})(Sl||(ee.ProgressToken=Sl={}));var As;(function(t){t.type=new G.NotificationType("$/progress")})(As||(As={}));var Dl=class{constructor(){}};ee.ProgressType=Dl;var Rl;(function(t){function e(n){return He.func(n)}t.is=e})(Rl||(Rl={}));ee.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}});var me;(function(t){t[t.Off=0]="Off",t[t.Messages=1]="Messages",t[t.Compact=2]="Compact",t[t.Verbose=3]="Verbose"})(me||(ee.Trace=me={}));var nv;(function(t){t.Off="off",t.Messages="messages",t.Compact="compact",t.Verbose="verbose"})(nv||(ee.TraceValues=nv={}));(function(t){function e(r){if(!He.string(r))return t.Off;switch(r=r.toLowerCase(),r){case"off":return t.Off;case"messages":return t.Messages;case"compact":return t.Compact;case"verbose":return t.Verbose;default:return t.Off}}t.fromString=e;function n(r){switch(r){case t.Off:return"off";case t.Messages:return"messages";case t.Compact:return"compact";case t.Verbose:return"verbose";default:return"off"}}t.toString=n})(me||(ee.Trace=me={}));var Xt;(function(t){t.Text="text",t.JSON="json"})(Xt||(ee.TraceFormat=Xt={}));(function(t){function e(n){return He.string(n)?(n=n.toLowerCase(),n==="json"?t.JSON:t.Text):t.Text}t.fromString=e})(Xt||(ee.TraceFormat=Xt={}));var Tl;(function(t){t.type=new G.NotificationType("$/setTrace")})(Tl||(ee.SetTraceNotification=Tl={}));var ea;(function(t){t.type=new G.NotificationType("$/logTrace")})(ea||(ee.LogTraceNotification=ea={}));var Ls;(function(t){t[t.Closed=1]="Closed",t[t.Disposed=2]="Disposed",t[t.AlreadyListening=3]="AlreadyListening"})(Ls||(ee.ConnectionErrors=Ls={}));var Ai=class t extends Error{constructor(e,n){super(n),this.code=e,Object.setPrototypeOf(this,t.prototype)}};ee.ConnectionError=Ai;var Pl;(function(t){function e(n){let r=n;return r&&He.func(r.cancelUndispatched)}t.is=e})(Pl||(ee.ConnectionStrategy=Pl={}));var ta;(function(t){function e(n){let r=n;return r&&(r.kind===void 0||r.kind==="id")&&He.func(r.createCancellationTokenSource)&&(r.dispose===void 0||He.func(r.dispose))}t.is=e})(ta||(ee.IdCancellationReceiverStrategy=ta={}));var El;(function(t){function e(n){let r=n;return r&&r.kind==="request"&&He.func(r.createCancellationTokenSource)&&(r.dispose===void 0||He.func(r.dispose))}t.is=e})(El||(ee.RequestCancellationReceiverStrategy=El={}));var na;(function(t){t.Message=Object.freeze({createCancellationTokenSource(n){return new _l.CancellationTokenSource}});function e(n){return ta.is(n)||El.is(n)}t.is=e})(na||(ee.CancellationReceiverStrategy=na={}));var ra;(function(t){t.Message=Object.freeze({sendCancellation(n,r){return n.sendNotification(js.type,{id:r})},cleanup(n){}});function e(n){let r=n;return r&&He.func(r.sendCancellation)&&He.func(r.cleanup)}t.is=e})(ra||(ee.CancellationSenderStrategy=ra={}));var ia;(function(t){t.Message=Object.freeze({receiver:na.Message,sender:ra.Message});function e(n){let r=n;return r&&na.is(r.receiver)&&ra.is(r.sender)}t.is=e})(ia||(ee.CancellationStrategy=ia={}));var sa;(function(t){function e(n){let r=n;return r&&He.func(r.handleMessage)}t.is=e})(sa||(ee.MessageStrategy=sa={}));var rv;(function(t){function e(n){let r=n;return r&&(ia.is(r.cancellationStrategy)||Pl.is(r.connectionStrategy)||sa.is(r.messageStrategy))}t.is=e})(rv||(ee.ConnectionOptions=rv={}));var Dn;(function(t){t[t.New=1]="New",t[t.Listening=2]="Listening",t[t.Closed=3]="Closed",t[t.Disposed=4]="Disposed"})(Dn||(Dn={}));function IT(t,e,n,r){let i=n!==void 0?n:ee.NullLogger,s=0,o=0,a=0,c="2.0",u,f=new Map,d,p=new Map,g=new Map,m,S=new tv.LinkedMap,D=new Map,P=new Set,k=new Map,x=me.Off,I=Xt.Text,M,Y=Dn.New,B=new Ms.Emitter,Z=new Ms.Emitter,fe=new Ms.Emitter,St=new Ms.Emitter,ht=new Ms.Emitter,Ot=r&&r.cancellationStrategy?r.cancellationStrategy:ia.Message;function gr(y){if(y===null)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+y.toString()}function In(y){return y===null?"res-unknown-"+(++a).toString():"res-"+y.toString()}function It(){return"not-"+(++o).toString()}function Dt(y,E){G.Message.isRequest(E)?y.set(gr(E.id),E):G.Message.isResponse(E)?y.set(In(E.id),E):y.set(It(),E)}function mr(y){}function Gr(){return Y===Dn.Listening}function Je(){return Y===Dn.Closed}function Cn(){return Y===Dn.Disposed}function _n(){(Y===Dn.New||Y===Dn.Listening)&&(Y=Dn.Closed,Z.fire(void 0))}function Sn(y){B.fire([y,void 0,void 0])}function Es(y){B.fire(y)}t.onClose(_n),t.onError(Sn),e.onClose(_n),e.onError(Es);function Xr(){m||S.size===0||(m=(0,ev.default)().timer.setImmediate(()=>{m=void 0,Pi()}))}function Jr(y){G.Message.isRequest(y)?Yr(y):G.Message.isNotification(y)?yr(y):G.Message.isResponse(y)?vr(y):qs(y)}function Pi(){if(S.size===0)return;let y=S.shift();try{let E=r?.messageStrategy;sa.is(E)?E.handleMessage(y,Jr):Jr(y)}finally{Xr()}}let xs=y=>{try{if(G.Message.isNotification(y)&&y.method===js.type.method){let E=y.params.id,F=gr(E),$=S.get(F);if(G.Message.isRequest($)){let ge=r?.connectionStrategy,Ee=ge&&ge.cancelUndispatched?ge.cancelUndispatched($,mr):void 0;if(Ee&&(Ee.error!==void 0||Ee.result!==void 0)){S.delete(F),k.delete(E),Ee.id=$.id,Bn(Ee,y.method,Date.now()),e.write(Ee).catch(()=>i.error("Sending response for canceled message failed."));return}}let ye=k.get(E);if(ye!==void 0){ye.cancel(),Gn(y);return}else P.add(E)}Dt(S,y)}finally{Xr()}};function Yr(y){if(Cn())return;function E(ie,Re,pe){let Ie={jsonrpc:c,id:y.id};ie instanceof G.ResponseError?Ie.error=ie.toJson():Ie.result=ie===void 0?null:ie,Bn(Ie,Re,pe),e.write(Ie).catch(()=>i.error("Sending response failed."))}function F(ie,Re,pe){let Ie={jsonrpc:c,id:y.id,error:ie.toJson()};Bn(Ie,Re,pe),e.write(Ie).catch(()=>i.error("Sending response failed."))}function $(ie,Re,pe){ie===void 0&&(ie=null);let Ie={jsonrpc:c,id:y.id,result:ie};Bn(Ie,Re,pe),e.write(Ie).catch(()=>i.error("Sending response failed."))}ks(y);let ye=f.get(y.method),ge,Ee;ye&&(ge=ye.type,Ee=ye.handler);let Oe=Date.now();if(Ee||u){let ie=y.id??String(Date.now()),Re=ta.is(Ot.receiver)?Ot.receiver.createCancellationTokenSource(ie):Ot.receiver.createCancellationTokenSource(y);y.id!==null&&P.has(y.id)&&Re.cancel(),y.id!==null&&k.set(ie,Re);try{let pe;if(Ee)if(y.params===void 0){if(ge!==void 0&&ge.numberOfParams!==0){F(new G.ResponseError(G.ErrorCodes.InvalidParams,`Request ${y.method} defines ${ge.numberOfParams} params but received none.`),y.method,Oe);return}pe=Ee(Re.token)}else if(Array.isArray(y.params)){if(ge!==void 0&&ge.parameterStructures===G.ParameterStructures.byName){F(new G.ResponseError(G.ErrorCodes.InvalidParams,`Request ${y.method} defines parameters by name but received parameters by position`),y.method,Oe);return}pe=Ee(...y.params,Re.token)}else{if(ge!==void 0&&ge.parameterStructures===G.ParameterStructures.byPosition){F(new G.ResponseError(G.ErrorCodes.InvalidParams,`Request ${y.method} defines parameters by position but received parameters by name`),y.method,Oe);return}pe=Ee(y.params,Re.token)}else u&&(pe=u(y.method,y.params,Re.token));let Ie=pe;pe?Ie.then?Ie.then(tt=>{k.delete(ie),E(tt,y.method,Oe)},tt=>{k.delete(ie),tt instanceof G.ResponseError?F(tt,y.method,Oe):tt&&He.string(tt.message)?F(new G.ResponseError(G.ErrorCodes.InternalError,`Request ${y.method} failed with message: ${tt.message}`),y.method,Oe):F(new G.ResponseError(G.ErrorCodes.InternalError,`Request ${y.method} failed unexpectedly without providing any details.`),y.method,Oe)}):(k.delete(ie),E(pe,y.method,Oe)):(k.delete(ie),$(pe,y.method,Oe))}catch(pe){k.delete(ie),pe instanceof G.ResponseError?E(pe,y.method,Oe):pe&&He.string(pe.message)?F(new G.ResponseError(G.ErrorCodes.InternalError,`Request ${y.method} failed with message: ${pe.message}`),y.method,Oe):F(new G.ResponseError(G.ErrorCodes.InternalError,`Request ${y.method} failed unexpectedly without providing any details.`),y.method,Oe)}}else F(new G.ResponseError(G.ErrorCodes.MethodNotFound,`Unhandled method ${y.method}`),y.method,Oe)}function vr(y){if(!Cn())if(y.id===null)y.error?i.error(`Received response message without id: Error is: 
${JSON.stringify(y.error,void 0,4)}`):i.error("Received response message without id. No further error information provided.");else{let E=y.id,F=D.get(E);if(Qr(y,F),F!==void 0){D.delete(E);try{if(y.error){let $=y.error;F.reject(new G.ResponseError($.code,$.message,$.data))}else if(y.result!==void 0)F.resolve(y.result);else throw new Error("Should never happen.")}catch($){$.message?i.error(`Response handler '${F.method}' failed with message: ${$.message}`):i.error(`Response handler '${F.method}' failed unexpectedly.`)}}}}function yr(y){if(Cn())return;let E,F;if(y.method===js.type.method){let $=y.params.id;P.delete($),Gn(y);return}else{let $=p.get(y.method);$&&(F=$.handler,E=$.type)}if(F||d)try{if(Gn(y),F)if(y.params===void 0)E!==void 0&&E.numberOfParams!==0&&E.parameterStructures!==G.ParameterStructures.byName&&i.error(`Notification ${y.method} defines ${E.numberOfParams} params but received none.`),F();else if(Array.isArray(y.params)){let $=y.params;y.method===As.type.method&&$.length===2&&Sl.is($[0])?F({token:$[0],value:$[1]}):(E!==void 0&&(E.parameterStructures===G.ParameterStructures.byName&&i.error(`Notification ${y.method} defines parameters by name but received parameters by position`),E.numberOfParams!==y.params.length&&i.error(`Notification ${y.method} defines ${E.numberOfParams} params but received ${$.length} arguments`)),F(...$))}else E!==void 0&&E.parameterStructures===G.ParameterStructures.byPosition&&i.error(`Notification ${y.method} defines parameters by position but received parameters by name`),F(y.params);else d&&d(y.method,y.params)}catch($){$.message?i.error(`Notification handler '${y.method}' failed with message: ${$.message}`):i.error(`Notification handler '${y.method}' failed unexpectedly.`)}else fe.fire(y)}function qs(y){if(!y){i.error("Received empty message.");return}i.error(`Received message which is neither a response nor a notification message:
${JSON.stringify(y,null,4)}`);let E=y;if(He.string(E.id)||He.number(E.id)){let F=E.id,$=D.get(F);$&&$.reject(new Error("The received response has neither a result nor an error property."))}}function ut(y){if(y!=null)switch(x){case me.Verbose:return JSON.stringify(y,null,4);case me.Compact:return JSON.stringify(y);default:return}}function Ei(y){if(!(x===me.Off||!M))if(I===Xt.Text){let E;(x===me.Verbose||x===me.Compact)&&y.params&&(E=`Params: ${ut(y.params)}

`),M.log(`Sending request '${y.method} - (${y.id})'.`,E)}else rt("send-request",y)}function wr(y){if(!(x===me.Off||!M))if(I===Xt.Text){let E;(x===me.Verbose||x===me.Compact)&&(y.params?E=`Params: ${ut(y.params)}

`:E=`No parameters provided.

`),M.log(`Sending notification '${y.method}'.`,E)}else rt("send-notification",y)}function Bn(y,E,F){if(!(x===me.Off||!M))if(I===Xt.Text){let $;(x===me.Verbose||x===me.Compact)&&(y.error&&y.error.data?$=`Error data: ${ut(y.error.data)}

`:y.result?$=`Result: ${ut(y.result)}

`:y.error===void 0&&($=`No result returned.

`)),M.log(`Sending response '${E} - (${y.id})'. Processing request took ${Date.now()-F}ms`,$)}else rt("send-response",y)}function ks(y){if(!(x===me.Off||!M))if(I===Xt.Text){let E;(x===me.Verbose||x===me.Compact)&&y.params&&(E=`Params: ${ut(y.params)}

`),M.log(`Received request '${y.method} - (${y.id})'.`,E)}else rt("receive-request",y)}function Gn(y){if(!(x===me.Off||!M||y.method===ea.type.method))if(I===Xt.Text){let E;(x===me.Verbose||x===me.Compact)&&(y.params?E=`Params: ${ut(y.params)}

`:E=`No parameters provided.

`),M.log(`Received notification '${y.method}'.`,E)}else rt("receive-notification",y)}function Qr(y,E){if(!(x===me.Off||!M))if(I===Xt.Text){let F;if((x===me.Verbose||x===me.Compact)&&(y.error&&y.error.data?F=`Error data: ${ut(y.error.data)}

`:y.result?F=`Result: ${ut(y.result)}

`:y.error===void 0&&(F=`No result returned.

`)),E){let $=y.error?` Request failed: ${y.error.message} (${y.error.code}).`:"";M.log(`Received response '${E.method} - (${y.id})' in ${Date.now()-E.timerStart}ms.${$}`,F)}else M.log(`Received response ${y.id} without active response promise.`,F)}else rt("receive-response",y)}function rt(y,E){if(!M||x===me.Off)return;let F={isLSPMessage:!0,type:y,message:E,timestamp:Date.now()};M.log(F)}function Bt(){if(Je())throw new Ai(Ls.Closed,"Connection is closed.");if(Cn())throw new Ai(Ls.Disposed,"Connection is disposed.")}function Nt(){if(Gr())throw new Ai(Ls.AlreadyListening,"Connection is already listening")}function Nn(){if(!Gr())throw new Error("Call listen() first.")}function Xn(y){return y===void 0?null:y}function Zr(y){if(y!==null)return y}function xi(y){return y!=null&&!Array.isArray(y)&&typeof y=="object"}function br(y,E){switch(y){case G.ParameterStructures.auto:return xi(E)?Zr(E):[Xn(E)];case G.ParameterStructures.byName:if(!xi(E))throw new Error("Received parameters by name but param is not an object literal.");return Zr(E);case G.ParameterStructures.byPosition:return[Xn(E)];default:throw new Error(`Unknown parameter structure ${y.toString()}`)}}function qi(y,E){let F,$=y.numberOfParams;switch($){case 0:F=void 0;break;case 1:F=br(y.parameterStructures,E[0]);break;default:F=[];for(let ye=0;ye<E.length&&ye<$;ye++)F.push(Xn(E[ye]));if(E.length<$)for(let ye=E.length;ye<$;ye++)F.push(null);break}return F}let sn={sendNotification:(y,...E)=>{Bt();let F,$;if(He.string(y)){F=y;let ge=E[0],Ee=0,Oe=G.ParameterStructures.auto;G.ParameterStructures.is(ge)&&(Ee=1,Oe=ge);let ie=E.length,Re=ie-Ee;switch(Re){case 0:$=void 0;break;case 1:$=br(Oe,E[Ee]);break;default:if(Oe===G.ParameterStructures.byName)throw new Error(`Received ${Re} parameters for 'by Name' notification parameter structure.`);$=E.slice(Ee,ie).map(pe=>Xn(pe));break}}else{let ge=E;F=y.method,$=qi(y,ge)}let ye={jsonrpc:c,method:F,params:$};return wr(ye),e.write(ye).catch(ge=>{throw i.error("Sending notification failed."),ge})},onNotification:(y,E)=>{Bt();let F;return He.func(y)?d=y:E&&(He.string(y)?(F=y,p.set(y,{type:void 0,handler:E})):(F=y.method,p.set(y.method,{type:y,handler:E}))),{dispose:()=>{F!==void 0?p.delete(F):d=void 0}}},onProgress:(y,E,F)=>{if(g.has(E))throw new Error(`Progress handler for token ${E} already registered`);return g.set(E,F),{dispose:()=>{g.delete(E)}}},sendProgress:(y,E,F)=>sn.sendNotification(As.type,{token:E,value:F}),onUnhandledProgress:St.event,sendRequest:(y,...E)=>{Bt(),Nn();let F,$,ye;if(He.string(y)){F=y;let ie=E[0],Re=E[E.length-1],pe=0,Ie=G.ParameterStructures.auto;G.ParameterStructures.is(ie)&&(pe=1,Ie=ie);let tt=E.length;_l.CancellationToken.is(Re)&&(tt=tt-1,ye=Re);let Rt=tt-pe;switch(Rt){case 0:$=void 0;break;case 1:$=br(Ie,E[pe]);break;default:if(Ie===G.ParameterStructures.byName)throw new Error(`Received ${Rt} parameters for 'by Name' request parameter structure.`);$=E.slice(pe,tt).map(Os=>Xn(Os));break}}else{let ie=E;F=y.method,$=qi(y,ie);let Re=y.numberOfParams;ye=_l.CancellationToken.is(ie[Re])?ie[Re]:void 0}let ge=s++,Ee;ye&&(Ee=ye.onCancellationRequested(()=>{let ie=Ot.sender.sendCancellation(sn,ge);return ie===void 0?(i.log(`Received no promise from cancellation strategy when cancelling id ${ge}`),Promise.resolve()):ie.catch(()=>{i.log(`Sending cancellation messages for id ${ge} failed`)})}));let Oe={jsonrpc:c,id:ge,method:F,params:$};return Ei(Oe),typeof Ot.sender.enableCancellation=="function"&&Ot.sender.enableCancellation(Oe),new Promise(async(ie,Re)=>{let pe=Rt=>{ie(Rt),Ot.sender.cleanup(ge),Ee?.dispose()},Ie=Rt=>{Re(Rt),Ot.sender.cleanup(ge),Ee?.dispose()},tt={method:F,timerStart:Date.now(),resolve:pe,reject:Ie};try{await e.write(Oe),D.set(ge,tt)}catch(Rt){throw i.error("Sending request failed."),tt.reject(new G.ResponseError(G.ErrorCodes.MessageWriteError,Rt.message?Rt.message:"Unknown reason")),Rt}})},onRequest:(y,E)=>{Bt();let F=null;return Rl.is(y)?(F=void 0,u=y):He.string(y)?(F=null,E!==void 0&&(F=y,f.set(y,{handler:E,type:void 0}))):E!==void 0&&(F=y.method,f.set(y.method,{type:y,handler:E})),{dispose:()=>{F!==null&&(F!==void 0?f.delete(F):u=void 0)}}},hasPendingResponse:()=>D.size>0,trace:async(y,E,F)=>{let $=!1,ye=Xt.Text;F!==void 0&&(He.boolean(F)?$=F:($=F.sendNotification||!1,ye=F.traceFormat||Xt.Text)),x=y,I=ye,x===me.Off?M=void 0:M=E,$&&!Je()&&!Cn()&&await sn.sendNotification(Tl.type,{value:me.toString(y)})},onError:B.event,onClose:Z.event,onUnhandledNotification:fe.event,onDispose:ht.event,end:()=>{e.end()},dispose:()=>{if(Cn())return;Y=Dn.Disposed,ht.fire(void 0);let y=new G.ResponseError(G.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(let E of D.values())E.reject(y);D=new Map,k=new Map,P=new Set,S=new tv.LinkedMap,He.func(e.dispose)&&e.dispose(),He.func(t.dispose)&&t.dispose()},listen:()=>{Bt(),Nt(),Y=Dn.Listening,t.listen(xs)},inspect:()=>{(0,ev.default)().console.log("inspect")}};return sn.onNotification(ea.type,y=>{if(x===me.Off||!M)return;let E=x===me.Verbose||x===me.Compact;M.log(y.message,E?y.verbose:void 0)}),sn.onNotification(As.type,y=>{let E=g.get(y.token);E?E(y.value):St.fire(y)}),sn}ee.createMessageConnection=IT});var oa=b(T=>{"use strict";Object.defineProperty(T,"__esModule",{value:!0});T.ProgressType=T.ProgressToken=T.createMessageConnection=T.NullLogger=T.ConnectionOptions=T.ConnectionStrategy=T.AbstractMessageBuffer=T.WriteableStreamMessageWriter=T.AbstractMessageWriter=T.MessageWriter=T.ReadableStreamMessageReader=T.AbstractMessageReader=T.MessageReader=T.SharedArrayReceiverStrategy=T.SharedArraySenderStrategy=T.CancellationToken=T.CancellationTokenSource=T.Emitter=T.Event=T.Disposable=T.LRUCache=T.Touch=T.LinkedMap=T.ParameterStructures=T.NotificationType9=T.NotificationType8=T.NotificationType7=T.NotificationType6=T.NotificationType5=T.NotificationType4=T.NotificationType3=T.NotificationType2=T.NotificationType1=T.NotificationType0=T.NotificationType=T.ErrorCodes=T.ResponseError=T.RequestType9=T.RequestType8=T.RequestType7=T.RequestType6=T.RequestType5=T.RequestType4=T.RequestType3=T.RequestType2=T.RequestType1=T.RequestType0=T.RequestType=T.Message=T.RAL=void 0;T.MessageStrategy=T.CancellationStrategy=T.CancellationSenderStrategy=T.CancellationReceiverStrategy=T.ConnectionError=T.ConnectionErrors=T.LogTraceNotification=T.SetTraceNotification=T.TraceFormat=T.TraceValues=T.Trace=void 0;var ke=el();Object.defineProperty(T,"Message",{enumerable:!0,get:function(){return ke.Message}});Object.defineProperty(T,"RequestType",{enumerable:!0,get:function(){return ke.RequestType}});Object.defineProperty(T,"RequestType0",{enumerable:!0,get:function(){return ke.RequestType0}});Object.defineProperty(T,"RequestType1",{enumerable:!0,get:function(){return ke.RequestType1}});Object.defineProperty(T,"RequestType2",{enumerable:!0,get:function(){return ke.RequestType2}});Object.defineProperty(T,"RequestType3",{enumerable:!0,get:function(){return ke.RequestType3}});Object.defineProperty(T,"RequestType4",{enumerable:!0,get:function(){return ke.RequestType4}});Object.defineProperty(T,"RequestType5",{enumerable:!0,get:function(){return ke.RequestType5}});Object.defineProperty(T,"RequestType6",{enumerable:!0,get:function(){return ke.RequestType6}});Object.defineProperty(T,"RequestType7",{enumerable:!0,get:function(){return ke.RequestType7}});Object.defineProperty(T,"RequestType8",{enumerable:!0,get:function(){return ke.RequestType8}});Object.defineProperty(T,"RequestType9",{enumerable:!0,get:function(){return ke.RequestType9}});Object.defineProperty(T,"ResponseError",{enumerable:!0,get:function(){return ke.ResponseError}});Object.defineProperty(T,"ErrorCodes",{enumerable:!0,get:function(){return ke.ErrorCodes}});Object.defineProperty(T,"NotificationType",{enumerable:!0,get:function(){return ke.NotificationType}});Object.defineProperty(T,"NotificationType0",{enumerable:!0,get:function(){return ke.NotificationType0}});Object.defineProperty(T,"NotificationType1",{enumerable:!0,get:function(){return ke.NotificationType1}});Object.defineProperty(T,"NotificationType2",{enumerable:!0,get:function(){return ke.NotificationType2}});Object.defineProperty(T,"NotificationType3",{enumerable:!0,get:function(){return ke.NotificationType3}});Object.defineProperty(T,"NotificationType4",{enumerable:!0,get:function(){return ke.NotificationType4}});Object.defineProperty(T,"NotificationType5",{enumerable:!0,get:function(){return ke.NotificationType5}});Object.defineProperty(T,"NotificationType6",{enumerable:!0,get:function(){return ke.NotificationType6}});Object.defineProperty(T,"NotificationType7",{enumerable:!0,get:function(){return ke.NotificationType7}});Object.defineProperty(T,"NotificationType8",{enumerable:!0,get:function(){return ke.NotificationType8}});Object.defineProperty(T,"NotificationType9",{enumerable:!0,get:function(){return ke.NotificationType9}});Object.defineProperty(T,"ParameterStructures",{enumerable:!0,get:function(){return ke.ParameterStructures}});var xl=nl();Object.defineProperty(T,"LinkedMap",{enumerable:!0,get:function(){return xl.LinkedMap}});Object.defineProperty(T,"LRUCache",{enumerable:!0,get:function(){return xl.LRUCache}});Object.defineProperty(T,"Touch",{enumerable:!0,get:function(){return xl.Touch}});var NT=Vm();Object.defineProperty(T,"Disposable",{enumerable:!0,get:function(){return NT.Disposable}});var sv=Ii();Object.defineProperty(T,"Event",{enumerable:!0,get:function(){return sv.Event}});Object.defineProperty(T,"Emitter",{enumerable:!0,get:function(){return sv.Emitter}});var ov=Xo();Object.defineProperty(T,"CancellationTokenSource",{enumerable:!0,get:function(){return ov.CancellationTokenSource}});Object.defineProperty(T,"CancellationToken",{enumerable:!0,get:function(){return ov.CancellationToken}});var av=Km();Object.defineProperty(T,"SharedArraySenderStrategy",{enumerable:!0,get:function(){return av.SharedArraySenderStrategy}});Object.defineProperty(T,"SharedArrayReceiverStrategy",{enumerable:!0,get:function(){return av.SharedArrayReceiverStrategy}});var ql=Bm();Object.defineProperty(T,"MessageReader",{enumerable:!0,get:function(){return ql.MessageReader}});Object.defineProperty(T,"AbstractMessageReader",{enumerable:!0,get:function(){return ql.AbstractMessageReader}});Object.defineProperty(T,"ReadableStreamMessageReader",{enumerable:!0,get:function(){return ql.ReadableStreamMessageReader}});var kl=Qm();Object.defineProperty(T,"MessageWriter",{enumerable:!0,get:function(){return kl.MessageWriter}});Object.defineProperty(T,"AbstractMessageWriter",{enumerable:!0,get:function(){return kl.AbstractMessageWriter}});Object.defineProperty(T,"WriteableStreamMessageWriter",{enumerable:!0,get:function(){return kl.WriteableStreamMessageWriter}});var FT=Zm();Object.defineProperty(T,"AbstractMessageBuffer",{enumerable:!0,get:function(){return FT.AbstractMessageBuffer}});var dt=iv();Object.defineProperty(T,"ConnectionStrategy",{enumerable:!0,get:function(){return dt.ConnectionStrategy}});Object.defineProperty(T,"ConnectionOptions",{enumerable:!0,get:function(){return dt.ConnectionOptions}});Object.defineProperty(T,"NullLogger",{enumerable:!0,get:function(){return dt.NullLogger}});Object.defineProperty(T,"createMessageConnection",{enumerable:!0,get:function(){return dt.createMessageConnection}});Object.defineProperty(T,"ProgressToken",{enumerable:!0,get:function(){return dt.ProgressToken}});Object.defineProperty(T,"ProgressType",{enumerable:!0,get:function(){return dt.ProgressType}});Object.defineProperty(T,"Trace",{enumerable:!0,get:function(){return dt.Trace}});Object.defineProperty(T,"TraceValues",{enumerable:!0,get:function(){return dt.TraceValues}});Object.defineProperty(T,"TraceFormat",{enumerable:!0,get:function(){return dt.TraceFormat}});Object.defineProperty(T,"SetTraceNotification",{enumerable:!0,get:function(){return dt.SetTraceNotification}});Object.defineProperty(T,"LogTraceNotification",{enumerable:!0,get:function(){return dt.LogTraceNotification}});Object.defineProperty(T,"ConnectionErrors",{enumerable:!0,get:function(){return dt.ConnectionErrors}});Object.defineProperty(T,"ConnectionError",{enumerable:!0,get:function(){return dt.ConnectionError}});Object.defineProperty(T,"CancellationReceiverStrategy",{enumerable:!0,get:function(){return dt.CancellationReceiverStrategy}});Object.defineProperty(T,"CancellationSenderStrategy",{enumerable:!0,get:function(){return dt.CancellationSenderStrategy}});Object.defineProperty(T,"CancellationStrategy",{enumerable:!0,get:function(){return dt.CancellationStrategy}});Object.defineProperty(T,"MessageStrategy",{enumerable:!0,get:function(){return dt.MessageStrategy}});var MT=_r();T.RAL=MT.default});var lv=b(Fl=>{"use strict";Object.defineProperty(Fl,"__esModule",{value:!0});var cv=require("util"),Jn=oa(),aa=class t extends Jn.AbstractMessageBuffer{constructor(e="utf-8"){super(e)}emptyBuffer(){return t.emptyBuffer}fromString(e,n){return Buffer.from(e,n)}toString(e,n){return e instanceof Buffer?e.toString(n):new cv.TextDecoder(n).decode(e)}asNative(e,n){return n===void 0?e instanceof Buffer?e:Buffer.from(e):e instanceof Buffer?e.slice(0,n):Buffer.from(e,0,n)}allocNative(e){return Buffer.allocUnsafe(e)}};aa.emptyBuffer=Buffer.allocUnsafe(0);var Ol=class{constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),Jn.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),Jn.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),Jn.Disposable.create(()=>this.stream.off("end",e))}onData(e){return this.stream.on("data",e),Jn.Disposable.create(()=>this.stream.off("data",e))}},Il=class{constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),Jn.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),Jn.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),Jn.Disposable.create(()=>this.stream.off("end",e))}write(e,n){return new Promise((r,i)=>{let s=o=>{o==null?r():i(o)};typeof e=="string"?this.stream.write(e,n,s):this.stream.write(e,s)})}end(){this.stream.end()}},uv=Object.freeze({messageBuffer:Object.freeze({create:t=>new aa(t)}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:(t,e)=>{try{return Promise.resolve(Buffer.from(JSON.stringify(t,void 0,0),e.charset))}catch(n){return Promise.reject(n)}}}),decoder:Object.freeze({name:"application/json",decode:(t,e)=>{try{return t instanceof Buffer?Promise.resolve(JSON.parse(t.toString(e.charset))):Promise.resolve(JSON.parse(new cv.TextDecoder(e.charset).decode(t)))}catch(n){return Promise.reject(n)}}})}),stream:Object.freeze({asReadableStream:t=>new Ol(t),asWritableStream:t=>new Il(t)}),console,timer:Object.freeze({setTimeout(t,e,...n){let r=setTimeout(t,e,...n);return{dispose:()=>clearTimeout(r)}},setImmediate(t,...e){let n=setImmediate(t,...e);return{dispose:()=>clearImmediate(n)}},setInterval(t,e,...n){let r=setInterval(t,e,...n);return{dispose:()=>clearInterval(r)}}})});function Nl(){return uv}(function(t){function e(){Jn.RAL.install(uv)}t.install=e})(Nl||(Nl={}));Fl.default=Nl});var ri=b(ue=>{"use strict";var AT=ue&&ue.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),LT=ue&&ue.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&AT(e,t,n)};Object.defineProperty(ue,"__esModule",{value:!0});ue.createMessageConnection=ue.createServerSocketTransport=ue.createClientSocketTransport=ue.createServerPipeTransport=ue.createClientPipeTransport=ue.generateRandomPipeName=ue.StreamMessageWriter=ue.StreamMessageReader=ue.SocketMessageWriter=ue.SocketMessageReader=ue.PortMessageWriter=ue.PortMessageReader=ue.IPCMessageWriter=ue.IPCMessageReader=void 0;var Li=lv();Li.default.install();var dv=require("path"),jT=require("os"),$T=require("crypto"),la=require("net"),Jt=oa();LT(oa(),ue);var Ml=class extends Jt.AbstractMessageReader{constructor(e){super(),this.process=e;let n=this.process;n.on("error",r=>this.fireError(r)),n.on("close",()=>this.fireClose())}listen(e){return this.process.on("message",e),Jt.Disposable.create(()=>this.process.off("message",e))}};ue.IPCMessageReader=Ml;var Al=class extends Jt.AbstractMessageWriter{constructor(e){super(),this.process=e,this.errorCount=0;let n=this.process;n.on("error",r=>this.fireError(r)),n.on("close",()=>this.fireClose)}write(e){try{return typeof this.process.send=="function"&&this.process.send(e,void 0,void 0,n=>{n?(this.errorCount++,this.handleError(n,e)):this.errorCount=0}),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};ue.IPCMessageWriter=Al;var Ll=class extends Jt.AbstractMessageReader{constructor(e){super(),this.onData=new Jt.Emitter,e.on("close",()=>this.fireClose),e.on("error",n=>this.fireError(n)),e.on("message",n=>{this.onData.fire(n)})}listen(e){return this.onData.event(e)}};ue.PortMessageReader=Ll;var jl=class extends Jt.AbstractMessageWriter{constructor(e){super(),this.port=e,this.errorCount=0,e.on("close",()=>this.fireClose()),e.on("error",n=>this.fireError(n))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};ue.PortMessageWriter=jl;var ti=class extends Jt.ReadableStreamMessageReader{constructor(e,n="utf-8"){super((0,Li.default)().stream.asReadableStream(e),n)}};ue.SocketMessageReader=ti;var ni=class extends Jt.WriteableStreamMessageWriter{constructor(e,n){super((0,Li.default)().stream.asWritableStream(e),n),this.socket=e}dispose(){super.dispose(),this.socket.destroy()}};ue.SocketMessageWriter=ni;var ca=class extends Jt.ReadableStreamMessageReader{constructor(e,n){super((0,Li.default)().stream.asReadableStream(e),n)}};ue.StreamMessageReader=ca;var ua=class extends Jt.WriteableStreamMessageWriter{constructor(e,n){super((0,Li.default)().stream.asWritableStream(e),n)}};ue.StreamMessageWriter=ua;var fv=process.env.XDG_RUNTIME_DIR,HT=new Map([["linux",107],["darwin",103]]);function UT(){let t=(0,$T.randomBytes)(21).toString("hex");if(process.platform==="win32")return`\\\\.\\pipe\\vscode-jsonrpc-${t}-sock`;let e;fv?e=dv.join(fv,`vscode-ipc-${t}.sock`):e=dv.join(jT.tmpdir(),`vscode-${t}.sock`);let n=HT.get(process.platform);return n!==void 0&&e.length>n&&(0,Li.default)().console.warn(`WARNING: IPC handle "${e}" is longer than ${n} characters.`),e}ue.generateRandomPipeName=UT;function VT(t,e="utf-8"){let n,r=new Promise((i,s)=>{n=i});return new Promise((i,s)=>{let o=(0,la.createServer)(a=>{o.close(),n([new ti(a,e),new ni(a,e)])});o.on("error",s),o.listen(t,()=>{o.removeListener("error",s),i({onConnected:()=>r})})})}ue.createClientPipeTransport=VT;function WT(t,e="utf-8"){let n=(0,la.createConnection)(t);return[new ti(n,e),new ni(n,e)]}ue.createServerPipeTransport=WT;function KT(t,e="utf-8"){let n,r=new Promise((i,s)=>{n=i});return new Promise((i,s)=>{let o=(0,la.createServer)(a=>{o.close(),n([new ti(a,e),new ni(a,e)])});o.on("error",s),o.listen(t,"127.0.0.1",()=>{o.removeListener("error",s),i({onConnected:()=>r})})})}ue.createClientSocketTransport=KT;function zT(t,e="utf-8"){let n=(0,la.createConnection)(t,"127.0.0.1");return[new ti(n,e),new ni(n,e)]}ue.createServerSocketTransport=zT;function BT(t){let e=t;return e.read!==void 0&&e.addListener!==void 0}function GT(t){let e=t;return e.write!==void 0&&e.addListener!==void 0}function XT(t,e,n,r){n||(n=Jt.NullLogger);let i=BT(t)?new ca(t):t,s=GT(e)?new ua(e):e;return Jt.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,Jt.createMessageConnection)(i,s,n,r)}ue.createMessageConnection=XT});var $l=b((i1,pv)=>{"use strict";pv.exports=ri()});var wa={};hT(wa,{AnnotatedTextEdit:()=>Yn,ChangeAnnotation:()=>ii,ChangeAnnotationIdentifier:()=>ft,CodeAction:()=>Cd,CodeActionContext:()=>bd,CodeActionKind:()=>wd,CodeActionTriggerKind:()=>zs,CodeDescription:()=>Jl,CodeLens:()=>_d,Color:()=>fa,ColorInformation:()=>Wl,ColorPresentation:()=>Kl,Command:()=>si,CompletionItem:()=>ad,CompletionItemKind:()=>td,CompletionItemLabelDetails:()=>od,CompletionItemTag:()=>rd,CompletionList:()=>cd,CreateFile:()=>$i,DeleteFile:()=>Ui,Diagnostic:()=>Us,DiagnosticRelatedInformation:()=>pa,DiagnosticSeverity:()=>Gl,DiagnosticTag:()=>Xl,DocumentHighlight:()=>pd,DocumentHighlightKind:()=>fd,DocumentLink:()=>Dd,DocumentSymbol:()=>yd,DocumentUri:()=>Hl,EOL:()=>JT,FoldingRange:()=>Bl,FoldingRangeKind:()=>zl,FormattingOptions:()=>Sd,Hover:()=>ud,InlayHint:()=>Id,InlayHintKind:()=>va,InlayHintLabelPart:()=>ya,InlineCompletionContext:()=>jd,InlineCompletionItem:()=>Fd,InlineCompletionList:()=>Md,InlineCompletionTriggerKind:()=>Ad,InlineValueContext:()=>Od,InlineValueEvaluatableExpression:()=>kd,InlineValueText:()=>xd,InlineValueVariableLookup:()=>qd,InsertReplaceEdit:()=>id,InsertTextFormat:()=>nd,InsertTextMode:()=>sd,Location:()=>Hs,LocationLink:()=>Vl,MarkedString:()=>Ks,MarkupContent:()=>Vi,MarkupKind:()=>ma,OptionalVersionedTextDocumentIdentifier:()=>Ws,ParameterInformation:()=>ld,Position:()=>on,Range:()=>Ke,RenameFile:()=>Hi,SelectedCompletionInfo:()=>Ld,SelectionRange:()=>Rd,SemanticTokenModifiers:()=>Pd,SemanticTokenTypes:()=>Td,SemanticTokens:()=>Ed,SignatureInformation:()=>dd,StringValue:()=>Nd,SymbolInformation:()=>md,SymbolKind:()=>hd,SymbolTag:()=>gd,TextDocument:()=>Hd,TextDocumentEdit:()=>Vs,TextDocumentIdentifier:()=>Ql,TextDocumentItem:()=>ed,TextEdit:()=>An,URI:()=>da,VersionedTextDocumentIdentifier:()=>Zl,WorkspaceChange:()=>Yl,WorkspaceEdit:()=>ha,WorkspaceFolder:()=>$d,WorkspaceSymbol:()=>vd,integer:()=>Ul,uinteger:()=>$s});var Hl,da,Ul,$s,on,Ke,Hs,Vl,fa,Wl,Kl,zl,Bl,pa,Gl,Xl,Jl,Us,si,An,ii,ft,Yn,Vs,$i,Hi,Ui,ha,ji,ga,Yl,Ql,Zl,Ws,ed,ma,Vi,td,nd,rd,id,sd,od,ad,cd,Ks,ud,ld,dd,fd,pd,hd,gd,md,vd,yd,wd,zs,bd,Cd,_d,Sd,Dd,Rd,Td,Pd,Ed,xd,qd,kd,Od,va,ya,Id,Nd,Fd,Md,Ad,Ld,jd,$d,JT,Hd,Ud,_,ba=pT(()=>{"use strict";(function(t){function e(n){return typeof n=="string"}t.is=e})(Hl||(Hl={}));(function(t){function e(n){return typeof n=="string"}t.is=e})(da||(da={}));(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function e(n){return typeof n=="number"&&t.MIN_VALUE<=n&&n<=t.MAX_VALUE}t.is=e})(Ul||(Ul={}));(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function e(n){return typeof n=="number"&&t.MIN_VALUE<=n&&n<=t.MAX_VALUE}t.is=e})($s||($s={}));(function(t){function e(r,i){return r===Number.MAX_VALUE&&(r=$s.MAX_VALUE),i===Number.MAX_VALUE&&(i=$s.MAX_VALUE),{line:r,character:i}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&_.uinteger(i.line)&&_.uinteger(i.character)}t.is=n})(on||(on={}));(function(t){function e(r,i,s,o){if(_.uinteger(r)&&_.uinteger(i)&&_.uinteger(s)&&_.uinteger(o))return{start:on.create(r,i),end:on.create(s,o)};if(on.is(r)&&on.is(i))return{start:r,end:i};throw new Error(`Range#create called with invalid arguments[${r}, ${i}, ${s}, ${o}]`)}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&on.is(i.start)&&on.is(i.end)}t.is=n})(Ke||(Ke={}));(function(t){function e(r,i){return{uri:r,range:i}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&Ke.is(i.range)&&(_.string(i.uri)||_.undefined(i.uri))}t.is=n})(Hs||(Hs={}));(function(t){function e(r,i,s,o){return{targetUri:r,targetRange:i,targetSelectionRange:s,originSelectionRange:o}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&Ke.is(i.targetRange)&&_.string(i.targetUri)&&Ke.is(i.targetSelectionRange)&&(Ke.is(i.originSelectionRange)||_.undefined(i.originSelectionRange))}t.is=n})(Vl||(Vl={}));(function(t){function e(r,i,s,o){return{red:r,green:i,blue:s,alpha:o}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&_.numberRange(i.red,0,1)&&_.numberRange(i.green,0,1)&&_.numberRange(i.blue,0,1)&&_.numberRange(i.alpha,0,1)}t.is=n})(fa||(fa={}));(function(t){function e(r,i){return{range:r,color:i}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&Ke.is(i.range)&&fa.is(i.color)}t.is=n})(Wl||(Wl={}));(function(t){function e(r,i,s){return{label:r,textEdit:i,additionalTextEdits:s}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&_.string(i.label)&&(_.undefined(i.textEdit)||An.is(i))&&(_.undefined(i.additionalTextEdits)||_.typedArray(i.additionalTextEdits,An.is))}t.is=n})(Kl||(Kl={}));(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(zl||(zl={}));(function(t){function e(r,i,s,o,a,c){let u={startLine:r,endLine:i};return _.defined(s)&&(u.startCharacter=s),_.defined(o)&&(u.endCharacter=o),_.defined(a)&&(u.kind=a),_.defined(c)&&(u.collapsedText=c),u}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&_.uinteger(i.startLine)&&_.uinteger(i.startLine)&&(_.undefined(i.startCharacter)||_.uinteger(i.startCharacter))&&(_.undefined(i.endCharacter)||_.uinteger(i.endCharacter))&&(_.undefined(i.kind)||_.string(i.kind))}t.is=n})(Bl||(Bl={}));(function(t){function e(r,i){return{location:r,message:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&Hs.is(i.location)&&_.string(i.message)}t.is=n})(pa||(pa={}));(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(Gl||(Gl={}));(function(t){t.Unnecessary=1,t.Deprecated=2})(Xl||(Xl={}));(function(t){function e(n){let r=n;return _.objectLiteral(r)&&_.string(r.href)}t.is=e})(Jl||(Jl={}));(function(t){function e(r,i,s,o,a,c){let u={range:r,message:i};return _.defined(s)&&(u.severity=s),_.defined(o)&&(u.code=o),_.defined(a)&&(u.source=a),_.defined(c)&&(u.relatedInformation=c),u}t.create=e;function n(r){var i;let s=r;return _.defined(s)&&Ke.is(s.range)&&_.string(s.message)&&(_.number(s.severity)||_.undefined(s.severity))&&(_.integer(s.code)||_.string(s.code)||_.undefined(s.code))&&(_.undefined(s.codeDescription)||_.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(_.string(s.source)||_.undefined(s.source))&&(_.undefined(s.relatedInformation)||_.typedArray(s.relatedInformation,pa.is))}t.is=n})(Us||(Us={}));(function(t){function e(r,i,...s){let o={title:r,command:i};return _.defined(s)&&s.length>0&&(o.arguments=s),o}t.create=e;function n(r){let i=r;return _.defined(i)&&_.string(i.title)&&_.string(i.command)}t.is=n})(si||(si={}));(function(t){function e(s,o){return{range:s,newText:o}}t.replace=e;function n(s,o){return{range:{start:s,end:s},newText:o}}t.insert=n;function r(s){return{range:s,newText:""}}t.del=r;function i(s){let o=s;return _.objectLiteral(o)&&_.string(o.newText)&&Ke.is(o.range)}t.is=i})(An||(An={}));(function(t){function e(r,i,s){let o={label:r};return i!==void 0&&(o.needsConfirmation=i),s!==void 0&&(o.description=s),o}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&_.string(i.label)&&(_.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(_.string(i.description)||i.description===void 0)}t.is=n})(ii||(ii={}));(function(t){function e(n){let r=n;return _.string(r)}t.is=e})(ft||(ft={}));(function(t){function e(s,o,a){return{range:s,newText:o,annotationId:a}}t.replace=e;function n(s,o,a){return{range:{start:s,end:s},newText:o,annotationId:a}}t.insert=n;function r(s,o){return{range:s,newText:"",annotationId:o}}t.del=r;function i(s){let o=s;return An.is(o)&&(ii.is(o.annotationId)||ft.is(o.annotationId))}t.is=i})(Yn||(Yn={}));(function(t){function e(r,i){return{textDocument:r,edits:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&Ws.is(i.textDocument)&&Array.isArray(i.edits)}t.is=n})(Vs||(Vs={}));(function(t){function e(r,i,s){let o={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}t.create=e;function n(r){let i=r;return i&&i.kind==="create"&&_.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||_.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||_.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||ft.is(i.annotationId))}t.is=n})($i||($i={}));(function(t){function e(r,i,s,o){let a={kind:"rename",oldUri:r,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(a.options=s),o!==void 0&&(a.annotationId=o),a}t.create=e;function n(r){let i=r;return i&&i.kind==="rename"&&_.string(i.oldUri)&&_.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||_.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||_.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||ft.is(i.annotationId))}t.is=n})(Hi||(Hi={}));(function(t){function e(r,i,s){let o={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}t.create=e;function n(r){let i=r;return i&&i.kind==="delete"&&_.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||_.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||_.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||ft.is(i.annotationId))}t.is=n})(Ui||(Ui={}));(function(t){function e(n){let r=n;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(i=>_.string(i.kind)?$i.is(i)||Hi.is(i)||Ui.is(i):Vs.is(i)))}t.is=e})(ha||(ha={}));ji=class{constructor(e,n){this.edits=e,this.changeAnnotations=n}insert(e,n,r){let i,s;if(r===void 0?i=An.insert(e,n):ft.is(r)?(s=r,i=Yn.insert(e,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),s=this.changeAnnotations.manage(r),i=Yn.insert(e,n,s)),this.edits.push(i),s!==void 0)return s}replace(e,n,r){let i,s;if(r===void 0?i=An.replace(e,n):ft.is(r)?(s=r,i=Yn.replace(e,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),s=this.changeAnnotations.manage(r),i=Yn.replace(e,n,s)),this.edits.push(i),s!==void 0)return s}delete(e,n){let r,i;if(n===void 0?r=An.del(e):ft.is(n)?(i=n,r=Yn.del(e,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=Yn.del(e,i)),this.edits.push(r),i!==void 0)return i}add(e){this.edits.push(e)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(e){if(e===void 0)throw new Error("Text edit change is not configured to manage change annotations.")}},ga=class{constructor(e){this._annotations=e===void 0?Object.create(null):e,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(e,n){let r;if(ft.is(e)?r=e:(r=this.nextId(),n=e),this._annotations[r]!==void 0)throw new Error(`Id ${r} is already in use.`);if(n===void 0)throw new Error(`No annotation provided for id ${r}`);return this._annotations[r]=n,this._size++,r}nextId(){return this._counter++,this._counter.toString()}},Yl=class{constructor(e){this._textEditChanges=Object.create(null),e!==void 0?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new ga(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach(n=>{if(Vs.is(n)){let r=new ji(n.edits,this._changeAnnotations);this._textEditChanges[n.textDocument.uri]=r}})):e.changes&&Object.keys(e.changes).forEach(n=>{let r=new ji(e.changes[n]);this._textEditChanges[n]=r})):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(e){if(Ws.is(e)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let n={uri:e.uri,version:e.version},r=this._textEditChanges[n.uri];if(!r){let i=[],s={textDocument:n,edits:i};this._workspaceEdit.documentChanges.push(s),r=new ji(i,this._changeAnnotations),this._textEditChanges[n.uri]=r}return r}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");let n=this._textEditChanges[e];if(!n){let r=[];this._workspaceEdit.changes[e]=r,n=new ji(r),this._textEditChanges[e]=n}return n}}initDocumentChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new ga,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))}createFile(e,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let i;ii.is(n)||ft.is(n)?i=n:r=n;let s,o;if(i===void 0?s=$i.create(e,r):(o=ft.is(i)?i:this._changeAnnotations.manage(i),s=$i.create(e,r,o)),this._workspaceEdit.documentChanges.push(s),o!==void 0)return o}renameFile(e,n,r,i){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let s;ii.is(r)||ft.is(r)?s=r:i=r;let o,a;if(s===void 0?o=Hi.create(e,n,i):(a=ft.is(s)?s:this._changeAnnotations.manage(s),o=Hi.create(e,n,i,a)),this._workspaceEdit.documentChanges.push(o),a!==void 0)return a}deleteFile(e,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let i;ii.is(n)||ft.is(n)?i=n:r=n;let s,o;if(i===void 0?s=Ui.create(e,r):(o=ft.is(i)?i:this._changeAnnotations.manage(i),s=Ui.create(e,r,o)),this._workspaceEdit.documentChanges.push(s),o!==void 0)return o}};(function(t){function e(r){return{uri:r}}t.create=e;function n(r){let i=r;return _.defined(i)&&_.string(i.uri)}t.is=n})(Ql||(Ql={}));(function(t){function e(r,i){return{uri:r,version:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&_.string(i.uri)&&_.integer(i.version)}t.is=n})(Zl||(Zl={}));(function(t){function e(r,i){return{uri:r,version:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&_.string(i.uri)&&(i.version===null||_.integer(i.version))}t.is=n})(Ws||(Ws={}));(function(t){function e(r,i,s,o){return{uri:r,languageId:i,version:s,text:o}}t.create=e;function n(r){let i=r;return _.defined(i)&&_.string(i.uri)&&_.string(i.languageId)&&_.integer(i.version)&&_.string(i.text)}t.is=n})(ed||(ed={}));(function(t){t.PlainText="plaintext",t.Markdown="markdown";function e(n){let r=n;return r===t.PlainText||r===t.Markdown}t.is=e})(ma||(ma={}));(function(t){function e(n){let r=n;return _.objectLiteral(n)&&ma.is(r.kind)&&_.string(r.value)}t.is=e})(Vi||(Vi={}));(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(td||(td={}));(function(t){t.PlainText=1,t.Snippet=2})(nd||(nd={}));(function(t){t.Deprecated=1})(rd||(rd={}));(function(t){function e(r,i,s){return{newText:r,insert:i,replace:s}}t.create=e;function n(r){let i=r;return i&&_.string(i.newText)&&Ke.is(i.insert)&&Ke.is(i.replace)}t.is=n})(id||(id={}));(function(t){t.asIs=1,t.adjustIndentation=2})(sd||(sd={}));(function(t){function e(n){let r=n;return r&&(_.string(r.detail)||r.detail===void 0)&&(_.string(r.description)||r.description===void 0)}t.is=e})(od||(od={}));(function(t){function e(n){return{label:n}}t.create=e})(ad||(ad={}));(function(t){function e(n,r){return{items:n||[],isIncomplete:!!r}}t.create=e})(cd||(cd={}));(function(t){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}t.fromPlainText=e;function n(r){let i=r;return _.string(i)||_.objectLiteral(i)&&_.string(i.language)&&_.string(i.value)}t.is=n})(Ks||(Ks={}));(function(t){function e(n){let r=n;return!!r&&_.objectLiteral(r)&&(Vi.is(r.contents)||Ks.is(r.contents)||_.typedArray(r.contents,Ks.is))&&(n.range===void 0||Ke.is(n.range))}t.is=e})(ud||(ud={}));(function(t){function e(n,r){return r?{label:n,documentation:r}:{label:n}}t.create=e})(ld||(ld={}));(function(t){function e(n,r,...i){let s={label:n};return _.defined(r)&&(s.documentation=r),_.defined(i)?s.parameters=i:s.parameters=[],s}t.create=e})(dd||(dd={}));(function(t){t.Text=1,t.Read=2,t.Write=3})(fd||(fd={}));(function(t){function e(n,r){let i={range:n};return _.number(r)&&(i.kind=r),i}t.create=e})(pd||(pd={}));(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(hd||(hd={}));(function(t){t.Deprecated=1})(gd||(gd={}));(function(t){function e(n,r,i,s,o){let a={name:n,kind:r,location:{uri:s,range:i}};return o&&(a.containerName=o),a}t.create=e})(md||(md={}));(function(t){function e(n,r,i,s){return s!==void 0?{name:n,kind:r,location:{uri:i,range:s}}:{name:n,kind:r,location:{uri:i}}}t.create=e})(vd||(vd={}));(function(t){function e(r,i,s,o,a,c){let u={name:r,detail:i,kind:s,range:o,selectionRange:a};return c!==void 0&&(u.children=c),u}t.create=e;function n(r){let i=r;return i&&_.string(i.name)&&_.number(i.kind)&&Ke.is(i.range)&&Ke.is(i.selectionRange)&&(i.detail===void 0||_.string(i.detail))&&(i.deprecated===void 0||_.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}t.is=n})(yd||(yd={}));(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})(wd||(wd={}));(function(t){t.Invoked=1,t.Automatic=2})(zs||(zs={}));(function(t){function e(r,i,s){let o={diagnostics:r};return i!=null&&(o.only=i),s!=null&&(o.triggerKind=s),o}t.create=e;function n(r){let i=r;return _.defined(i)&&_.typedArray(i.diagnostics,Us.is)&&(i.only===void 0||_.typedArray(i.only,_.string))&&(i.triggerKind===void 0||i.triggerKind===zs.Invoked||i.triggerKind===zs.Automatic)}t.is=n})(bd||(bd={}));(function(t){function e(r,i,s){let o={title:r},a=!0;return typeof i=="string"?(a=!1,o.kind=i):si.is(i)?o.command=i:o.edit=i,a&&s!==void 0&&(o.kind=s),o}t.create=e;function n(r){let i=r;return i&&_.string(i.title)&&(i.diagnostics===void 0||_.typedArray(i.diagnostics,Us.is))&&(i.kind===void 0||_.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||si.is(i.command))&&(i.isPreferred===void 0||_.boolean(i.isPreferred))&&(i.edit===void 0||ha.is(i.edit))}t.is=n})(Cd||(Cd={}));(function(t){function e(r,i){let s={range:r};return _.defined(i)&&(s.data=i),s}t.create=e;function n(r){let i=r;return _.defined(i)&&Ke.is(i.range)&&(_.undefined(i.command)||si.is(i.command))}t.is=n})(_d||(_d={}));(function(t){function e(r,i){return{tabSize:r,insertSpaces:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&_.uinteger(i.tabSize)&&_.boolean(i.insertSpaces)}t.is=n})(Sd||(Sd={}));(function(t){function e(r,i,s){return{range:r,target:i,data:s}}t.create=e;function n(r){let i=r;return _.defined(i)&&Ke.is(i.range)&&(_.undefined(i.target)||_.string(i.target))}t.is=n})(Dd||(Dd={}));(function(t){function e(r,i){return{range:r,parent:i}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&Ke.is(i.range)&&(i.parent===void 0||t.is(i.parent))}t.is=n})(Rd||(Rd={}));(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(Td||(Td={}));(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(Pd||(Pd={}));(function(t){function e(n){let r=n;return _.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}t.is=e})(Ed||(Ed={}));(function(t){function e(r,i){return{range:r,text:i}}t.create=e;function n(r){let i=r;return i!=null&&Ke.is(i.range)&&_.string(i.text)}t.is=n})(xd||(xd={}));(function(t){function e(r,i,s){return{range:r,variableName:i,caseSensitiveLookup:s}}t.create=e;function n(r){let i=r;return i!=null&&Ke.is(i.range)&&_.boolean(i.caseSensitiveLookup)&&(_.string(i.variableName)||i.variableName===void 0)}t.is=n})(qd||(qd={}));(function(t){function e(r,i){return{range:r,expression:i}}t.create=e;function n(r){let i=r;return i!=null&&Ke.is(i.range)&&(_.string(i.expression)||i.expression===void 0)}t.is=n})(kd||(kd={}));(function(t){function e(r,i){return{frameId:r,stoppedLocation:i}}t.create=e;function n(r){let i=r;return _.defined(i)&&Ke.is(r.stoppedLocation)}t.is=n})(Od||(Od={}));(function(t){t.Type=1,t.Parameter=2;function e(n){return n===1||n===2}t.is=e})(va||(va={}));(function(t){function e(r){return{value:r}}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&(i.tooltip===void 0||_.string(i.tooltip)||Vi.is(i.tooltip))&&(i.location===void 0||Hs.is(i.location))&&(i.command===void 0||si.is(i.command))}t.is=n})(ya||(ya={}));(function(t){function e(r,i,s){let o={position:r,label:i};return s!==void 0&&(o.kind=s),o}t.create=e;function n(r){let i=r;return _.objectLiteral(i)&&on.is(i.position)&&(_.string(i.label)||_.typedArray(i.label,ya.is))&&(i.kind===void 0||va.is(i.kind))&&i.textEdits===void 0||_.typedArray(i.textEdits,An.is)&&(i.tooltip===void 0||_.string(i.tooltip)||Vi.is(i.tooltip))&&(i.paddingLeft===void 0||_.boolean(i.paddingLeft))&&(i.paddingRight===void 0||_.boolean(i.paddingRight))}t.is=n})(Id||(Id={}));(function(t){function e(n){return{kind:"snippet",value:n}}t.createSnippet=e})(Nd||(Nd={}));(function(t){function e(n,r,i,s){return{insertText:n,filterText:r,range:i,command:s}}t.create=e})(Fd||(Fd={}));(function(t){function e(n){return{items:n}}t.create=e})(Md||(Md={}));(function(t){t.Invoked=0,t.Automatic=1})(Ad||(Ad={}));(function(t){function e(n,r){return{range:n,text:r}}t.create=e})(Ld||(Ld={}));(function(t){function e(n,r){return{triggerKind:n,selectedCompletionInfo:r}}t.create=e})(jd||(jd={}));(function(t){function e(n){let r=n;return _.objectLiteral(r)&&da.is(r.uri)&&_.string(r.name)}t.is=e})($d||($d={}));JT=[`
`,`\r
`,"\r"];(function(t){function e(s,o,a,c){return new Ud(s,o,a,c)}t.create=e;function n(s){let o=s;return!!(_.defined(o)&&_.string(o.uri)&&(_.undefined(o.languageId)||_.string(o.languageId))&&_.uinteger(o.lineCount)&&_.func(o.getText)&&_.func(o.positionAt)&&_.func(o.offsetAt))}t.is=n;function r(s,o){let a=s.getText(),c=i(o,(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),u=a.length;for(let f=c.length-1;f>=0;f--){let d=c[f],p=s.offsetAt(d.range.start),g=s.offsetAt(d.range.end);if(g<=u)a=a.substring(0,p)+d.newText+a.substring(g,a.length);else throw new Error("Overlapping edit");u=p}return a}t.applyEdits=r;function i(s,o){if(s.length<=1)return s;let a=s.length/2|0,c=s.slice(0,a),u=s.slice(a);i(c,o),i(u,o);let f=0,d=0,p=0;for(;f<c.length&&d<u.length;)o(c[f],u[d])<=0?s[p++]=c[f++]:s[p++]=u[d++];for(;f<c.length;)s[p++]=c[f++];for(;d<u.length;)s[p++]=u[d++];return s}})(Hd||(Hd={}));Ud=class{constructor(e,n,r,i){this._uri=e,this._languageId=n,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let n=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(n,r)}return this._content}update(e,n){this._content=e.text,this._version=n,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],n=this._content,r=!0;for(let i=0;i<n.length;i++){r&&(e.push(i),r=!1);let s=n.charAt(i);r=s==="\r"||s===`
`,s==="\r"&&i+1<n.length&&n.charAt(i+1)===`
`&&i++}r&&n.length>0&&e.push(n.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let n=this.getLineOffsets(),r=0,i=n.length;if(i===0)return on.create(0,e);for(;r<i;){let o=Math.floor((r+i)/2);n[o]>e?i=o:r=o+1}let s=r-1;return on.create(s,e-n[s])}offsetAt(e){let n=this.getLineOffsets();if(e.line>=n.length)return this._content.length;if(e.line<0)return 0;let r=n[e.line],i=e.line+1<n.length?n[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,i),r)}get lineCount(){return this.getLineOffsets().length}};(function(t){let e=Object.prototype.toString;function n(g){return typeof g<"u"}t.defined=n;function r(g){return typeof g>"u"}t.undefined=r;function i(g){return g===!0||g===!1}t.boolean=i;function s(g){return e.call(g)==="[object String]"}t.string=s;function o(g){return e.call(g)==="[object Number]"}t.number=o;function a(g,m,S){return e.call(g)==="[object Number]"&&m<=g&&g<=S}t.numberRange=a;function c(g){return e.call(g)==="[object Number]"&&-2147483648<=g&&g<=2147483647}t.integer=c;function u(g){return e.call(g)==="[object Number]"&&0<=g&&g<=2147483647}t.uinteger=u;function f(g){return e.call(g)==="[object Function]"}t.func=f;function d(g){return g!==null&&typeof g=="object"}t.objectLiteral=d;function p(g,m){return Array.isArray(g)&&g.every(m)}t.typedArray=p})(_||(_={}))});var Ae=b(Mt=>{"use strict";Object.defineProperty(Mt,"__esModule",{value:!0});Mt.ProtocolNotificationType=Mt.ProtocolNotificationType0=Mt.ProtocolRequestType=Mt.ProtocolRequestType0=Mt.RegistrationType=Mt.MessageDirection=void 0;var Wi=ri(),hv;(function(t){t.clientToServer="clientToServer",t.serverToClient="serverToClient",t.both="both"})(hv||(Mt.MessageDirection=hv={}));var Vd=class{constructor(e){this.method=e}};Mt.RegistrationType=Vd;var Wd=class extends Wi.RequestType0{constructor(e){super(e)}};Mt.ProtocolRequestType0=Wd;var Kd=class extends Wi.RequestType{constructor(e){super(e,Wi.ParameterStructures.byName)}};Mt.ProtocolRequestType=Kd;var zd=class extends Wi.NotificationType0{constructor(e){super(e)}};Mt.ProtocolNotificationType0=zd;var Bd=class extends Wi.NotificationType{constructor(e){super(e,Wi.ParameterStructures.byName)}};Mt.ProtocolNotificationType=Bd});var Ca=b(Ze=>{"use strict";Object.defineProperty(Ze,"__esModule",{value:!0});Ze.objectLiteral=Ze.typedArray=Ze.stringArray=Ze.array=Ze.func=Ze.error=Ze.number=Ze.string=Ze.boolean=void 0;function YT(t){return t===!0||t===!1}Ze.boolean=YT;function gv(t){return typeof t=="string"||t instanceof String}Ze.string=gv;function QT(t){return typeof t=="number"||t instanceof Number}Ze.number=QT;function ZT(t){return t instanceof Error}Ze.error=ZT;function eP(t){return typeof t=="function"}Ze.func=eP;function mv(t){return Array.isArray(t)}Ze.array=mv;function tP(t){return mv(t)&&t.every(e=>gv(e))}Ze.stringArray=tP;function nP(t,e){return Array.isArray(t)&&t.every(e)}Ze.typedArray=nP;function rP(t){return t!==null&&typeof t=="object"}Ze.objectLiteral=rP});var wv=b(_a=>{"use strict";Object.defineProperty(_a,"__esModule",{value:!0});_a.ImplementationRequest=void 0;var vv=Ae(),yv;(function(t){t.method="textDocument/implementation",t.messageDirection=vv.MessageDirection.clientToServer,t.type=new vv.ProtocolRequestType(t.method)})(yv||(_a.ImplementationRequest=yv={}))});var _v=b(Sa=>{"use strict";Object.defineProperty(Sa,"__esModule",{value:!0});Sa.TypeDefinitionRequest=void 0;var bv=Ae(),Cv;(function(t){t.method="textDocument/typeDefinition",t.messageDirection=bv.MessageDirection.clientToServer,t.type=new bv.ProtocolRequestType(t.method)})(Cv||(Sa.TypeDefinitionRequest=Cv={}))});var Rv=b(Ki=>{"use strict";Object.defineProperty(Ki,"__esModule",{value:!0});Ki.DidChangeWorkspaceFoldersNotification=Ki.WorkspaceFoldersRequest=void 0;var Da=Ae(),Sv;(function(t){t.method="workspace/workspaceFolders",t.messageDirection=Da.MessageDirection.serverToClient,t.type=new Da.ProtocolRequestType0(t.method)})(Sv||(Ki.WorkspaceFoldersRequest=Sv={}));var Dv;(function(t){t.method="workspace/didChangeWorkspaceFolders",t.messageDirection=Da.MessageDirection.clientToServer,t.type=new Da.ProtocolNotificationType(t.method)})(Dv||(Ki.DidChangeWorkspaceFoldersNotification=Dv={}))});var Ev=b(Ra=>{"use strict";Object.defineProperty(Ra,"__esModule",{value:!0});Ra.ConfigurationRequest=void 0;var Tv=Ae(),Pv;(function(t){t.method="workspace/configuration",t.messageDirection=Tv.MessageDirection.serverToClient,t.type=new Tv.ProtocolRequestType(t.method)})(Pv||(Ra.ConfigurationRequest=Pv={}))});var kv=b(zi=>{"use strict";Object.defineProperty(zi,"__esModule",{value:!0});zi.ColorPresentationRequest=zi.DocumentColorRequest=void 0;var Ta=Ae(),xv;(function(t){t.method="textDocument/documentColor",t.messageDirection=Ta.MessageDirection.clientToServer,t.type=new Ta.ProtocolRequestType(t.method)})(xv||(zi.DocumentColorRequest=xv={}));var qv;(function(t){t.method="textDocument/colorPresentation",t.messageDirection=Ta.MessageDirection.clientToServer,t.type=new Ta.ProtocolRequestType(t.method)})(qv||(zi.ColorPresentationRequest=qv={}))});var Nv=b(Bi=>{"use strict";Object.defineProperty(Bi,"__esModule",{value:!0});Bi.FoldingRangeRefreshRequest=Bi.FoldingRangeRequest=void 0;var Pa=Ae(),Ov;(function(t){t.method="textDocument/foldingRange",t.messageDirection=Pa.MessageDirection.clientToServer,t.type=new Pa.ProtocolRequestType(t.method)})(Ov||(Bi.FoldingRangeRequest=Ov={}));var Iv;(function(t){t.method="workspace/foldingRange/refresh",t.messageDirection=Pa.MessageDirection.serverToClient,t.type=new Pa.ProtocolRequestType0(t.method)})(Iv||(Bi.FoldingRangeRefreshRequest=Iv={}))});var Av=b(Ea=>{"use strict";Object.defineProperty(Ea,"__esModule",{value:!0});Ea.DeclarationRequest=void 0;var Fv=Ae(),Mv;(function(t){t.method="textDocument/declaration",t.messageDirection=Fv.MessageDirection.clientToServer,t.type=new Fv.ProtocolRequestType(t.method)})(Mv||(Ea.DeclarationRequest=Mv={}))});var $v=b(xa=>{"use strict";Object.defineProperty(xa,"__esModule",{value:!0});xa.SelectionRangeRequest=void 0;var Lv=Ae(),jv;(function(t){t.method="textDocument/selectionRange",t.messageDirection=Lv.MessageDirection.clientToServer,t.type=new Lv.ProtocolRequestType(t.method)})(jv||(xa.SelectionRangeRequest=jv={}))});var Wv=b(Rr=>{"use strict";Object.defineProperty(Rr,"__esModule",{value:!0});Rr.WorkDoneProgressCancelNotification=Rr.WorkDoneProgressCreateRequest=Rr.WorkDoneProgress=void 0;var iP=ri(),qa=Ae(),Hv;(function(t){t.type=new iP.ProgressType;function e(n){return n===t.type}t.is=e})(Hv||(Rr.WorkDoneProgress=Hv={}));var Uv;(function(t){t.method="window/workDoneProgress/create",t.messageDirection=qa.MessageDirection.serverToClient,t.type=new qa.ProtocolRequestType(t.method)})(Uv||(Rr.WorkDoneProgressCreateRequest=Uv={}));var Vv;(function(t){t.method="window/workDoneProgress/cancel",t.messageDirection=qa.MessageDirection.clientToServer,t.type=new qa.ProtocolNotificationType(t.method)})(Vv||(Rr.WorkDoneProgressCancelNotification=Vv={}))});var Gv=b(Tr=>{"use strict";Object.defineProperty(Tr,"__esModule",{value:!0});Tr.CallHierarchyOutgoingCallsRequest=Tr.CallHierarchyIncomingCallsRequest=Tr.CallHierarchyPrepareRequest=void 0;var Gi=Ae(),Kv;(function(t){t.method="textDocument/prepareCallHierarchy",t.messageDirection=Gi.MessageDirection.clientToServer,t.type=new Gi.ProtocolRequestType(t.method)})(Kv||(Tr.CallHierarchyPrepareRequest=Kv={}));var zv;(function(t){t.method="callHierarchy/incomingCalls",t.messageDirection=Gi.MessageDirection.clientToServer,t.type=new Gi.ProtocolRequestType(t.method)})(zv||(Tr.CallHierarchyIncomingCallsRequest=zv={}));var Bv;(function(t){t.method="callHierarchy/outgoingCalls",t.messageDirection=Gi.MessageDirection.clientToServer,t.type=new Gi.ProtocolRequestType(t.method)})(Bv||(Tr.CallHierarchyOutgoingCallsRequest=Bv={}))});var ey=b(At=>{"use strict";Object.defineProperty(At,"__esModule",{value:!0});At.SemanticTokensRefreshRequest=At.SemanticTokensRangeRequest=At.SemanticTokensDeltaRequest=At.SemanticTokensRequest=At.SemanticTokensRegistrationType=At.TokenFormat=void 0;var Qn=Ae(),Xv;(function(t){t.Relative="relative"})(Xv||(At.TokenFormat=Xv={}));var Bs;(function(t){t.method="textDocument/semanticTokens",t.type=new Qn.RegistrationType(t.method)})(Bs||(At.SemanticTokensRegistrationType=Bs={}));var Jv;(function(t){t.method="textDocument/semanticTokens/full",t.messageDirection=Qn.MessageDirection.clientToServer,t.type=new Qn.ProtocolRequestType(t.method),t.registrationMethod=Bs.method})(Jv||(At.SemanticTokensRequest=Jv={}));var Yv;(function(t){t.method="textDocument/semanticTokens/full/delta",t.messageDirection=Qn.MessageDirection.clientToServer,t.type=new Qn.ProtocolRequestType(t.method),t.registrationMethod=Bs.method})(Yv||(At.SemanticTokensDeltaRequest=Yv={}));var Qv;(function(t){t.method="textDocument/semanticTokens/range",t.messageDirection=Qn.MessageDirection.clientToServer,t.type=new Qn.ProtocolRequestType(t.method),t.registrationMethod=Bs.method})(Qv||(At.SemanticTokensRangeRequest=Qv={}));var Zv;(function(t){t.method="workspace/semanticTokens/refresh",t.messageDirection=Qn.MessageDirection.serverToClient,t.type=new Qn.ProtocolRequestType0(t.method)})(Zv||(At.SemanticTokensRefreshRequest=Zv={}))});var ry=b(ka=>{"use strict";Object.defineProperty(ka,"__esModule",{value:!0});ka.ShowDocumentRequest=void 0;var ty=Ae(),ny;(function(t){t.method="window/showDocument",t.messageDirection=ty.MessageDirection.serverToClient,t.type=new ty.ProtocolRequestType(t.method)})(ny||(ka.ShowDocumentRequest=ny={}))});var oy=b(Oa=>{"use strict";Object.defineProperty(Oa,"__esModule",{value:!0});Oa.LinkedEditingRangeRequest=void 0;var iy=Ae(),sy;(function(t){t.method="textDocument/linkedEditingRange",t.messageDirection=iy.MessageDirection.clientToServer,t.type=new iy.ProtocolRequestType(t.method)})(sy||(Oa.LinkedEditingRangeRequest=sy={}))});var hy=b(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.WillDeleteFilesRequest=vt.DidDeleteFilesNotification=vt.DidRenameFilesNotification=vt.WillRenameFilesRequest=vt.DidCreateFilesNotification=vt.WillCreateFilesRequest=vt.FileOperationPatternKind=void 0;var an=Ae(),ay;(function(t){t.file="file",t.folder="folder"})(ay||(vt.FileOperationPatternKind=ay={}));var cy;(function(t){t.method="workspace/willCreateFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolRequestType(t.method)})(cy||(vt.WillCreateFilesRequest=cy={}));var uy;(function(t){t.method="workspace/didCreateFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolNotificationType(t.method)})(uy||(vt.DidCreateFilesNotification=uy={}));var ly;(function(t){t.method="workspace/willRenameFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolRequestType(t.method)})(ly||(vt.WillRenameFilesRequest=ly={}));var dy;(function(t){t.method="workspace/didRenameFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolNotificationType(t.method)})(dy||(vt.DidRenameFilesNotification=dy={}));var fy;(function(t){t.method="workspace/didDeleteFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolNotificationType(t.method)})(fy||(vt.DidDeleteFilesNotification=fy={}));var py;(function(t){t.method="workspace/willDeleteFiles",t.messageDirection=an.MessageDirection.clientToServer,t.type=new an.ProtocolRequestType(t.method)})(py||(vt.WillDeleteFilesRequest=py={}))});var wy=b(Pr=>{"use strict";Object.defineProperty(Pr,"__esModule",{value:!0});Pr.MonikerRequest=Pr.MonikerKind=Pr.UniquenessLevel=void 0;var gy=Ae(),my;(function(t){t.document="document",t.project="project",t.group="group",t.scheme="scheme",t.global="global"})(my||(Pr.UniquenessLevel=my={}));var vy;(function(t){t.$import="import",t.$export="export",t.local="local"})(vy||(Pr.MonikerKind=vy={}));var yy;(function(t){t.method="textDocument/moniker",t.messageDirection=gy.MessageDirection.clientToServer,t.type=new gy.ProtocolRequestType(t.method)})(yy||(Pr.MonikerRequest=yy={}))});var Sy=b(Er=>{"use strict";Object.defineProperty(Er,"__esModule",{value:!0});Er.TypeHierarchySubtypesRequest=Er.TypeHierarchySupertypesRequest=Er.TypeHierarchyPrepareRequest=void 0;var Xi=Ae(),by;(function(t){t.method="textDocument/prepareTypeHierarchy",t.messageDirection=Xi.MessageDirection.clientToServer,t.type=new Xi.ProtocolRequestType(t.method)})(by||(Er.TypeHierarchyPrepareRequest=by={}));var Cy;(function(t){t.method="typeHierarchy/supertypes",t.messageDirection=Xi.MessageDirection.clientToServer,t.type=new Xi.ProtocolRequestType(t.method)})(Cy||(Er.TypeHierarchySupertypesRequest=Cy={}));var _y;(function(t){t.method="typeHierarchy/subtypes",t.messageDirection=Xi.MessageDirection.clientToServer,t.type=new Xi.ProtocolRequestType(t.method)})(_y||(Er.TypeHierarchySubtypesRequest=_y={}))});var Ty=b(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0});Ji.InlineValueRefreshRequest=Ji.InlineValueRequest=void 0;var Ia=Ae(),Dy;(function(t){t.method="textDocument/inlineValue",t.messageDirection=Ia.MessageDirection.clientToServer,t.type=new Ia.ProtocolRequestType(t.method)})(Dy||(Ji.InlineValueRequest=Dy={}));var Ry;(function(t){t.method="workspace/inlineValue/refresh",t.messageDirection=Ia.MessageDirection.serverToClient,t.type=new Ia.ProtocolRequestType0(t.method)})(Ry||(Ji.InlineValueRefreshRequest=Ry={}))});var qy=b(xr=>{"use strict";Object.defineProperty(xr,"__esModule",{value:!0});xr.InlayHintRefreshRequest=xr.InlayHintResolveRequest=xr.InlayHintRequest=void 0;var Yi=Ae(),Py;(function(t){t.method="textDocument/inlayHint",t.messageDirection=Yi.MessageDirection.clientToServer,t.type=new Yi.ProtocolRequestType(t.method)})(Py||(xr.InlayHintRequest=Py={}));var Ey;(function(t){t.method="inlayHint/resolve",t.messageDirection=Yi.MessageDirection.clientToServer,t.type=new Yi.ProtocolRequestType(t.method)})(Ey||(xr.InlayHintResolveRequest=Ey={}));var xy;(function(t){t.method="workspace/inlayHint/refresh",t.messageDirection=Yi.MessageDirection.serverToClient,t.type=new Yi.ProtocolRequestType0(t.method)})(xy||(xr.InlayHintRefreshRequest=xy={}))});var Ay=b(cn=>{"use strict";Object.defineProperty(cn,"__esModule",{value:!0});cn.DiagnosticRefreshRequest=cn.WorkspaceDiagnosticRequest=cn.DocumentDiagnosticRequest=cn.DocumentDiagnosticReportKind=cn.DiagnosticServerCancellationData=void 0;var My=ri(),sP=Ca(),Qi=Ae(),ky;(function(t){function e(n){let r=n;return r&&sP.boolean(r.retriggerRequest)}t.is=e})(ky||(cn.DiagnosticServerCancellationData=ky={}));var Oy;(function(t){t.Full="full",t.Unchanged="unchanged"})(Oy||(cn.DocumentDiagnosticReportKind=Oy={}));var Iy;(function(t){t.method="textDocument/diagnostic",t.messageDirection=Qi.MessageDirection.clientToServer,t.type=new Qi.ProtocolRequestType(t.method),t.partialResult=new My.ProgressType})(Iy||(cn.DocumentDiagnosticRequest=Iy={}));var Ny;(function(t){t.method="workspace/diagnostic",t.messageDirection=Qi.MessageDirection.clientToServer,t.type=new Qi.ProtocolRequestType(t.method),t.partialResult=new My.ProgressType})(Ny||(cn.WorkspaceDiagnosticRequest=Ny={}));var Fy;(function(t){t.method="workspace/diagnostic/refresh",t.messageDirection=Qi.MessageDirection.serverToClient,t.type=new Qi.ProtocolRequestType0(t.method)})(Fy||(cn.DiagnosticRefreshRequest=Fy={}))});var Wy=b(ze=>{"use strict";Object.defineProperty(ze,"__esModule",{value:!0});ze.DidCloseNotebookDocumentNotification=ze.DidSaveNotebookDocumentNotification=ze.DidChangeNotebookDocumentNotification=ze.NotebookCellArrayChange=ze.DidOpenNotebookDocumentNotification=ze.NotebookDocumentSyncRegistrationType=ze.NotebookDocument=ze.NotebookCell=ze.ExecutionSummary=ze.NotebookCellKind=void 0;var Gs=(ba(),xu(wa)),Rn=Ca(),Ln=Ae(),Gd;(function(t){t.Markup=1,t.Code=2;function e(n){return n===1||n===2}t.is=e})(Gd||(ze.NotebookCellKind=Gd={}));var Xd;(function(t){function e(i,s){let o={executionOrder:i};return(s===!0||s===!1)&&(o.success=s),o}t.create=e;function n(i){let s=i;return Rn.objectLiteral(s)&&Gs.uinteger.is(s.executionOrder)&&(s.success===void 0||Rn.boolean(s.success))}t.is=n;function r(i,s){return i===s?!0:i==null||s===null||s===void 0?!1:i.executionOrder===s.executionOrder&&i.success===s.success}t.equals=r})(Xd||(ze.ExecutionSummary=Xd={}));var Na;(function(t){function e(s,o){return{kind:s,document:o}}t.create=e;function n(s){let o=s;return Rn.objectLiteral(o)&&Gd.is(o.kind)&&Gs.DocumentUri.is(o.document)&&(o.metadata===void 0||Rn.objectLiteral(o.metadata))}t.is=n;function r(s,o){let a=new Set;return s.document!==o.document&&a.add("document"),s.kind!==o.kind&&a.add("kind"),s.executionSummary!==o.executionSummary&&a.add("executionSummary"),(s.metadata!==void 0||o.metadata!==void 0)&&!i(s.metadata,o.metadata)&&a.add("metadata"),(s.executionSummary!==void 0||o.executionSummary!==void 0)&&!Xd.equals(s.executionSummary,o.executionSummary)&&a.add("executionSummary"),a}t.diff=r;function i(s,o){if(s===o)return!0;if(s==null||o===null||o===void 0||typeof s!=typeof o||typeof s!="object")return!1;let a=Array.isArray(s),c=Array.isArray(o);if(a!==c)return!1;if(a&&c){if(s.length!==o.length)return!1;for(let u=0;u<s.length;u++)if(!i(s[u],o[u]))return!1}if(Rn.objectLiteral(s)&&Rn.objectLiteral(o)){let u=Object.keys(s),f=Object.keys(o);if(u.length!==f.length||(u.sort(),f.sort(),!i(u,f)))return!1;for(let d=0;d<u.length;d++){let p=u[d];if(!i(s[p],o[p]))return!1}}return!0}})(Na||(ze.NotebookCell=Na={}));var Ly;(function(t){function e(r,i,s,o){return{uri:r,notebookType:i,version:s,cells:o}}t.create=e;function n(r){let i=r;return Rn.objectLiteral(i)&&Rn.string(i.uri)&&Gs.integer.is(i.version)&&Rn.typedArray(i.cells,Na.is)}t.is=n})(Ly||(ze.NotebookDocument=Ly={}));var Zi;(function(t){t.method="notebookDocument/sync",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.RegistrationType(t.method)})(Zi||(ze.NotebookDocumentSyncRegistrationType=Zi={}));var jy;(function(t){t.method="notebookDocument/didOpen",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolNotificationType(t.method),t.registrationMethod=Zi.method})(jy||(ze.DidOpenNotebookDocumentNotification=jy={}));var $y;(function(t){function e(r){let i=r;return Rn.objectLiteral(i)&&Gs.uinteger.is(i.start)&&Gs.uinteger.is(i.deleteCount)&&(i.cells===void 0||Rn.typedArray(i.cells,Na.is))}t.is=e;function n(r,i,s){let o={start:r,deleteCount:i};return s!==void 0&&(o.cells=s),o}t.create=n})($y||(ze.NotebookCellArrayChange=$y={}));var Hy;(function(t){t.method="notebookDocument/didChange",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolNotificationType(t.method),t.registrationMethod=Zi.method})(Hy||(ze.DidChangeNotebookDocumentNotification=Hy={}));var Uy;(function(t){t.method="notebookDocument/didSave",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolNotificationType(t.method),t.registrationMethod=Zi.method})(Uy||(ze.DidSaveNotebookDocumentNotification=Uy={}));var Vy;(function(t){t.method="notebookDocument/didClose",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolNotificationType(t.method),t.registrationMethod=Zi.method})(Vy||(ze.DidCloseNotebookDocumentNotification=Vy={}))});var By=b(Fa=>{"use strict";Object.defineProperty(Fa,"__esModule",{value:!0});Fa.InlineCompletionRequest=void 0;var Ky=Ae(),zy;(function(t){t.method="textDocument/inlineCompletion",t.messageDirection=Ky.MessageDirection.clientToServer,t.type=new Ky.ProtocolRequestType(t.method)})(zy||(Fa.InlineCompletionRequest=zy={}))});var ob=b(v=>{"use strict";Object.defineProperty(v,"__esModule",{value:!0});v.WorkspaceSymbolRequest=v.CodeActionResolveRequest=v.CodeActionRequest=v.DocumentSymbolRequest=v.DocumentHighlightRequest=v.ReferencesRequest=v.DefinitionRequest=v.SignatureHelpRequest=v.SignatureHelpTriggerKind=v.HoverRequest=v.CompletionResolveRequest=v.CompletionRequest=v.CompletionTriggerKind=v.PublishDiagnosticsNotification=v.WatchKind=v.RelativePattern=v.FileChangeType=v.DidChangeWatchedFilesNotification=v.WillSaveTextDocumentWaitUntilRequest=v.WillSaveTextDocumentNotification=v.TextDocumentSaveReason=v.DidSaveTextDocumentNotification=v.DidCloseTextDocumentNotification=v.DidChangeTextDocumentNotification=v.TextDocumentContentChangeEvent=v.DidOpenTextDocumentNotification=v.TextDocumentSyncKind=v.TelemetryEventNotification=v.LogMessageNotification=v.ShowMessageRequest=v.ShowMessageNotification=v.MessageType=v.DidChangeConfigurationNotification=v.ExitNotification=v.ShutdownRequest=v.InitializedNotification=v.InitializeErrorCodes=v.InitializeRequest=v.WorkDoneProgressOptions=v.TextDocumentRegistrationOptions=v.StaticRegistrationOptions=v.PositionEncodingKind=v.FailureHandlingKind=v.ResourceOperationKind=v.UnregistrationRequest=v.RegistrationRequest=v.DocumentSelector=v.NotebookCellTextDocumentFilter=v.NotebookDocumentFilter=v.TextDocumentFilter=void 0;v.MonikerRequest=v.MonikerKind=v.UniquenessLevel=v.WillDeleteFilesRequest=v.DidDeleteFilesNotification=v.WillRenameFilesRequest=v.DidRenameFilesNotification=v.WillCreateFilesRequest=v.DidCreateFilesNotification=v.FileOperationPatternKind=v.LinkedEditingRangeRequest=v.ShowDocumentRequest=v.SemanticTokensRegistrationType=v.SemanticTokensRefreshRequest=v.SemanticTokensRangeRequest=v.SemanticTokensDeltaRequest=v.SemanticTokensRequest=v.TokenFormat=v.CallHierarchyPrepareRequest=v.CallHierarchyOutgoingCallsRequest=v.CallHierarchyIncomingCallsRequest=v.WorkDoneProgressCancelNotification=v.WorkDoneProgressCreateRequest=v.WorkDoneProgress=v.SelectionRangeRequest=v.DeclarationRequest=v.FoldingRangeRefreshRequest=v.FoldingRangeRequest=v.ColorPresentationRequest=v.DocumentColorRequest=v.ConfigurationRequest=v.DidChangeWorkspaceFoldersNotification=v.WorkspaceFoldersRequest=v.TypeDefinitionRequest=v.ImplementationRequest=v.ApplyWorkspaceEditRequest=v.ExecuteCommandRequest=v.PrepareRenameRequest=v.RenameRequest=v.PrepareSupportDefaultBehavior=v.DocumentOnTypeFormattingRequest=v.DocumentRangesFormattingRequest=v.DocumentRangeFormattingRequest=v.DocumentFormattingRequest=v.DocumentLinkResolveRequest=v.DocumentLinkRequest=v.CodeLensRefreshRequest=v.CodeLensResolveRequest=v.CodeLensRequest=v.WorkspaceSymbolResolveRequest=void 0;v.InlineCompletionRequest=v.DidCloseNotebookDocumentNotification=v.DidSaveNotebookDocumentNotification=v.DidChangeNotebookDocumentNotification=v.NotebookCellArrayChange=v.DidOpenNotebookDocumentNotification=v.NotebookDocumentSyncRegistrationType=v.NotebookDocument=v.NotebookCell=v.ExecutionSummary=v.NotebookCellKind=v.DiagnosticRefreshRequest=v.WorkspaceDiagnosticRequest=v.DocumentDiagnosticRequest=v.DocumentDiagnosticReportKind=v.DiagnosticServerCancellationData=v.InlayHintRefreshRequest=v.InlayHintResolveRequest=v.InlayHintRequest=v.InlineValueRefreshRequest=v.InlineValueRequest=v.TypeHierarchySupertypesRequest=v.TypeHierarchySubtypesRequest=v.TypeHierarchyPrepareRequest=void 0;var N=Ae(),Gy=(ba(),xu(wa)),ot=Ca(),oP=wv();Object.defineProperty(v,"ImplementationRequest",{enumerable:!0,get:function(){return oP.ImplementationRequest}});var aP=_v();Object.defineProperty(v,"TypeDefinitionRequest",{enumerable:!0,get:function(){return aP.TypeDefinitionRequest}});var nb=Rv();Object.defineProperty(v,"WorkspaceFoldersRequest",{enumerable:!0,get:function(){return nb.WorkspaceFoldersRequest}});Object.defineProperty(v,"DidChangeWorkspaceFoldersNotification",{enumerable:!0,get:function(){return nb.DidChangeWorkspaceFoldersNotification}});var cP=Ev();Object.defineProperty(v,"ConfigurationRequest",{enumerable:!0,get:function(){return cP.ConfigurationRequest}});var rb=kv();Object.defineProperty(v,"DocumentColorRequest",{enumerable:!0,get:function(){return rb.DocumentColorRequest}});Object.defineProperty(v,"ColorPresentationRequest",{enumerable:!0,get:function(){return rb.ColorPresentationRequest}});var ib=Nv();Object.defineProperty(v,"FoldingRangeRequest",{enumerable:!0,get:function(){return ib.FoldingRangeRequest}});Object.defineProperty(v,"FoldingRangeRefreshRequest",{enumerable:!0,get:function(){return ib.FoldingRangeRefreshRequest}});var uP=Av();Object.defineProperty(v,"DeclarationRequest",{enumerable:!0,get:function(){return uP.DeclarationRequest}});var lP=$v();Object.defineProperty(v,"SelectionRangeRequest",{enumerable:!0,get:function(){return lP.SelectionRangeRequest}});var ef=Wv();Object.defineProperty(v,"WorkDoneProgress",{enumerable:!0,get:function(){return ef.WorkDoneProgress}});Object.defineProperty(v,"WorkDoneProgressCreateRequest",{enumerable:!0,get:function(){return ef.WorkDoneProgressCreateRequest}});Object.defineProperty(v,"WorkDoneProgressCancelNotification",{enumerable:!0,get:function(){return ef.WorkDoneProgressCancelNotification}});var tf=Gv();Object.defineProperty(v,"CallHierarchyIncomingCallsRequest",{enumerable:!0,get:function(){return tf.CallHierarchyIncomingCallsRequest}});Object.defineProperty(v,"CallHierarchyOutgoingCallsRequest",{enumerable:!0,get:function(){return tf.CallHierarchyOutgoingCallsRequest}});Object.defineProperty(v,"CallHierarchyPrepareRequest",{enumerable:!0,get:function(){return tf.CallHierarchyPrepareRequest}});var es=ey();Object.defineProperty(v,"TokenFormat",{enumerable:!0,get:function(){return es.TokenFormat}});Object.defineProperty(v,"SemanticTokensRequest",{enumerable:!0,get:function(){return es.SemanticTokensRequest}});Object.defineProperty(v,"SemanticTokensDeltaRequest",{enumerable:!0,get:function(){return es.SemanticTokensDeltaRequest}});Object.defineProperty(v,"SemanticTokensRangeRequest",{enumerable:!0,get:function(){return es.SemanticTokensRangeRequest}});Object.defineProperty(v,"SemanticTokensRefreshRequest",{enumerable:!0,get:function(){return es.SemanticTokensRefreshRequest}});Object.defineProperty(v,"SemanticTokensRegistrationType",{enumerable:!0,get:function(){return es.SemanticTokensRegistrationType}});var dP=ry();Object.defineProperty(v,"ShowDocumentRequest",{enumerable:!0,get:function(){return dP.ShowDocumentRequest}});var fP=oy();Object.defineProperty(v,"LinkedEditingRangeRequest",{enumerable:!0,get:function(){return fP.LinkedEditingRangeRequest}});var oi=hy();Object.defineProperty(v,"FileOperationPatternKind",{enumerable:!0,get:function(){return oi.FileOperationPatternKind}});Object.defineProperty(v,"DidCreateFilesNotification",{enumerable:!0,get:function(){return oi.DidCreateFilesNotification}});Object.defineProperty(v,"WillCreateFilesRequest",{enumerable:!0,get:function(){return oi.WillCreateFilesRequest}});Object.defineProperty(v,"DidRenameFilesNotification",{enumerable:!0,get:function(){return oi.DidRenameFilesNotification}});Object.defineProperty(v,"WillRenameFilesRequest",{enumerable:!0,get:function(){return oi.WillRenameFilesRequest}});Object.defineProperty(v,"DidDeleteFilesNotification",{enumerable:!0,get:function(){return oi.DidDeleteFilesNotification}});Object.defineProperty(v,"WillDeleteFilesRequest",{enumerable:!0,get:function(){return oi.WillDeleteFilesRequest}});var nf=wy();Object.defineProperty(v,"UniquenessLevel",{enumerable:!0,get:function(){return nf.UniquenessLevel}});Object.defineProperty(v,"MonikerKind",{enumerable:!0,get:function(){return nf.MonikerKind}});Object.defineProperty(v,"MonikerRequest",{enumerable:!0,get:function(){return nf.MonikerRequest}});var rf=Sy();Object.defineProperty(v,"TypeHierarchyPrepareRequest",{enumerable:!0,get:function(){return rf.TypeHierarchyPrepareRequest}});Object.defineProperty(v,"TypeHierarchySubtypesRequest",{enumerable:!0,get:function(){return rf.TypeHierarchySubtypesRequest}});Object.defineProperty(v,"TypeHierarchySupertypesRequest",{enumerable:!0,get:function(){return rf.TypeHierarchySupertypesRequest}});var sb=Ty();Object.defineProperty(v,"InlineValueRequest",{enumerable:!0,get:function(){return sb.InlineValueRequest}});Object.defineProperty(v,"InlineValueRefreshRequest",{enumerable:!0,get:function(){return sb.InlineValueRefreshRequest}});var sf=qy();Object.defineProperty(v,"InlayHintRequest",{enumerable:!0,get:function(){return sf.InlayHintRequest}});Object.defineProperty(v,"InlayHintResolveRequest",{enumerable:!0,get:function(){return sf.InlayHintResolveRequest}});Object.defineProperty(v,"InlayHintRefreshRequest",{enumerable:!0,get:function(){return sf.InlayHintRefreshRequest}});var Xs=Ay();Object.defineProperty(v,"DiagnosticServerCancellationData",{enumerable:!0,get:function(){return Xs.DiagnosticServerCancellationData}});Object.defineProperty(v,"DocumentDiagnosticReportKind",{enumerable:!0,get:function(){return Xs.DocumentDiagnosticReportKind}});Object.defineProperty(v,"DocumentDiagnosticRequest",{enumerable:!0,get:function(){return Xs.DocumentDiagnosticRequest}});Object.defineProperty(v,"WorkspaceDiagnosticRequest",{enumerable:!0,get:function(){return Xs.WorkspaceDiagnosticRequest}});Object.defineProperty(v,"DiagnosticRefreshRequest",{enumerable:!0,get:function(){return Xs.DiagnosticRefreshRequest}});var jn=Wy();Object.defineProperty(v,"NotebookCellKind",{enumerable:!0,get:function(){return jn.NotebookCellKind}});Object.defineProperty(v,"ExecutionSummary",{enumerable:!0,get:function(){return jn.ExecutionSummary}});Object.defineProperty(v,"NotebookCell",{enumerable:!0,get:function(){return jn.NotebookCell}});Object.defineProperty(v,"NotebookDocument",{enumerable:!0,get:function(){return jn.NotebookDocument}});Object.defineProperty(v,"NotebookDocumentSyncRegistrationType",{enumerable:!0,get:function(){return jn.NotebookDocumentSyncRegistrationType}});Object.defineProperty(v,"DidOpenNotebookDocumentNotification",{enumerable:!0,get:function(){return jn.DidOpenNotebookDocumentNotification}});Object.defineProperty(v,"NotebookCellArrayChange",{enumerable:!0,get:function(){return jn.NotebookCellArrayChange}});Object.defineProperty(v,"DidChangeNotebookDocumentNotification",{enumerable:!0,get:function(){return jn.DidChangeNotebookDocumentNotification}});Object.defineProperty(v,"DidSaveNotebookDocumentNotification",{enumerable:!0,get:function(){return jn.DidSaveNotebookDocumentNotification}});Object.defineProperty(v,"DidCloseNotebookDocumentNotification",{enumerable:!0,get:function(){return jn.DidCloseNotebookDocumentNotification}});var pP=By();Object.defineProperty(v,"InlineCompletionRequest",{enumerable:!0,get:function(){return pP.InlineCompletionRequest}});var Jd;(function(t){function e(n){let r=n;return ot.string(r)||ot.string(r.language)||ot.string(r.scheme)||ot.string(r.pattern)}t.is=e})(Jd||(v.TextDocumentFilter=Jd={}));var Yd;(function(t){function e(n){let r=n;return ot.objectLiteral(r)&&(ot.string(r.notebookType)||ot.string(r.scheme)||ot.string(r.pattern))}t.is=e})(Yd||(v.NotebookDocumentFilter=Yd={}));var Qd;(function(t){function e(n){let r=n;return ot.objectLiteral(r)&&(ot.string(r.notebook)||Yd.is(r.notebook))&&(r.language===void 0||ot.string(r.language))}t.is=e})(Qd||(v.NotebookCellTextDocumentFilter=Qd={}));var Zd;(function(t){function e(n){if(!Array.isArray(n))return!1;for(let r of n)if(!ot.string(r)&&!Jd.is(r)&&!Qd.is(r))return!1;return!0}t.is=e})(Zd||(v.DocumentSelector=Zd={}));var Xy;(function(t){t.method="client/registerCapability",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolRequestType(t.method)})(Xy||(v.RegistrationRequest=Xy={}));var Jy;(function(t){t.method="client/unregisterCapability",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolRequestType(t.method)})(Jy||(v.UnregistrationRequest=Jy={}));var Yy;(function(t){t.Create="create",t.Rename="rename",t.Delete="delete"})(Yy||(v.ResourceOperationKind=Yy={}));var Qy;(function(t){t.Abort="abort",t.Transactional="transactional",t.TextOnlyTransactional="textOnlyTransactional",t.Undo="undo"})(Qy||(v.FailureHandlingKind=Qy={}));var Zy;(function(t){t.UTF8="utf-8",t.UTF16="utf-16",t.UTF32="utf-32"})(Zy||(v.PositionEncodingKind=Zy={}));var ew;(function(t){function e(n){let r=n;return r&&ot.string(r.id)&&r.id.length>0}t.hasId=e})(ew||(v.StaticRegistrationOptions=ew={}));var tw;(function(t){function e(n){let r=n;return r&&(r.documentSelector===null||Zd.is(r.documentSelector))}t.is=e})(tw||(v.TextDocumentRegistrationOptions=tw={}));var nw;(function(t){function e(r){let i=r;return ot.objectLiteral(i)&&(i.workDoneProgress===void 0||ot.boolean(i.workDoneProgress))}t.is=e;function n(r){let i=r;return i&&ot.boolean(i.workDoneProgress)}t.hasWorkDoneProgress=n})(nw||(v.WorkDoneProgressOptions=nw={}));var rw;(function(t){t.method="initialize",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(rw||(v.InitializeRequest=rw={}));var iw;(function(t){t.unknownProtocolVersion=1})(iw||(v.InitializeErrorCodes=iw={}));var sw;(function(t){t.method="initialized",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(sw||(v.InitializedNotification=sw={}));var ow;(function(t){t.method="shutdown",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType0(t.method)})(ow||(v.ShutdownRequest=ow={}));var aw;(function(t){t.method="exit",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType0(t.method)})(aw||(v.ExitNotification=aw={}));var cw;(function(t){t.method="workspace/didChangeConfiguration",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(cw||(v.DidChangeConfigurationNotification=cw={}));var uw;(function(t){t.Error=1,t.Warning=2,t.Info=3,t.Log=4,t.Debug=5})(uw||(v.MessageType=uw={}));var lw;(function(t){t.method="window/showMessage",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolNotificationType(t.method)})(lw||(v.ShowMessageNotification=lw={}));var dw;(function(t){t.method="window/showMessageRequest",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolRequestType(t.method)})(dw||(v.ShowMessageRequest=dw={}));var fw;(function(t){t.method="window/logMessage",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolNotificationType(t.method)})(fw||(v.LogMessageNotification=fw={}));var pw;(function(t){t.method="telemetry/event",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolNotificationType(t.method)})(pw||(v.TelemetryEventNotification=pw={}));var hw;(function(t){t.None=0,t.Full=1,t.Incremental=2})(hw||(v.TextDocumentSyncKind=hw={}));var gw;(function(t){t.method="textDocument/didOpen",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(gw||(v.DidOpenTextDocumentNotification=gw={}));var mw;(function(t){function e(r){let i=r;return i!=null&&typeof i.text=="string"&&i.range!==void 0&&(i.rangeLength===void 0||typeof i.rangeLength=="number")}t.isIncremental=e;function n(r){let i=r;return i!=null&&typeof i.text=="string"&&i.range===void 0&&i.rangeLength===void 0}t.isFull=n})(mw||(v.TextDocumentContentChangeEvent=mw={}));var vw;(function(t){t.method="textDocument/didChange",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(vw||(v.DidChangeTextDocumentNotification=vw={}));var yw;(function(t){t.method="textDocument/didClose",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(yw||(v.DidCloseTextDocumentNotification=yw={}));var ww;(function(t){t.method="textDocument/didSave",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(ww||(v.DidSaveTextDocumentNotification=ww={}));var bw;(function(t){t.Manual=1,t.AfterDelay=2,t.FocusOut=3})(bw||(v.TextDocumentSaveReason=bw={}));var Cw;(function(t){t.method="textDocument/willSave",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(Cw||(v.WillSaveTextDocumentNotification=Cw={}));var _w;(function(t){t.method="textDocument/willSaveWaitUntil",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(_w||(v.WillSaveTextDocumentWaitUntilRequest=_w={}));var Sw;(function(t){t.method="workspace/didChangeWatchedFiles",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolNotificationType(t.method)})(Sw||(v.DidChangeWatchedFilesNotification=Sw={}));var Dw;(function(t){t.Created=1,t.Changed=2,t.Deleted=3})(Dw||(v.FileChangeType=Dw={}));var Rw;(function(t){function e(n){let r=n;return ot.objectLiteral(r)&&(Gy.URI.is(r.baseUri)||Gy.WorkspaceFolder.is(r.baseUri))&&ot.string(r.pattern)}t.is=e})(Rw||(v.RelativePattern=Rw={}));var Tw;(function(t){t.Create=1,t.Change=2,t.Delete=4})(Tw||(v.WatchKind=Tw={}));var Pw;(function(t){t.method="textDocument/publishDiagnostics",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolNotificationType(t.method)})(Pw||(v.PublishDiagnosticsNotification=Pw={}));var Ew;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.TriggerForIncompleteCompletions=3})(Ew||(v.CompletionTriggerKind=Ew={}));var xw;(function(t){t.method="textDocument/completion",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(xw||(v.CompletionRequest=xw={}));var qw;(function(t){t.method="completionItem/resolve",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(qw||(v.CompletionResolveRequest=qw={}));var kw;(function(t){t.method="textDocument/hover",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(kw||(v.HoverRequest=kw={}));var Ow;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.ContentChange=3})(Ow||(v.SignatureHelpTriggerKind=Ow={}));var Iw;(function(t){t.method="textDocument/signatureHelp",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Iw||(v.SignatureHelpRequest=Iw={}));var Nw;(function(t){t.method="textDocument/definition",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Nw||(v.DefinitionRequest=Nw={}));var Fw;(function(t){t.method="textDocument/references",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Fw||(v.ReferencesRequest=Fw={}));var Mw;(function(t){t.method="textDocument/documentHighlight",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Mw||(v.DocumentHighlightRequest=Mw={}));var Aw;(function(t){t.method="textDocument/documentSymbol",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Aw||(v.DocumentSymbolRequest=Aw={}));var Lw;(function(t){t.method="textDocument/codeAction",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Lw||(v.CodeActionRequest=Lw={}));var jw;(function(t){t.method="codeAction/resolve",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(jw||(v.CodeActionResolveRequest=jw={}));var $w;(function(t){t.method="workspace/symbol",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})($w||(v.WorkspaceSymbolRequest=$w={}));var Hw;(function(t){t.method="workspaceSymbol/resolve",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Hw||(v.WorkspaceSymbolResolveRequest=Hw={}));var Uw;(function(t){t.method="textDocument/codeLens",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Uw||(v.CodeLensRequest=Uw={}));var Vw;(function(t){t.method="codeLens/resolve",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Vw||(v.CodeLensResolveRequest=Vw={}));var Ww;(function(t){t.method="workspace/codeLens/refresh",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolRequestType0(t.method)})(Ww||(v.CodeLensRefreshRequest=Ww={}));var Kw;(function(t){t.method="textDocument/documentLink",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Kw||(v.DocumentLinkRequest=Kw={}));var zw;(function(t){t.method="documentLink/resolve",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(zw||(v.DocumentLinkResolveRequest=zw={}));var Bw;(function(t){t.method="textDocument/formatting",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Bw||(v.DocumentFormattingRequest=Bw={}));var Gw;(function(t){t.method="textDocument/rangeFormatting",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Gw||(v.DocumentRangeFormattingRequest=Gw={}));var Xw;(function(t){t.method="textDocument/rangesFormatting",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Xw||(v.DocumentRangesFormattingRequest=Xw={}));var Jw;(function(t){t.method="textDocument/onTypeFormatting",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Jw||(v.DocumentOnTypeFormattingRequest=Jw={}));var Yw;(function(t){t.Identifier=1})(Yw||(v.PrepareSupportDefaultBehavior=Yw={}));var Qw;(function(t){t.method="textDocument/rename",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Qw||(v.RenameRequest=Qw={}));var Zw;(function(t){t.method="textDocument/prepareRename",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(Zw||(v.PrepareRenameRequest=Zw={}));var eb;(function(t){t.method="workspace/executeCommand",t.messageDirection=N.MessageDirection.clientToServer,t.type=new N.ProtocolRequestType(t.method)})(eb||(v.ExecuteCommandRequest=eb={}));var tb;(function(t){t.method="workspace/applyEdit",t.messageDirection=N.MessageDirection.serverToClient,t.type=new N.ProtocolRequestType("workspace/applyEdit")})(tb||(v.ApplyWorkspaceEditRequest=tb={}))});var cb=b(Ma=>{"use strict";Object.defineProperty(Ma,"__esModule",{value:!0});Ma.createProtocolConnection=void 0;var ab=ri();function hP(t,e,n,r){return ab.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,ab.createMessageConnection)(t,e,n,r)}Ma.createProtocolConnection=hP});var lb=b(Lt=>{"use strict";var gP=Lt&&Lt.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Aa=Lt&&Lt.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&gP(e,t,n)};Object.defineProperty(Lt,"__esModule",{value:!0});Lt.LSPErrorCodes=Lt.createProtocolConnection=void 0;Aa(ri(),Lt);Aa((ba(),xu(wa)),Lt);Aa(Ae(),Lt);Aa(ob(),Lt);var mP=cb();Object.defineProperty(Lt,"createProtocolConnection",{enumerable:!0,get:function(){return mP.createProtocolConnection}});var ub;(function(t){t.lspReservedErrorRangeStart=-32899,t.RequestFailed=-32803,t.ServerCancelled=-32802,t.ContentModified=-32801,t.RequestCancelled=-32800,t.lspReservedErrorRangeEnd=-32800})(ub||(Lt.LSPErrorCodes=ub={}))});var J=b($n=>{"use strict";var vP=$n&&$n.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),db=$n&&$n.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&vP(e,t,n)};Object.defineProperty($n,"__esModule",{value:!0});$n.createProtocolConnection=void 0;var yP=$l();db($l(),$n);db(lb(),$n);function wP(t,e,n,r){return(0,yP.createMessageConnection)(t,e,n,r)}$n.createProtocolConnection=wP});var qr=b(le=>{"use strict";var bP=le&&le.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),CP=le&&le.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&bP(e,t,n)};Object.defineProperty(le,"__esModule",{value:!0});le.LoadedTSFilesMetaRequest=le.GetVirtualCodeRequest=le.GetVirtualFileRequest=le.GetServicePluginsRequest=le.UpdateServicePluginStateNotification=le.UpdateVirtualCodeStateNotification=le.DocumentDrop_DataTransferItemFileDataRequest=le.DocumentDrop_DataTransferItemAsStringRequest=le.DocumentDropRequest=le.ReloadProjectNotification=le.WriteVirtualFilesNotification=le.AutoInsertRequest=le.GetMatchTsConfigRequest=le.FindFileReferenceRequest=void 0;var jt=J();CP(J(),le);var fb;(function(t){t.type=new jt.RequestType("volar/client/findFileReference")})(fb||(le.FindFileReferenceRequest=fb={}));var pb;(function(t){t.type=new jt.RequestType("volar/client/tsconfig")})(pb||(le.GetMatchTsConfigRequest=pb={}));var hb;(function(t){t.type=new jt.RequestType("volar/client/autoInsert")})(hb||(le.AutoInsertRequest=hb={}));var gb;(function(t){t.type=new jt.NotificationType("volar/client/writeVirtualFiles")})(gb||(le.WriteVirtualFilesNotification=gb={}));var mb;(function(t){t.type=new jt.NotificationType("volar/client/reloadProject")})(mb||(le.ReloadProjectNotification=mb={}));var vb;(function(t){t.type=new jt.RequestType("volar/client/documentDrop")})(vb||(le.DocumentDropRequest=vb={}));var yb;(function(t){t.type=new jt.RequestType("volar/client/documentDrop/asString")})(yb||(le.DocumentDrop_DataTransferItemAsStringRequest=yb={}));var wb;(function(t){t.type=new jt.RequestType("volar/client/documentDrop/fileData")})(wb||(le.DocumentDrop_DataTransferItemFileDataRequest=wb={}));var bb;(function(t){t.type=new jt.NotificationType("volar/client/labs/updateVirtualFileState")})(bb||(le.UpdateVirtualCodeStateNotification=bb={}));var Cb;(function(t){t.type=new jt.NotificationType("volar/client/labs/updateServicePluginState")})(Cb||(le.UpdateServicePluginStateNotification=Cb={}));var _b;(function(t){t.type=new jt.RequestType("volar/client/servicePlugins")})(_b||(le.GetServicePluginsRequest=_b={}));var Sb;(function(t){t.type=new jt.RequestType("volar/client/virtualFiles")})(Sb||(le.GetVirtualFileRequest=Sb={}));var Db;(function(t){t.type=new jt.RequestType("volar/client/virtualFile")})(Db||(le.GetVirtualCodeRequest=Db={}));var Rb;(function(t){t.type=new jt.RequestType0("volar/client/loadedTsFiles")})(Rb||(le.LoadedTSFilesMetaRequest=Rb={}))});var Tb=b(of=>{"use strict";Object.defineProperty(of,"__esModule",{value:!0});of.activate=SP;var _P=qr(),$t=require("vscode");function SP(t,e){let n=!1,r;s();let i=[$t.workspace.onDidChangeTextDocument(o,null),$t.window.onDidChangeActiveTextEditor(s,null)];return $t.Disposable.from(...i);function s(){n=!1;let c=$t.window.activeTextEditor;if(!c)return;let u=c.document;$t.languages.match(t,u)&&(n=!0)}function o({document:c,contentChanges:u,reason:f}){if(!n||u.length===0||f===$t.TextDocumentChangeReason.Undo||f===$t.TextDocumentChangeReason.Redo)return;let d=$t.window.activeTextEditor?.document;if(c!==d)return;let p=u[u.length-1];if(p.text.length===0)return;let g=p.text[p.text.length-1],m=e.initializeResult?.capabilities;if(m?.experimental?.autoInsertionProvider){let{triggerCharacters:S,configurationSections:D}=m.experimental.autoInsertionProvider;for(let P=0;P<S.length;P++){let k=S[P],x=D?.[P];if(g.match(new RegExp(k))&&(!x||x.some(I=>$t.workspace.getConfiguration().get(I)))){a(c,p);return}}}}function a(c,u){r&&(clearTimeout(r),r=void 0);let f=c.version,d=()=>c!==$t.window.activeTextEditor?.document||$t.window.activeTextEditor?.document.version!==f;r=setTimeout(async()=>{if(r=void 0,d())return;let p=$t.window.activeTextEditor;if(!p)return;let g=new $t.Range(u.range.start,c.positionAt(c.offsetAt(u.range.start)+u.text.length)),m=p.selections.find(P=>g.contains(P.active))?.active;if(!m)return;let S={textDocument:e.code2ProtocolConverter.asTextDocumentIdentifier(c),selection:e.code2ProtocolConverter.asPosition(m),change:{rangeLength:u.rangeLength,rangeOffset:u.rangeOffset,text:u.text}},D=await e.sendRequest(_P.AutoInsertRequest.type,S);D&&n&&!d()&&p.insertSnippet(new $t.SnippetString(D))},100)}}});var Pb=b(cf=>{"use strict";Object.defineProperty(cf,"__esModule",{value:!0});cf.activate=DP;var Js=require("vscode"),af=qr();function DP(t,e){let n;return Js.Disposable.from(e.onRequest(af.DocumentDrop_DataTransferItemAsStringRequest.type,async({mimeType:r})=>await n.get(r)?.asString()??""),e.onRequest(af.DocumentDrop_DataTransferItemFileDataRequest.type,async({mimeType:r})=>await n.get(r)?.asFile()?.data()??new Uint8Array),Js.languages.registerDocumentDropEditProvider(t,{async provideDocumentDropEdits(r,i,s){n=s;let o=await e.sendRequest(af.DocumentDropRequest.type,{textDocument:e.code2ProtocolConverter.asTextDocumentIdentifier(r),position:e.code2ProtocolConverter.asPosition(i),dataTransfer:[...s].map(([a,c])=>{let u=c.asFile();return{mimeType:a,value:c.value,file:u?{name:u.name,uri:u.uri?e.code2ProtocolConverter.asUri(u.uri):void 0}:void 0}})});if(o){let a=new Js.DocumentDropEdit(o.insertTextFormat===2?new Js.SnippetString(o.insertText):o.insertText);if(o.additionalEdit&&(a.additionalEdit=await e.protocol2CodeConverter.asWorkspaceEdit(o.additionalEdit)),o.createDataTransferFile){a.additionalEdit??=new Js.WorkspaceEdit;for(let c of o.createDataTransferFile){let u=s.get(c.contentsMimeType)?.asFile();u&&a.additionalEdit.createFile(e.protocol2CodeConverter.asUri(c.uri),{ignoreIfExists:c.options?.ignoreIfExists,overwrite:c.options?.overwrite,contents:await u.data()})}}return a}}}))}});var Eb=b(lf=>{"use strict";Object.defineProperty(lf,"__esModule",{value:!0});lf.activate=TP;var uf=require("vscode"),RP=qr();function TP(t,e){return uf.commands.registerCommand(t,()=>{uf.window.activeTextEditor&&e.sendNotification(RP.WriteVirtualFilesNotification.type,e.code2ProtocolConverter.asTextDocumentIdentifier(uf.window.activeTextEditor.document))})}});var hf=b(pf=>{"use strict";Object.defineProperty(pf,"__esModule",{value:!0});var df;function ff(){if(df===void 0)throw new Error("No runtime abstraction layer installed");return df}(function(t){function e(n){if(n===void 0)throw new Error("No runtime abstraction layer provided");df=n}t.install=e})(ff||(ff={}));pf.default=ff});var mf=b(Le=>{"use strict";Object.defineProperty(Le,"__esModule",{value:!0});Le.config=Le.loadMessageBundle=Le.localize=Le.format=Le.setPseudo=Le.isPseudo=Le.isDefined=Le.BundleFormat=Le.MessageFormat=void 0;var qb=hf(),PP;(function(t){t.file="file",t.bundle="bundle",t.both="both"})(PP=Le.MessageFormat||(Le.MessageFormat={}));var EP;(function(t){t.standalone="standalone",t.languagePack="languagePack"})(EP=Le.BundleFormat||(Le.BundleFormat={}));var xb;(function(t){function e(n){var r=n;return r&&gf(r.key)&&gf(r.comment)}t.is=e})(xb||(xb={}));function gf(t){return typeof t<"u"}Le.isDefined=gf;Le.isPseudo=!1;function xP(t){Le.isPseudo=t}Le.setPseudo=xP;function kb(t,e){var n;return Le.isPseudo&&(t="\uFF3B"+t.replace(/[aouei]/g,"$&$&")+"\uFF3D"),e.length===0?n=t:n=t.replace(/\{(\d+)\}/g,function(r,i){var s=i[0],o=e[s],a=r;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),n}Le.format=kb;function qP(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return kb(e,n)}Le.localize=qP;function kP(t){return(0,qb.default)().loadMessageBundle(t)}Le.loadMessageBundle=kP;function OP(t){return(0,qb.default)().config(t)}Le.config=OP});var Mb=b(Hn=>{"use strict";Object.defineProperty(Hn,"__esModule",{value:!0});Hn.config=Hn.loadMessageBundle=Hn.BundleFormat=Hn.MessageFormat=void 0;var Tn=require("path"),un=require("fs"),IP=hf(),Ht=mf(),Ib=mf();Object.defineProperty(Hn,"MessageFormat",{enumerable:!0,get:function(){return Ib.MessageFormat}});Object.defineProperty(Hn,"BundleFormat",{enumerable:!0,get:function(){return Ib.BundleFormat}});var Nb=Object.prototype.toString;function NP(t){return Nb.call(t)==="[object Number]"}function Zn(t){return Nb.call(t)==="[object String]"}function FP(t){return t===!0||t===!1}function ts(t){return JSON.parse(un.readFileSync(t,"utf8"))}var La,oe;function MP(){if(oe={locale:void 0,language:void 0,languagePackSupport:!1,cacheLanguageResolution:!0,messageFormat:Ht.MessageFormat.bundle},Zn(process.env.VSCODE_NLS_CONFIG))try{var t=JSON.parse(process.env.VSCODE_NLS_CONFIG),e=void 0;if(t.availableLanguages){var n=t.availableLanguages["*"];Zn(n)&&(e=n)}if(Zn(t.locale)&&(oe.locale=t.locale.toLowerCase()),e===void 0?oe.language=oe.locale:e!=="en"&&(oe.language=e),FP(t._languagePackSupport)&&(oe.languagePackSupport=t._languagePackSupport),Zn(t._cacheRoot)&&(oe.cacheRoot=t._cacheRoot),Zn(t._languagePackId)&&(oe.languagePackId=t._languagePackId),Zn(t._translationsConfigFile)){oe.translationsConfigFile=t._translationsConfigFile;try{oe.translationsConfig=ts(oe.translationsConfigFile)}catch{if(t._corruptedFile){var r=Tn.dirname(t._corruptedFile);un.exists(r,function(s){s&&un.writeFile(t._corruptedFile,"corrupted","utf8",function(o){console.error(o)})})}}}}catch{}(0,Ht.setPseudo)(oe.locale==="pseudo"),La=Object.create(null)}MP();function AP(){return oe.languagePackSupport===!0&&oe.cacheRoot!==void 0&&oe.languagePackId!==void 0&&oe.translationsConfigFile!==void 0&&oe.translationsConfig!==void 0}function vf(t){return function(e,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];if(NP(e)){if(e>=t.length){console.error(`Broken localize call found. Index out of bounds. Stacktrace is
: `.concat(new Error("").stack));return}return(0,Ht.format)(t[e],r)}else{if(Zn(n))return console.warn("Message ".concat(n," didn't get externalized correctly.")),(0,Ht.format)(n,r);console.error(`Broken localize call found. Stacktrace is
: `.concat(new Error("").stack))}}}function LP(t){var e;if(oe.cacheLanguageResolution&&e)e=e;else{if(Ht.isPseudo||!oe.language)e=".nls.json";else for(var n=oe.language;n;){var r=".nls."+n+".json";if(un.existsSync(t+r)){e=r;break}else{var i=n.lastIndexOf("-");i>0?n=n.substring(0,i):(e=".nls.json",n=null)}}oe.cacheLanguageResolution&&(e=e)}return t+e}function jP(t){for(var e=oe.language;e;){var n=Tn.join(t,"nls.bundle.".concat(e,".json"));if(un.existsSync(n))return n;var r=e.lastIndexOf("-");r>0?e=e.substring(0,r):e=void 0}if(e===void 0){var n=Tn.join(t,"nls.bundle.json");if(un.existsSync(n))return n}}function $P(t){var e=ts(Tn.join(t,"nls.metadata.json")),n=Object.create(null);for(var r in e){var i=e[r];n[r]=i.messages}return n}function HP(t,e){var n=oe.translationsConfig[t.id];if(n){var r=ts(n).contents,i=ts(Tn.join(e,"nls.metadata.json")),s=Object.create(null);for(var o in i){var a=i[o],c=r["".concat(t.outDir,"/").concat(o)];if(c){for(var u=[],f=0;f<a.keys.length;f++){var d=a.keys[f],p=Zn(d)?d:d.key,g=c[p];g===void 0&&(g=a.messages[f]),u.push(g)}s[o]=u}else s[o]=a.messages}return s}}function UP(t){var e=new Date;un.utimes(t,e,e,function(){})}function yf(t,e){return La[t]=e,e}function VP(t,e){var n,r=Tn.join(oe.cacheRoot,"".concat(t.id,"-").concat(t.hash,".json")),i=!1,s=!1;try{return n=JSON.parse(un.readFileSync(r,{encoding:"utf8",flag:"r"})),UP(r),n}catch(o){if(o.code==="ENOENT")s=!0;else if(o instanceof SyntaxError)console.log("Syntax error parsing message bundle: ".concat(o.message,".")),un.unlink(r,function(a){a&&console.error("Deleting corrupted bundle ".concat(r," failed."))}),i=!0;else throw o}if(n=HP(t,e),!n||i)return n;if(s)try{un.writeFileSync(r,JSON.stringify(n),{encoding:"utf8",flag:"wx"})}catch(o){if(o.code==="EEXIST")return n;throw o}return n}function Ob(t){try{return $P(t)}catch(e){console.log("Generating default bundle from meta data failed.",e);return}}function WP(t,e){var n;if(AP())try{n=VP(t,e)}catch(i){console.log("Load or create bundle failed ",i)}if(!n){if(oe.languagePackSupport)return Ob(e);var r=jP(e);if(r)try{return ts(r)}catch(i){console.log("Loading in the box message bundle failed.",i)}n=Ob(e)}return n}function KP(t){for(var e,n=Tn.dirname(t);e=Tn.join(n,"nls.metadata.header.json"),!un.existsSync(e);){var r=Tn.dirname(n);if(r===n){e=void 0;break}else n=r}return e}function wf(t){if(!t)return Ht.localize;var e=Tn.extname(t);if(e&&(t=t.substr(0,t.length-e.length)),oe.messageFormat===Ht.MessageFormat.both||oe.messageFormat===Ht.MessageFormat.bundle){var n=KP(t);if(n){var r=Tn.dirname(n),i=La[r];if(i===void 0)try{var s=JSON.parse(un.readFileSync(n,"utf8"));try{var o=WP(s,r);i=yf(r,o?{header:s,nlsBundle:o}:null)}catch(f){console.error("Failed to load nls bundle",f),i=yf(r,null)}}catch(f){console.error("Failed to read header file",f),i=yf(r,null)}if(i){var a=t.substr(r.length+1).replace(/\\/g,"/"),c=i.nlsBundle[a];return c===void 0?(console.error("Messages for file ".concat(t," not found. See console for details.")),function(){return"Messages not found."}):vf(c)}}}if(oe.messageFormat===Ht.MessageFormat.both||oe.messageFormat===Ht.MessageFormat.file)try{var u=ts(LP(t));return Array.isArray(u)?vf(u):(0,Ht.isDefined)(u.messages)&&(0,Ht.isDefined)(u.keys)?vf(u.messages):(console.error("String bundle '".concat(t,"' uses an unsupported format.")),function(){return"File bundle has unsupported format. See console for details"})}catch(f){f.code!=="ENOENT"&&console.error("Failed to load single file bundle",f)}return console.error("Failed to load message bundle for file ".concat(t)),function(){return"Failed to load message bundle. See console for details."}}Hn.loadMessageBundle=wf;function Fb(t){return t&&(Zn(t.locale)&&(oe.locale=t.locale.toLowerCase(),oe.language=oe.locale,La=Object.create(null)),t.messageFormat!==void 0&&(oe.messageFormat=t.messageFormat),t.bundleFormat===Ht.BundleFormat.standalone&&oe.languagePackSupport===!0&&(oe.languagePackSupport=!1)),(0,Ht.setPseudo)(oe.locale==="pseudo"),wf}Hn.config=Fb;IP.default.install(Object.freeze({loadMessageBundle:wf,config:Fb}))});var Ab=b(bf=>{"use strict";Object.defineProperty(bf,"__esModule",{value:!0});bf.activate=XP;var ai=require("vscode"),zP=Mb(),BP=qr(),GP=zP.loadMessageBundle();function XP(t,e){return ai.commands.registerCommand(t,async n=>{await ai.window.withProgress({location:ai.ProgressLocation.Window,title:GP("progress.title","Finding file references")},async r=>{if(!n){let c=ai.window.activeTextEditor;if(!c)return;n=c.document.uri}let i=await e.sendRequest(BP.FindFileReferenceRequest.type,{textDocument:{uri:n.toString()}});if(!i)return;let s=i.map(c=>e.protocol2CodeConverter.asLocation(c)),o=ai.workspace.getConfiguration("references"),a=o.inspect("preferredLocation");await o.update("preferredLocation","view");try{await ai.commands.executeCommand("editor.action.showReferences",n,new ai.Position(0,0),s)}finally{await o.update("preferredLocation",a?.workspaceFolderValue??a?.workspaceValue)}})})}});var Lb=b(_f=>{"use strict";Object.defineProperty(_f,"__esModule",{value:!0});_f.activate=YP;var Cf=require("vscode"),JP=qr();function YP(t,e){return Cf.commands.registerCommand(t,()=>{Cf.window.activeTextEditor&&e.sendNotification(JP.ReloadProjectNotification.type,e.code2ProtocolConverter.asTextDocumentIdentifier(Cf.window.activeTextEditor.document))})}});var Sf=b((H1,$b)=>{"use strict";function Un(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function jb(t,e){for(var n="",r=0,i=-1,s=0,o,a=0;a<=t.length;++a){if(a<t.length)o=t.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(i===a-1||s===1))if(i!==a-1&&s===2){if(n.length<2||r!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){var c=n.lastIndexOf("/");if(c!==n.length-1){c===-1?(n="",r=0):(n=n.slice(0,c),r=n.length-1-n.lastIndexOf("/")),i=a,s=0;continue}}else if(n.length===2||n.length===1){n="",r=0,i=a,s=0;continue}}e&&(n.length>0?n+="/..":n="..",r=2)}else n.length>0?n+="/"+t.slice(i+1,a):n=t.slice(i+1,a),r=a-i-1;i=a,s=0}else o===46&&s!==-1?++s:s=-1}return n}function QP(t,e){var n=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+r:n+t+r:r}var ns={resolve:function(){for(var e="",n=!1,r,i=arguments.length-1;i>=-1&&!n;i--){var s;i>=0?s=arguments[i]:(r===void 0&&(r=process.cwd()),s=r),Un(s),s.length!==0&&(e=s+"/"+e,n=s.charCodeAt(0)===47)}return e=jb(e,!n),n?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(e){if(Un(e),e.length===0)return".";var n=e.charCodeAt(0)===47,r=e.charCodeAt(e.length-1)===47;return e=jb(e,!n),e.length===0&&!n&&(e="."),e.length>0&&r&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return Un(e),e.length>0&&e.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var e,n=0;n<arguments.length;++n){var r=arguments[n];Un(r),r.length>0&&(e===void 0?e=r:e+="/"+r)}return e===void 0?".":ns.normalize(e)},relative:function(e,n){if(Un(e),Un(n),e===n||(e=ns.resolve(e),n=ns.resolve(n),e===n))return"";for(var r=1;r<e.length&&e.charCodeAt(r)===47;++r);for(var i=e.length,s=i-r,o=1;o<n.length&&n.charCodeAt(o)===47;++o);for(var a=n.length,c=a-o,u=s<c?s:c,f=-1,d=0;d<=u;++d){if(d===u){if(c>u){if(n.charCodeAt(o+d)===47)return n.slice(o+d+1);if(d===0)return n.slice(o+d)}else s>u&&(e.charCodeAt(r+d)===47?f=d:d===0&&(f=0));break}var p=e.charCodeAt(r+d),g=n.charCodeAt(o+d);if(p!==g)break;p===47&&(f=d)}var m="";for(d=r+f+1;d<=i;++d)(d===i||e.charCodeAt(d)===47)&&(m.length===0?m+="..":m+="/..");return m.length>0?m+n.slice(o+f):(o+=f,n.charCodeAt(o)===47&&++o,n.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(Un(e),e.length===0)return".";for(var n=e.charCodeAt(0),r=n===47,i=-1,s=!0,o=e.length-1;o>=1;--o)if(n=e.charCodeAt(o),n===47){if(!s){i=o;break}}else s=!1;return i===-1?r?"/":".":r&&i===1?"//":e.slice(0,i)},basename:function(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');Un(e);var r=0,i=-1,s=!0,o;if(n!==void 0&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,c=-1;for(o=e.length-1;o>=0;--o){var u=e.charCodeAt(o);if(u===47){if(!s){r=o+1;break}}else c===-1&&(s=!1,c=o+1),a>=0&&(u===n.charCodeAt(a)?--a===-1&&(i=o):(a=-1,i=c))}return r===i?i=c:i===-1&&(i=e.length),e.slice(r,i)}else{for(o=e.length-1;o>=0;--o)if(e.charCodeAt(o)===47){if(!s){r=o+1;break}}else i===-1&&(s=!1,i=o+1);return i===-1?"":e.slice(r,i)}},extname:function(e){Un(e);for(var n=-1,r=0,i=-1,s=!0,o=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(c===47){if(!s){r=a+1;break}continue}i===-1&&(s=!1,i=a+1),c===46?n===-1?n=a:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(e===null||typeof e!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return QP("/",e)},parse:function(e){Un(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return n;var r=e.charCodeAt(0),i=r===47,s;i?(n.root="/",s=1):s=0;for(var o=-1,a=0,c=-1,u=!0,f=e.length-1,d=0;f>=s;--f){if(r=e.charCodeAt(f),r===47){if(!u){a=f+1;break}continue}c===-1&&(u=!1,c=f+1),r===46?o===-1?o=f:d!==1&&(d=1):o!==-1&&(d=-1)}return o===-1||c===-1||d===0||d===1&&o===c-1&&o===a+1?c!==-1&&(a===0&&i?n.base=n.name=e.slice(1,c):n.base=n.name=e.slice(a,c)):(a===0&&i?(n.name=e.slice(1,o),n.base=e.slice(1,c)):(n.name=e.slice(a,o),n.base=e.slice(a,c)),n.ext=e.slice(o,c)),a>0?n.dir=e.slice(0,a-1):i&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};ns.posix=ns;$b.exports=ns});var Hb=b(Df=>{"use strict";Object.defineProperty(Df,"__esModule",{value:!0});Df.activate=tE;var Pn=require("vscode"),ZP=qr(),eE=Sf();function tE(t,e,n){let r=[],i=Pn.languages.createLanguageStatusItem(e,t),s,o;a(),Pn.window.onDidChangeActiveTextEditor(()=>{clearTimeout(o),o=setTimeout(()=>a,100)},void 0,r),r.push(Pn.commands.registerCommand(e,async()=>{if(s){let c=await Pn.workspace.openTextDocument(s);await Pn.window.showTextDocument(c)}})),r.push(...r);async function a(){if(!Pn.window.activeTextEditor||!Pn.languages.match(t,Pn.window.activeTextEditor.document))return;let c=await n.sendRequest(ZP.GetMatchTsConfigRequest.type,n.code2ProtocolConverter.asTextDocumentIdentifier(Pn.window.activeTextEditor.document));c?.uri?(s=Pn.Uri.parse(c.uri),i.text=eE.relative(Pn.workspace.rootPath?.replace(/\\/g,"/")||"/",s.fsPath.replace(/\\/g,"/")),i.command={title:"Open config file",command:e}):(i.text="No tsconfig",i.command=void 0)}}});var ja=b(Rf=>{"use strict";Object.defineProperty(Rf,"__esModule",{value:!0});Rf.quickPick=nE;var Ub=require("vscode");function nE(t,e){return new Promise(n=>{let r=Ub.window.createQuickPick(),i=[];for(let s of Array.isArray(t)?t:[t]){let o=Object.values(s);if(o.length){i.length&&i.push({label:"",kind:Ub.QuickPickItemKind.Separator});for(let a of o)a&&i.push(a)}}r.items=i,r.placeholder=e,r.onDidChangeSelection(s=>{if(s[0])for(let o of Array.isArray(t)?t:[t])for(let a in o){let c=o[a];if(s[0]===c){n(a),r.hide();break}}}),r.onDidHide(()=>{r.dispose(),n(void 0)}),r.show()})}});var Vb=b(Ys=>{"use strict";Object.defineProperty(Ys,"__esModule",{value:!0});Ys.readFile=iE;Ys.readDirectory=sE;Ys.stat=oE;var Tf=require("vscode"),rE=new TextDecoder("utf8");async function iE(t){try{return rE.decode(await Tf.workspace.fs.readFile(t))}catch{}}async function sE(t){try{return await Tf.workspace.fs.readDirectory(t)}catch{return[]}}async function oE(t){try{return await Tf.workspace.fs.stat(t)}catch{}}});var Wb=b((K1,aE)=>{aE.exports={name:"typescript",author:"Microsoft Corp.",homepage:"https://www.typescriptlang.org/",version:"5.7.2",license:"Apache-2.0",description:"TypeScript is a language for application scale JavaScript development",keywords:["TypeScript","Microsoft","compiler","language","javascript"],bugs:{url:"https://github.com/microsoft/TypeScript/issues"},repository:{type:"git",url:"https://github.com/microsoft/TypeScript.git"},main:"./lib/typescript.js",typings:"./lib/typescript.d.ts",bin:{tsc:"./bin/tsc",tsserver:"./bin/tsserver"},engines:{node:">=14.17"},files:["bin","lib","!lib/enu","LICENSE.txt","README.md","SECURITY.md","ThirdPartyNoticeText.txt","!**/.gitattributes"],devDependencies:{"@dprint/formatter":"^0.4.1","@dprint/typescript":"0.93.0","@esfx/canceltoken":"^1.0.0","@eslint/js":"^9.11.1","@octokit/rest":"^21.0.2","@types/chai":"^4.3.20","@types/diff":"^5.2.2","@types/minimist":"^1.2.5","@types/mocha":"^10.0.8","@types/ms":"^0.7.34","@types/node":"latest","@types/source-map-support":"^0.5.10","@types/which":"^3.0.4","@typescript-eslint/rule-tester":"^8.8.0","@typescript-eslint/type-utils":"^8.8.0","@typescript-eslint/utils":"^8.8.0","azure-devops-node-api":"^14.1.0",c8:"^10.1.2",chai:"^4.5.0",chalk:"^4.1.2",chokidar:"^3.6.0",diff:"^5.2.0",dprint:"^0.47.2",esbuild:"^0.24.0",eslint:"^9.11.1","eslint-formatter-autolinkable-stylish":"^1.4.0","eslint-plugin-regexp":"^2.6.0","fast-xml-parser":"^4.5.0",glob:"^10.4.5",globals:"^15.9.0",hereby:"^1.10.0","jsonc-parser":"^3.3.1",knip:"^5.30.6",minimist:"^1.2.8",mocha:"^10.7.3","mocha-fivemat-progress-reporter":"^0.1.0","monocart-coverage-reports":"^2.11.0",ms:"^2.1.3",playwright:"^1.47.2","source-map-support":"^0.5.21",tslib:"^2.7.0",typescript:"^5.6.2","typescript-eslint":"^8.8.0",which:"^3.0.1"},overrides:{"typescript@*":"$typescript"},scripts:{test:"hereby runtests-parallel --light=false","test:eslint-rules":"hereby run-eslint-rules-tests",build:"npm run build:compiler && npm run build:tests","build:compiler":"hereby local","build:tests":"hereby tests","build:tests:notypecheck":"hereby tests --no-typecheck",clean:"hereby clean",gulp:"hereby",lint:"hereby lint",knip:"hereby knip",format:"dprint fmt","setup-hooks":"node scripts/link-hooks.mjs"},browser:{fs:!1,os:!1,path:!1,crypto:!1,buffer:!1,"source-map-support":!1,inspector:!1,perf_hooks:!1},packageManager:"npm@8.19.4",volta:{node:"20.1.0",npm:"8.19.4"},gitHead:"d701d908d534e68cfab24b6df15539014ac348a3"}});var Bb=b(Ua=>{"use strict";Object.defineProperty(Ua,"__esModule",{value:!0});Ua.activate=uE;Ua.getTsdk=xf;var Ha=Sf(),Ye=require("vscode"),cE=ja(),Ef=Vb(),$a="node_modules/typescript/lib",Pf="typescript.tsdk";function uE(t,e,n,r,i){let s=[],o=Ye.languages.createLanguageStatusItem(e,t);return o.command={title:"Select Version",command:e},s.push({dispose:()=>o.dispose()}),s.push(Ye.commands.registerCommand(e,a)),Ye.workspace.onDidChangeConfiguration(c,void 0,s),Ye.window.onDidChangeActiveTextEditor(u,void 0,s),u(),Ye.Disposable.from(...s);async function a(){let f=await xf(n),d=zb(),p=await Kb(),g=!!p,m=!!d&&!p?.isWeb,S=d!==$a&&!p?.isWeb;if(!g&&!m&&!S){await Ye.window.showErrorMessage(`Could not find any TypeScript version. Please point your \`${Pf}\` setting to a valid TypeScript distribution.`,"Open Settings")==="Open Settings"&&Ye.commands.executeCommand("workbench.action.openSettings",Pf);return}let D=await(0,cE.quickPick)([{useVSCodeTsdk:g?{label:(f?.isWorkspacePath?"":"\u2022 ")+"Use VS Code's Version",description:p.version,detail:p.isWeb?p.path:void 0}:void 0,useConfigWorkspaceTsdk:m?{label:(f?.isWorkspacePath?"\u2022 ":"")+"Use Workspace Version",description:await Qs(await qf(d)??"/")??"Could not load the TypeScript version at this path",detail:d}:void 0,useDefaultWorkspaceTsdk:S?{label:(f?.isWorkspacePath?"\u2022 ":"")+"Use Workspace Version",description:await Qs(await qf($a)??"/")??"Could not load the TypeScript version at this path",detail:$a}:void 0}]);if(D===void 0)return;D==="useDefaultWorkspaceTsdk"&&await Ye.workspace.getConfiguration("typescript").update("tsdk",$a);let P=D==="useConfigWorkspaceTsdk"||D==="useDefaultWorkspaceTsdk";P!==kf(n)&&(n.workspaceState.update("typescript.useWorkspaceTsdk",P),i?.()),u()}function c(f){f.affectsConfiguration(Pf)&&kf(n)&&i?.()}async function u(){let f=(await xf(n))?.version;o.text=f??"x.x.x",o.text=r(o.text)}}async function xf(t){if(kf(t)){let n=zb();if(n){let r=await qf(n);if(r){let i=await Qs(r);if(i!==void 0)return{tsdk:r,version:i,isWorkspacePath:!0}}}}let e=await Kb();return e?{tsdk:e.path,version:e.version,isWorkspacePath:!1}:void 0}async function qf(t){if(Ha.isAbsolute(t)){let e=Ye.Uri.joinPath(Ye.Uri.file(t),"typescript.js");if((await Ef.stat(e))?.type===Ye.FileType.File)return t}else if(Ye.workspace.workspaceFolders)for(let e of Ye.workspace.workspaceFolders){let n=Ha.join(e.uri.fsPath.replace(/\\/g,"/"),t),r=Ye.Uri.joinPath(Ye.Uri.file(n),"typescript.js");if((await Ef.stat(r))?.type===Ye.FileType.File)return n}}async function Kb(){let t=Ye.extensions.getExtension("ms-vscode.vscode-typescript-next");if(t){let n=Ha.join(t.extensionPath.replace(/\\/g,"/"),"node_modules/typescript/lib"),r=await Qs(n);return r?{path:n,version:r,isWeb:!1}:void 0}if(Ye.env.appRoot){let n=Ha.join(Ye.env.appRoot.replace(/\\/g,"/"),"extensions/node_modules/typescript/lib"),r=await Qs(n);return r?{path:n,version:r,isWeb:!1}:void 0}let e=Wb().version;return{path:`/node_modules/typescript@${e}/lib`,version:e,isWeb:!0}}function zb(){return Ye.workspace.getConfiguration("typescript").get("tsdk")?.replace(/\\/g,"/")}function kf(t){return t.workspaceState.get("typescript.useWorkspaceTsdk",!1)}async function Qs(t){let i=t.toString().split("/").slice(0,-1).join("/")+"/package.json";try{let s=await Ef.readFile(Ye.Uri.file(i));if(s===void 0)return;let o=JSON.parse(s);return!o||typeof o.version!="string"?void 0:o.version}catch{return}}});var ln=b(Be=>{"use strict";Object.defineProperty(Be,"__esModule",{value:!0});Be.asPromise=Be.thenable=Be.typedArray=Be.stringArray=Be.array=Be.func=Be.error=Be.number=Be.string=Be.boolean=void 0;function lE(t){return t===!0||t===!1}Be.boolean=lE;function Gb(t){return typeof t=="string"||t instanceof String}Be.string=Gb;function dE(t){return typeof t=="number"||t instanceof Number}Be.number=dE;function fE(t){return t instanceof Error}Be.error=fE;function Xb(t){return typeof t=="function"}Be.func=Xb;function Jb(t){return Array.isArray(t)}Be.array=Jb;function pE(t){return Jb(t)&&t.every(e=>Gb(e))}Be.stringArray=pE;function hE(t,e){return Array.isArray(t)&&t.every(e)}Be.typedArray=hE;function Yb(t){return t&&Xb(t.then)}Be.thenable=Yb;function gE(t){return t instanceof Promise?t:Yb(t)?new Promise((e,n)=>{t.then(r=>e(r),r=>n(r))}):Promise.resolve(t)}Be.asPromise=gE});var Va=b(yt=>{"use strict";Object.defineProperty(yt,"__esModule",{value:!0});yt.forEach=yt.mapAsync=yt.map=yt.clearTestMode=yt.setTestMode=yt.Semaphore=yt.Delayer=void 0;var eo=J(),Of=class{constructor(e){this.defaultDelay=e,this.timeout=void 0,this.completionPromise=void 0,this.onSuccess=void 0,this.task=void 0}trigger(e,n=this.defaultDelay){return this.task=e,n>=0&&this.cancelTimeout(),this.completionPromise||(this.completionPromise=new Promise(r=>{this.onSuccess=r}).then(()=>{this.completionPromise=void 0,this.onSuccess=void 0;var r=this.task();return this.task=void 0,r})),(n>=0||this.timeout===void 0)&&(this.timeout=(0,eo.RAL)().timer.setTimeout(()=>{this.timeout=void 0,this.onSuccess(void 0)},n>=0?n:this.defaultDelay)),this.completionPromise}forceDelivery(){if(!this.completionPromise)return;this.cancelTimeout();let e=this.task();return this.completionPromise=void 0,this.onSuccess=void 0,this.task=void 0,e}isTriggered(){return this.timeout!==void 0}cancel(){this.cancelTimeout(),this.completionPromise=void 0}cancelTimeout(){this.timeout!==void 0&&(this.timeout.dispose(),this.timeout=void 0)}};yt.Delayer=Of;var If=class{constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((n,r)=>{this._waiting.push({thunk:e,resolve:n,reject:r}),this.runNext()})}get active(){return this._active}runNext(){this._waiting.length===0||this._active===this._capacity||(0,eo.RAL)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(this._waiting.length===0||this._active===this._capacity)return;let e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{let n=e.thunk();n instanceof Promise?n.then(r=>{this._active--,e.resolve(r),this.runNext()},r=>{this._active--,e.reject(r),this.runNext()}):(this._active--,e.resolve(n),this.runNext())}catch(n){this._active--,e.reject(n),this.runNext()}}};yt.Semaphore=If;var Nf=!1;function mE(){Nf=!0}yt.setTestMode=mE;function vE(){Nf=!1}yt.clearTestMode=vE;var Qb=15,Zs=class{constructor(e=Qb){this.yieldAfter=Nf===!0?Math.max(e,2):Math.max(e,Qb),this.startTime=Date.now(),this.counter=0,this.total=0,this.counterInterval=1}start(){this.counter=0,this.total=0,this.counterInterval=1,this.startTime=Date.now()}shouldYield(){if(++this.counter>=this.counterInterval){let e=Date.now()-this.startTime,n=Math.max(0,this.yieldAfter-e);if(this.total+=this.counter,this.counter=0,e>=this.yieldAfter||n<=1)return this.counterInterval=1,this.total=0,!0;switch(e){case 0:case 1:this.counterInterval=this.total*2;break}}return!1}};async function yE(t,e,n,r){if(t.length===0)return[];let i=new Array(t.length),s=new Zs(r?.yieldAfter);function o(c){s.start();for(let u=c;u<t.length;u++)if(i[u]=e(t[u]),s.shouldYield())return r?.yieldCallback&&r.yieldCallback(),u+1;return-1}let a=o(0);for(;a!==-1&&!(n!==void 0&&n.isCancellationRequested);)a=await new Promise(c=>{(0,eo.RAL)().timer.setImmediate(()=>{c(o(a))})});return i}yt.map=yE;async function wE(t,e,n,r){if(t.length===0)return[];let i=new Array(t.length),s=new Zs(r?.yieldAfter);async function o(c){s.start();for(let u=c;u<t.length;u++)if(i[u]=await e(t[u],n),s.shouldYield())return r?.yieldCallback&&r.yieldCallback(),u+1;return-1}let a=await o(0);for(;a!==-1&&!(n!==void 0&&n.isCancellationRequested);)a=await new Promise(c=>{(0,eo.RAL)().timer.setImmediate(()=>{c(o(a))})});return i}yt.mapAsync=wE;async function bE(t,e,n,r){if(t.length===0)return;let i=new Zs(r?.yieldAfter);function s(a){i.start();for(let c=a;c<t.length;c++)if(e(t[c]),i.shouldYield())return r?.yieldCallback&&r.yieldCallback(),c+1;return-1}let o=s(0);for(;o!==-1&&!(n!==void 0&&n.isCancellationRequested);)o=await new Promise(a=>{(0,eo.RAL)().timer.setImmediate(()=>{a(s(o))})})}yt.forEach=bE});var Af=b(Mf=>{"use strict";Object.defineProperty(Mf,"__esModule",{value:!0});var CE=require("vscode"),Ff=class extends CE.CompletionItem{constructor(e){super(e)}};Mf.default=Ff});var $f=b(jf=>{"use strict";Object.defineProperty(jf,"__esModule",{value:!0});var _E=require("vscode"),Lf=class extends _E.CodeLens{constructor(e){super(e)}};jf.default=Lf});var Vf=b(Uf=>{"use strict";Object.defineProperty(Uf,"__esModule",{value:!0});var SE=require("vscode"),Hf=class extends SE.DocumentLink{constructor(e,n){super(e,n)}};Uf.default=Hf});var zf=b(Kf=>{"use strict";Object.defineProperty(Kf,"__esModule",{value:!0});var DE=require("vscode"),Wf=class extends DE.CodeAction{constructor(e,n){super(e),this.data=n}};Kf.default=Wf});var Xf=b(rs=>{"use strict";Object.defineProperty(rs,"__esModule",{value:!0});rs.ProtocolDiagnostic=rs.DiagnosticCode=void 0;var RE=require("vscode"),Bf=ln(),Zb;(function(t){function e(n){let r=n;return r!=null&&(Bf.number(r.value)||Bf.string(r.value))&&Bf.string(r.target)}t.is=e})(Zb||(rs.DiagnosticCode=Zb={}));var Gf=class extends RE.Diagnostic{constructor(e,n,r,i){super(e,n,r),this.data=i,this.hasDiagnosticCode=!1}};rs.ProtocolDiagnostic=Gf});var Qf=b(Yf=>{"use strict";Object.defineProperty(Yf,"__esModule",{value:!0});var TE=require("vscode"),Jf=class extends TE.CallHierarchyItem{constructor(e,n,r,i,s,o,a){super(e,n,r,i,s,o),a!==void 0&&(this.data=a)}};Yf.default=Jf});var tp=b(ep=>{"use strict";Object.defineProperty(ep,"__esModule",{value:!0});var PE=require("vscode"),Zf=class extends PE.TypeHierarchyItem{constructor(e,n,r,i,s,o,a){super(e,n,r,i,s,o),a!==void 0&&(this.data=a)}};ep.default=Zf});var ip=b(rp=>{"use strict";Object.defineProperty(rp,"__esModule",{value:!0});var Wa=require("vscode"),np=class extends Wa.SymbolInformation{constructor(e,n,r,i,s){let o=!(i instanceof Wa.Uri);super(e,n,r,o?i:new Wa.Location(i,new Wa.Range(0,0,0,0))),this.hasRange=o,s!==void 0&&(this.data=s)}};rp.default=np});var ap=b(op=>{"use strict";Object.defineProperty(op,"__esModule",{value:!0});var EE=require("vscode"),sp=class extends EE.InlayHint{constructor(e,n,r){super(e,n,r)}};op.default=sp});var rC=b(Ka=>{"use strict";Object.defineProperty(Ka,"__esModule",{value:!0});Ka.createConverter=void 0;var et=require("vscode"),Q=J(),ci=ln(),eC=Va(),xE=Af(),qE=$f(),kE=Vf(),tC=zf(),nC=Xf(),OE=Qf(),IE=tp(),NE=ip(),FE=ap(),cp;(function(t){function e(n){let r=n;return r&&!!r.inserting&&!!r.replacing}t.is=e})(cp||(cp={}));function ME(t){let n=t||(h=>h.toString());function r(h){return n(h)}function i(h){return{uri:n(h.uri)}}function s(h){return{uri:n(h.uri),languageId:h.languageId,version:h.version,text:h.getText()}}function o(h){return{uri:n(h.uri),version:h.version}}function a(h){return{textDocument:s(h)}}function c(h){let R=h;return!!R.document&&!!R.contentChanges}function u(h){let R=h;return!!R.uri&&!!R.version}function f(h,R,V){if(u(h))return{textDocument:{uri:n(h.uri),version:h.version},contentChanges:[{text:h.getText()}]};if(c(h)){let we=R,Te=V;return{textDocument:{uri:n(we),version:Te},contentChanges:h.contentChanges.map(Gt=>{let Fn=Gt.range;return{range:{start:{line:Fn.start.line,character:Fn.start.character},end:{line:Fn.end.line,character:Fn.end.character}},rangeLength:Gt.rangeLength,text:Gt.text}})}}else throw Error("Unsupported text document change parameter")}function d(h){return{textDocument:i(h)}}function p(h,R=!1){let V={textDocument:i(h)};return R&&(V.text=h.getText()),V}function g(h){switch(h){case et.TextDocumentSaveReason.Manual:return Q.TextDocumentSaveReason.Manual;case et.TextDocumentSaveReason.AfterDelay:return Q.TextDocumentSaveReason.AfterDelay;case et.TextDocumentSaveReason.FocusOut:return Q.TextDocumentSaveReason.FocusOut}return Q.TextDocumentSaveReason.Manual}function m(h){return{textDocument:i(h.document),reason:g(h.reason)}}function S(h){return{files:h.files.map(R=>({uri:n(R)}))}}function D(h){return{files:h.files.map(R=>({oldUri:n(R.oldUri),newUri:n(R.newUri)}))}}function P(h){return{files:h.files.map(R=>({uri:n(R)}))}}function k(h){return{files:h.files.map(R=>({uri:n(R)}))}}function x(h){return{files:h.files.map(R=>({oldUri:n(R.oldUri),newUri:n(R.newUri)}))}}function I(h){return{files:h.files.map(R=>({uri:n(R)}))}}function M(h,R){return{textDocument:i(h),position:It(R)}}function Y(h){switch(h){case et.CompletionTriggerKind.TriggerCharacter:return Q.CompletionTriggerKind.TriggerCharacter;case et.CompletionTriggerKind.TriggerForIncompleteCompletions:return Q.CompletionTriggerKind.TriggerForIncompleteCompletions;default:return Q.CompletionTriggerKind.Invoked}}function B(h,R,V){return{textDocument:i(h),position:It(R),context:{triggerKind:Y(V.triggerKind),triggerCharacter:V.triggerCharacter}}}function Z(h){switch(h){case et.SignatureHelpTriggerKind.Invoke:return Q.SignatureHelpTriggerKind.Invoked;case et.SignatureHelpTriggerKind.TriggerCharacter:return Q.SignatureHelpTriggerKind.TriggerCharacter;case et.SignatureHelpTriggerKind.ContentChange:return Q.SignatureHelpTriggerKind.ContentChange}}function fe(h){return{label:h.label}}function St(h){return h.map(fe)}function ht(h){return{label:h.label,parameters:St(h.parameters)}}function Ot(h){return h.map(ht)}function gr(h){return h===void 0?h:{signatures:Ot(h.signatures),activeSignature:h.activeSignature,activeParameter:h.activeParameter}}function In(h,R,V){return{textDocument:i(h),position:It(R),context:{isRetrigger:V.isRetrigger,triggerCharacter:V.triggerCharacter,triggerKind:Z(V.triggerKind),activeSignatureHelp:gr(V.activeSignatureHelp)}}}function It(h){return{line:h.line,character:h.character}}function Dt(h){return h==null?h:{line:h.line>Q.uinteger.MAX_VALUE?Q.uinteger.MAX_VALUE:h.line,character:h.character>Q.uinteger.MAX_VALUE?Q.uinteger.MAX_VALUE:h.character}}function mr(h,R){return eC.map(h,Dt,R)}function Gr(h){return h.map(Dt)}function Je(h){return h==null?h:{start:Dt(h.start),end:Dt(h.end)}}function Cn(h){return h.map(Je)}function _n(h){return h==null?h:Q.Location.create(r(h.uri),Je(h.range))}function Sn(h){switch(h){case et.DiagnosticSeverity.Error:return Q.DiagnosticSeverity.Error;case et.DiagnosticSeverity.Warning:return Q.DiagnosticSeverity.Warning;case et.DiagnosticSeverity.Information:return Q.DiagnosticSeverity.Information;case et.DiagnosticSeverity.Hint:return Q.DiagnosticSeverity.Hint}}function Es(h){if(!h)return;let R=[];for(let V of h){let we=Xr(V);we!==void 0&&R.push(we)}return R.length>0?R:void 0}function Xr(h){switch(h){case et.DiagnosticTag.Unnecessary:return Q.DiagnosticTag.Unnecessary;case et.DiagnosticTag.Deprecated:return Q.DiagnosticTag.Deprecated;default:return}}function Jr(h){return{message:h.message,location:_n(h.location)}}function Pi(h){return h.map(Jr)}function xs(h){if(h!=null)return ci.number(h)||ci.string(h)?h:{value:h.value,target:r(h.target)}}function Yr(h){let R=Q.Diagnostic.create(Je(h.range),h.message),V=h instanceof nC.ProtocolDiagnostic?h:void 0;V!==void 0&&V.data!==void 0&&(R.data=V.data);let we=xs(h.code);return nC.DiagnosticCode.is(we)?V!==void 0&&V.hasDiagnosticCode?R.code=we:(R.code=we.value,R.codeDescription={href:we.target}):R.code=we,ci.number(h.severity)&&(R.severity=Sn(h.severity)),Array.isArray(h.tags)&&(R.tags=Es(h.tags)),h.relatedInformation&&(R.relatedInformation=Pi(h.relatedInformation)),h.source&&(R.source=h.source),R}function vr(h,R){return h==null?h:eC.map(h,Yr,R)}function yr(h){return h==null?h:h.map(Yr)}function qs(h,R){switch(h){case"$string":return R;case Q.MarkupKind.PlainText:return{kind:h,value:R};case Q.MarkupKind.Markdown:return{kind:h,value:R.value};default:return`Unsupported Markup content received. Kind is: ${h}`}}function ut(h){switch(h){case et.CompletionItemTag.Deprecated:return Q.CompletionItemTag.Deprecated}}function Ei(h){if(h===void 0)return h;let R=[];for(let V of h){let we=ut(V);we!==void 0&&R.push(we)}return R}function wr(h,R){return R!==void 0?R:h+1}function Bn(h,R=!1){let V,we;ci.string(h.label)?V=h.label:(V=h.label.label,R&&(h.label.detail!==void 0||h.label.description!==void 0)&&(we={detail:h.label.detail,description:h.label.description}));let Te={label:V};we!==void 0&&(Te.labelDetails=we);let it=h instanceof xE.default?h:void 0;h.detail&&(Te.detail=h.detail),h.documentation&&(!it||it.documentationFormat==="$string"?Te.documentation=h.documentation:Te.documentation=qs(it.documentationFormat,h.documentation)),h.filterText&&(Te.filterText=h.filterText),ks(Te,h),ci.number(h.kind)&&(Te.kind=wr(h.kind,it&&it.originalItemKind)),h.sortText&&(Te.sortText=h.sortText),h.additionalTextEdits&&(Te.additionalTextEdits=rt(h.additionalTextEdits)),h.commitCharacters&&(Te.commitCharacters=h.commitCharacters.slice()),h.command&&(Te.command=$(h.command)),(h.preselect===!0||h.preselect===!1)&&(Te.preselect=h.preselect);let Gt=Ei(h.tags);if(it){if(it.data!==void 0&&(Te.data=it.data),it.deprecated===!0||it.deprecated===!1){if(it.deprecated===!0&&Gt!==void 0&&Gt.length>0){let Fn=Gt.indexOf(et.CompletionItemTag.Deprecated);Fn!==-1&&Gt.splice(Fn,1)}Te.deprecated=it.deprecated}it.insertTextMode!==void 0&&(Te.insertTextMode=it.insertTextMode)}return Gt!==void 0&&Gt.length>0&&(Te.tags=Gt),Te.insertTextMode===void 0&&h.keepWhitespace===!0&&(Te.insertTextMode=Q.InsertTextMode.adjustIndentation),Te}function ks(h,R){let V=Q.InsertTextFormat.PlainText,we,Te;R.textEdit?(we=R.textEdit.newText,Te=R.textEdit.range):R.insertText instanceof et.SnippetString?(V=Q.InsertTextFormat.Snippet,we=R.insertText.value):we=R.insertText,R.range&&(Te=R.range),h.insertTextFormat=V,R.fromEdit&&we!==void 0&&Te!==void 0?h.textEdit=Gn(we,Te):h.insertText=we}function Gn(h,R){return cp.is(R)?Q.InsertReplaceEdit.create(h,Je(R.inserting),Je(R.replacing)):{newText:h,range:Je(R)}}function Qr(h){return{range:Je(h.range),newText:h.newText}}function rt(h){return h==null?h:h.map(Qr)}function Bt(h){return h<=et.SymbolKind.TypeParameter?h+1:Q.SymbolKind.Property}function Nt(h){return h}function Nn(h){return h.map(Nt)}function Xn(h,R,V){return{textDocument:i(h),position:It(R),context:{includeDeclaration:V.includeDeclaration}}}async function Zr(h,R){let V=Q.CodeAction.create(h.title);if(h instanceof tC.default&&h.data!==void 0&&(V.data=h.data),h.kind!==void 0&&(V.kind=y(h.kind)),h.diagnostics!==void 0&&(V.diagnostics=await vr(h.diagnostics,R)),h.edit!==void 0)throw new Error("VS Code code actions can only be converted to a protocol code action without an edit.");return h.command!==void 0&&(V.command=$(h.command)),h.isPreferred!==void 0&&(V.isPreferred=h.isPreferred),h.disabled!==void 0&&(V.disabled={reason:h.disabled.reason}),V}function xi(h){let R=Q.CodeAction.create(h.title);if(h instanceof tC.default&&h.data!==void 0&&(R.data=h.data),h.kind!==void 0&&(R.kind=y(h.kind)),h.diagnostics!==void 0&&(R.diagnostics=yr(h.diagnostics)),h.edit!==void 0)throw new Error("VS Code code actions can only be converted to a protocol code action without an edit.");return h.command!==void 0&&(R.command=$(h.command)),h.isPreferred!==void 0&&(R.isPreferred=h.isPreferred),h.disabled!==void 0&&(R.disabled={reason:h.disabled.reason}),R}async function br(h,R){if(h==null)return h;let V;return h.only&&ci.string(h.only.value)&&(V=[h.only.value]),Q.CodeActionContext.create(await vr(h.diagnostics,R),V,sn(h.triggerKind))}function qi(h){if(h==null)return h;let R;return h.only&&ci.string(h.only.value)&&(R=[h.only.value]),Q.CodeActionContext.create(yr(h.diagnostics),R,sn(h.triggerKind))}function sn(h){switch(h){case et.CodeActionTriggerKind.Invoke:return Q.CodeActionTriggerKind.Invoked;case et.CodeActionTriggerKind.Automatic:return Q.CodeActionTriggerKind.Automatic;default:return}}function y(h){if(h!=null)return h.value}function E(h){return h==null?h:Q.InlineValueContext.create(h.frameId,Je(h.stoppedLocation))}function F(h,R,V){return{context:Q.InlineCompletionContext.create(V.triggerKind,V.selectedCompletionInfo),textDocument:i(h),position:Dt(R)}}function $(h){let R=Q.Command.create(h.title,h.command);return h.arguments&&(R.arguments=h.arguments),R}function ye(h){let R=Q.CodeLens.create(Je(h.range));return h.command&&(R.command=$(h.command)),h instanceof qE.default&&h.data&&(R.data=h.data),R}function ge(h,R){let V={tabSize:h.tabSize,insertSpaces:h.insertSpaces};return R.trimTrailingWhitespace&&(V.trimTrailingWhitespace=!0),R.trimFinalNewlines&&(V.trimFinalNewlines=!0),R.insertFinalNewline&&(V.insertFinalNewline=!0),V}function Ee(h){return{textDocument:i(h)}}function Oe(h){return{textDocument:i(h)}}function ie(h){let R=Q.DocumentLink.create(Je(h.range));h.target&&(R.target=r(h.target)),h.tooltip!==void 0&&(R.tooltip=h.tooltip);let V=h instanceof kE.default?h:void 0;return V&&V.data&&(R.data=V.data),R}function Re(h){return{textDocument:i(h)}}function pe(h){let R={name:h.name,kind:Bt(h.kind),uri:r(h.uri),range:Je(h.range),selectionRange:Je(h.selectionRange)};return h.detail!==void 0&&h.detail.length>0&&(R.detail=h.detail),h.tags!==void 0&&(R.tags=Nn(h.tags)),h instanceof OE.default&&h.data!==void 0&&(R.data=h.data),R}function Ie(h){let R={name:h.name,kind:Bt(h.kind),uri:r(h.uri),range:Je(h.range),selectionRange:Je(h.selectionRange)};return h.detail!==void 0&&h.detail.length>0&&(R.detail=h.detail),h.tags!==void 0&&(R.tags=Nn(h.tags)),h instanceof IE.default&&h.data!==void 0&&(R.data=h.data),R}function tt(h){let R=h instanceof NE.default?{name:h.name,kind:Bt(h.kind),location:h.hasRange?_n(h.location):{uri:n(h.location.uri)},data:h.data}:{name:h.name,kind:Bt(h.kind),location:_n(h.location)};return h.tags!==void 0&&(R.tags=Nn(h.tags)),h.containerName!==""&&(R.containerName=h.containerName),R}function Rt(h){let R=typeof h.label=="string"?h.label:h.label.map(Os),V=Q.InlayHint.create(Dt(h.position),R);return h.kind!==void 0&&(V.kind=h.kind),h.textEdits!==void 0&&(V.textEdits=rt(h.textEdits)),h.tooltip!==void 0&&(V.tooltip=Is(h.tooltip)),h.paddingLeft!==void 0&&(V.paddingLeft=h.paddingLeft),h.paddingRight!==void 0&&(V.paddingRight=h.paddingRight),h instanceof FE.default&&h.data!==void 0&&(V.data=h.data),V}function Os(h){let R=Q.InlayHintLabelPart.create(h.value);return h.location!==void 0&&(R.location=_n(h.location)),h.command!==void 0&&(R.command=$(h.command)),h.tooltip!==void 0&&(R.tooltip=Is(h.tooltip)),R}function Is(h){return typeof h=="string"?h:{kind:Q.MarkupKind.Markdown,value:h.value}}return{asUri:r,asTextDocumentIdentifier:i,asTextDocumentItem:s,asVersionedTextDocumentIdentifier:o,asOpenTextDocumentParams:a,asChangeTextDocumentParams:f,asCloseTextDocumentParams:d,asSaveTextDocumentParams:p,asWillSaveTextDocumentParams:m,asDidCreateFilesParams:S,asDidRenameFilesParams:D,asDidDeleteFilesParams:P,asWillCreateFilesParams:k,asWillRenameFilesParams:x,asWillDeleteFilesParams:I,asTextDocumentPositionParams:M,asCompletionParams:B,asSignatureHelpParams:In,asWorkerPosition:It,asRange:Je,asRanges:Cn,asPosition:Dt,asPositions:mr,asPositionsSync:Gr,asLocation:_n,asDiagnosticSeverity:Sn,asDiagnosticTag:Xr,asDiagnostic:Yr,asDiagnostics:vr,asDiagnosticsSync:yr,asCompletionItem:Bn,asTextEdit:Qr,asSymbolKind:Bt,asSymbolTag:Nt,asSymbolTags:Nn,asReferenceParams:Xn,asCodeAction:Zr,asCodeActionSync:xi,asCodeActionContext:br,asCodeActionContextSync:qi,asInlineValueContext:E,asCommand:$,asCodeLens:ye,asFormattingOptions:ge,asDocumentSymbolParams:Ee,asCodeLensParams:Oe,asDocumentLink:ie,asDocumentLinkParams:Re,asCallHierarchyItem:pe,asTypeHierarchyItem:Ie,asInlayHint:Rt,asWorkspaceSymbol:tt,asInlineCompletionParams:F}}Ka.createConverter=ME});var oC=b(Ba=>{"use strict";Object.defineProperty(Ba,"__esModule",{value:!0});Ba.createConverter=void 0;var A=require("vscode"),X=J(),dn=ln(),he=Va(),AE=Af(),LE=$f(),jE=Vf(),$E=zf(),iC=Xf(),HE=Qf(),UE=tp(),VE=ip(),WE=ap(),sC=J(),za;(function(t){function e(n){let r=n;return r&&dn.string(r.language)&&dn.string(r.value)}t.is=e})(za||(za={}));function KE(t,e,n){let i=t||(l=>A.Uri.parse(l));function s(l){return i(l)}function o(l){let w=[];for(let q of l)if(typeof q=="string")w.push(q);else if(sC.NotebookCellTextDocumentFilter.is(q))if(typeof q.notebook=="string")w.push({notebookType:q.notebook,language:q.language});else{let W=q.notebook.notebookType??"*";w.push({notebookType:W,scheme:q.notebook.scheme,pattern:q.notebook.pattern,language:q.language})}else sC.TextDocumentFilter.is(q)&&w.push({language:q.language,scheme:q.scheme,pattern:q.pattern});return w}async function a(l,w){return he.map(l,u,w)}function c(l){let w=new Array(l.length);for(let q=0;q<l.length;q++)w[q]=u(l[q]);return w}function u(l){let w=new iC.ProtocolDiagnostic(m(l.range),l.message,D(l.severity),l.data);if(l.code!==void 0){if(typeof l.code=="string"||typeof l.code=="number")X.CodeDescription.is(l.codeDescription)?w.code={value:l.code,target:s(l.codeDescription.href)}:w.code=l.code;else if(iC.DiagnosticCode.is(l.code)){w.hasDiagnosticCode=!0;let q=l.code;w.code={value:q.value,target:s(q.target)}}}return l.source&&(w.source=l.source),l.relatedInformation&&(w.relatedInformation=f(l.relatedInformation)),Array.isArray(l.tags)&&(w.tags=d(l.tags)),w}function f(l){let w=new Array(l.length);for(let q=0;q<l.length;q++){let W=l[q];w[q]=new A.DiagnosticRelatedInformation(Sn(W.location),W.message)}return w}function d(l){if(!l)return;let w=[];for(let q of l){let W=p(q);W!==void 0&&w.push(W)}return w.length>0?w:void 0}function p(l){switch(l){case X.DiagnosticTag.Unnecessary:return A.DiagnosticTag.Unnecessary;case X.DiagnosticTag.Deprecated:return A.DiagnosticTag.Deprecated;default:return}}function g(l){return l?new A.Position(l.line,l.character):void 0}function m(l){return l?new A.Range(l.start.line,l.start.character,l.end.line,l.end.character):void 0}async function S(l,w){return he.map(l,q=>new A.Range(q.start.line,q.start.character,q.end.line,q.end.character),w)}function D(l){if(l==null)return A.DiagnosticSeverity.Error;switch(l){case X.DiagnosticSeverity.Error:return A.DiagnosticSeverity.Error;case X.DiagnosticSeverity.Warning:return A.DiagnosticSeverity.Warning;case X.DiagnosticSeverity.Information:return A.DiagnosticSeverity.Information;case X.DiagnosticSeverity.Hint:return A.DiagnosticSeverity.Hint}return A.DiagnosticSeverity.Error}function P(l){if(dn.string(l))return x(l);if(za.is(l))return x().appendCodeblock(l.value,l.language);if(Array.isArray(l)){let w=[];for(let q of l){let W=x();za.is(q)?W.appendCodeblock(q.value,q.language):W.appendMarkdown(q),w.push(W)}return w}else return x(l)}function k(l){if(dn.string(l))return l;switch(l.kind){case X.MarkupKind.Markdown:return x(l.value);case X.MarkupKind.PlainText:return l.value;default:return`Unsupported Markup content received. Kind is: ${l.kind}`}}function x(l){let w;if(l===void 0||typeof l=="string")w=new A.MarkdownString(l);else switch(l.kind){case X.MarkupKind.Markdown:w=new A.MarkdownString(l.value);break;case X.MarkupKind.PlainText:w=new A.MarkdownString,w.appendText(l.value);break;default:w=new A.MarkdownString,w.appendText(`Unsupported Markup content received. Kind is: ${l.kind}`);break}return w.isTrusted=e,w.supportHtml=n,w}function I(l){if(l)return new A.Hover(P(l.contents),m(l.range))}async function M(l,w,q){if(!l)return;if(Array.isArray(l))return he.map(l,Mn=>St(Mn,w),q);let W=l,{defaultRange:st,commitCharacters:lt}=Y(W,w),Se=await he.map(W.items,Mn=>St(Mn,lt,st,W.itemDefaults?.insertTextMode,W.itemDefaults?.insertTextFormat,W.itemDefaults?.data),q);return new A.CompletionList(Se,W.isIncomplete)}function Y(l,w){let q=l.itemDefaults?.editRange,W=l.itemDefaults?.commitCharacters??w;return X.Range.is(q)?{defaultRange:m(q),commitCharacters:W}:q!==void 0?{defaultRange:{inserting:m(q.insert),replacing:m(q.replace)},commitCharacters:W}:{defaultRange:void 0,commitCharacters:W}}function B(l){return X.CompletionItemKind.Text<=l&&l<=X.CompletionItemKind.TypeParameter?[l-1,void 0]:[A.CompletionItemKind.Text,l]}function Z(l){switch(l){case X.CompletionItemTag.Deprecated:return A.CompletionItemTag.Deprecated}}function fe(l){if(l==null)return[];let w=[];for(let q of l){let W=Z(q);W!==void 0&&w.push(W)}return w}function St(l,w,q,W,st,lt){let Se=fe(l.tags),Mn=ht(l),xe=new AE.default(Mn);l.detail&&(xe.detail=l.detail),l.documentation&&(xe.documentation=k(l.documentation),xe.documentationFormat=dn.string(l.documentation)?"$string":l.documentation.kind),l.filterText&&(xe.filterText=l.filterText);let Vo=Ot(l,q,st);if(Vo&&(xe.insertText=Vo.text,xe.range=Vo.range,xe.fromEdit=Vo.fromEdit),dn.number(l.kind)){let[uT,Am]=B(l.kind);xe.kind=uT,Am&&(xe.originalItemKind=Am)}l.sortText&&(xe.sortText=l.sortText),l.additionalTextEdits&&(xe.additionalTextEdits=Dt(l.additionalTextEdits));let Fm=l.commitCharacters!==void 0?dn.stringArray(l.commitCharacters)?l.commitCharacters:void 0:w;Fm&&(xe.commitCharacters=Fm.slice()),l.command&&(xe.command=rt(l.command)),(l.deprecated===!0||l.deprecated===!1)&&(xe.deprecated=l.deprecated,l.deprecated===!0&&Se.push(A.CompletionItemTag.Deprecated)),(l.preselect===!0||l.preselect===!1)&&(xe.preselect=l.preselect);let Mm=l.data??lt;Mm!==void 0&&(xe.data=Mm),Se.length>0&&(xe.tags=Se);let Pu=l.insertTextMode??W;return Pu!==void 0&&(xe.insertTextMode=Pu,Pu===X.InsertTextMode.asIs&&(xe.keepWhitespace=!0)),xe}function ht(l){return X.CompletionItemLabelDetails.is(l.labelDetails)?{label:l.label,detail:l.labelDetails.detail,description:l.labelDetails.description}:l.label}function Ot(l,w,q){let W=l.insertTextFormat??q;if(l.textEdit!==void 0||w!==void 0){let[st,lt]=l.textEdit!==void 0?gr(l.textEdit):[w,l.textEditText??l.label];return W===X.InsertTextFormat.Snippet?{text:new A.SnippetString(lt),range:st,fromEdit:!0}:{text:lt,range:st,fromEdit:!0}}else return l.insertText?W===X.InsertTextFormat.Snippet?{text:new A.SnippetString(l.insertText),fromEdit:!1}:{text:l.insertText,fromEdit:!1}:void 0}function gr(l){return X.InsertReplaceEdit.is(l)?[{inserting:m(l.insert),replacing:m(l.replace)},l.newText]:[m(l.range),l.newText]}function In(l){if(l)return new A.TextEdit(m(l.range),l.newText)}async function It(l,w){if(l)return he.map(l,In,w)}function Dt(l){if(!l)return;let w=new Array(l.length);for(let q=0;q<l.length;q++)w[q]=In(l[q]);return w}async function mr(l,w){if(!l)return;let q=new A.SignatureHelp;return dn.number(l.activeSignature)?q.activeSignature=l.activeSignature:q.activeSignature=0,dn.number(l.activeParameter)?q.activeParameter=l.activeParameter:q.activeParameter=0,l.signatures&&(q.signatures=await Gr(l.signatures,w)),q}async function Gr(l,w){return he.mapAsync(l,Je,w)}async function Je(l,w){let q=new A.SignatureInformation(l.label);return l.documentation!==void 0&&(q.documentation=k(l.documentation)),l.parameters!==void 0&&(q.parameters=await Cn(l.parameters,w)),l.activeParameter!==void 0&&(q.activeParameter=l.activeParameter),q}function Cn(l,w){return he.map(l,_n,w)}function _n(l){let w=new A.ParameterInformation(l.label);return l.documentation&&(w.documentation=k(l.documentation)),w}function Sn(l){return l?new A.Location(i(l.uri),m(l.range)):void 0}async function Es(l,w){if(l)return Pi(l,w)}async function Xr(l,w){if(l)return Pi(l,w)}function Jr(l){if(!l)return;let w={targetUri:i(l.targetUri),targetRange:m(l.targetRange),originSelectionRange:m(l.originSelectionRange),targetSelectionRange:m(l.targetSelectionRange)};if(!w.targetSelectionRange)throw new Error("targetSelectionRange must not be undefined or null");return w}async function Pi(l,w){if(l)if(dn.array(l)){if(l.length===0)return[];if(X.LocationLink.is(l[0])){let q=l;return he.map(q,Jr,w)}else{let q=l;return he.map(q,Sn,w)}}else return X.LocationLink.is(l)?[Jr(l)]:Sn(l)}async function xs(l,w){if(l)return he.map(l,Sn,w)}async function Yr(l,w){if(l)return he.map(l,vr,w)}function vr(l){let w=new A.DocumentHighlight(m(l.range));return dn.number(l.kind)&&(w.kind=yr(l.kind)),w}function yr(l){switch(l){case X.DocumentHighlightKind.Text:return A.DocumentHighlightKind.Text;case X.DocumentHighlightKind.Read:return A.DocumentHighlightKind.Read;case X.DocumentHighlightKind.Write:return A.DocumentHighlightKind.Write}return A.DocumentHighlightKind.Text}async function qs(l,w){if(l)return he.map(l,Bn,w)}function ut(l){return l<=X.SymbolKind.TypeParameter?l-1:A.SymbolKind.Property}function Ei(l){switch(l){case X.SymbolTag.Deprecated:return A.SymbolTag.Deprecated;default:return}}function wr(l){if(l==null)return;let w=[];for(let q of l){let W=Ei(q);W!==void 0&&w.push(W)}return w.length===0?void 0:w}function Bn(l){let w=l.data,q=l.location,W=q.range===void 0||w!==void 0?new VE.default(l.name,ut(l.kind),l.containerName??"",q.range===void 0?i(q.uri):new A.Location(i(l.location.uri),m(q.range)),w):new A.SymbolInformation(l.name,ut(l.kind),l.containerName??"",new A.Location(i(l.location.uri),m(q.range)));return Qr(W,l),W}async function ks(l,w){if(l!=null)return he.map(l,Gn,w)}function Gn(l){let w=new A.DocumentSymbol(l.name,l.detail||"",ut(l.kind),m(l.range),m(l.selectionRange));if(Qr(w,l),l.children!==void 0&&l.children.length>0){let q=[];for(let W of l.children)q.push(Gn(W));w.children=q}return w}function Qr(l,w){l.tags=wr(w.tags),w.deprecated&&(l.tags?l.tags.includes(A.SymbolTag.Deprecated)||(l.tags=l.tags.concat(A.SymbolTag.Deprecated)):l.tags=[A.SymbolTag.Deprecated])}function rt(l){let w={title:l.title,command:l.command};return l.arguments&&(w.arguments=l.arguments),w}async function Bt(l,w){if(l)return he.map(l,rt,w)}let Nt=new Map;Nt.set(X.CodeActionKind.Empty,A.CodeActionKind.Empty),Nt.set(X.CodeActionKind.QuickFix,A.CodeActionKind.QuickFix),Nt.set(X.CodeActionKind.Refactor,A.CodeActionKind.Refactor),Nt.set(X.CodeActionKind.RefactorExtract,A.CodeActionKind.RefactorExtract),Nt.set(X.CodeActionKind.RefactorInline,A.CodeActionKind.RefactorInline),Nt.set(X.CodeActionKind.RefactorRewrite,A.CodeActionKind.RefactorRewrite),Nt.set(X.CodeActionKind.Source,A.CodeActionKind.Source),Nt.set(X.CodeActionKind.SourceOrganizeImports,A.CodeActionKind.SourceOrganizeImports);function Nn(l){if(l==null)return;let w=Nt.get(l);if(w)return w;let q=l.split(".");w=A.CodeActionKind.Empty;for(let W of q)w=w.append(W);return w}function Xn(l){if(l!=null)return l.map(w=>Nn(w))}async function Zr(l,w){if(l==null)return;let q=new $E.default(l.title,l.data);return l.kind!==void 0&&(q.kind=Nn(l.kind)),l.diagnostics!==void 0&&(q.diagnostics=c(l.diagnostics)),l.edit!==void 0&&(q.edit=await sn(l.edit,w)),l.command!==void 0&&(q.command=rt(l.command)),l.isPreferred!==void 0&&(q.isPreferred=l.isPreferred),l.disabled!==void 0&&(q.disabled={reason:l.disabled.reason}),q}function xi(l,w){return he.mapAsync(l,async q=>X.Command.is(q)?rt(q):Zr(q,w),w)}function br(l){if(!l)return;let w=new LE.default(m(l.range));return l.command&&(w.command=rt(l.command)),l.data!==void 0&&l.data!==null&&(w.data=l.data),w}async function qi(l,w){if(l)return he.map(l,br,w)}async function sn(l,w){if(!l)return;let q=new Map;if(l.changeAnnotations!==void 0){let lt=l.changeAnnotations;await he.forEach(Object.keys(lt),Se=>{let Mn=y(lt[Se]);q.set(Se,Mn)},w)}let W=lt=>{if(lt!==void 0)return q.get(lt)},st=new A.WorkspaceEdit;if(l.documentChanges){let lt=l.documentChanges;await he.forEach(lt,Se=>{if(X.CreateFile.is(Se))st.createFile(i(Se.uri),Se.options,W(Se.annotationId));else if(X.RenameFile.is(Se))st.renameFile(i(Se.oldUri),i(Se.newUri),Se.options,W(Se.annotationId));else if(X.DeleteFile.is(Se))st.deleteFile(i(Se.uri),Se.options,W(Se.annotationId));else if(X.TextDocumentEdit.is(Se)){let Mn=i(Se.textDocument.uri);for(let xe of Se.edits)X.AnnotatedTextEdit.is(xe)?st.replace(Mn,m(xe.range),xe.newText,W(xe.annotationId)):st.replace(Mn,m(xe.range),xe.newText)}else throw new Error(`Unknown workspace edit change received:
${JSON.stringify(Se,void 0,4)}`)},w)}else if(l.changes){let lt=l.changes;await he.forEach(Object.keys(lt),Se=>{st.set(i(Se),Dt(lt[Se]))},w)}return st}function y(l){if(l!==void 0)return{label:l.label,needsConfirmation:!!l.needsConfirmation,description:l.description}}function E(l){let w=m(l.range),q=l.target?s(l.target):void 0,W=new jE.default(w,q);return l.tooltip!==void 0&&(W.tooltip=l.tooltip),l.data!==void 0&&l.data!==null&&(W.data=l.data),W}async function F(l,w){if(l)return he.map(l,E,w)}function $(l){return new A.Color(l.red,l.green,l.blue,l.alpha)}function ye(l){return new A.ColorInformation(m(l.range),$(l.color))}async function ge(l,w){if(l)return he.map(l,ye,w)}function Ee(l){let w=new A.ColorPresentation(l.label);return w.additionalTextEdits=Dt(l.additionalTextEdits),l.textEdit&&(w.textEdit=In(l.textEdit)),w}async function Oe(l,w){if(l)return he.map(l,Ee,w)}function ie(l){if(l)switch(l){case X.FoldingRangeKind.Comment:return A.FoldingRangeKind.Comment;case X.FoldingRangeKind.Imports:return A.FoldingRangeKind.Imports;case X.FoldingRangeKind.Region:return A.FoldingRangeKind.Region}}function Re(l){return new A.FoldingRange(l.startLine,l.endLine,ie(l.kind))}async function pe(l,w){if(l)return he.map(l,Re,w)}function Ie(l){return new A.SelectionRange(m(l.range),l.parent?Ie(l.parent):void 0)}async function tt(l,w){return Array.isArray(l)?he.map(l,Ie,w):[]}function Rt(l){return X.InlineValueText.is(l)?new A.InlineValueText(m(l.range),l.text):X.InlineValueVariableLookup.is(l)?new A.InlineValueVariableLookup(m(l.range),l.variableName,l.caseSensitiveLookup):new A.InlineValueEvaluatableExpression(m(l.range),l.expression)}async function Os(l,w){return Array.isArray(l)?he.map(l,Rt,w):[]}async function Is(l,w){let q=typeof l.label=="string"?l.label:await he.map(l.label,h,w),W=new WE.default(g(l.position),q);return l.kind!==void 0&&(W.kind=l.kind),l.textEdits!==void 0&&(W.textEdits=await It(l.textEdits,w)),l.tooltip!==void 0&&(W.tooltip=R(l.tooltip)),l.paddingLeft!==void 0&&(W.paddingLeft=l.paddingLeft),l.paddingRight!==void 0&&(W.paddingRight=l.paddingRight),l.data!==void 0&&(W.data=l.data),W}function h(l){let w=new A.InlayHintLabelPart(l.value);return l.location!==void 0&&(w.location=Sn(l.location)),l.tooltip!==void 0&&(w.tooltip=R(l.tooltip)),l.command!==void 0&&(w.command=rt(l.command)),w}function R(l){return typeof l=="string"?l:x(l)}async function V(l,w){if(Array.isArray(l))return he.mapAsync(l,Is,w)}function we(l){if(l===null)return;let w=new HE.default(ut(l.kind),l.name,l.detail||"",s(l.uri),m(l.range),m(l.selectionRange),l.data);return l.tags!==void 0&&(w.tags=wr(l.tags)),w}async function Te(l,w){if(l!==null)return he.map(l,we,w)}async function it(l,w){return new A.CallHierarchyIncomingCall(we(l.from),await S(l.fromRanges,w))}async function Gt(l,w){if(l!==null)return he.mapAsync(l,it,w)}async function Fn(l,w){return new A.CallHierarchyOutgoingCall(we(l.to),await S(l.fromRanges,w))}async function eT(l,w){if(l!==null)return he.mapAsync(l,Fn,w)}async function tT(l,w){if(l!=null)return new A.SemanticTokens(new Uint32Array(l.data),l.resultId)}function Im(l){return new A.SemanticTokensEdit(l.start,l.deleteCount,l.data!==void 0?new Uint32Array(l.data):void 0)}async function nT(l,w){if(l!=null)return new A.SemanticTokensEdits(l.edits.map(Im),l.resultId)}function rT(l){return l}async function iT(l,w){if(l!=null)return new A.LinkedEditingRanges(await S(l.ranges,w),sT(l.wordPattern))}function sT(l){if(l!=null)return new RegExp(l)}function Nm(l){if(l===null)return;let w=new UE.default(ut(l.kind),l.name,l.detail||"",s(l.uri),m(l.range),m(l.selectionRange),l.data);return l.tags!==void 0&&(w.tags=wr(l.tags)),w}async function oT(l,w){if(l!==null)return he.map(l,Nm,w)}function aT(l){if(dn.string(l))return l;if(X.RelativePattern.is(l)){if(X.URI.is(l.baseUri))return new A.RelativePattern(s(l.baseUri),l.pattern);if(X.WorkspaceFolder.is(l.baseUri)){let w=A.workspace.getWorkspaceFolder(s(l.baseUri.uri));return w!==void 0?new A.RelativePattern(w,l.pattern):void 0}}}async function cT(l,w){if(!l)return;if(Array.isArray(l))return he.map(l,st=>Tu(st),w);let q=l,W=await he.map(q.items,st=>Tu(st),w);return new A.InlineCompletionList(W)}function Tu(l){let w;typeof l.insertText=="string"?w=l.insertText:w=new A.SnippetString(l.insertText.value);let q;l.command&&(q=rt(l.command));let W=new A.InlineCompletionItem(w,m(l.range),q);return l.filterText&&(W.filterText=l.filterText),W}return{asUri:s,asDocumentSelector:o,asDiagnostics:a,asDiagnostic:u,asRange:m,asRanges:S,asPosition:g,asDiagnosticSeverity:D,asDiagnosticTag:p,asHover:I,asCompletionResult:M,asCompletionItem:St,asTextEdit:In,asTextEdits:It,asSignatureHelp:mr,asSignatureInformations:Gr,asSignatureInformation:Je,asParameterInformations:Cn,asParameterInformation:_n,asDeclarationResult:Es,asDefinitionResult:Xr,asLocation:Sn,asReferences:xs,asDocumentHighlights:Yr,asDocumentHighlight:vr,asDocumentHighlightKind:yr,asSymbolKind:ut,asSymbolTag:Ei,asSymbolTags:wr,asSymbolInformations:qs,asSymbolInformation:Bn,asDocumentSymbols:ks,asDocumentSymbol:Gn,asCommand:rt,asCommands:Bt,asCodeAction:Zr,asCodeActionKind:Nn,asCodeActionKinds:Xn,asCodeActionResult:xi,asCodeLens:br,asCodeLenses:qi,asWorkspaceEdit:sn,asDocumentLink:E,asDocumentLinks:F,asFoldingRangeKind:ie,asFoldingRange:Re,asFoldingRanges:pe,asColor:$,asColorInformation:ye,asColorInformations:ge,asColorPresentation:Ee,asColorPresentations:Oe,asSelectionRange:Ie,asSelectionRanges:tt,asInlineValue:Rt,asInlineValues:Os,asInlayHint:Is,asInlayHints:V,asSemanticTokensLegend:rT,asSemanticTokens:tT,asSemanticTokensEdit:Im,asSemanticTokensEdits:nT,asCallHierarchyItem:we,asCallHierarchyItems:Te,asCallHierarchyIncomingCall:it,asCallHierarchyIncomingCalls:Gt,asCallHierarchyOutgoingCall:Fn,asCallHierarchyOutgoingCalls:eT,asLinkedEditingRanges:iT,asTypeHierarchyItem:Nm,asTypeHierarchyItems:oT,asGlobPattern:aT,asInlineCompletionResult:cT,asInlineCompletionItem:Tu}}Ba.createConverter=KE});var je=b(fn=>{"use strict";Object.defineProperty(fn,"__esModule",{value:!0});fn.generateUuid=fn.parse=fn.isUUID=fn.v4=fn.empty=void 0;var to=class{constructor(e){this._value=e}asHex(){return this._value}equals(e){return this.asHex()===e.asHex()}},no=class t extends to{static _oneOf(e){return e[Math.floor(e.length*Math.random())]}static _randomHex(){return t._oneOf(t._chars)}constructor(){super([t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),"-",t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),"-","4",t._randomHex(),t._randomHex(),t._randomHex(),"-",t._oneOf(t._timeHighBits),t._randomHex(),t._randomHex(),t._randomHex(),"-",t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex()].join(""))}};no._chars=["0","1","2","3","4","5","6","6","7","8","9","a","b","c","d","e","f"];no._timeHighBits=["8","9","a","b"];fn.empty=new to("00000000-0000-0000-0000-000000000000");function aC(){return new no}fn.v4=aC;var zE=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function cC(t){return zE.test(t)}fn.isUUID=cC;function BE(t){if(!cC(t))throw new Error("invalid uuid");return new to(t)}fn.parse=BE;function GE(){return aC().asHex()}fn.generateUuid=GE});var lp=b(Ga=>{"use strict";Object.defineProperty(Ga,"__esModule",{value:!0});Ga.ProgressPart=void 0;var uC=require("vscode"),lC=J(),dC=ln(),up=class{constructor(e,n,r){this._client=e,this._token=n,this._reported=0,this._infinite=!1,this._lspProgressDisposable=this._client.onProgress(lC.WorkDoneProgress.type,this._token,i=>{switch(i.kind){case"begin":this.begin(i);break;case"report":this.report(i);break;case"end":this.done(),r&&r(this);break}})}begin(e){this._infinite=e.percentage===void 0,this._lspProgressDisposable!==void 0&&uC.window.withProgress({location:uC.ProgressLocation.Window,cancellable:e.cancellable,title:e.title},async(n,r)=>{if(this._lspProgressDisposable!==void 0)return this._progress=n,this._cancellationToken=r,this._tokenDisposable=this._cancellationToken.onCancellationRequested(()=>{this._client.sendNotification(lC.WorkDoneProgressCancelNotification.type,{token:this._token})}),this.report(e),new Promise((i,s)=>{this._resolve=i,this._reject=s})})}report(e){if(this._infinite&&dC.string(e.message))this._progress!==void 0&&this._progress.report({message:e.message});else if(dC.number(e.percentage)){let n=Math.max(0,Math.min(e.percentage,100)),r=Math.max(0,n-this._reported);this._reported+=r,this._progress!==void 0&&this._progress.report({message:e.message,increment:r})}}cancel(){this.cleanup(),this._reject!==void 0&&(this._reject(),this._resolve=void 0,this._reject=void 0)}done(){this.cleanup(),this._resolve!==void 0&&(this._resolve(),this._resolve=void 0,this._reject=void 0)}cleanup(){this._lspProgressDisposable!==void 0&&(this._lspProgressDisposable.dispose(),this._lspProgressDisposable=void 0),this._tokenDisposable!==void 0&&(this._tokenDisposable.dispose(),this._tokenDisposable=void 0),this._progress=void 0,this._cancellationToken=void 0}};Ga.ProgressPart=up});var de=b(at=>{"use strict";Object.defineProperty(at,"__esModule",{value:!0});at.WorkspaceFeature=at.TextDocumentLanguageFeature=at.TextDocumentEventFeature=at.DynamicDocumentFeature=at.DynamicFeature=at.StaticFeature=at.ensure=at.LSPCancellationError=void 0;var ui=require("vscode"),dp=J(),Tt=ln(),fC=je(),fp=class extends ui.CancellationError{constructor(e){super(),this.data=e}};at.LSPCancellationError=fp;function XE(t,e){return t[e]===void 0&&(t[e]={}),t[e]}at.ensure=XE;var pC;(function(t){function e(n){let r=n;return r!=null&&Tt.func(r.fillClientCapabilities)&&Tt.func(r.initialize)&&Tt.func(r.getState)&&Tt.func(r.clear)&&(r.fillInitializeParams===void 0||Tt.func(r.fillInitializeParams))}t.is=e})(pC||(at.StaticFeature=pC={}));var hC;(function(t){function e(n){let r=n;return r!=null&&Tt.func(r.fillClientCapabilities)&&Tt.func(r.initialize)&&Tt.func(r.getState)&&Tt.func(r.clear)&&(r.fillInitializeParams===void 0||Tt.func(r.fillInitializeParams))&&Tt.func(r.register)&&Tt.func(r.unregister)&&r.registrationType!==void 0}t.is=e})(hC||(at.DynamicFeature=hC={}));var ro=class{constructor(e){this._client=e}getState(){let e=this.getDocumentSelectors(),n=0;for(let i of e){n++;for(let s of ui.workspace.textDocuments)if(ui.languages.match(i,s)>0)return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!0}}let r=n>0;return{kind:"document",id:this.registrationType.method,registrations:r,matches:!1}}};at.DynamicDocumentFeature=ro;var pp=class extends ro{static textDocumentFilter(e,n){for(let r of e)if(ui.languages.match(r,n)>0)return!0;return!1}constructor(e,n,r,i,s,o,a){super(e),this._event=n,this._type=r,this._middleware=i,this._createParams=s,this._textDocument=o,this._selectorFilter=a,this._selectors=new Map,this._onNotificationSent=new ui.EventEmitter}getStateInfo(){return[this._selectors.values(),!1]}getDocumentSelectors(){return this._selectors.values()}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=this._event(n=>{this.callback(n).catch(r=>{this._client.error(`Sending document notification ${this._type.method} failed.`,r)})})),this._selectors.set(e.id,this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)))}async callback(e){let n=async r=>{let i=this._createParams(r);await this._client.sendNotification(this._type,i),this.notificationSent(this.getTextDocument(r),this._type,i)};if(this.matches(e)){let r=this._middleware();return r?r(e,i=>n(i)):n(e)}}matches(e){return this._client.hasDedicatedTextSynchronizationFeature(this._textDocument(e))?!1:!this._selectorFilter||this._selectorFilter(this._selectors.values(),e)}get onNotificationSent(){return this._onNotificationSent.event}notificationSent(e,n,r){this._onNotificationSent.fire({textDocument:e,type:n,params:r})}unregister(e){this._selectors.delete(e),this._selectors.size===0&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._selectors.clear(),this._onNotificationSent.dispose(),this._listener&&(this._listener.dispose(),this._listener=void 0)}getProvider(e){for(let n of this._selectors.values())if(ui.languages.match(n,e)>0)return{send:r=>this.callback(r)}}};at.TextDocumentEventFeature=pp;var hp=class extends ro{constructor(e,n){super(e),this._registrationType=n,this._registrations=new Map}*getDocumentSelectors(){for(let e of this._registrations.values()){let n=e.data.registerOptions.documentSelector;n!==null&&(yield this._client.protocol2CodeConverter.asDocumentSelector(n))}}get registrationType(){return this._registrationType}register(e){if(!e.registerOptions.documentSelector)return;let n=this.registerLanguageProvider(e.registerOptions,e.id);this._registrations.set(e.id,{disposable:n[0],data:e,provider:n[1]})}unregister(e){let n=this._registrations.get(e);n!==void 0&&n.disposable.dispose()}clear(){this._registrations.forEach(e=>{e.disposable.dispose()}),this._registrations.clear()}getRegistration(e,n){if(n){if(dp.TextDocumentRegistrationOptions.is(n)){let r=dp.StaticRegistrationOptions.hasId(n)?n.id:fC.generateUuid(),i=n.documentSelector??e;if(i)return[r,Object.assign({},n,{documentSelector:i})]}else if(Tt.boolean(n)&&n===!0||dp.WorkDoneProgressOptions.is(n)){if(!e)return[void 0,void 0];let r=Tt.boolean(n)&&n===!0?{documentSelector:e}:Object.assign({},n,{documentSelector:e});return[fC.generateUuid(),r]}}else return[void 0,void 0];return[void 0,void 0]}getRegistrationOptions(e,n){if(!(!e||!n))return Tt.boolean(n)&&n===!0?{documentSelector:e}:Object.assign({},n,{documentSelector:e})}getProvider(e){for(let n of this._registrations.values()){let r=n.data.registerOptions.documentSelector;if(r!==null&&ui.languages.match(this._client.protocol2CodeConverter.asDocumentSelector(r),e)>0)return n.provider}}getAllProviders(){let e=[];for(let n of this._registrations.values())e.push(n.provider);return e}};at.TextDocumentLanguageFeature=hp;var gp=class{constructor(e,n){this._client=e,this._registrationType=n,this._registrations=new Map}getState(){let e=this._registrations.size>0;return{kind:"workspace",id:this._registrationType.method,registrations:e}}get registrationType(){return this._registrationType}register(e){let n=this.registerLanguageProvider(e.registerOptions);this._registrations.set(e.id,{disposable:n[0],provider:n[1]})}unregister(e){let n=this._registrations.get(e);n!==void 0&&n.disposable.dispose()}clear(){this._registrations.forEach(e=>{e.disposable.dispose()}),this._registrations.clear()}getProviders(){let e=[];for(let n of this._registrations.values())e.push(n.provider);return e}};at.WorkspaceFeature=gp});var mC=b((uM,gC)=>{"use strict";var JE=typeof process=="object"&&process&&process.platform==="win32";gC.exports=JE?{sep:"\\"}:{sep:"/"}});var CC=b((lM,bC)=>{"use strict";bC.exports=yC;function yC(t,e,n){t instanceof RegExp&&(t=vC(t,n)),e instanceof RegExp&&(e=vC(e,n));var r=wC(t,e,n);return r&&{start:r[0],end:r[1],pre:n.slice(0,r[0]),body:n.slice(r[0]+t.length,r[1]),post:n.slice(r[1]+e.length)}}function vC(t,e){var n=e.match(t);return n?n[0]:null}yC.range=wC;function wC(t,e,n){var r,i,s,o,a,c=n.indexOf(t),u=n.indexOf(e,c+1),f=c;if(c>=0&&u>0){if(t===e)return[c,u];for(r=[],s=n.length;f>=0&&!a;)f==c?(r.push(f),c=n.indexOf(t,f+1)):r.length==1?a=[r.pop(),u]:(i=r.pop(),i<s&&(s=i,o=u),u=n.indexOf(e,f+1)),f=c<u&&c>=0?c:u;r.length&&(a=[s,o])}return a}});var xC=b((dM,EC)=>{"use strict";var _C=CC();EC.exports=ZE;var SC="\0SLASH"+Math.random()+"\0",DC="\0OPEN"+Math.random()+"\0",vp="\0CLOSE"+Math.random()+"\0",RC="\0COMMA"+Math.random()+"\0",TC="\0PERIOD"+Math.random()+"\0";function mp(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function YE(t){return t.split("\\\\").join(SC).split("\\{").join(DC).split("\\}").join(vp).split("\\,").join(RC).split("\\.").join(TC)}function QE(t){return t.split(SC).join("\\").split(DC).join("{").split(vp).join("}").split(RC).join(",").split(TC).join(".")}function PC(t){if(!t)return[""];var e=[],n=_C("{","}",t);if(!n)return t.split(",");var r=n.pre,i=n.body,s=n.post,o=r.split(",");o[o.length-1]+="{"+i+"}";var a=PC(s);return s.length&&(o[o.length-1]+=a.shift(),o.push.apply(o,a)),e.push.apply(e,o),e}function ZE(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),io(YE(t),!0).map(QE)):[]}function ex(t){return"{"+t+"}"}function tx(t){return/^-?0\d/.test(t)}function nx(t,e){return t<=e}function rx(t,e){return t>=e}function io(t,e){var n=[],r=_C("{","}",t);if(!r)return[t];var i=r.pre,s=r.post.length?io(r.post,!1):[""];if(/\$$/.test(r.pre))for(var o=0;o<s.length;o++){var a=i+"{"+r.body+"}"+s[o];n.push(a)}else{var c=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),u=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),f=c||u,d=r.body.indexOf(",")>=0;if(!f&&!d)return r.post.match(/,.*\}/)?(t=r.pre+"{"+r.body+vp+r.post,io(t)):[t];var p;if(f)p=r.body.split(/\.\./);else if(p=PC(r.body),p.length===1&&(p=io(p[0],!1).map(ex),p.length===1))return s.map(function(St){return r.pre+p[0]+St});var g;if(f){var m=mp(p[0]),S=mp(p[1]),D=Math.max(p[0].length,p[1].length),P=p.length==3?Math.abs(mp(p[2])):1,k=nx,x=S<m;x&&(P*=-1,k=rx);var I=p.some(tx);g=[];for(var M=m;k(M,S);M+=P){var Y;if(u)Y=String.fromCharCode(M),Y==="\\"&&(Y="");else if(Y=String(M),I){var B=D-Y.length;if(B>0){var Z=new Array(B+1).join("0");M<0?Y="-"+Z+Y.slice(1):Y=Z+Y}}g.push(Y)}}else{g=[];for(var fe=0;fe<p.length;fe++)g.push.apply(g,io(p[fe],!1))}for(var fe=0;fe<g.length;fe++)for(var o=0;o<s.length;o++){var a=i+g[fe]+s[o];(!e||f||a)&&n.push(a)}}return n}});var Ja=b((pM,_p)=>{"use strict";var Yt=_p.exports=(t,e,n={})=>(Xa(e),!n.nocomment&&e.charAt(0)==="#"?!1:new is(e,n).match(t));_p.exports=Yt;var bp=mC();Yt.sep=bp.sep;var En=Symbol("globstar **");Yt.GLOBSTAR=En;var ix=xC(),qC={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},Cp="[^/]",yp=Cp+"*?",sx="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",ox="(?:(?!(?:\\/|^)\\.).)*?",IC=t=>t.split("").reduce((e,n)=>(e[n]=!0,e),{}),kC=IC("().*{}+?[]^$\\!"),ax=IC("[.("),OC=/\/+/;Yt.filter=(t,e={})=>(n,r,i)=>Yt(n,t,e);var kr=(t,e={})=>{let n={};return Object.keys(t).forEach(r=>n[r]=t[r]),Object.keys(e).forEach(r=>n[r]=e[r]),n};Yt.defaults=t=>{if(!t||typeof t!="object"||!Object.keys(t).length)return Yt;let e=Yt,n=(r,i,s)=>e(r,i,kr(t,s));return n.Minimatch=class extends e.Minimatch{constructor(i,s){super(i,kr(t,s))}},n.Minimatch.defaults=r=>e.defaults(kr(t,r)).Minimatch,n.filter=(r,i)=>e.filter(r,kr(t,i)),n.defaults=r=>e.defaults(kr(t,r)),n.makeRe=(r,i)=>e.makeRe(r,kr(t,i)),n.braceExpand=(r,i)=>e.braceExpand(r,kr(t,i)),n.match=(r,i,s)=>e.match(r,i,kr(t,s)),n};Yt.braceExpand=(t,e)=>NC(t,e);var NC=(t,e={})=>(Xa(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:ix(t)),cx=1024*64,Xa=t=>{if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>cx)throw new TypeError("pattern is too long")},wp=Symbol("subparse");Yt.makeRe=(t,e)=>new is(t,e||{}).makeRe();Yt.match=(t,e,n={})=>{let r=new is(e,n);return t=t.filter(i=>r.match(i)),r.options.nonull&&!t.length&&t.push(e),t};var ux=t=>t.replace(/\\(.)/g,"$1"),lx=t=>t.replace(/\\([^-\]])/g,"$1"),dx=t=>t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),fx=t=>t.replace(/[[\]\\]/g,"\\$&"),is=class{constructor(e,n){Xa(e),n||(n={}),this.options=n,this.set=[],this.pattern=e,this.windowsPathsNoEscape=!!n.windowsPathsNoEscape||n.allowWindowsEscape===!1,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!n.partial,this.make()}debug(){}make(){let e=this.pattern,n=this.options;if(!n.nocomment&&e.charAt(0)==="#"){this.comment=!0;return}if(!e){this.empty=!0;return}this.parseNegate();let r=this.globSet=this.braceExpand();n.debug&&(this.debug=(...i)=>console.error(...i)),this.debug(this.pattern,r),r=this.globParts=r.map(i=>i.split(OC)),this.debug(this.pattern,r),r=r.map((i,s,o)=>i.map(this.parse,this)),this.debug(this.pattern,r),r=r.filter(i=>i.indexOf(!1)===-1),this.debug(this.pattern,r),this.set=r}parseNegate(){if(this.options.nonegate)return;let e=this.pattern,n=!1,r=0;for(let i=0;i<e.length&&e.charAt(i)==="!";i++)n=!n,r++;r&&(this.pattern=e.slice(r)),this.negate=n}matchOne(e,n,r){var i=this.options;this.debug("matchOne",{this:this,file:e,pattern:n}),this.debug("matchOne",e.length,n.length);for(var s=0,o=0,a=e.length,c=n.length;s<a&&o<c;s++,o++){this.debug("matchOne loop");var u=n[o],f=e[s];if(this.debug(n,u,f),u===!1)return!1;if(u===En){this.debug("GLOBSTAR",[n,u,f]);var d=s,p=o+1;if(p===c){for(this.debug("** at the end");s<a;s++)if(e[s]==="."||e[s]===".."||!i.dot&&e[s].charAt(0)===".")return!1;return!0}for(;d<a;){var g=e[d];if(this.debug(`
globstar while`,e,d,n,p,g),this.matchOne(e.slice(d),n.slice(p),r))return this.debug("globstar found match!",d,a,g),!0;if(g==="."||g===".."||!i.dot&&g.charAt(0)==="."){this.debug("dot detected!",e,d,n,p);break}this.debug("globstar swallow a segment, and continue"),d++}return!!(r&&(this.debug(`
>>> no match, partial?`,e,d,n,p),d===a))}var m;if(typeof u=="string"?(m=f===u,this.debug("string match",u,f,m)):(m=f.match(u),this.debug("pattern match",u,f,m)),!m)return!1}if(s===a&&o===c)return!0;if(s===a)return r;if(o===c)return s===a-1&&e[s]==="";throw new Error("wtf?")}braceExpand(){return NC(this.pattern,this.options)}parse(e,n){Xa(e);let r=this.options;if(e==="**")if(r.noglobstar)e="*";else return En;if(e==="")return"";let i="",s=!1,o=!1,a=[],c=[],u,f=!1,d=-1,p=-1,g,m,S,D=e.charAt(0)===".",P=r.dot||D,k=()=>D?"":P?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",x=B=>B.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",I=()=>{if(u){switch(u){case"*":i+=yp,s=!0;break;case"?":i+=Cp,s=!0;break;default:i+="\\"+u;break}this.debug("clearStateChar %j %j",u,i),u=!1}};for(let B=0,Z;B<e.length&&(Z=e.charAt(B));B++){if(this.debug("%s	%s %s %j",e,B,i,Z),o){if(Z==="/")return!1;kC[Z]&&(i+="\\"),i+=Z,o=!1;continue}switch(Z){case"/":return!1;case"\\":if(f&&e.charAt(B+1)==="-"){i+=Z;continue}I(),o=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",e,B,i,Z),f){this.debug("  in class"),Z==="!"&&B===p+1&&(Z="^"),i+=Z;continue}this.debug("call clearStateChar %j",u),I(),u=Z,r.noext&&I();continue;case"(":{if(f){i+="(";continue}if(!u){i+="\\(";continue}let fe={type:u,start:B-1,reStart:i.length,open:qC[u].open,close:qC[u].close};this.debug(this.pattern,"	",fe),a.push(fe),i+=fe.open,fe.start===0&&fe.type!=="!"&&(D=!0,i+=x(e.slice(B+1))),this.debug("plType %j %j",u,i),u=!1;continue}case")":{let fe=a[a.length-1];if(f||!fe){i+="\\)";continue}a.pop(),I(),s=!0,m=fe,i+=m.close,m.type==="!"&&c.push(Object.assign(m,{reEnd:i.length}));continue}case"|":{let fe=a[a.length-1];if(f||!fe){i+="\\|";continue}I(),i+="|",fe.start===0&&fe.type!=="!"&&(D=!0,i+=x(e.slice(B+1)));continue}case"[":if(I(),f){i+="\\"+Z;continue}f=!0,p=B,d=i.length,i+=Z;continue;case"]":if(B===p+1||!f){i+="\\"+Z;continue}g=e.substring(p+1,B);try{RegExp("["+fx(lx(g))+"]"),i+=Z}catch{i=i.substring(0,d)+"(?:$.)"}s=!0,f=!1;continue;default:I(),kC[Z]&&!(Z==="^"&&f)&&(i+="\\"),i+=Z;break}}for(f&&(g=e.slice(p+1),S=this.parse(g,wp),i=i.substring(0,d)+"\\["+S[0],s=s||S[1]),m=a.pop();m;m=a.pop()){let B;B=i.slice(m.reStart+m.open.length),this.debug("setting tail",i,m),B=B.replace(/((?:\\{2}){0,64})(\\?)\|/g,(fe,St,ht)=>(ht||(ht="\\"),St+St+ht+"|")),this.debug(`tail=%j
   %s`,B,B,m,i);let Z=m.type==="*"?yp:m.type==="?"?Cp:"\\"+m.type;s=!0,i=i.slice(0,m.reStart)+Z+"\\("+B}I(),o&&(i+="\\\\");let M=ax[i.charAt(0)];for(let B=c.length-1;B>-1;B--){let Z=c[B],fe=i.slice(0,Z.reStart),St=i.slice(Z.reStart,Z.reEnd-8),ht=i.slice(Z.reEnd),Ot=i.slice(Z.reEnd-8,Z.reEnd)+ht,gr=fe.split(")").length,In=fe.split("(").length-gr,It=ht;for(let mr=0;mr<In;mr++)It=It.replace(/\)[+*?]?/,"");ht=It;let Dt=ht===""&&n!==wp?"(?:$|\\/)":"";i=fe+St+ht+Dt+Ot}if(i!==""&&s&&(i="(?=.)"+i),M&&(i=k()+i),n===wp)return[i,s];if(r.nocase&&!s&&(s=e.toUpperCase()!==e.toLowerCase()),!s)return ux(e);let Y=r.nocase?"i":"";try{return Object.assign(new RegExp("^"+i+"$",Y),{_glob:e,_src:i})}catch{return new RegExp("$.")}}makeRe(){if(this.regexp||this.regexp===!1)return this.regexp;let e=this.set;if(!e.length)return this.regexp=!1,this.regexp;let n=this.options,r=n.noglobstar?yp:n.dot?sx:ox,i=n.nocase?"i":"",s=e.map(o=>(o=o.map(a=>typeof a=="string"?dx(a):a===En?En:a._src).reduce((a,c)=>(a[a.length-1]===En&&c===En||a.push(c),a),[]),o.forEach((a,c)=>{a!==En||o[c-1]===En||(c===0?o.length>1?o[c+1]="(?:\\/|"+r+"\\/)?"+o[c+1]:o[c]=r:c===o.length-1?o[c-1]+="(?:\\/|"+r+")?":(o[c-1]+="(?:\\/|\\/"+r+"\\/)"+o[c+1],o[c+1]=En))}),o.filter(a=>a!==En).join("/"))).join("|");s="^(?:"+s+")$",this.negate&&(s="^(?!"+s+").*$");try{this.regexp=new RegExp(s,i)}catch{this.regexp=!1}return this.regexp}match(e,n=this.partial){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&n)return!0;let r=this.options;bp.sep!=="/"&&(e=e.split(bp.sep).join("/")),e=e.split(OC),this.debug(this.pattern,"split",e);let i=this.set;this.debug(this.pattern,"set",i);let s;for(let o=e.length-1;o>=0&&(s=e[o],!s);o--);for(let o=0;o<i.length;o++){let a=i[o],c=e;if(r.matchBase&&a.length===1&&(c=[s]),this.matchOne(c,a,n))return r.flipNegate?!0:!this.negate}return r.flipNegate?!1:this.negate}static defaults(e){return Yt.defaults(e).Minimatch}};Yt.Minimatch=is});var qp=b(Or=>{"use strict";Object.defineProperty(Or,"__esModule",{value:!0});Or.DiagnosticFeature=Or.DiagnosticPullMode=Or.vsdiag=void 0;var px=Ja(),ae=require("vscode"),Ge=J(),hx=je(),Sp=de();function Ya(t,e){return t[e]===void 0&&(t[e]={}),t[e]}var pn;(function(t){let e;(function(n){n.full="full",n.unChanged="unChanged"})(e=t.DocumentDiagnosticReportKind||(t.DocumentDiagnosticReportKind={}))})(pn||(Or.vsdiag=pn={}));var Qa;(function(t){t.onType="onType",t.onSave="onSave"})(Qa||(Or.DiagnosticPullMode=Qa={}));var wt;(function(t){t.active="open",t.reschedule="reschedule",t.outDated="drop"})(wt||(wt={}));var Dp=class t{constructor(){this.open=new Set,this._onOpen=new ae.EventEmitter,this._onClose=new ae.EventEmitter,t.fillTabResources(this.open);let e=n=>{if(n.closed.length===0&&n.opened.length===0)return;let r=this.open,i=new Set;t.fillTabResources(i);let s=new Set,o=new Set(i);for(let a of r.values())i.has(a)?o.delete(a):s.add(a);if(this.open=i,s.size>0){let a=new Set;for(let c of s)a.add(ae.Uri.parse(c));this._onClose.fire(a)}if(o.size>0){let a=new Set;for(let c of o)a.add(ae.Uri.parse(c));this._onOpen.fire(a)}};ae.window.tabGroups.onDidChangeTabs!==void 0?this.disposable=ae.window.tabGroups.onDidChangeTabs(e):this.disposable={dispose:()=>{}}}get onClose(){return this._onClose.event}get onOpen(){return this._onOpen.event}dispose(){this.disposable.dispose()}isActive(e){return e instanceof ae.Uri?ae.window.activeTextEditor?.document.uri===e:ae.window.activeTextEditor?.document===e}isVisible(e){let n=e instanceof ae.Uri?e:e.uri;return this.open.has(n.toString())}getTabResources(){let e=new Set;return t.fillTabResources(new Set,e),e}static fillTabResources(e,n){let r=e??new Set;for(let i of ae.window.tabGroups.all)for(let s of i.tabs){let o=s.input,a;o instanceof ae.TabInputText?a=o.uri:o instanceof ae.TabInputTextDiff?a=o.modified:o instanceof ae.TabInputCustom&&(a=o.uri),a!==void 0&&!r.has(a.toString())&&(r.add(a.toString()),n!==void 0&&n.add(a))}}},nt;(function(t){t[t.document=1]="document",t[t.workspace=2]="workspace"})(nt||(nt={}));var er;(function(t){function e(n){return n instanceof ae.Uri?n.toString():n.uri.toString()}t.asKey=e})(er||(er={}));var Rp=class{constructor(){this.documentPullStates=new Map,this.workspacePullStates=new Map}track(e,n,r){let i=e===nt.document?this.documentPullStates:this.workspacePullStates,[s,o,a]=n instanceof ae.Uri?[n.toString(),n,r]:[n.uri.toString(),n.uri,n.version],c=i.get(s);return c===void 0&&(c={document:o,pulledVersion:a,resultId:void 0},i.set(s,c)),c}update(e,n,r,i){let s=e===nt.document?this.documentPullStates:this.workspacePullStates,[o,a,c,u]=n instanceof ae.Uri?[n.toString(),n,r,i]:[n.uri.toString(),n.uri,n.version,r],f=s.get(o);f===void 0?(f={document:a,pulledVersion:c,resultId:u},s.set(o,f)):(f.pulledVersion=c,f.resultId=u)}unTrack(e,n){let r=er.asKey(n);(e===nt.document?this.documentPullStates:this.workspacePullStates).delete(r)}tracks(e,n){let r=er.asKey(n);return(e===nt.document?this.documentPullStates:this.workspacePullStates).has(r)}getResultId(e,n){let r=er.asKey(n);return(e===nt.document?this.documentPullStates:this.workspacePullStates).get(r)?.resultId}getAllResultIds(){let e=[];for(let[n,r]of this.workspacePullStates)this.documentPullStates.has(n)&&(r=this.documentPullStates.get(n)),r.resultId!==void 0&&e.push({uri:n,value:r.resultId});return e}},Tp=class{constructor(e,n,r){this.client=e,this.tabs=n,this.options=r,this.isDisposed=!1,this.onDidChangeDiagnosticsEmitter=new ae.EventEmitter,this.provider=this.createProvider(),this.diagnostics=ae.languages.createDiagnosticCollection(r.identifier),this.openRequests=new Map,this.documentStates=new Rp,this.workspaceErrorCounter=0}knows(e,n){let r=n instanceof ae.Uri?n:n.uri;return this.documentStates.tracks(e,n)||this.openRequests.has(r.toString())}forget(e,n){this.documentStates.unTrack(e,n)}pull(e,n){if(this.isDisposed)return;let r=e instanceof ae.Uri?e:e.uri;this.pullAsync(e).then(()=>{n&&n()},i=>{this.client.error(`Document pull failed for text document ${r.toString()}`,i,!1)})}async pullAsync(e,n){if(this.isDisposed)return;let r=e instanceof ae.Uri,i=r?e:e.uri,s=i.toString();n=r?n:e.version;let o=this.openRequests.get(s),a=r?this.documentStates.track(nt.document,e,n):this.documentStates.track(nt.document,e);if(o===void 0){let c=new ae.CancellationTokenSource;this.openRequests.set(s,{state:wt.active,document:e,version:n,tokenSource:c});let u,f;try{u=await this.provider.provideDiagnostics(e,a.resultId,c.token)??{kind:pn.DocumentDiagnosticReportKind.full,items:[]}}catch(d){if(d instanceof Sp.LSPCancellationError&&Ge.DiagnosticServerCancellationData.is(d.data)&&d.data.retriggerRequest===!1&&(f={state:wt.outDated,document:e}),f===void 0&&d instanceof ae.CancellationError)f={state:wt.reschedule,document:e};else throw d}if(f=f??this.openRequests.get(s),f===void 0){this.client.error(`Lost request state in diagnostic pull model. Clearing diagnostics for ${s}`),this.diagnostics.delete(i);return}if(this.openRequests.delete(s),!this.tabs.isVisible(e)){this.documentStates.unTrack(nt.document,e);return}if(f.state===wt.outDated)return;u!==void 0&&(u.kind===pn.DocumentDiagnosticReportKind.full&&this.diagnostics.set(i,u.items),a.pulledVersion=n,a.resultId=u.resultId),f.state===wt.reschedule&&this.pull(e)}else o.state===wt.active?(o.tokenSource.cancel(),this.openRequests.set(s,{state:wt.reschedule,document:o.document})):o.state===wt.outDated&&this.openRequests.set(s,{state:wt.reschedule,document:o.document})}forgetDocument(e){let n=e instanceof ae.Uri?e:e.uri,r=n.toString(),i=this.openRequests.get(r);this.options.workspaceDiagnostics?i!==void 0?this.openRequests.set(r,{state:wt.reschedule,document:e}):this.pull(e,()=>{this.forget(nt.document,e)}):(i!==void 0&&(i.state===wt.active&&i.tokenSource.cancel(),this.openRequests.set(r,{state:wt.outDated,document:e})),this.diagnostics.delete(n),this.forget(nt.document,e))}pullWorkspace(){this.isDisposed||this.pullWorkspaceAsync().then(()=>{this.workspaceTimeout=(0,Ge.RAL)().timer.setTimeout(()=>{this.pullWorkspace()},2e3)},e=>{!(e instanceof Sp.LSPCancellationError)&&!Ge.DiagnosticServerCancellationData.is(e.data)&&(this.client.error("Workspace diagnostic pull failed.",e,!1),this.workspaceErrorCounter++),this.workspaceErrorCounter<=5&&(this.workspaceTimeout=(0,Ge.RAL)().timer.setTimeout(()=>{this.pullWorkspace()},2e3))})}async pullWorkspaceAsync(){if(!this.provider.provideWorkspaceDiagnostics||this.isDisposed)return;this.workspaceCancellation!==void 0&&(this.workspaceCancellation.cancel(),this.workspaceCancellation=void 0),this.workspaceCancellation=new ae.CancellationTokenSource;let e=this.documentStates.getAllResultIds().map(n=>({uri:this.client.protocol2CodeConverter.asUri(n.uri),value:n.value}));await this.provider.provideWorkspaceDiagnostics(e,this.workspaceCancellation.token,n=>{if(!(!n||this.isDisposed))for(let r of n.items)r.kind===pn.DocumentDiagnosticReportKind.full&&(this.documentStates.tracks(nt.document,r.uri)||this.diagnostics.set(r.uri,r.items)),this.documentStates.update(nt.workspace,r.uri,r.version??void 0,r.resultId)})}createProvider(){let e={onDidChangeDiagnostics:this.onDidChangeDiagnosticsEmitter.event,provideDiagnostics:(n,r,i)=>{let s=(a,c,u)=>{let f={identifier:this.options.identifier,textDocument:{uri:this.client.code2ProtocolConverter.asUri(a instanceof ae.Uri?a:a.uri)},previousResultId:c};return this.isDisposed===!0||!this.client.isRunning()?{kind:pn.DocumentDiagnosticReportKind.full,items:[]}:this.client.sendRequest(Ge.DocumentDiagnosticRequest.type,f,u).then(async d=>d==null||this.isDisposed||u.isCancellationRequested?{kind:pn.DocumentDiagnosticReportKind.full,items:[]}:d.kind===Ge.DocumentDiagnosticReportKind.Full?{kind:pn.DocumentDiagnosticReportKind.full,resultId:d.resultId,items:await this.client.protocol2CodeConverter.asDiagnostics(d.items,u)}:{kind:pn.DocumentDiagnosticReportKind.unChanged,resultId:d.resultId},d=>this.client.handleFailedRequest(Ge.DocumentDiagnosticRequest.type,u,d,{kind:pn.DocumentDiagnosticReportKind.full,items:[]}))},o=this.client.middleware;return o.provideDiagnostics?o.provideDiagnostics(n,r,i,s):s(n,r,i)}};return this.options.workspaceDiagnostics&&(e.provideWorkspaceDiagnostics=(n,r,i)=>{let s=async u=>u.kind===Ge.DocumentDiagnosticReportKind.Full?{kind:pn.DocumentDiagnosticReportKind.full,uri:this.client.protocol2CodeConverter.asUri(u.uri),resultId:u.resultId,version:u.version,items:await this.client.protocol2CodeConverter.asDiagnostics(u.items,r)}:{kind:pn.DocumentDiagnosticReportKind.unChanged,uri:this.client.protocol2CodeConverter.asUri(u.uri),resultId:u.resultId,version:u.version},o=u=>{let f=[];for(let d of u)f.push({uri:this.client.code2ProtocolConverter.asUri(d.uri),value:d.value});return f},a=(u,f)=>{let d=(0,hx.generateUuid)(),p=this.client.onProgress(Ge.WorkspaceDiagnosticRequest.partialResult,d,async m=>{if(m==null){i(null);return}let S={items:[]};for(let D of m.items)try{S.items.push(await s(D))}catch(P){this.client.error("Converting workspace diagnostics failed.",P)}i(S)}),g={identifier:this.options.identifier,previousResultIds:o(u),partialResultToken:d};return this.isDisposed===!0||!this.client.isRunning()?{items:[]}:this.client.sendRequest(Ge.WorkspaceDiagnosticRequest.type,g,f).then(async m=>{if(f.isCancellationRequested)return{items:[]};let S={items:[]};for(let D of m.items)S.items.push(await s(D));return p.dispose(),i(S),{items:[]}},m=>(p.dispose(),this.client.handleFailedRequest(Ge.DocumentDiagnosticRequest.type,f,m,{items:[]})))},c=this.client.middleware;return c.provideWorkspaceDiagnostics?c.provideWorkspaceDiagnostics(n,r,i,a):a(n,r,i)}),e}dispose(){this.isDisposed=!0,this.workspaceCancellation?.cancel(),this.workspaceTimeout?.dispose();for(let[e,n]of this.openRequests)n.state===wt.active&&n.tokenSource.cancel(),this.openRequests.set(e,{state:wt.outDated,document:n.document});this.diagnostics.dispose()}},Pp=class{constructor(e){this.diagnosticRequestor=e,this.documents=new Ge.LinkedMap,this.isDisposed=!1}add(e){if(this.isDisposed===!0)return;let n=er.asKey(e);this.documents.has(n)||(this.documents.set(n,e,Ge.Touch.Last),this.trigger())}remove(e){let n=er.asKey(e);this.documents.delete(n),this.documents.size===0?this.stop():n===this.endDocumentKey()&&(this.endDocument=this.documents.last)}trigger(){if(this.isDisposed!==!0){if(this.intervalHandle!==void 0){this.endDocument=this.documents.last;return}this.endDocument=this.documents.last,this.intervalHandle=(0,Ge.RAL)().timer.setInterval(()=>{let e=this.documents.first;if(e!==void 0){let n=er.asKey(e);this.diagnosticRequestor.pull(e),this.documents.set(n,e,Ge.Touch.Last),n===this.endDocumentKey()&&this.stop()}},200)}}dispose(){this.isDisposed=!0,this.stop(),this.documents.clear()}stop(){this.intervalHandle?.dispose(),this.intervalHandle=void 0,this.endDocument=void 0}endDocumentKey(){return this.endDocument!==void 0?er.asKey(this.endDocument):void 0}},Ep=class{constructor(e,n,r){let i=e.clientOptions.diagnosticPullOptions??{onChange:!0,onSave:!1},s=e.protocol2CodeConverter.asDocumentSelector(r.documentSelector),o=[],a=m=>{let S=r.documentSelector;if(i.match!==void 0)return i.match(S,m);for(let D of S)if(Ge.TextDocumentFilter.is(D)){if(typeof D=="string"||D.language!==void 0&&D.language!=="*"||D.scheme!==void 0&&D.scheme!=="*"&&D.scheme!==m.scheme)return!1;if(D.pattern!==void 0){let P=new px.Minimatch(D.pattern,{noext:!0});if(!P.makeRe()||!P.match(m.fsPath))return!1}}return!0},c=m=>m instanceof ae.Uri?a(m):ae.languages.match(s,m)>0&&n.isVisible(m),u=m=>m instanceof ae.Uri?this.activeTextDocument?.uri.toString()===m.toString():this.activeTextDocument===m;this.diagnosticRequestor=new Tp(e,n,r),this.backgroundScheduler=new Pp(this.diagnosticRequestor);let f=m=>{!c(m)||!r.interFileDependencies||u(m)||this.backgroundScheduler.add(m)};this.activeTextDocument=ae.window.activeTextEditor?.document,ae.window.onDidChangeActiveTextEditor(m=>{let S=this.activeTextDocument;this.activeTextDocument=m?.document,S!==void 0&&f(S),this.activeTextDocument!==void 0&&this.backgroundScheduler.remove(this.activeTextDocument)});let d=e.getFeature(Ge.DidOpenTextDocumentNotification.method);o.push(d.onNotificationSent(m=>{let S=m.textDocument;this.diagnosticRequestor.knows(nt.document,S)||c(S)&&this.diagnosticRequestor.pull(S,()=>{f(S)})})),o.push(n.onOpen(m=>{for(let S of m){if(this.diagnosticRequestor.knows(nt.document,S))continue;let D=S.toString(),P;for(let k of ae.workspace.textDocuments)if(D===k.uri.toString()){P=k;break}P!==void 0&&c(P)&&this.diagnosticRequestor.pull(P,()=>{f(P)})}}));let p=new Set;for(let m of ae.workspace.textDocuments)c(m)&&(this.diagnosticRequestor.pull(m,()=>{f(m)}),p.add(m.uri.toString()));if(i.onTabs===!0)for(let m of n.getTabResources())!p.has(m.toString())&&c(m)&&this.diagnosticRequestor.pull(m,()=>{f(m)});if(i.onChange===!0){let m=e.getFeature(Ge.DidChangeTextDocumentNotification.method);o.push(m.onNotificationSent(async S=>{let D=S.textDocument;(i.filter===void 0||!i.filter(D,Qa.onType))&&this.diagnosticRequestor.knows(nt.document,D)&&this.diagnosticRequestor.pull(D,()=>{this.backgroundScheduler.trigger()})}))}if(i.onSave===!0){let m=e.getFeature(Ge.DidSaveTextDocumentNotification.method);o.push(m.onNotificationSent(S=>{let D=S.textDocument;(i.filter===void 0||!i.filter(D,Qa.onSave))&&this.diagnosticRequestor.knows(nt.document,D)&&this.diagnosticRequestor.pull(S.textDocument,()=>{this.backgroundScheduler.trigger()})}))}let g=e.getFeature(Ge.DidCloseTextDocumentNotification.method);o.push(g.onNotificationSent(m=>{this.cleanUpDocument(m.textDocument)})),n.onClose(m=>{for(let S of m)this.cleanUpDocument(S)}),this.diagnosticRequestor.onDidChangeDiagnosticsEmitter.event(()=>{for(let m of ae.workspace.textDocuments)c(m)&&this.diagnosticRequestor.pull(m)}),r.workspaceDiagnostics===!0&&r.identifier!=="da348dc5-c30a-4515-9d98-31ff3be38d14"&&this.diagnosticRequestor.pullWorkspace(),this.disposable=ae.Disposable.from(...o,this.backgroundScheduler,this.diagnosticRequestor)}get onDidChangeDiagnosticsEmitter(){return this.diagnosticRequestor.onDidChangeDiagnosticsEmitter}get diagnostics(){return this.diagnosticRequestor.provider}cleanUpDocument(e){this.diagnosticRequestor.knows(nt.document,e)&&(this.diagnosticRequestor.forgetDocument(e),this.backgroundScheduler.remove(e))}},xp=class extends Sp.TextDocumentLanguageFeature{constructor(e){super(e,Ge.DocumentDiagnosticRequest.type)}fillClientCapabilities(e){let n=Ya(Ya(e,"textDocument"),"diagnostic");n.dynamicRegistration=!0,n.relatedDocumentSupport=!1,Ya(Ya(e,"workspace"),"diagnostics").refreshSupport=!0}initialize(e,n){this._client.onRequest(Ge.DiagnosticRefreshRequest.type,async()=>{for(let o of this.getAllProviders())o.onDidChangeDiagnosticsEmitter.fire()});let[i,s]=this.getRegistration(n,e.diagnosticProvider);!i||!s||this.register({id:i,registerOptions:s})}clear(){this.tabs!==void 0&&(this.tabs.dispose(),this.tabs=void 0),super.clear()}registerLanguageProvider(e){this.tabs===void 0&&(this.tabs=new Dp);let n=new Ep(this._client,this.tabs,e);return[n.disposable,n]}};Or.DiagnosticFeature=xp});var AC=b(nc=>{"use strict";Object.defineProperty(nc,"__esModule",{value:!0});nc.NotebookDocumentSyncFeature=void 0;var bt=require("vscode"),gx=Ja(),tr=J(),mx=je(),FC=ln();function MC(t,e){return t[e]===void 0&&(t[e]={}),t[e]}var ss;(function(t){let e;(function(n){function r(p,g){return{version:p.version,uri:g.asUri(p.uri)}}n.asVersionedNotebookDocumentIdentifier=r;function i(p,g,m){let S=tr.NotebookDocument.create(m.asUri(p.uri),p.notebookType,p.version,s(g,m));return Object.keys(p.metadata).length>0&&(S.metadata=o(p.metadata)),S}n.asNotebookDocument=i;function s(p,g){return p.map(m=>a(m,g))}n.asNotebookCells=s;function o(p){return u(new Set,p)}n.asMetadata=o;function a(p,g){let m=tr.NotebookCell.create(c(p.kind),g.asUri(p.document.uri));return Object.keys(p.metadata).length>0&&(m.metadata=o(p.metadata)),p.executionSummary!==void 0&&FC.number(p.executionSummary.executionOrder)&&FC.boolean(p.executionSummary.success)&&(m.executionSummary={executionOrder:p.executionSummary.executionOrder,success:p.executionSummary.success}),m}n.asNotebookCell=a;function c(p){switch(p){case bt.NotebookCellKind.Markup:return tr.NotebookCellKind.Markup;case bt.NotebookCellKind.Code:return tr.NotebookCellKind.Code}}function u(p,g){if(p.has(g))throw new Error("Can't deep copy cyclic structures.");if(Array.isArray(g)){let m=[];for(let S of g)if(S!==null&&typeof S=="object"||Array.isArray(S))m.push(u(p,S));else{if(S instanceof RegExp)throw new Error("Can't transfer regular expressions to the server");m.push(S)}return m}else{let m=Object.keys(g),S=Object.create(null);for(let D of m){let P=g[D];if(P!==null&&typeof P=="object"||Array.isArray(P))S[D]=u(p,P);else{if(P instanceof RegExp)throw new Error("Can't transfer regular expressions to the server");S[D]=P}}return S}}function f(p,g){let m=g.asChangeTextDocumentParams(p,p.document.uri,p.document.version);return{document:m.textDocument,changes:m.contentChanges}}n.asTextContentChange=f;function d(p,g){let m=Object.create(null);if(p.metadata&&(m.metadata=t.c2p.asMetadata(p.metadata)),p.cells!==void 0){let S=Object.create(null),D=p.cells;D.structure&&(S.structure={array:{start:D.structure.array.start,deleteCount:D.structure.array.deleteCount,cells:D.structure.array.cells!==void 0?D.structure.array.cells.map(P=>t.c2p.asNotebookCell(P,g)):void 0},didOpen:D.structure.didOpen!==void 0?D.structure.didOpen.map(P=>g.asOpenTextDocumentParams(P.document).textDocument):void 0,didClose:D.structure.didClose!==void 0?D.structure.didClose.map(P=>g.asCloseTextDocumentParams(P.document).textDocument):void 0}),D.data!==void 0&&(S.data=D.data.map(P=>t.c2p.asNotebookCell(P,g))),D.textContent!==void 0&&(S.textContent=D.textContent.map(P=>t.c2p.asTextContentChange(P,g))),Object.keys(S).length>0&&(m.cells=S)}return m}n.asNotebookDocumentChangeEvent=d})(e=t.c2p||(t.c2p={}))})(ss||(ss={}));var kp;(function(t){function e(a,c,u){let f=a.length,d=c.length,p=0;for(;p<d&&p<f&&n(a[p],c[p],u);)p++;if(p<d&&p<f){let g=f-1,m=d-1;for(;g>=0&&m>=0&&n(a[g],c[m],u);)g--,m--;let S=g+1-p,D=p===m+1?void 0:c.slice(p,m+1);return D!==void 0?{start:p,deleteCount:S,cells:D}:{start:p,deleteCount:S}}else return p<d?{start:p,deleteCount:0,cells:c.slice(p)}:p<f?{start:p,deleteCount:f-p}:void 0}t.computeDiff=e;function n(a,c,u=!0){return a.kind!==c.kind||a.document.uri.toString()!==c.document.uri.toString()||a.document.languageId!==c.document.languageId||!r(a.executionSummary,c.executionSummary)?!1:!u||u&&s(a.metadata,c.metadata)}function r(a,c){return a===c?!0:a===void 0||c===void 0?!1:a.executionOrder===c.executionOrder&&a.success===c.success&&i(a.timing,c.timing)}function i(a,c){return a===c?!0:a===void 0||c===void 0?!1:a.startTime===c.startTime&&a.endTime===c.endTime}function s(a,c){if(a===c)return!0;if(a==null||c===null||c===void 0||typeof a!=typeof c||typeof a!="object")return!1;let u=Array.isArray(a),f=Array.isArray(c);if(u!==f)return!1;if(u&&f){if(a.length!==c.length)return!1;for(let d=0;d<a.length;d++)if(!s(a[d],c[d]))return!1}if(o(a)&&o(c)){let d=Object.keys(a),p=Object.keys(c);if(d.length!==p.length||(d.sort(),p.sort(),!s(d,p)))return!1;for(let g=0;g<d.length;g++){let m=d[g];if(!s(a[m],c[m]))return!1}return!0}return!1}function o(a){return a!==null&&typeof a=="object"}t.isObjectLiteral=o})(kp||(kp={}));var Op;(function(t){function e(n,r){if(typeof n=="string")return n==="*"||r.notebookType===n;if(n.notebookType!==void 0&&n.notebookType!=="*"&&r.notebookType!==n.notebookType)return!1;let i=r.uri;if(n.scheme!==void 0&&n.scheme!=="*"&&i.scheme!==n.scheme)return!1;if(n.pattern!==void 0){let s=new gx.Minimatch(n.pattern,{noext:!0});if(!s.makeRe()||!s.match(i.fsPath))return!1}return!0}t.matchNotebook=e})(Op||(Op={}));var Za;(function(t){function e(r){let i=r.notebookSelector,s=[];for(let o of i){let a=(typeof o.notebook=="string"?o.notebook:o.notebook?.notebookType)??"*",c=typeof o.notebook=="string"?void 0:o.notebook?.scheme,u=typeof o.notebook=="string"?void 0:o.notebook?.pattern;if(o.cells!==void 0)for(let f of o.cells)s.push(n(a,c,u,f.language));else s.push(n(a,c,u,void 0))}return s}t.asDocumentSelector=e;function n(r,i,s,o){return i===void 0&&s===void 0?{notebook:r,language:o}:{notebook:{notebookType:r,scheme:i,pattern:s},language:o}}})(Za||(Za={}));var ec;(function(t){function e(n){return{cells:n,uris:new Set(n.map(r=>r.document.uri.toString()))}}t.create=e})(ec||(ec={}));var os=class{constructor(e,n){this.client=e,this.options=n,this.notebookSyncInfo=new Map,this.notebookDidOpen=new Set,this.disposables=[],this.selector=e.protocol2CodeConverter.asDocumentSelector(Za.asDocumentSelector(n)),bt.workspace.onDidOpenNotebookDocument(r=>{this.notebookDidOpen.add(r.uri.toString()),this.didOpen(r)},void 0,this.disposables);for(let r of bt.workspace.notebookDocuments)this.notebookDidOpen.add(r.uri.toString()),this.didOpen(r);bt.workspace.onDidChangeNotebookDocument(r=>this.didChangeNotebookDocument(r),void 0,this.disposables),this.options.save===!0&&bt.workspace.onDidSaveNotebookDocument(r=>this.didSave(r),void 0,this.disposables),bt.workspace.onDidCloseNotebookDocument(r=>{this.didClose(r),this.notebookDidOpen.delete(r.uri.toString())},void 0,this.disposables)}getState(){for(let e of bt.workspace.notebookDocuments)if(this.getMatchingCells(e)!==void 0)return{kind:"document",id:"$internal",registrations:!0,matches:!0};return{kind:"document",id:"$internal",registrations:!0,matches:!1}}get mode(){return"notebook"}handles(e){return bt.languages.match(this.selector,e)>0}didOpenNotebookCellTextDocument(e,n){if(bt.languages.match(this.selector,n.document)===0||!this.notebookDidOpen.has(e.uri.toString()))return;let r=this.notebookSyncInfo.get(e.uri.toString()),i=this.cellMatches(e,n);if(r!==void 0){let s=r.uris.has(n.document.uri.toString());if(i&&s||!i&&!s)return;if(i){let o=this.getMatchingCells(e);if(o!==void 0){let a=this.asNotebookDocumentChangeEvent(e,void 0,r,o);a!==void 0&&this.doSendChange(a,o).catch(()=>{})}}}else i&&this.doSendOpen(e,[n]).catch(()=>{})}didChangeNotebookCellTextDocument(e,n){bt.languages.match(this.selector,n.document)!==0&&this.doSendChange({notebook:e,cells:{textContent:[n]}},void 0).catch(()=>{})}didCloseNotebookCellTextDocument(e,n){let r=this.notebookSyncInfo.get(e.uri.toString());if(r===void 0)return;let i=n.document.uri,s=r.cells.findIndex(o=>o.document.uri.toString()===i.toString());if(s!==-1)if(s===0&&r.cells.length===1)this.doSendClose(e,r.cells).catch(()=>{});else{let o=r.cells.slice(),a=o.splice(s,1);this.doSendChange({notebook:e,cells:{structure:{array:{start:s,deleteCount:1},didClose:a}}},o).catch(()=>{})}}dispose(){for(let e of this.disposables)e.dispose()}didOpen(e,n=this.getMatchingCells(e),r=this.notebookSyncInfo.get(e.uri.toString())){if(r!==void 0)if(n!==void 0){let i=this.asNotebookDocumentChangeEvent(e,void 0,r,n);i!==void 0&&this.doSendChange(i,n).catch(()=>{})}else this.doSendClose(e,[]).catch(()=>{});else{if(n===void 0)return;this.doSendOpen(e,n).catch(()=>{})}}didChangeNotebookDocument(e){let n=e.notebook,r=this.notebookSyncInfo.get(n.uri.toString());if(r===void 0){if(e.contentChanges.length===0)return;let i=this.getMatchingCells(n);if(i===void 0)return;this.didOpen(n,i,r)}else{let i=this.getMatchingCells(n);if(i===void 0){this.didClose(n,r);return}let s=this.asNotebookDocumentChangeEvent(e.notebook,e,r,i);s!==void 0&&this.doSendChange(s,i).catch(()=>{})}}didSave(e){this.notebookSyncInfo.get(e.uri.toString())!==void 0&&this.doSendSave(e).catch(()=>{})}didClose(e,n=this.notebookSyncInfo.get(e.uri.toString())){if(n===void 0)return;let r=e.getCells().filter(i=>n.uris.has(i.document.uri.toString()));this.doSendClose(e,r).catch(()=>{})}async sendDidOpenNotebookDocument(e){let n=this.getMatchingCells(e);if(n!==void 0)return this.doSendOpen(e,n)}async doSendOpen(e,n){let r=async(s,o)=>{let a=ss.c2p.asNotebookDocument(s,o,this.client.code2ProtocolConverter),c=o.map(u=>this.client.code2ProtocolConverter.asTextDocumentItem(u.document));try{await this.client.sendNotification(tr.DidOpenNotebookDocumentNotification.type,{notebookDocument:a,cellTextDocuments:c})}catch(u){throw this.client.error("Sending DidOpenNotebookDocumentNotification failed",u),u}},i=this.client.middleware?.notebooks;return this.notebookSyncInfo.set(e.uri.toString(),ec.create(n)),i?.didOpen!==void 0?i.didOpen(e,n,r):r(e,n)}async sendDidChangeNotebookDocument(e){return this.doSendChange(e,void 0)}async doSendChange(e,n=this.getMatchingCells(e.notebook)){let r=async s=>{try{await this.client.sendNotification(tr.DidChangeNotebookDocumentNotification.type,{notebookDocument:ss.c2p.asVersionedNotebookDocumentIdentifier(s.notebook,this.client.code2ProtocolConverter),change:ss.c2p.asNotebookDocumentChangeEvent(s,this.client.code2ProtocolConverter)})}catch(o){throw this.client.error("Sending DidChangeNotebookDocumentNotification failed",o),o}},i=this.client.middleware?.notebooks;return e.cells?.structure!==void 0&&this.notebookSyncInfo.set(e.notebook.uri.toString(),ec.create(n??[])),i?.didChange!==void 0?i?.didChange(e,r):r(e)}async sendDidSaveNotebookDocument(e){return this.doSendSave(e)}async doSendSave(e){let n=async i=>{try{await this.client.sendNotification(tr.DidSaveNotebookDocumentNotification.type,{notebookDocument:{uri:this.client.code2ProtocolConverter.asUri(i.uri)}})}catch(s){throw this.client.error("Sending DidSaveNotebookDocumentNotification failed",s),s}},r=this.client.middleware?.notebooks;return r?.didSave!==void 0?r.didSave(e,n):n(e)}async sendDidCloseNotebookDocument(e){return this.doSendClose(e,this.getMatchingCells(e)??[])}async doSendClose(e,n){let r=async(s,o)=>{try{await this.client.sendNotification(tr.DidCloseNotebookDocumentNotification.type,{notebookDocument:{uri:this.client.code2ProtocolConverter.asUri(s.uri)},cellTextDocuments:o.map(a=>this.client.code2ProtocolConverter.asTextDocumentIdentifier(a.document))})}catch(a){throw this.client.error("Sending DidCloseNotebookDocumentNotification failed",a),a}},i=this.client.middleware?.notebooks;return this.notebookSyncInfo.delete(e.uri.toString()),i?.didClose!==void 0?i.didClose(e,n,r):r(e,n)}asNotebookDocumentChangeEvent(e,n,r,i){if(n!==void 0&&n.notebook!==e)throw new Error("Notebook must be identical");let s={notebook:e};n?.metadata!==void 0&&(s.metadata=ss.c2p.asMetadata(n.metadata));let o;if(n?.cellChanges!==void 0&&n.cellChanges.length>0){let a=[];o=new Set(i.map(c=>c.document.uri.toString()));for(let c of n.cellChanges)o.has(c.cell.document.uri.toString())&&(c.executionSummary!==void 0||c.metadata!==void 0)&&a.push(c.cell);a.length>0&&(s.cells=s.cells??{},s.cells.data=a)}if((n?.contentChanges!==void 0&&n.contentChanges.length>0||n===void 0)&&r!==void 0&&i!==void 0){let a=r.cells,c=i,u=kp.computeDiff(a,c,!1),f,d;if(u!==void 0){f=u.cells===void 0?new Map:new Map(u.cells.map(m=>[m.document.uri.toString(),m])),d=u.deleteCount===0?new Map:new Map(a.slice(u.start,u.start+u.deleteCount).map(m=>[m.document.uri.toString(),m]));for(let m of Array.from(d.keys()))f.has(m)&&(d.delete(m),f.delete(m));s.cells=s.cells??{};let p=[],g=[];if(f.size>0||d.size>0){for(let m of f.values())p.push(m);for(let m of d.values())g.push(m)}s.cells.structure={array:u,didOpen:p,didClose:g}}}return Object.keys(s).length>1?s:void 0}getMatchingCells(e,n=e.getCells()){if(this.options.notebookSelector!==void 0){for(let r of this.options.notebookSelector)if(r.notebook===void 0||Op.matchNotebook(r.notebook,e)){let i=this.filterCells(e,n,r.cells);return i.length===0?void 0:i}}}cellMatches(e,n){let r=this.getMatchingCells(e,[n]);return r!==void 0&&r[0]===n}filterCells(e,n,r){let i=r!==void 0?n.filter(s=>{let o=s.document.languageId;return r.some(a=>a.language==="*"||o===a.language)}):n;return typeof this.client.clientOptions.notebookDocumentOptions?.filterCells=="function"?this.client.clientOptions.notebookDocumentOptions.filterCells(e,i):i}},tc=class t{constructor(e){this.client=e,this.registrations=new Map,this.registrationType=tr.NotebookDocumentSyncRegistrationType.type,bt.workspace.onDidOpenTextDocument(n=>{if(n.uri.scheme!==t.CellScheme)return;let[r,i]=this.findNotebookDocumentAndCell(n);if(!(r===void 0||i===void 0))for(let s of this.registrations.values())s instanceof os&&s.didOpenNotebookCellTextDocument(r,i)}),bt.workspace.onDidChangeTextDocument(n=>{if(n.contentChanges.length===0)return;let r=n.document;if(r.uri.scheme!==t.CellScheme)return;let[i]=this.findNotebookDocumentAndCell(r);if(i!==void 0)for(let s of this.registrations.values())s instanceof os&&s.didChangeNotebookCellTextDocument(i,n)}),bt.workspace.onDidCloseTextDocument(n=>{if(n.uri.scheme!==t.CellScheme)return;let[r,i]=this.findNotebookDocumentAndCell(n);if(!(r===void 0||i===void 0))for(let s of this.registrations.values())s instanceof os&&s.didCloseNotebookCellTextDocument(r,i)})}getState(){if(this.registrations.size===0)return{kind:"document",id:this.registrationType.method,registrations:!1,matches:!1};for(let e of this.registrations.values()){let n=e.getState();if(n.kind==="document"&&n.registrations===!0&&n.matches===!0)return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!0}}return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!1}}fillClientCapabilities(e){let n=MC(MC(e,"notebookDocument"),"synchronization");n.dynamicRegistration=!0,n.executionSummarySupport=!0}preInitialize(e){let n=e.notebookDocumentSync;n!==void 0&&(this.dedicatedChannel=this.client.protocol2CodeConverter.asDocumentSelector(Za.asDocumentSelector(n)))}initialize(e){let n=e.notebookDocumentSync;if(n===void 0)return;let r=n.id??mx.generateUuid();this.register({id:r,registerOptions:n})}register(e){let n=new os(this.client,e.registerOptions);this.registrations.set(e.id,n)}unregister(e){let n=this.registrations.get(e);n&&n.dispose()}clear(){for(let e of this.registrations.values())e.dispose();this.registrations.clear()}handles(e){if(e.uri.scheme!==t.CellScheme)return!1;if(this.dedicatedChannel!==void 0&&bt.languages.match(this.dedicatedChannel,e)>0)return!0;for(let n of this.registrations.values())if(n.handles(e))return!0;return!1}getProvider(e){for(let n of this.registrations.values())if(n.handles(e.document))return n}findNotebookDocumentAndCell(e){let n=e.uri.toString();for(let r of bt.workspace.notebookDocuments)for(let i of r.getCells())if(i.document.uri.toString()===n)return[r,i];return[void 0,void 0]}};nc.NotebookDocumentSyncFeature=tc;tc.CellScheme="vscode-notebook-cell"});var jC=b(Ir=>{"use strict";Object.defineProperty(Ir,"__esModule",{value:!0});Ir.SyncConfigurationFeature=Ir.toJSONObject=Ir.ConfigurationFeature=void 0;var as=require("vscode"),so=J(),vx=ln(),yx=je(),LC=de(),Ip=class{constructor(e){this._client=e}getState(){return{kind:"static"}}fillClientCapabilities(e){e.workspace=e.workspace||{},e.workspace.configuration=!0}initialize(){let e=this._client;e.onRequest(so.ConfigurationRequest.type,(n,r)=>{let i=o=>{let a=[];for(let c of o.items){let u=c.scopeUri!==void 0&&c.scopeUri!==null?this._client.protocol2CodeConverter.asUri(c.scopeUri):void 0;a.push(this.getConfiguration(u,c.section!==null?c.section:void 0))}return a},s=e.middleware.workspace;return s&&s.configuration?s.configuration(n,r,i):i(n,r)})}getConfiguration(e,n){let r=null;if(n){let i=n.lastIndexOf(".");if(i===-1)r=li(as.workspace.getConfiguration(void 0,e).get(n));else{let s=as.workspace.getConfiguration(n.substr(0,i),e);s&&(r=li(s.get(n.substr(i+1))))}}else{let i=as.workspace.getConfiguration(void 0,e);r={};for(let s of Object.keys(i))i.has(s)&&(r[s]=li(i.get(s)))}return r===void 0&&(r=null),r}clear(){}};Ir.ConfigurationFeature=Ip;function li(t){if(t){if(Array.isArray(t))return t.map(li);if(typeof t=="object"){let e=Object.create(null);for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=li(t[n]));return e}}return t}Ir.toJSONObject=li;var Np=class{constructor(e){this._client=e,this.isCleared=!1,this._listeners=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._listeners.size>0}}get registrationType(){return so.DidChangeConfigurationNotification.type}fillClientCapabilities(e){(0,LC.ensure)((0,LC.ensure)(e,"workspace"),"didChangeConfiguration").dynamicRegistration=!0}initialize(){this.isCleared=!1;let e=this._client.clientOptions.synchronize?.configurationSection;e!==void 0&&this.register({id:yx.generateUuid(),registerOptions:{section:e}})}register(e){let n=as.workspace.onDidChangeConfiguration(r=>{this.onDidChangeConfiguration(e.registerOptions.section,r)});this._listeners.set(e.id,n),e.registerOptions.section!==void 0&&this.onDidChangeConfiguration(e.registerOptions.section,void 0)}unregister(e){let n=this._listeners.get(e);n&&(this._listeners.delete(e),n.dispose())}clear(){for(let e of this._listeners.values())e.dispose();this._listeners.clear(),this.isCleared=!0}onDidChangeConfiguration(e,n){if(this.isCleared)return;let r;if(vx.string(e)?r=[e]:r=e,r!==void 0&&n!==void 0&&!r.some(a=>n.affectsConfiguration(a)))return;let i=async o=>o===void 0?this._client.sendNotification(so.DidChangeConfigurationNotification.type,{settings:null}):this._client.sendNotification(so.DidChangeConfigurationNotification.type,{settings:this.extractSettingsInformation(o)}),s=this._client.middleware.workspace?.didChangeConfiguration;(s?s(r,i):i(r)).catch(o=>{this._client.error(`Sending notification ${so.DidChangeConfigurationNotification.type.method} failed`,o)})}extractSettingsInformation(e){function n(s,o){let a=s;for(let c=0;c<o.length-1;c++){let u=a[o[c]];u||(u=Object.create(null),a[o[c]]=u),a=u}return a}let r=this._client.clientOptions.workspaceFolder?this._client.clientOptions.workspaceFolder.uri:void 0,i=Object.create(null);for(let s=0;s<e.length;s++){let o=e[s],a=o.indexOf("."),c=null;if(a>=0?c=as.workspace.getConfiguration(o.substr(0,a),r).get(o.substr(a+1)):c=as.workspace.getConfiguration(void 0,r).get(o),c){let u=e[s].split(".");n(i,u)[u[u.length-1]]=li(c)}}return i}};Ir.SyncConfigurationFeature=Np});var $C=b(Ut=>{"use strict";Object.defineProperty(Ut,"__esModule",{value:!0});Ut.DidSaveTextDocumentFeature=Ut.WillSaveWaitUntilFeature=Ut.WillSaveFeature=Ut.DidChangeTextDocumentFeature=Ut.DidCloseTextDocumentFeature=Ut.DidOpenTextDocumentFeature=void 0;var Qt=require("vscode"),qe=J(),Ue=de(),cs=je(),Fp=class extends Ue.TextDocumentEventFeature{constructor(e,n){super(e,Qt.workspace.onDidOpenTextDocument,qe.DidOpenTextDocumentNotification.type,()=>e.middleware.didOpen,r=>e.code2ProtocolConverter.asOpenTextDocumentParams(r),r=>r,Ue.TextDocumentEventFeature.textDocumentFilter),this._syncedDocuments=n}get openDocuments(){return this._syncedDocuments.values()}fillClientCapabilities(e){(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;n&&r&&r.openClose&&this.register({id:cs.generateUuid(),registerOptions:{documentSelector:n}})}get registrationType(){return qe.DidOpenTextDocumentNotification.type}register(e){if(super.register(e),!e.registerOptions.documentSelector)return;let n=this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector);Qt.workspace.textDocuments.forEach(r=>{let i=r.uri.toString();if(!this._syncedDocuments.has(i)&&Qt.languages.match(n,r)>0&&!this._client.hasDedicatedTextSynchronizationFeature(r)){let s=this._client.middleware,o=a=>this._client.sendNotification(this._type,this._createParams(a));(s.didOpen?s.didOpen(r,o):o(r)).catch(a=>{this._client.error(`Sending document notification ${this._type.method} failed`,a)}),this._syncedDocuments.set(i,r)}})}getTextDocument(e){return e}notificationSent(e,n,r){this._syncedDocuments.set(e.uri.toString(),e),super.notificationSent(e,n,r)}};Ut.DidOpenTextDocumentFeature=Fp;var Mp=class extends Ue.TextDocumentEventFeature{constructor(e,n,r){super(e,Qt.workspace.onDidCloseTextDocument,qe.DidCloseTextDocumentNotification.type,()=>e.middleware.didClose,i=>e.code2ProtocolConverter.asCloseTextDocumentParams(i),i=>i,Ue.TextDocumentEventFeature.textDocumentFilter),this._syncedDocuments=n,this._pendingTextDocumentChanges=r}get registrationType(){return qe.DidCloseTextDocumentNotification.type}fillClientCapabilities(e){(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;n&&r&&r.openClose&&this.register({id:cs.generateUuid(),registerOptions:{documentSelector:n}})}async callback(e){await super.callback(e),this._pendingTextDocumentChanges.delete(e.uri.toString())}getTextDocument(e){return e}notificationSent(e,n,r){this._syncedDocuments.delete(e.uri.toString()),super.notificationSent(e,n,r)}unregister(e){let n=this._selectors.get(e);super.unregister(e);let r=this._selectors.values();this._syncedDocuments.forEach(i=>{if(Qt.languages.match(n,i)>0&&!this._selectorFilter(r,i)&&!this._client.hasDedicatedTextSynchronizationFeature(i)){let s=this._client.middleware,o=a=>this._client.sendNotification(this._type,this._createParams(a));this._syncedDocuments.delete(i.uri.toString()),(s.didClose?s.didClose(i,o):o(i)).catch(a=>{this._client.error(`Sending document notification ${this._type.method} failed`,a)})}})}};Ut.DidCloseTextDocumentFeature=Mp;var Ap=class extends Ue.DynamicDocumentFeature{constructor(e,n){super(e),this._changeData=new Map,this._onNotificationSent=new Qt.EventEmitter,this._onPendingChangeAdded=new Qt.EventEmitter,this._pendingTextDocumentChanges=n,this._syncKind=qe.TextDocumentSyncKind.None}get onNotificationSent(){return this._onNotificationSent.event}get onPendingChangeAdded(){return this._onPendingChangeAdded.event}get syncKind(){return this._syncKind}get registrationType(){return qe.DidChangeTextDocumentNotification.type}fillClientCapabilities(e){(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;n&&r&&r.change!==void 0&&r.change!==qe.TextDocumentSyncKind.None&&this.register({id:cs.generateUuid(),registerOptions:Object.assign({},{documentSelector:n},{syncKind:r.change})})}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=Qt.workspace.onDidChangeTextDocument(this.callback,this)),this._changeData.set(e.id,{syncKind:e.registerOptions.syncKind,documentSelector:this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)}),this.updateSyncKind(e.registerOptions.syncKind))}*getDocumentSelectors(){for(let e of this._changeData.values())yield e.documentSelector}async callback(e){if(e.contentChanges.length===0)return;let n=e.document.uri,r=e.document.version,i=[];for(let s of this._changeData.values())if(Qt.languages.match(s.documentSelector,e.document)>0&&!this._client.hasDedicatedTextSynchronizationFeature(e.document)){let o=this._client.middleware;if(s.syncKind===qe.TextDocumentSyncKind.Incremental){let a=async c=>{let u=this._client.code2ProtocolConverter.asChangeTextDocumentParams(c,n,r);await this._client.sendNotification(qe.DidChangeTextDocumentNotification.type,u),this.notificationSent(c.document,qe.DidChangeTextDocumentNotification.type,u)};i.push(o.didChange?o.didChange(e,c=>a(c)):a(e))}else if(s.syncKind===qe.TextDocumentSyncKind.Full){let a=async c=>{let u=c.document.uri.toString();this._pendingTextDocumentChanges.set(u,c.document),this._onPendingChangeAdded.fire()};i.push(o.didChange?o.didChange(e,c=>a(c)):a(e))}}return Promise.all(i).then(void 0,s=>{throw this._client.error(`Sending document notification ${qe.DidChangeTextDocumentNotification.type.method} failed`,s),s})}notificationSent(e,n,r){this._onNotificationSent.fire({textDocument:e,type:n,params:r})}unregister(e){if(this._changeData.delete(e),this._changeData.size===0)this._listener&&(this._listener.dispose(),this._listener=void 0),this._syncKind=qe.TextDocumentSyncKind.None;else{this._syncKind=qe.TextDocumentSyncKind.None;for(let n of this._changeData.values())if(this.updateSyncKind(n.syncKind),this._syncKind===qe.TextDocumentSyncKind.Full)break}}clear(){this._pendingTextDocumentChanges.clear(),this._changeData.clear(),this._syncKind=qe.TextDocumentSyncKind.None,this._listener&&(this._listener.dispose(),this._listener=void 0)}getPendingDocumentChanges(e){if(this._pendingTextDocumentChanges.size===0)return[];let n;if(e.size===0)n=Array.from(this._pendingTextDocumentChanges.values()),this._pendingTextDocumentChanges.clear();else{n=[];for(let r of this._pendingTextDocumentChanges)e.has(r[0])||(n.push(r[1]),this._pendingTextDocumentChanges.delete(r[0]))}return n}getProvider(e){for(let n of this._changeData.values())if(Qt.languages.match(n.documentSelector,e)>0)return{send:r=>this.callback(r)}}updateSyncKind(e){if(this._syncKind!==qe.TextDocumentSyncKind.Full)switch(e){case qe.TextDocumentSyncKind.Full:this._syncKind=e;break;case qe.TextDocumentSyncKind.Incremental:this._syncKind===qe.TextDocumentSyncKind.None&&(this._syncKind=qe.TextDocumentSyncKind.Incremental);break}}};Ut.DidChangeTextDocumentFeature=Ap;var Lp=class extends Ue.TextDocumentEventFeature{constructor(e){super(e,Qt.workspace.onWillSaveTextDocument,qe.WillSaveTextDocumentNotification.type,()=>e.middleware.willSave,n=>e.code2ProtocolConverter.asWillSaveTextDocumentParams(n),n=>n.document,(n,r)=>Ue.TextDocumentEventFeature.textDocumentFilter(n,r.document))}get registrationType(){return qe.WillSaveTextDocumentNotification.type}fillClientCapabilities(e){let n=(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization");n.willSave=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;n&&r&&r.willSave&&this.register({id:cs.generateUuid(),registerOptions:{documentSelector:n}})}getTextDocument(e){return e.document}};Ut.WillSaveFeature=Lp;var jp=class extends Ue.DynamicDocumentFeature{constructor(e){super(e),this._selectors=new Map}getDocumentSelectors(){return this._selectors.values()}get registrationType(){return qe.WillSaveTextDocumentWaitUntilRequest.type}fillClientCapabilities(e){let n=(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization");n.willSaveWaitUntil=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;n&&r&&r.willSaveWaitUntil&&this.register({id:cs.generateUuid(),registerOptions:{documentSelector:n}})}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=Qt.workspace.onWillSaveTextDocument(this.callback,this)),this._selectors.set(e.id,this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)))}callback(e){if(Ue.TextDocumentEventFeature.textDocumentFilter(this._selectors.values(),e.document)&&!this._client.hasDedicatedTextSynchronizationFeature(e.document)){let n=this._client.middleware,r=i=>this._client.sendRequest(qe.WillSaveTextDocumentWaitUntilRequest.type,this._client.code2ProtocolConverter.asWillSaveTextDocumentParams(i)).then(async s=>{let o=await this._client.protocol2CodeConverter.asTextEdits(s);return o===void 0?[]:o});e.waitUntil(n.willSaveWaitUntil?n.willSaveWaitUntil(e,r):r(e))}}unregister(e){this._selectors.delete(e),this._selectors.size===0&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._selectors.clear(),this._listener&&(this._listener.dispose(),this._listener=void 0)}};Ut.WillSaveWaitUntilFeature=jp;var $p=class extends Ue.TextDocumentEventFeature{constructor(e){super(e,Qt.workspace.onDidSaveTextDocument,qe.DidSaveTextDocumentNotification.type,()=>e.middleware.didSave,n=>e.code2ProtocolConverter.asSaveTextDocumentParams(n,this._includeText),n=>n,Ue.TextDocumentEventFeature.textDocumentFilter),this._includeText=!1}get registrationType(){return qe.DidSaveTextDocumentNotification.type}fillClientCapabilities(e){(0,Ue.ensure)((0,Ue.ensure)(e,"textDocument"),"synchronization").didSave=!0}initialize(e,n){let r=e.resolvedTextDocumentSync;if(n&&r&&r.save){let i=typeof r.save=="boolean"?{includeText:!1}:{includeText:!!r.save.includeText};this.register({id:cs.generateUuid(),registerOptions:Object.assign({},{documentSelector:n},i)})}}register(e){this._includeText=!!e.registerOptions.includeText,super.register(e)}getTextDocument(e){return e}};Ut.DidSaveTextDocumentFeature=$p});var HC=b(rc=>{"use strict";Object.defineProperty(rc,"__esModule",{value:!0});rc.CompletionItemFeature=void 0;var wx=require("vscode"),ce=J(),Hp=de(),bx=je(),Cx=[ce.CompletionItemKind.Text,ce.CompletionItemKind.Method,ce.CompletionItemKind.Function,ce.CompletionItemKind.Constructor,ce.CompletionItemKind.Field,ce.CompletionItemKind.Variable,ce.CompletionItemKind.Class,ce.CompletionItemKind.Interface,ce.CompletionItemKind.Module,ce.CompletionItemKind.Property,ce.CompletionItemKind.Unit,ce.CompletionItemKind.Value,ce.CompletionItemKind.Enum,ce.CompletionItemKind.Keyword,ce.CompletionItemKind.Snippet,ce.CompletionItemKind.Color,ce.CompletionItemKind.File,ce.CompletionItemKind.Reference,ce.CompletionItemKind.Folder,ce.CompletionItemKind.EnumMember,ce.CompletionItemKind.Constant,ce.CompletionItemKind.Struct,ce.CompletionItemKind.Event,ce.CompletionItemKind.Operator,ce.CompletionItemKind.TypeParameter],Up=class extends Hp.TextDocumentLanguageFeature{constructor(e){super(e,ce.CompletionRequest.type),this.labelDetailsSupport=new Map}fillClientCapabilities(e){let n=(0,Hp.ensure)((0,Hp.ensure)(e,"textDocument"),"completion");n.dynamicRegistration=!0,n.contextSupport=!0,n.completionItem={snippetSupport:!0,commitCharactersSupport:!0,documentationFormat:[ce.MarkupKind.Markdown,ce.MarkupKind.PlainText],deprecatedSupport:!0,preselectSupport:!0,tagSupport:{valueSet:[ce.CompletionItemTag.Deprecated]},insertReplaceSupport:!0,resolveSupport:{properties:["documentation","detail","additionalTextEdits"]},insertTextModeSupport:{valueSet:[ce.InsertTextMode.asIs,ce.InsertTextMode.adjustIndentation]},labelDetailsSupport:!0},n.insertTextMode=ce.InsertTextMode.adjustIndentation,n.completionItemKind={valueSet:Cx},n.completionList={itemDefaults:["commitCharacters","editRange","insertTextFormat","insertTextMode","data"]}}initialize(e,n){let r=this.getRegistrationOptions(n,e.completionProvider);r&&this.register({id:bx.generateUuid(),registerOptions:r})}registerLanguageProvider(e,n){this.labelDetailsSupport.set(n,!!e.completionItem?.labelDetailsSupport);let r=e.triggerCharacters??[],i=e.allCommitCharacters,s=e.documentSelector,o={provideCompletionItems:(a,c,u,f)=>{let d=this._client,p=this._client.middleware,g=(m,S,D,P)=>d.sendRequest(ce.CompletionRequest.type,d.code2ProtocolConverter.asCompletionParams(m,S,D),P).then(k=>P.isCancellationRequested?null:d.protocol2CodeConverter.asCompletionResult(k,i,P),k=>d.handleFailedRequest(ce.CompletionRequest.type,P,k,null));return p.provideCompletionItem?p.provideCompletionItem(a,c,f,u,g):g(a,c,f,u)},resolveCompletionItem:e.resolveProvider?(a,c)=>{let u=this._client,f=this._client.middleware,d=(p,g)=>u.sendRequest(ce.CompletionResolveRequest.type,u.code2ProtocolConverter.asCompletionItem(p,!!this.labelDetailsSupport.get(n)),g).then(m=>g.isCancellationRequested?null:u.protocol2CodeConverter.asCompletionItem(m),m=>u.handleFailedRequest(ce.CompletionResolveRequest.type,g,m,p));return f.resolveCompletionItem?f.resolveCompletionItem(a,c,d):d(a,c)}:void 0};return[wx.languages.registerCompletionItemProvider(this._client.protocol2CodeConverter.asDocumentSelector(s),o,...r),o]}};rc.CompletionItemFeature=Up});var UC=b(ic=>{"use strict";Object.defineProperty(ic,"__esModule",{value:!0});ic.HoverFeature=void 0;var _x=require("vscode"),oo=J(),Vp=de(),Sx=je(),Wp=class extends Vp.TextDocumentLanguageFeature{constructor(e){super(e,oo.HoverRequest.type)}fillClientCapabilities(e){let n=(0,Vp.ensure)((0,Vp.ensure)(e,"textDocument"),"hover");n.dynamicRegistration=!0,n.contentFormat=[oo.MarkupKind.Markdown,oo.MarkupKind.PlainText]}initialize(e,n){let r=this.getRegistrationOptions(n,e.hoverProvider);r&&this.register({id:Sx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideHover:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(oo.HoverRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asHover(g),g=>a.handleFailedRequest(oo.HoverRequest.type,p,g,null)),u=a.middleware;return u.provideHover?u.provideHover(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return _x.languages.registerHoverProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};ic.HoverFeature=Wp});var VC=b(sc=>{"use strict";Object.defineProperty(sc,"__esModule",{value:!0});sc.DefinitionFeature=void 0;var Dx=require("vscode"),Kp=J(),zp=de(),Rx=je(),Bp=class extends zp.TextDocumentLanguageFeature{constructor(e){super(e,Kp.DefinitionRequest.type)}fillClientCapabilities(e){let n=(0,zp.ensure)((0,zp.ensure)(e,"textDocument"),"definition");n.dynamicRegistration=!0,n.linkSupport=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.definitionProvider);r&&this.register({id:Rx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDefinition:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Kp.DefinitionRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asDefinitionResult(g,p),g=>a.handleFailedRequest(Kp.DefinitionRequest.type,p,g,null)),u=a.middleware;return u.provideDefinition?u.provideDefinition(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Dx.languages.registerDefinitionProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};sc.DefinitionFeature=Bp});var KC=b(oc=>{"use strict";Object.defineProperty(oc,"__esModule",{value:!0});oc.SignatureHelpFeature=void 0;var WC=require("vscode"),ao=J(),Gp=de(),Tx=je(),Xp=class extends Gp.TextDocumentLanguageFeature{constructor(e){super(e,ao.SignatureHelpRequest.type)}fillClientCapabilities(e){let n=(0,Gp.ensure)((0,Gp.ensure)(e,"textDocument"),"signatureHelp");n.dynamicRegistration=!0,n.signatureInformation={documentationFormat:[ao.MarkupKind.Markdown,ao.MarkupKind.PlainText]},n.signatureInformation.parameterInformation={labelOffsetSupport:!0},n.signatureInformation.activeParameterSupport=!0,n.contextSupport=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.signatureHelpProvider);r&&this.register({id:Tx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n={provideSignatureHelp:(r,i,s,o)=>{let a=this._client,c=(f,d,p,g)=>a.sendRequest(ao.SignatureHelpRequest.type,a.code2ProtocolConverter.asSignatureHelpParams(f,d,p),g).then(m=>g.isCancellationRequested?null:a.protocol2CodeConverter.asSignatureHelp(m,g),m=>a.handleFailedRequest(ao.SignatureHelpRequest.type,g,m,null)),u=a.middleware;return u.provideSignatureHelp?u.provideSignatureHelp(r,i,o,s,c):c(r,i,o,s)}};return[this.registerProvider(e,n),n]}registerProvider(e,n){let r=this._client.protocol2CodeConverter.asDocumentSelector(e.documentSelector);if(e.retriggerCharacters===void 0){let i=e.triggerCharacters||[];return WC.languages.registerSignatureHelpProvider(r,n,...i)}else{let i={triggerCharacters:e.triggerCharacters||[],retriggerCharacters:e.retriggerCharacters||[]};return WC.languages.registerSignatureHelpProvider(r,n,i)}}};oc.SignatureHelpFeature=Xp});var zC=b(ac=>{"use strict";Object.defineProperty(ac,"__esModule",{value:!0});ac.DocumentHighlightFeature=void 0;var Px=require("vscode"),Jp=J(),Yp=de(),Ex=je(),Qp=class extends Yp.TextDocumentLanguageFeature{constructor(e){super(e,Jp.DocumentHighlightRequest.type)}fillClientCapabilities(e){(0,Yp.ensure)((0,Yp.ensure)(e,"textDocument"),"documentHighlight").dynamicRegistration=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentHighlightProvider);r&&this.register({id:Ex.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDocumentHighlights:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Jp.DocumentHighlightRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asDocumentHighlights(g,p),g=>a.handleFailedRequest(Jp.DocumentHighlightRequest.type,p,g,null)),u=a.middleware;return u.provideDocumentHighlights?u.provideDocumentHighlights(i,s,o,c):c(i,s,o)}};return[Px.languages.registerDocumentHighlightProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};ac.DocumentHighlightFeature=Qp});var th=b(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});Vn.DocumentSymbolFeature=Vn.SupportedSymbolTags=Vn.SupportedSymbolKinds=void 0;var xx=require("vscode"),ve=J(),Zp=de(),qx=je();Vn.SupportedSymbolKinds=[ve.SymbolKind.File,ve.SymbolKind.Module,ve.SymbolKind.Namespace,ve.SymbolKind.Package,ve.SymbolKind.Class,ve.SymbolKind.Method,ve.SymbolKind.Property,ve.SymbolKind.Field,ve.SymbolKind.Constructor,ve.SymbolKind.Enum,ve.SymbolKind.Interface,ve.SymbolKind.Function,ve.SymbolKind.Variable,ve.SymbolKind.Constant,ve.SymbolKind.String,ve.SymbolKind.Number,ve.SymbolKind.Boolean,ve.SymbolKind.Array,ve.SymbolKind.Object,ve.SymbolKind.Key,ve.SymbolKind.Null,ve.SymbolKind.EnumMember,ve.SymbolKind.Struct,ve.SymbolKind.Event,ve.SymbolKind.Operator,ve.SymbolKind.TypeParameter];Vn.SupportedSymbolTags=[ve.SymbolTag.Deprecated];var eh=class extends Zp.TextDocumentLanguageFeature{constructor(e){super(e,ve.DocumentSymbolRequest.type)}fillClientCapabilities(e){let n=(0,Zp.ensure)((0,Zp.ensure)(e,"textDocument"),"documentSymbol");n.dynamicRegistration=!0,n.symbolKind={valueSet:Vn.SupportedSymbolKinds},n.hierarchicalDocumentSymbolSupport=!0,n.tagSupport={valueSet:Vn.SupportedSymbolTags},n.labelSupport=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentSymbolProvider);r&&this.register({id:qx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDocumentSymbols:(s,o)=>{let a=this._client,c=async(f,d)=>{try{let p=await a.sendRequest(ve.DocumentSymbolRequest.type,a.code2ProtocolConverter.asDocumentSymbolParams(f),d);if(d.isCancellationRequested||p===void 0||p===null)return null;if(p.length===0)return[];{let g=p[0];return ve.DocumentSymbol.is(g)?await a.protocol2CodeConverter.asDocumentSymbols(p,d):await a.protocol2CodeConverter.asSymbolInformations(p,d)}}catch(p){return a.handleFailedRequest(ve.DocumentSymbolRequest.type,d,p,null)}},u=a.middleware;return u.provideDocumentSymbols?u.provideDocumentSymbols(s,o,c):c(s,o)}},i=e.label!==void 0?{label:e.label}:void 0;return[xx.languages.registerDocumentSymbolProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r,i),r]}};Vn.DocumentSymbolFeature=eh});var GC=b(cc=>{"use strict";Object.defineProperty(cc,"__esModule",{value:!0});cc.WorkspaceSymbolFeature=void 0;var kx=require("vscode"),co=J(),nh=de(),BC=th(),Ox=je(),rh=class extends nh.WorkspaceFeature{constructor(e){super(e,co.WorkspaceSymbolRequest.type)}fillClientCapabilities(e){let n=(0,nh.ensure)((0,nh.ensure)(e,"workspace"),"symbol");n.dynamicRegistration=!0,n.symbolKind={valueSet:BC.SupportedSymbolKinds},n.tagSupport={valueSet:BC.SupportedSymbolTags},n.resolveSupport={properties:["location.range"]}}initialize(e){e.workspaceSymbolProvider&&this.register({id:Ox.generateUuid(),registerOptions:e.workspaceSymbolProvider===!0?{workDoneProgress:!1}:e.workspaceSymbolProvider})}registerLanguageProvider(e){let n={provideWorkspaceSymbols:(r,i)=>{let s=this._client,o=(c,u)=>s.sendRequest(co.WorkspaceSymbolRequest.type,{query:c},u).then(f=>u.isCancellationRequested?null:s.protocol2CodeConverter.asSymbolInformations(f,u),f=>s.handleFailedRequest(co.WorkspaceSymbolRequest.type,u,f,null)),a=s.middleware;return a.provideWorkspaceSymbols?a.provideWorkspaceSymbols(r,i,o):o(r,i)},resolveWorkspaceSymbol:e.resolveProvider===!0?(r,i)=>{let s=this._client,o=(c,u)=>s.sendRequest(co.WorkspaceSymbolResolveRequest.type,s.code2ProtocolConverter.asWorkspaceSymbol(c),u).then(f=>u.isCancellationRequested?null:s.protocol2CodeConverter.asSymbolInformation(f),f=>s.handleFailedRequest(co.WorkspaceSymbolResolveRequest.type,u,f,null)),a=s.middleware;return a.resolveWorkspaceSymbol?a.resolveWorkspaceSymbol(r,i,o):o(r,i)}:void 0};return[kx.languages.registerWorkspaceSymbolProvider(n),n]}};cc.WorkspaceSymbolFeature=rh});var XC=b(uc=>{"use strict";Object.defineProperty(uc,"__esModule",{value:!0});uc.ReferencesFeature=void 0;var Ix=require("vscode"),ih=J(),sh=de(),Nx=je(),oh=class extends sh.TextDocumentLanguageFeature{constructor(e){super(e,ih.ReferencesRequest.type)}fillClientCapabilities(e){(0,sh.ensure)((0,sh.ensure)(e,"textDocument"),"references").dynamicRegistration=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.referencesProvider);r&&this.register({id:Nx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideReferences:(i,s,o,a)=>{let c=this._client,u=(d,p,g,m)=>c.sendRequest(ih.ReferencesRequest.type,c.code2ProtocolConverter.asReferenceParams(d,p,g),m).then(S=>m.isCancellationRequested?null:c.protocol2CodeConverter.asReferences(S,m),S=>c.handleFailedRequest(ih.ReferencesRequest.type,m,S,null)),f=c.middleware;return f.provideReferences?f.provideReferences(i,s,o,a,u):u(i,s,o,a)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Ix.languages.registerReferenceProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};uc.ReferencesFeature=oh});var JC=b(lc=>{"use strict";Object.defineProperty(lc,"__esModule",{value:!0});lc.CodeActionFeature=void 0;var Fx=require("vscode"),Zt=J(),Mx=je(),ah=de(),ch=class extends ah.TextDocumentLanguageFeature{constructor(e){super(e,Zt.CodeActionRequest.type)}fillClientCapabilities(e){let n=(0,ah.ensure)((0,ah.ensure)(e,"textDocument"),"codeAction");n.dynamicRegistration=!0,n.isPreferredSupport=!0,n.disabledSupport=!0,n.dataSupport=!0,n.resolveSupport={properties:["edit"]},n.codeActionLiteralSupport={codeActionKind:{valueSet:[Zt.CodeActionKind.Empty,Zt.CodeActionKind.QuickFix,Zt.CodeActionKind.Refactor,Zt.CodeActionKind.RefactorExtract,Zt.CodeActionKind.RefactorInline,Zt.CodeActionKind.RefactorRewrite,Zt.CodeActionKind.Source,Zt.CodeActionKind.SourceOrganizeImports]}},n.honorsChangeAnnotations=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.codeActionProvider);r&&this.register({id:Mx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideCodeActions:(i,s,o,a)=>{let c=this._client,u=async(d,p,g,m)=>{let S={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d),range:c.code2ProtocolConverter.asRange(p),context:c.code2ProtocolConverter.asCodeActionContextSync(g)};return c.sendRequest(Zt.CodeActionRequest.type,S,m).then(D=>m.isCancellationRequested||D===null||D===void 0?null:c.protocol2CodeConverter.asCodeActionResult(D,m),D=>c.handleFailedRequest(Zt.CodeActionRequest.type,m,D,null))},f=c.middleware;return f.provideCodeActions?f.provideCodeActions(i,s,o,a,u):u(i,s,o,a)},resolveCodeAction:e.resolveProvider?(i,s)=>{let o=this._client,a=this._client.middleware,c=async(u,f)=>o.sendRequest(Zt.CodeActionResolveRequest.type,o.code2ProtocolConverter.asCodeActionSync(u),f).then(d=>f.isCancellationRequested?u:o.protocol2CodeConverter.asCodeAction(d,f),d=>o.handleFailedRequest(Zt.CodeActionResolveRequest.type,f,d,u));return a.resolveCodeAction?a.resolveCodeAction(i,s,c):c(i,s)}:void 0};return[Fx.languages.registerCodeActionsProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r,e.codeActionKinds?{providedCodeActionKinds:this._client.protocol2CodeConverter.asCodeActionKinds(e.codeActionKinds)}:void 0),r]}};lc.CodeActionFeature=ch});var QC=b(dc=>{"use strict";Object.defineProperty(dc,"__esModule",{value:!0});dc.CodeLensFeature=void 0;var YC=require("vscode"),us=J(),Ax=je(),uo=de(),uh=class extends uo.TextDocumentLanguageFeature{constructor(e){super(e,us.CodeLensRequest.type)}fillClientCapabilities(e){(0,uo.ensure)((0,uo.ensure)(e,"textDocument"),"codeLens").dynamicRegistration=!0,(0,uo.ensure)((0,uo.ensure)(e,"workspace"),"codeLens").refreshSupport=!0}initialize(e,n){this._client.onRequest(us.CodeLensRefreshRequest.type,async()=>{for(let s of this.getAllProviders())s.onDidChangeCodeLensEmitter.fire()});let i=this.getRegistrationOptions(n,e.codeLensProvider);i&&this.register({id:Ax.generateUuid(),registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r=new YC.EventEmitter,i={onDidChangeCodeLenses:r.event,provideCodeLenses:(s,o)=>{let a=this._client,c=(f,d)=>a.sendRequest(us.CodeLensRequest.type,a.code2ProtocolConverter.asCodeLensParams(f),d).then(p=>d.isCancellationRequested?null:a.protocol2CodeConverter.asCodeLenses(p,d),p=>a.handleFailedRequest(us.CodeLensRequest.type,d,p,null)),u=a.middleware;return u.provideCodeLenses?u.provideCodeLenses(s,o,c):c(s,o)},resolveCodeLens:e.resolveProvider?(s,o)=>{let a=this._client,c=(f,d)=>a.sendRequest(us.CodeLensResolveRequest.type,a.code2ProtocolConverter.asCodeLens(f),d).then(p=>d.isCancellationRequested?f:a.protocol2CodeConverter.asCodeLens(p),p=>a.handleFailedRequest(us.CodeLensResolveRequest.type,d,p,f)),u=a.middleware;return u.resolveCodeLens?u.resolveCodeLens(s,o,c):c(s,o)}:void 0};return[YC.languages.registerCodeLensProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),i),{provider:i,onDidChangeCodeLensEmitter:r}]}};dc.CodeLensFeature=uh});var ZC=b(Nr=>{"use strict";Object.defineProperty(Nr,"__esModule",{value:!0});Nr.DocumentOnTypeFormattingFeature=Nr.DocumentRangeFormattingFeature=Nr.DocumentFormattingFeature=void 0;var fc=require("vscode"),xn=J(),ph=je(),nr=de(),ls;(function(t){function e(n){let r=fc.workspace.getConfiguration("files",n);return{trimTrailingWhitespace:r.get("trimTrailingWhitespace"),trimFinalNewlines:r.get("trimFinalNewlines"),insertFinalNewline:r.get("insertFinalNewline")}}t.fromConfiguration=e})(ls||(ls={}));var lh=class extends nr.TextDocumentLanguageFeature{constructor(e){super(e,xn.DocumentFormattingRequest.type)}fillClientCapabilities(e){(0,nr.ensure)((0,nr.ensure)(e,"textDocument"),"formatting").dynamicRegistration=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentFormattingProvider);r&&this.register({id:ph.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDocumentFormattingEdits:(i,s,o)=>{let a=this._client,c=(f,d,p)=>{let g={textDocument:a.code2ProtocolConverter.asTextDocumentIdentifier(f),options:a.code2ProtocolConverter.asFormattingOptions(d,ls.fromConfiguration(f))};return a.sendRequest(xn.DocumentFormattingRequest.type,g,p).then(m=>p.isCancellationRequested?null:a.protocol2CodeConverter.asTextEdits(m,p),m=>a.handleFailedRequest(xn.DocumentFormattingRequest.type,p,m,null))},u=a.middleware;return u.provideDocumentFormattingEdits?u.provideDocumentFormattingEdits(i,s,o,c):c(i,s,o)}};return[fc.languages.registerDocumentFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};Nr.DocumentFormattingFeature=lh;var dh=class extends nr.TextDocumentLanguageFeature{constructor(e){super(e,xn.DocumentRangeFormattingRequest.type)}fillClientCapabilities(e){let n=(0,nr.ensure)((0,nr.ensure)(e,"textDocument"),"rangeFormatting");n.dynamicRegistration=!0,n.rangesSupport=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentRangeFormattingProvider);r&&this.register({id:ph.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDocumentRangeFormattingEdits:(i,s,o,a)=>{let c=this._client,u=(d,p,g,m)=>{let S={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d),range:c.code2ProtocolConverter.asRange(p),options:c.code2ProtocolConverter.asFormattingOptions(g,ls.fromConfiguration(d))};return c.sendRequest(xn.DocumentRangeFormattingRequest.type,S,m).then(D=>m.isCancellationRequested?null:c.protocol2CodeConverter.asTextEdits(D,m),D=>c.handleFailedRequest(xn.DocumentRangeFormattingRequest.type,m,D,null))},f=c.middleware;return f.provideDocumentRangeFormattingEdits?f.provideDocumentRangeFormattingEdits(i,s,o,a,u):u(i,s,o,a)}};return e.rangesSupport&&(r.provideDocumentRangesFormattingEdits=(i,s,o,a)=>{let c=this._client,u=(d,p,g,m)=>{let S={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d),ranges:c.code2ProtocolConverter.asRanges(p),options:c.code2ProtocolConverter.asFormattingOptions(g,ls.fromConfiguration(d))};return c.sendRequest(xn.DocumentRangesFormattingRequest.type,S,m).then(D=>m.isCancellationRequested?null:c.protocol2CodeConverter.asTextEdits(D,m),D=>c.handleFailedRequest(xn.DocumentRangesFormattingRequest.type,m,D,null))},f=c.middleware;return f.provideDocumentRangesFormattingEdits?f.provideDocumentRangesFormattingEdits(i,s,o,a,u):u(i,s,o,a)}),[fc.languages.registerDocumentRangeFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};Nr.DocumentRangeFormattingFeature=dh;var fh=class extends nr.TextDocumentLanguageFeature{constructor(e){super(e,xn.DocumentOnTypeFormattingRequest.type)}fillClientCapabilities(e){(0,nr.ensure)((0,nr.ensure)(e,"textDocument"),"onTypeFormatting").dynamicRegistration=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentOnTypeFormattingProvider);r&&this.register({id:ph.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideOnTypeFormattingEdits:(s,o,a,c,u)=>{let f=this._client,d=(g,m,S,D,P)=>{let k={textDocument:f.code2ProtocolConverter.asTextDocumentIdentifier(g),position:f.code2ProtocolConverter.asPosition(m),ch:S,options:f.code2ProtocolConverter.asFormattingOptions(D,ls.fromConfiguration(g))};return f.sendRequest(xn.DocumentOnTypeFormattingRequest.type,k,P).then(x=>P.isCancellationRequested?null:f.protocol2CodeConverter.asTextEdits(x,P),x=>f.handleFailedRequest(xn.DocumentOnTypeFormattingRequest.type,P,x,null))},p=f.middleware;return p.provideOnTypeFormattingEdits?p.provideOnTypeFormattingEdits(s,o,a,c,u,d):d(s,o,a,c,u)}},i=e.moreTriggerCharacter||[];return[fc.languages.registerOnTypeFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r,e.firstTriggerCharacter,...i),r]}};Nr.DocumentOnTypeFormattingFeature=fh});var t_=b(pc=>{"use strict";Object.defineProperty(pc,"__esModule",{value:!0});pc.RenameFeature=void 0;var Lx=require("vscode"),di=J(),jx=je(),e_=ln(),hh=de(),gh=class extends hh.TextDocumentLanguageFeature{constructor(e){super(e,di.RenameRequest.type)}fillClientCapabilities(e){let n=(0,hh.ensure)((0,hh.ensure)(e,"textDocument"),"rename");n.dynamicRegistration=!0,n.prepareSupport=!0,n.prepareSupportDefaultBehavior=di.PrepareSupportDefaultBehavior.Identifier,n.honorsChangeAnnotations=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.renameProvider);r&&(e_.boolean(e.renameProvider)&&(r.prepareProvider=!1),this.register({id:jx.generateUuid(),registerOptions:r}))}registerLanguageProvider(e){let n=e.documentSelector,r={provideRenameEdits:(i,s,o,a)=>{let c=this._client,u=(d,p,g,m)=>{let S={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d),position:c.code2ProtocolConverter.asPosition(p),newName:g};return c.sendRequest(di.RenameRequest.type,S,m).then(D=>m.isCancellationRequested?null:c.protocol2CodeConverter.asWorkspaceEdit(D,m),D=>c.handleFailedRequest(di.RenameRequest.type,m,D,null,!1))},f=c.middleware;return f.provideRenameEdits?f.provideRenameEdits(i,s,o,a,u):u(i,s,o,a)},prepareRename:e.prepareProvider?(i,s,o)=>{let a=this._client,c=(f,d,p)=>{let g={textDocument:a.code2ProtocolConverter.asTextDocumentIdentifier(f),position:a.code2ProtocolConverter.asPosition(d)};return a.sendRequest(di.PrepareRenameRequest.type,g,p).then(m=>p.isCancellationRequested?null:di.Range.is(m)?a.protocol2CodeConverter.asRange(m):this.isDefaultBehavior(m)?m.defaultBehavior===!0?null:Promise.reject(new Error("The element can't be renamed.")):m&&di.Range.is(m.range)?{range:a.protocol2CodeConverter.asRange(m.range),placeholder:m.placeholder}:Promise.reject(new Error("The element can't be renamed.")),m=>{throw typeof m.message=="string"?new Error(m.message):new Error("The element can't be renamed.")})},u=a.middleware;return u.prepareRename?u.prepareRename(i,s,o,c):c(i,s,o)}:void 0};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Lx.languages.registerRenameProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}isDefaultBehavior(e){let n=e;return n&&e_.boolean(n.defaultBehavior)}};pc.RenameFeature=gh});var n_=b(hc=>{"use strict";Object.defineProperty(hc,"__esModule",{value:!0});hc.DocumentLinkFeature=void 0;var $x=require("vscode"),lo=J(),mh=de(),Hx=je(),vh=class extends mh.TextDocumentLanguageFeature{constructor(e){super(e,lo.DocumentLinkRequest.type)}fillClientCapabilities(e){let n=(0,mh.ensure)((0,mh.ensure)(e,"textDocument"),"documentLink");n.dynamicRegistration=!0,n.tooltipSupport=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.documentLinkProvider);r&&this.register({id:Hx.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDocumentLinks:(i,s)=>{let o=this._client,a=(u,f)=>o.sendRequest(lo.DocumentLinkRequest.type,o.code2ProtocolConverter.asDocumentLinkParams(u),f).then(d=>f.isCancellationRequested?null:o.protocol2CodeConverter.asDocumentLinks(d,f),d=>o.handleFailedRequest(lo.DocumentLinkRequest.type,f,d,null)),c=o.middleware;return c.provideDocumentLinks?c.provideDocumentLinks(i,s,a):a(i,s)},resolveDocumentLink:e.resolveProvider?(i,s)=>{let o=this._client,a=(u,f)=>o.sendRequest(lo.DocumentLinkResolveRequest.type,o.code2ProtocolConverter.asDocumentLink(u),f).then(d=>f.isCancellationRequested?u:o.protocol2CodeConverter.asDocumentLink(d),d=>o.handleFailedRequest(lo.DocumentLinkResolveRequest.type,f,d,u)),c=o.middleware;return c.resolveDocumentLink?c.resolveDocumentLink(i,s,a):a(i,s)}:void 0};return[$x.languages.registerDocumentLinkProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};hc.DocumentLinkFeature=vh});var i_=b(gc=>{"use strict";Object.defineProperty(gc,"__esModule",{value:!0});gc.ExecuteCommandFeature=void 0;var Ux=require("vscode"),yh=J(),Vx=je(),r_=de(),wh=class{constructor(e){this._client=e,this._commands=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._commands.size>0}}get registrationType(){return yh.ExecuteCommandRequest.type}fillClientCapabilities(e){(0,r_.ensure)((0,r_.ensure)(e,"workspace"),"executeCommand").dynamicRegistration=!0}initialize(e){e.executeCommandProvider&&this.register({id:Vx.generateUuid(),registerOptions:Object.assign({},e.executeCommandProvider)})}register(e){let n=this._client,r=n.middleware,i=(s,o)=>{let a={command:s,arguments:o};return n.sendRequest(yh.ExecuteCommandRequest.type,a).then(void 0,c=>n.handleFailedRequest(yh.ExecuteCommandRequest.type,void 0,c,void 0))};if(e.registerOptions.commands){let s=[];for(let o of e.registerOptions.commands)s.push(Ux.commands.registerCommand(o,(...a)=>r.executeCommand?r.executeCommand(o,a,i):i(o,a)));this._commands.set(e.id,s)}}unregister(e){let n=this._commands.get(e);n&&n.forEach(r=>r.dispose())}clear(){this._commands.forEach(e=>{e.forEach(n=>n.dispose())}),this._commands.clear()}};gc.ExecuteCommandFeature=wh});var s_=b(vc=>{"use strict";Object.defineProperty(vc,"__esModule",{value:!0});vc.FileSystemWatcherFeature=void 0;var Wx=require("vscode"),fi=J(),mc=de(),bh=class{constructor(e,n){this._client=e,this._notifyFileEvent=n,this._watchers=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._watchers.size>0}}get registrationType(){return fi.DidChangeWatchedFilesNotification.type}fillClientCapabilities(e){(0,mc.ensure)((0,mc.ensure)(e,"workspace"),"didChangeWatchedFiles").dynamicRegistration=!0,(0,mc.ensure)((0,mc.ensure)(e,"workspace"),"didChangeWatchedFiles").relativePatternSupport=!0}initialize(e,n){}register(e){if(!Array.isArray(e.registerOptions.watchers))return;let n=[];for(let r of e.registerOptions.watchers){let i=this._client.protocol2CodeConverter.asGlobPattern(r.globPattern);if(i===void 0)continue;let s=!0,o=!0,a=!0;r.kind!==void 0&&r.kind!==null&&(s=(r.kind&fi.WatchKind.Create)!==0,o=(r.kind&fi.WatchKind.Change)!==0,a=(r.kind&fi.WatchKind.Delete)!==0);let c=Wx.workspace.createFileSystemWatcher(i,!s,!o,!a);this.hookListeners(c,s,o,a,n),n.push(c)}this._watchers.set(e.id,n)}registerRaw(e,n){let r=[];for(let i of n)this.hookListeners(i,!0,!0,!0,r);this._watchers.set(e,r)}hookListeners(e,n,r,i,s){n&&e.onDidCreate(o=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(o),type:fi.FileChangeType.Created}),null,s),r&&e.onDidChange(o=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(o),type:fi.FileChangeType.Changed}),null,s),i&&e.onDidDelete(o=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(o),type:fi.FileChangeType.Deleted}),null,s)}unregister(e){let n=this._watchers.get(e);if(n)for(let r of n)r.dispose()}clear(){this._watchers.forEach(e=>{for(let n of e)n.dispose()}),this._watchers.clear()}};vc.FileSystemWatcherFeature=bh});var o_=b(yc=>{"use strict";Object.defineProperty(yc,"__esModule",{value:!0});yc.ColorProviderFeature=void 0;var Kx=require("vscode"),fo=J(),Ch=de(),_h=class extends Ch.TextDocumentLanguageFeature{constructor(e){super(e,fo.DocumentColorRequest.type)}fillClientCapabilities(e){(0,Ch.ensure)((0,Ch.ensure)(e,"textDocument"),"colorProvider").dynamicRegistration=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.colorProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideColorPresentations:(i,s,o)=>{let a=this._client,c=(f,d,p)=>{let g={color:f,textDocument:a.code2ProtocolConverter.asTextDocumentIdentifier(d.document),range:a.code2ProtocolConverter.asRange(d.range)};return a.sendRequest(fo.ColorPresentationRequest.type,g,p).then(m=>p.isCancellationRequested?null:this._client.protocol2CodeConverter.asColorPresentations(m,p),m=>a.handleFailedRequest(fo.ColorPresentationRequest.type,p,m,null))},u=a.middleware;return u.provideColorPresentations?u.provideColorPresentations(i,s,o,c):c(i,s,o)},provideDocumentColors:(i,s)=>{let o=this._client,a=(u,f)=>{let d={textDocument:o.code2ProtocolConverter.asTextDocumentIdentifier(u)};return o.sendRequest(fo.DocumentColorRequest.type,d,f).then(p=>f.isCancellationRequested?null:this._client.protocol2CodeConverter.asColorInformations(p,f),p=>o.handleFailedRequest(fo.DocumentColorRequest.type,f,p,null))},c=o.middleware;return c.provideDocumentColors?c.provideDocumentColors(i,s,a):a(i,s)}};return[Kx.languages.registerColorProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};yc.ColorProviderFeature=_h});var a_=b(wc=>{"use strict";Object.defineProperty(wc,"__esModule",{value:!0});wc.ImplementationFeature=void 0;var zx=require("vscode"),Sh=J(),Dh=de(),Rh=class extends Dh.TextDocumentLanguageFeature{constructor(e){super(e,Sh.ImplementationRequest.type)}fillClientCapabilities(e){let n=(0,Dh.ensure)((0,Dh.ensure)(e,"textDocument"),"implementation");n.dynamicRegistration=!0,n.linkSupport=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.implementationProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideImplementation:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Sh.ImplementationRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asDefinitionResult(g,p),g=>a.handleFailedRequest(Sh.ImplementationRequest.type,p,g,null)),u=a.middleware;return u.provideImplementation?u.provideImplementation(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return zx.languages.registerImplementationProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};wc.ImplementationFeature=Rh});var c_=b(bc=>{"use strict";Object.defineProperty(bc,"__esModule",{value:!0});bc.TypeDefinitionFeature=void 0;var Bx=require("vscode"),Th=J(),po=de(),Ph=class extends po.TextDocumentLanguageFeature{constructor(e){super(e,Th.TypeDefinitionRequest.type)}fillClientCapabilities(e){(0,po.ensure)((0,po.ensure)(e,"textDocument"),"typeDefinition").dynamicRegistration=!0;let n=(0,po.ensure)((0,po.ensure)(e,"textDocument"),"typeDefinition");n.dynamicRegistration=!0,n.linkSupport=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.typeDefinitionProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideTypeDefinition:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Th.TypeDefinitionRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asDefinitionResult(g,p),g=>a.handleFailedRequest(Th.TypeDefinitionRequest.type,p,g,null)),u=a.middleware;return u.provideTypeDefinition?u.provideTypeDefinition(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Bx.languages.registerTypeDefinitionProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};bc.TypeDefinitionFeature=Ph});var u_=b(ds=>{"use strict";Object.defineProperty(ds,"__esModule",{value:!0});ds.WorkspaceFoldersFeature=ds.arrayDiff=void 0;var Gx=je(),Cc=require("vscode"),ho=J();function Eh(t,e){if(t!=null)return t[e]}function xh(t,e){return t.filter(n=>e.indexOf(n)<0)}ds.arrayDiff=xh;var qh=class{constructor(e){this._client=e,this._listeners=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._listeners.size>0}}get registrationType(){return ho.DidChangeWorkspaceFoldersNotification.type}fillInitializeParams(e){let n=Cc.workspace.workspaceFolders;this.initializeWithFolders(n),n===void 0?e.workspaceFolders=null:e.workspaceFolders=n.map(r=>this.asProtocol(r))}initializeWithFolders(e){this._initialFolders=e}fillClientCapabilities(e){e.workspace=e.workspace||{},e.workspace.workspaceFolders=!0}initialize(e){let n=this._client;n.onRequest(ho.WorkspaceFoldersRequest.type,s=>{let o=()=>{let c=Cc.workspace.workspaceFolders;return c===void 0?null:c.map(f=>this.asProtocol(f))},a=n.middleware.workspace;return a&&a.workspaceFolders?a.workspaceFolders(s,o):o(s)});let r=Eh(Eh(Eh(e,"workspace"),"workspaceFolders"),"changeNotifications"),i;typeof r=="string"?i=r:r===!0&&(i=Gx.generateUuid()),i&&this.register({id:i,registerOptions:void 0})}sendInitialEvent(e){let n;if(this._initialFolders&&e){let r=xh(this._initialFolders,e),i=xh(e,this._initialFolders);(i.length>0||r.length>0)&&(n=this.doSendEvent(i,r))}else this._initialFolders?n=this.doSendEvent([],this._initialFolders):e&&(n=this.doSendEvent(e,[]));n!==void 0&&n.catch(r=>{this._client.error(`Sending notification ${ho.DidChangeWorkspaceFoldersNotification.type.method} failed`,r)})}doSendEvent(e,n){let r={event:{added:e.map(i=>this.asProtocol(i)),removed:n.map(i=>this.asProtocol(i))}};return this._client.sendNotification(ho.DidChangeWorkspaceFoldersNotification.type,r)}register(e){let n=e.id,r=this._client,i=Cc.workspace.onDidChangeWorkspaceFolders(s=>{let o=u=>this.doSendEvent(u.added,u.removed),a=r.middleware.workspace;(a&&a.didChangeWorkspaceFolders?a.didChangeWorkspaceFolders(s,o):o(s)).catch(u=>{this._client.error(`Sending notification ${ho.DidChangeWorkspaceFoldersNotification.type.method} failed`,u)})});this._listeners.set(n,i),this.sendInitialEvent(Cc.workspace.workspaceFolders)}unregister(e){let n=this._listeners.get(e);n!==void 0&&(this._listeners.delete(e),n.dispose())}clear(){for(let e of this._listeners.values())e.dispose();this._listeners.clear()}asProtocol(e){return e===void 0?null:{uri:this._client.code2ProtocolConverter.asUri(e.uri),name:e.name}}};ds.WorkspaceFoldersFeature=qh});var d_=b(_c=>{"use strict";Object.defineProperty(_c,"__esModule",{value:!0});_c.FoldingRangeFeature=void 0;var l_=require("vscode"),pi=J(),go=de(),kh=class extends go.TextDocumentLanguageFeature{constructor(e){super(e,pi.FoldingRangeRequest.type)}fillClientCapabilities(e){let n=(0,go.ensure)((0,go.ensure)(e,"textDocument"),"foldingRange");n.dynamicRegistration=!0,n.rangeLimit=5e3,n.lineFoldingOnly=!0,n.foldingRangeKind={valueSet:[pi.FoldingRangeKind.Comment,pi.FoldingRangeKind.Imports,pi.FoldingRangeKind.Region]},n.foldingRange={collapsedText:!1},(0,go.ensure)((0,go.ensure)(e,"workspace"),"foldingRange").refreshSupport=!0}initialize(e,n){this._client.onRequest(pi.FoldingRangeRefreshRequest.type,async()=>{for(let s of this.getAllProviders())s.onDidChangeFoldingRange.fire()});let[r,i]=this.getRegistration(n,e.foldingRangeProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r=new l_.EventEmitter,i={onDidChangeFoldingRanges:r.event,provideFoldingRanges:(s,o,a)=>{let c=this._client,u=(d,p,g)=>{let m={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d)};return c.sendRequest(pi.FoldingRangeRequest.type,m,g).then(S=>g.isCancellationRequested?null:c.protocol2CodeConverter.asFoldingRanges(S,g),S=>c.handleFailedRequest(pi.FoldingRangeRequest.type,g,S,null))},f=c.middleware;return f.provideFoldingRanges?f.provideFoldingRanges(s,o,a,u):u(s,o,a)}};return[l_.languages.registerFoldingRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),i),{provider:i,onDidChangeFoldingRange:r}]}};_c.FoldingRangeFeature=kh});var f_=b(Sc=>{"use strict";Object.defineProperty(Sc,"__esModule",{value:!0});Sc.DeclarationFeature=void 0;var Xx=require("vscode"),Oh=J(),Ih=de(),Nh=class extends Ih.TextDocumentLanguageFeature{constructor(e){super(e,Oh.DeclarationRequest.type)}fillClientCapabilities(e){let n=(0,Ih.ensure)((0,Ih.ensure)(e,"textDocument"),"declaration");n.dynamicRegistration=!0,n.linkSupport=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.declarationProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideDeclaration:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Oh.DeclarationRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asDeclarationResult(g,p),g=>a.handleFailedRequest(Oh.DeclarationRequest.type,p,g,null)),u=a.middleware;return u.provideDeclaration?u.provideDeclaration(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Xx.languages.registerDeclarationProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};Sc.DeclarationFeature=Nh});var p_=b(Dc=>{"use strict";Object.defineProperty(Dc,"__esModule",{value:!0});Dc.SelectionRangeFeature=void 0;var Jx=require("vscode"),Fh=J(),Mh=de(),Ah=class extends Mh.TextDocumentLanguageFeature{constructor(e){super(e,Fh.SelectionRangeRequest.type)}fillClientCapabilities(e){let n=(0,Mh.ensure)((0,Mh.ensure)(e,"textDocument"),"selectionRange");n.dynamicRegistration=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.selectionRangeProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideSelectionRanges:(i,s,o)=>{let a=this._client,c=async(f,d,p)=>{let g={textDocument:a.code2ProtocolConverter.asTextDocumentIdentifier(f),positions:a.code2ProtocolConverter.asPositionsSync(d,p)};return a.sendRequest(Fh.SelectionRangeRequest.type,g,p).then(m=>p.isCancellationRequested?null:a.protocol2CodeConverter.asSelectionRanges(m,p),m=>a.handleFailedRequest(Fh.SelectionRangeRequest.type,p,m,null))},u=a.middleware;return u.provideSelectionRanges?u.provideSelectionRanges(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return Jx.languages.registerSelectionRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};Dc.SelectionRangeFeature=Ah});var g_=b(Rc=>{"use strict";Object.defineProperty(Rc,"__esModule",{value:!0});Rc.ProgressFeature=void 0;var h_=J(),Yx=lp();function Qx(t,e){return t[e]===void 0&&(t[e]=Object.create(null)),t[e]}var Lh=class{constructor(e){this._client=e,this.activeParts=new Set}getState(){return{kind:"window",id:h_.WorkDoneProgressCreateRequest.method,registrations:this.activeParts.size>0}}fillClientCapabilities(e){Qx(e,"window").workDoneProgress=!0}initialize(){let e=this._client,n=i=>{this.activeParts.delete(i)},r=i=>{this.activeParts.add(new Yx.ProgressPart(this._client,i.token,n))};e.onRequest(h_.WorkDoneProgressCreateRequest.type,r)}clear(){for(let e of this.activeParts)e.done();this.activeParts.clear()}};Rc.ProgressFeature=Lh});var m_=b(Tc=>{"use strict";Object.defineProperty(Tc,"__esModule",{value:!0});Tc.CallHierarchyFeature=void 0;var Zx=require("vscode"),hi=J(),jh=de(),$h=class{constructor(e){this.client=e,this.middleware=e.middleware}prepareCallHierarchy(e,n,r){let i=this.client,s=this.middleware,o=(a,c,u)=>{let f=i.code2ProtocolConverter.asTextDocumentPositionParams(a,c);return i.sendRequest(hi.CallHierarchyPrepareRequest.type,f,u).then(d=>u.isCancellationRequested?null:i.protocol2CodeConverter.asCallHierarchyItems(d,u),d=>i.handleFailedRequest(hi.CallHierarchyPrepareRequest.type,u,d,null))};return s.prepareCallHierarchy?s.prepareCallHierarchy(e,n,r,o):o(e,n,r)}provideCallHierarchyIncomingCalls(e,n){let r=this.client,i=this.middleware,s=(o,a)=>{let c={item:r.code2ProtocolConverter.asCallHierarchyItem(o)};return r.sendRequest(hi.CallHierarchyIncomingCallsRequest.type,c,a).then(u=>a.isCancellationRequested?null:r.protocol2CodeConverter.asCallHierarchyIncomingCalls(u,a),u=>r.handleFailedRequest(hi.CallHierarchyIncomingCallsRequest.type,a,u,null))};return i.provideCallHierarchyIncomingCalls?i.provideCallHierarchyIncomingCalls(e,n,s):s(e,n)}provideCallHierarchyOutgoingCalls(e,n){let r=this.client,i=this.middleware,s=(o,a)=>{let c={item:r.code2ProtocolConverter.asCallHierarchyItem(o)};return r.sendRequest(hi.CallHierarchyOutgoingCallsRequest.type,c,a).then(u=>a.isCancellationRequested?null:r.protocol2CodeConverter.asCallHierarchyOutgoingCalls(u,a),u=>r.handleFailedRequest(hi.CallHierarchyOutgoingCallsRequest.type,a,u,null))};return i.provideCallHierarchyOutgoingCalls?i.provideCallHierarchyOutgoingCalls(e,n,s):s(e,n)}},Hh=class extends jh.TextDocumentLanguageFeature{constructor(e){super(e,hi.CallHierarchyPrepareRequest.type)}fillClientCapabilities(e){let n=e,r=(0,jh.ensure)((0,jh.ensure)(n,"textDocument"),"callHierarchy");r.dynamicRegistration=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.callHierarchyProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=this._client,r=new $h(n);return[Zx.languages.registerCallHierarchyProvider(this._client.protocol2CodeConverter.asDocumentSelector(e.documentSelector),r),r]}};Tc.CallHierarchyFeature=Hh});var v_=b(Ec=>{"use strict";Object.defineProperty(Ec,"__esModule",{value:!0});Ec.SemanticTokensFeature=void 0;var Pc=require("vscode"),te=J(),mo=de(),e0=ln(),Uh=class extends mo.TextDocumentLanguageFeature{constructor(e){super(e,te.SemanticTokensRegistrationType.type)}fillClientCapabilities(e){let n=(0,mo.ensure)((0,mo.ensure)(e,"textDocument"),"semanticTokens");n.dynamicRegistration=!0,n.tokenTypes=[te.SemanticTokenTypes.namespace,te.SemanticTokenTypes.type,te.SemanticTokenTypes.class,te.SemanticTokenTypes.enum,te.SemanticTokenTypes.interface,te.SemanticTokenTypes.struct,te.SemanticTokenTypes.typeParameter,te.SemanticTokenTypes.parameter,te.SemanticTokenTypes.variable,te.SemanticTokenTypes.property,te.SemanticTokenTypes.enumMember,te.SemanticTokenTypes.event,te.SemanticTokenTypes.function,te.SemanticTokenTypes.method,te.SemanticTokenTypes.macro,te.SemanticTokenTypes.keyword,te.SemanticTokenTypes.modifier,te.SemanticTokenTypes.comment,te.SemanticTokenTypes.string,te.SemanticTokenTypes.number,te.SemanticTokenTypes.regexp,te.SemanticTokenTypes.operator,te.SemanticTokenTypes.decorator],n.tokenModifiers=[te.SemanticTokenModifiers.declaration,te.SemanticTokenModifiers.definition,te.SemanticTokenModifiers.readonly,te.SemanticTokenModifiers.static,te.SemanticTokenModifiers.deprecated,te.SemanticTokenModifiers.abstract,te.SemanticTokenModifiers.async,te.SemanticTokenModifiers.modification,te.SemanticTokenModifiers.documentation,te.SemanticTokenModifiers.defaultLibrary],n.formats=[te.TokenFormat.Relative],n.requests={range:!0,full:{delta:!0}},n.multilineTokenSupport=!1,n.overlappingTokenSupport=!1,n.serverCancelSupport=!0,n.augmentsSyntaxTokens=!0,(0,mo.ensure)((0,mo.ensure)(e,"workspace"),"semanticTokens").refreshSupport=!0}initialize(e,n){this._client.onRequest(te.SemanticTokensRefreshRequest.type,async()=>{for(let o of this.getAllProviders())o.onDidChangeSemanticTokensEmitter.fire()});let[i,s]=this.getRegistration(n,e.semanticTokensProvider);!i||!s||this.register({id:i,registerOptions:s})}registerLanguageProvider(e){let n=e.documentSelector,r=e0.boolean(e.full)?e.full:e.full!==void 0,i=e.full!==void 0&&typeof e.full!="boolean"&&e.full.delta===!0,s=new Pc.EventEmitter,o=r?{onDidChangeSemanticTokens:s.event,provideDocumentSemanticTokens:(g,m)=>{let S=this._client,D=S.middleware,P=(k,x)=>{let I={textDocument:S.code2ProtocolConverter.asTextDocumentIdentifier(k)};return S.sendRequest(te.SemanticTokensRequest.type,I,x).then(M=>x.isCancellationRequested?null:S.protocol2CodeConverter.asSemanticTokens(M,x),M=>S.handleFailedRequest(te.SemanticTokensRequest.type,x,M,null))};return D.provideDocumentSemanticTokens?D.provideDocumentSemanticTokens(g,m,P):P(g,m)},provideDocumentSemanticTokensEdits:i?(g,m,S)=>{let D=this._client,P=D.middleware,k=(x,I,M)=>{let Y={textDocument:D.code2ProtocolConverter.asTextDocumentIdentifier(x),previousResultId:I};return D.sendRequest(te.SemanticTokensDeltaRequest.type,Y,M).then(async B=>M.isCancellationRequested?null:te.SemanticTokens.is(B)?await D.protocol2CodeConverter.asSemanticTokens(B,M):await D.protocol2CodeConverter.asSemanticTokensEdits(B,M),B=>D.handleFailedRequest(te.SemanticTokensDeltaRequest.type,M,B,null))};return P.provideDocumentSemanticTokensEdits?P.provideDocumentSemanticTokensEdits(g,m,S,k):k(g,m,S)}:void 0}:void 0,c=e.range===!0?{provideDocumentRangeSemanticTokens:(g,m,S)=>{let D=this._client,P=D.middleware,k=(x,I,M)=>{let Y={textDocument:D.code2ProtocolConverter.asTextDocumentIdentifier(x),range:D.code2ProtocolConverter.asRange(I)};return D.sendRequest(te.SemanticTokensRangeRequest.type,Y,M).then(B=>M.isCancellationRequested?null:D.protocol2CodeConverter.asSemanticTokens(B,M),B=>D.handleFailedRequest(te.SemanticTokensRangeRequest.type,M,B,null))};return P.provideDocumentRangeSemanticTokens?P.provideDocumentRangeSemanticTokens(g,m,S,k):k(g,m,S)}}:void 0,u=[],f=this._client,d=f.protocol2CodeConverter.asSemanticTokensLegend(e.legend),p=f.protocol2CodeConverter.asDocumentSelector(n);return o!==void 0&&u.push(Pc.languages.registerDocumentSemanticTokensProvider(p,o,d)),c!==void 0&&u.push(Pc.languages.registerDocumentRangeSemanticTokensProvider(p,c,d)),[new Pc.Disposable(()=>u.forEach(g=>g.dispose())),{range:c,full:o,onDidChangeSemanticTokensEmitter:s}]}};Ec.SemanticTokensFeature=Uh});var b_=b(Vt=>{"use strict";Object.defineProperty(Vt,"__esModule",{value:!0});Vt.WillDeleteFilesFeature=Vt.WillRenameFilesFeature=Vt.WillCreateFilesFeature=Vt.DidDeleteFilesFeature=Vt.DidRenameFilesFeature=Vt.DidCreateFilesFeature=void 0;var hn=require("vscode"),t0=Ja(),rr=J(),n0=je();function y_(t,e){return t[e]===void 0&&(t[e]={}),t[e]}function r0(t,e){return t[e]}function w_(t,e,n){t[e]=n}var vo=class t{constructor(e,n,r,i,s){this._client=e,this._event=n,this._registrationType=r,this._clientCapability=i,this._serverCapability=s,this._filters=new Map}getState(){return{kind:"workspace",id:this._registrationType.method,registrations:this._filters.size>0}}filterSize(){return this._filters.size}get registrationType(){return this._registrationType}fillClientCapabilities(e){let n=y_(y_(e,"workspace"),"fileOperations");w_(n,"dynamicRegistration",!0),w_(n,this._clientCapability,!0)}initialize(e){let n=e.workspace?.fileOperations,r=n!==void 0?r0(n,this._serverCapability):void 0;if(r?.filters!==void 0)try{this.register({id:n0.generateUuid(),registerOptions:{filters:r.filters}})}catch(i){this._client.warn(`Ignoring invalid glob pattern for ${this._serverCapability} registration: ${i}`)}}register(e){this._listener||(this._listener=this._event(this.send,this));let n=e.registerOptions.filters.map(r=>{let i=new t0.Minimatch(r.pattern.glob,t.asMinimatchOptions(r.pattern.options));if(!i.makeRe())throw new Error(`Invalid pattern ${r.pattern.glob}!`);return{scheme:r.scheme,matcher:i,kind:r.pattern.matches}});this._filters.set(e.id,n)}unregister(e){this._filters.delete(e),this._filters.size===0&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._filters.clear(),this._listener&&(this._listener.dispose(),this._listener=void 0)}getFileType(e){return t.getFileType(e)}async filter(e,n){let r=await Promise.all(e.files.map(async s=>{let o=n(s),a=o.fsPath.replace(/\\/g,"/");for(let c of this._filters.values())for(let u of c)if(!(u.scheme!==void 0&&u.scheme!==o.scheme)){if(u.matcher.match(a)){if(u.kind===void 0)return!0;let f=await this.getFileType(o);if(f===void 0)return this._client.error(`Failed to determine file type for ${o.toString()}.`),!0;if(f===hn.FileType.File&&u.kind===rr.FileOperationPatternKind.file||f===hn.FileType.Directory&&u.kind===rr.FileOperationPatternKind.folder)return!0}else if(u.kind===rr.FileOperationPatternKind.folder&&await t.getFileType(o)===hn.FileType.Directory&&u.matcher.match(`${a}/`))return!0}return!1})),i=e.files.filter((s,o)=>r[o]);return{...e,files:i}}static async getFileType(e){try{return(await hn.workspace.fs.stat(e)).type}catch{return}}static asMinimatchOptions(e){let n={dot:!0};return e?.ignoreCase===!0&&(n.nocase=!0),n}},xc=class extends vo{constructor(e,n,r,i,s,o,a){super(e,n,r,i,s),this._notificationType=r,this._accessUri=o,this._createParams=a}async send(e){let n=await this.filter(e,this._accessUri);if(n.files.length){let r=async i=>this._client.sendNotification(this._notificationType,this._createParams(i));return this.doSend(n,r)}}},qc=class extends xc{constructor(){super(...arguments),this._fsPathFileTypes=new Map}async getFileType(e){let n=e.fsPath;if(this._fsPathFileTypes.has(n))return this._fsPathFileTypes.get(n);let r=await vo.getFileType(e);return r&&this._fsPathFileTypes.set(n,r),r}async cacheFileTypes(e,n){await this.filter(e,n)}clearFileTypeCache(){this._fsPathFileTypes.clear()}unregister(e){super.unregister(e),this.filterSize()===0&&this._willListener&&(this._willListener.dispose(),this._willListener=void 0)}clear(){super.clear(),this._willListener&&(this._willListener.dispose(),this._willListener=void 0)}},Vh=class extends xc{constructor(e){super(e,hn.workspace.onDidCreateFiles,rr.DidCreateFilesNotification.type,"didCreate","didCreate",n=>n,e.code2ProtocolConverter.asDidCreateFilesParams)}doSend(e,n){let r=this._client.middleware.workspace;return r?.didCreateFiles?r.didCreateFiles(e,n):n(e)}};Vt.DidCreateFilesFeature=Vh;var Wh=class extends qc{constructor(e){super(e,hn.workspace.onDidRenameFiles,rr.DidRenameFilesNotification.type,"didRename","didRename",n=>n.oldUri,e.code2ProtocolConverter.asDidRenameFilesParams)}register(e){this._willListener||(this._willListener=hn.workspace.onWillRenameFiles(this.willRename,this)),super.register(e)}willRename(e){e.waitUntil(this.cacheFileTypes(e,n=>n.oldUri))}doSend(e,n){this.clearFileTypeCache();let r=this._client.middleware.workspace;return r?.didRenameFiles?r.didRenameFiles(e,n):n(e)}};Vt.DidRenameFilesFeature=Wh;var Kh=class extends qc{constructor(e){super(e,hn.workspace.onDidDeleteFiles,rr.DidDeleteFilesNotification.type,"didDelete","didDelete",n=>n,e.code2ProtocolConverter.asDidDeleteFilesParams)}register(e){this._willListener||(this._willListener=hn.workspace.onWillDeleteFiles(this.willDelete,this)),super.register(e)}willDelete(e){e.waitUntil(this.cacheFileTypes(e,n=>n))}doSend(e,n){this.clearFileTypeCache();let r=this._client.middleware.workspace;return r?.didDeleteFiles?r.didDeleteFiles(e,n):n(e)}};Vt.DidDeleteFilesFeature=Kh;var yo=class extends vo{constructor(e,n,r,i,s,o,a){super(e,n,r,i,s),this._requestType=r,this._accessUri=o,this._createParams=a}async send(e){let n=this.waitUntil(e);e.waitUntil(n)}async waitUntil(e){let n=await this.filter(e,this._accessUri);if(n.files.length){let r=i=>this._client.sendRequest(this._requestType,this._createParams(i),i.token).then(this._client.protocol2CodeConverter.asWorkspaceEdit);return this.doSend(n,r)}else return}},zh=class extends yo{constructor(e){super(e,hn.workspace.onWillCreateFiles,rr.WillCreateFilesRequest.type,"willCreate","willCreate",n=>n,e.code2ProtocolConverter.asWillCreateFilesParams)}doSend(e,n){let r=this._client.middleware.workspace;return r?.willCreateFiles?r.willCreateFiles(e,n):n(e)}};Vt.WillCreateFilesFeature=zh;var Bh=class extends yo{constructor(e){super(e,hn.workspace.onWillRenameFiles,rr.WillRenameFilesRequest.type,"willRename","willRename",n=>n.oldUri,e.code2ProtocolConverter.asWillRenameFilesParams)}doSend(e,n){let r=this._client.middleware.workspace;return r?.willRenameFiles?r.willRenameFiles(e,n):n(e)}};Vt.WillRenameFilesFeature=Bh;var Gh=class extends yo{constructor(e){super(e,hn.workspace.onWillDeleteFiles,rr.WillDeleteFilesRequest.type,"willDelete","willDelete",n=>n,e.code2ProtocolConverter.asWillDeleteFilesParams)}doSend(e,n){let r=this._client.middleware.workspace;return r?.willDeleteFiles?r.willDeleteFiles(e,n):n(e)}};Vt.WillDeleteFilesFeature=Gh});var C_=b(kc=>{"use strict";Object.defineProperty(kc,"__esModule",{value:!0});kc.LinkedEditingFeature=void 0;var i0=require("vscode"),Xh=J(),Jh=de(),Yh=class extends Jh.TextDocumentLanguageFeature{constructor(e){super(e,Xh.LinkedEditingRangeRequest.type)}fillClientCapabilities(e){let n=(0,Jh.ensure)((0,Jh.ensure)(e,"textDocument"),"linkedEditingRange");n.dynamicRegistration=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.linkedEditingRangeProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r={provideLinkedEditingRanges:(i,s,o)=>{let a=this._client,c=(f,d,p)=>a.sendRequest(Xh.LinkedEditingRangeRequest.type,a.code2ProtocolConverter.asTextDocumentPositionParams(f,d),p).then(g=>p.isCancellationRequested?null:a.protocol2CodeConverter.asLinkedEditingRanges(g,p),g=>a.handleFailedRequest(Xh.LinkedEditingRangeRequest.type,p,g,null)),u=a.middleware;return u.provideLinkedEditingRange?u.provideLinkedEditingRange(i,s,o,c):c(i,s,o)}};return[this.registerProvider(n,r),r]}registerProvider(e,n){return i0.languages.registerLinkedEditingRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};kc.LinkedEditingFeature=Yh});var __=b(Oc=>{"use strict";Object.defineProperty(Oc,"__esModule",{value:!0});Oc.TypeHierarchyFeature=void 0;var s0=require("vscode"),gi=J(),Qh=de(),Zh=class{constructor(e){this.client=e,this.middleware=e.middleware}prepareTypeHierarchy(e,n,r){let i=this.client,s=this.middleware,o=(a,c,u)=>{let f=i.code2ProtocolConverter.asTextDocumentPositionParams(a,c);return i.sendRequest(gi.TypeHierarchyPrepareRequest.type,f,u).then(d=>u.isCancellationRequested?null:i.protocol2CodeConverter.asTypeHierarchyItems(d,u),d=>i.handleFailedRequest(gi.TypeHierarchyPrepareRequest.type,u,d,null))};return s.prepareTypeHierarchy?s.prepareTypeHierarchy(e,n,r,o):o(e,n,r)}provideTypeHierarchySupertypes(e,n){let r=this.client,i=this.middleware,s=(o,a)=>{let c={item:r.code2ProtocolConverter.asTypeHierarchyItem(o)};return r.sendRequest(gi.TypeHierarchySupertypesRequest.type,c,a).then(u=>a.isCancellationRequested?null:r.protocol2CodeConverter.asTypeHierarchyItems(u,a),u=>r.handleFailedRequest(gi.TypeHierarchySupertypesRequest.type,a,u,null))};return i.provideTypeHierarchySupertypes?i.provideTypeHierarchySupertypes(e,n,s):s(e,n)}provideTypeHierarchySubtypes(e,n){let r=this.client,i=this.middleware,s=(o,a)=>{let c={item:r.code2ProtocolConverter.asTypeHierarchyItem(o)};return r.sendRequest(gi.TypeHierarchySubtypesRequest.type,c,a).then(u=>a.isCancellationRequested?null:r.protocol2CodeConverter.asTypeHierarchyItems(u,a),u=>r.handleFailedRequest(gi.TypeHierarchySubtypesRequest.type,a,u,null))};return i.provideTypeHierarchySubtypes?i.provideTypeHierarchySubtypes(e,n,s):s(e,n)}},eg=class extends Qh.TextDocumentLanguageFeature{constructor(e){super(e,gi.TypeHierarchyPrepareRequest.type)}fillClientCapabilities(e){let n=(0,Qh.ensure)((0,Qh.ensure)(e,"textDocument"),"typeHierarchy");n.dynamicRegistration=!0}initialize(e,n){let[r,i]=this.getRegistration(n,e.typeHierarchyProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=this._client,r=new Zh(n);return[s0.languages.registerTypeHierarchyProvider(n.protocol2CodeConverter.asDocumentSelector(e.documentSelector),r),r]}};Oc.TypeHierarchyFeature=eg});var D_=b(Nc=>{"use strict";Object.defineProperty(Nc,"__esModule",{value:!0});Nc.InlineValueFeature=void 0;var S_=require("vscode"),Ic=J(),wo=de(),tg=class extends wo.TextDocumentLanguageFeature{constructor(e){super(e,Ic.InlineValueRequest.type)}fillClientCapabilities(e){(0,wo.ensure)((0,wo.ensure)(e,"textDocument"),"inlineValue").dynamicRegistration=!0,(0,wo.ensure)((0,wo.ensure)(e,"workspace"),"inlineValue").refreshSupport=!0}initialize(e,n){this._client.onRequest(Ic.InlineValueRefreshRequest.type,async()=>{for(let s of this.getAllProviders())s.onDidChangeInlineValues.fire()});let[r,i]=this.getRegistration(n,e.inlineValueProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r=new S_.EventEmitter,i={onDidChangeInlineValues:r.event,provideInlineValues:(s,o,a,c)=>{let u=this._client,f=(p,g,m,S)=>{let D={textDocument:u.code2ProtocolConverter.asTextDocumentIdentifier(p),range:u.code2ProtocolConverter.asRange(g),context:u.code2ProtocolConverter.asInlineValueContext(m)};return u.sendRequest(Ic.InlineValueRequest.type,D,S).then(P=>S.isCancellationRequested?null:u.protocol2CodeConverter.asInlineValues(P,S),P=>u.handleFailedRequest(Ic.InlineValueRequest.type,S,P,null))},d=u.middleware;return d.provideInlineValues?d.provideInlineValues(s,o,a,c,f):f(s,o,a,c)}};return[this.registerProvider(n,i),{provider:i,onDidChangeInlineValues:r}]}registerProvider(e,n){return S_.languages.registerInlineValuesProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};Nc.InlineValueFeature=tg});var T_=b(Fc=>{"use strict";Object.defineProperty(Fc,"__esModule",{value:!0});Fc.InlayHintsFeature=void 0;var R_=require("vscode"),fs=J(),bo=de(),ng=class extends bo.TextDocumentLanguageFeature{constructor(e){super(e,fs.InlayHintRequest.type)}fillClientCapabilities(e){let n=(0,bo.ensure)((0,bo.ensure)(e,"textDocument"),"inlayHint");n.dynamicRegistration=!0,n.resolveSupport={properties:["tooltip","textEdits","label.tooltip","label.location","label.command"]},(0,bo.ensure)((0,bo.ensure)(e,"workspace"),"inlayHint").refreshSupport=!0}initialize(e,n){this._client.onRequest(fs.InlayHintRefreshRequest.type,async()=>{for(let s of this.getAllProviders())s.onDidChangeInlayHints.fire()});let[r,i]=this.getRegistration(n,e.inlayHintProvider);!r||!i||this.register({id:r,registerOptions:i})}registerLanguageProvider(e){let n=e.documentSelector,r=new R_.EventEmitter,i={onDidChangeInlayHints:r.event,provideInlayHints:(s,o,a)=>{let c=this._client,u=async(d,p,g)=>{let m={textDocument:c.code2ProtocolConverter.asTextDocumentIdentifier(d),range:c.code2ProtocolConverter.asRange(p)};try{let S=await c.sendRequest(fs.InlayHintRequest.type,m,g);return g.isCancellationRequested?null:c.protocol2CodeConverter.asInlayHints(S,g)}catch(S){return c.handleFailedRequest(fs.InlayHintRequest.type,g,S,null)}},f=c.middleware;return f.provideInlayHints?f.provideInlayHints(s,o,a,u):u(s,o,a)}};return i.resolveInlayHint=e.resolveProvider===!0?(s,o)=>{let a=this._client,c=async(f,d)=>{try{let p=await a.sendRequest(fs.InlayHintResolveRequest.type,a.code2ProtocolConverter.asInlayHint(f),d);if(d.isCancellationRequested)return null;let g=a.protocol2CodeConverter.asInlayHint(p,d);return d.isCancellationRequested?null:g}catch(p){return a.handleFailedRequest(fs.InlayHintResolveRequest.type,d,p,null)}},u=a.middleware;return u.resolveInlayHint?u.resolveInlayHint(s,o,c):c(s,o)}:void 0,[this.registerProvider(n,i),{provider:i,onDidChangeInlayHints:r}]}registerProvider(e,n){return R_.languages.registerInlayHintsProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),n)}};Fc.InlayHintsFeature=ng});var P_=b(Mc=>{"use strict";Object.defineProperty(Mc,"__esModule",{value:!0});Mc.InlineCompletionItemFeature=void 0;var o0=require("vscode"),rg=J(),ig=de(),a0=je(),sg=class extends ig.TextDocumentLanguageFeature{constructor(e){super(e,rg.InlineCompletionRequest.type)}fillClientCapabilities(e){let n=(0,ig.ensure)((0,ig.ensure)(e,"textDocument"),"inlineCompletion");n.dynamicRegistration=!0}initialize(e,n){let r=this.getRegistrationOptions(n,e.inlineCompletionProvider);r&&this.register({id:a0.generateUuid(),registerOptions:r})}registerLanguageProvider(e){let n=e.documentSelector,r={provideInlineCompletionItems:(i,s,o,a)=>{let c=this._client,u=this._client.middleware,f=(d,p,g,m)=>c.sendRequest(rg.InlineCompletionRequest.type,c.code2ProtocolConverter.asInlineCompletionParams(d,p,g),m).then(S=>m.isCancellationRequested?null:c.protocol2CodeConverter.asInlineCompletionResult(S,m),S=>c.handleFailedRequest(rg.InlineCompletionRequest.type,m,S,null));return u.provideInlineCompletionItems?u.provideInlineCompletionItems(i,s,o,a,f):f(i,s,o,a)}};return[o0.languages.registerInlineCompletionItemProvider(this._client.protocol2CodeConverter.asDocumentSelector(n),r),r]}};Mc.InlineCompletionItemFeature=sg});var dg=b(ct=>{"use strict";Object.defineProperty(ct,"__esModule",{value:!0});ct.ProposedFeatures=ct.BaseLanguageClient=ct.MessageTransports=ct.SuspendMode=ct.State=ct.CloseAction=ct.ErrorAction=ct.RevealOutputChannelOn=void 0;var be=require("vscode"),O=J(),c0=rC(),u0=oC(),kn=ln(),Ac=Va(),E_=je(),l0=lp(),qn=de(),d0=qp(),x_=AC(),q_=jC(),ps=$C(),f0=HC(),p0=UC(),h0=VC(),g0=KC(),m0=zC(),v0=th(),y0=GC(),w0=XC(),b0=JC(),C0=QC(),og=ZC(),_0=t_(),S0=n_(),D0=i_(),R0=s_(),T0=o_(),P0=a_(),E0=c_(),x0=u_(),q0=d_(),k0=f_(),O0=p_(),I0=g_(),N0=m_(),F0=v_(),hs=b_(),M0=C_(),A0=__(),L0=D_(),j0=T_(),$0=P_(),mi;(function(t){t[t.Debug=0]="Debug",t[t.Info=1]="Info",t[t.Warn=2]="Warn",t[t.Error=3]="Error",t[t.Never=4]="Never"})(mi||(ct.RevealOutputChannelOn=mi={}));var _o;(function(t){t[t.Continue=1]="Continue",t[t.Shutdown=2]="Shutdown"})(_o||(ct.ErrorAction=_o={}));var Fr;(function(t){t[t.DoNotRestart=1]="DoNotRestart",t[t.Restart=2]="Restart"})(Fr||(ct.CloseAction=Fr={}));var Co;(function(t){t[t.Stopped=1]="Stopped",t[t.Starting=3]="Starting",t[t.Running=2]="Running"})(Co||(ct.State=Co={}));var k_;(function(t){t.off="off",t.on="on"})(k_||(ct.SuspendMode=k_={}));var ag;(function(t){function e(n){return n==null?!1:typeof n=="boolean"||typeof n=="object"&&n!==null&&kn.stringArray(n.enabledCommands)?n:!1}t.sanitizeIsTrusted=e})(ag||(ag={}));var cg=class{constructor(e,n){this.client=e,this.maxRestartCount=n,this.restarts=[]}error(e,n,r){return r&&r<=3?{action:_o.Continue}:{action:_o.Shutdown}}closed(){return this.restarts.push(Date.now()),this.restarts.length<=this.maxRestartCount?{action:Fr.Restart}:this.restarts[this.restarts.length-1]-this.restarts[0]<=3*60*1e3?{action:Fr.DoNotRestart,message:`The ${this.client.name} server crashed ${this.maxRestartCount+1} times in the last 3 minutes. The server will not be restarted. See the output for more information.`}:(this.restarts.shift(),{action:Fr.Restart})}},se;(function(t){t.Initial="initial",t.Starting="starting",t.StartFailed="startFailed",t.Running="running",t.Stopping="stopping",t.Stopped="stopped"})(se||(se={}));var O_;(function(t){function e(n){return n&&O.MessageReader.is(n.reader)&&O.MessageWriter.is(n.writer)}t.is=e})(O_||(ct.MessageTransports=O_={}));var So=class t{constructor(e,n,r){this._traceFormat=O.TraceFormat.Text,this._diagnosticQueue=new Map,this._diagnosticQueueState={state:"idle"},this._features=[],this._dynamicFeatures=new Map,this.workspaceEditLock=new Ac.Semaphore(1),this._id=e,this._name=n,r=r||{};let i={isTrusted:!1,supportHtml:!1};r.markdown!==void 0&&(i.isTrusted=ag.sanitizeIsTrusted(r.markdown.isTrusted),i.supportHtml=r.markdown.supportHtml===!0),this._clientOptions={documentSelector:r.documentSelector??[],synchronize:r.synchronize??{},diagnosticCollectionName:r.diagnosticCollectionName,outputChannelName:r.outputChannelName??this._name,revealOutputChannelOn:r.revealOutputChannelOn??mi.Error,stdioEncoding:r.stdioEncoding??"utf8",initializationOptions:r.initializationOptions,initializationFailedHandler:r.initializationFailedHandler,progressOnInitialization:!!r.progressOnInitialization,errorHandler:r.errorHandler??this.createDefaultErrorHandler(r.connectionOptions?.maxRestartCount),middleware:r.middleware??{},uriConverters:r.uriConverters,workspaceFolder:r.workspaceFolder,connectionOptions:r.connectionOptions,markdown:i,diagnosticPullOptions:r.diagnosticPullOptions??{onChange:!0,onSave:!1},notebookDocumentOptions:r.notebookDocumentOptions??{}},this._clientOptions.synchronize=this._clientOptions.synchronize||{},this._state=se.Initial,this._ignoredRegistrations=new Set,this._listeners=[],this._notificationHandlers=new Map,this._pendingNotificationHandlers=new Map,this._notificationDisposables=new Map,this._requestHandlers=new Map,this._pendingRequestHandlers=new Map,this._requestDisposables=new Map,this._progressHandlers=new Map,this._pendingProgressHandlers=new Map,this._progressDisposables=new Map,this._connection=void 0,this._initializeResult=void 0,r.outputChannel?(this._outputChannel=r.outputChannel,this._disposeOutputChannel=!1):(this._outputChannel=void 0,this._disposeOutputChannel=!0),this._traceOutputChannel=r.traceOutputChannel,this._diagnostics=void 0,this._pendingOpenNotifications=new Set,this._pendingChangeSemaphore=new Ac.Semaphore(1),this._pendingChangeDelayer=new Ac.Delayer(250),this._fileEvents=[],this._fileEventDelayer=new Ac.Delayer(250),this._onStop=void 0,this._telemetryEmitter=new O.Emitter,this._stateChangeEmitter=new O.Emitter,this._trace=O.Trace.Off,this._tracer={log:(s,o)=>{kn.string(s)?this.logTrace(s,o):this.logObjectTrace(s)}},this._c2p=c0.createConverter(r.uriConverters?r.uriConverters.code2Protocol:void 0),this._p2c=u0.createConverter(r.uriConverters?r.uriConverters.protocol2Code:void 0,this._clientOptions.markdown.isTrusted,this._clientOptions.markdown.supportHtml),this._syncedDocuments=new Map,this.registerBuiltinFeatures()}get name(){return this._name}get middleware(){return this._clientOptions.middleware??Object.create(null)}get clientOptions(){return this._clientOptions}get protocol2CodeConverter(){return this._p2c}get code2ProtocolConverter(){return this._c2p}get onTelemetry(){return this._telemetryEmitter.event}get onDidChangeState(){return this._stateChangeEmitter.event}get outputChannel(){return this._outputChannel||(this._outputChannel=be.window.createOutputChannel(this._clientOptions.outputChannelName?this._clientOptions.outputChannelName:this._name)),this._outputChannel}get traceOutputChannel(){return this._traceOutputChannel?this._traceOutputChannel:this.outputChannel}get diagnostics(){return this._diagnostics}get state(){return this.getPublicState()}get $state(){return this._state}set $state(e){let n=this.getPublicState();this._state=e;let r=this.getPublicState();r!==n&&this._stateChangeEmitter.fire({oldState:n,newState:r})}getPublicState(){switch(this.$state){case se.Starting:return Co.Starting;case se.Running:return Co.Running;default:return Co.Stopped}}get initializeResult(){return this._initializeResult}async sendRequest(e,...n){if(this.$state===se.StartFailed||this.$state===se.Stopping||this.$state===se.Stopped)return Promise.reject(new O.ResponseError(O.ErrorCodes.ConnectionInactive,"Client is not running"));let r=await this.$start();this._didChangeTextDocumentFeature.syncKind===O.TextDocumentSyncKind.Full&&await this.sendPendingFullTextDocumentChanges(r);let i=this._clientOptions.middleware?.sendRequest;if(i!==void 0){let s,o;return n.length===1?O.CancellationToken.is(n[0])?o=n[0]:s=n[0]:n.length===2&&(s=n[0],o=n[1]),i(e,s,o,(a,c,u)=>{let f=[];return c!==void 0&&f.push(c),u!==void 0&&f.push(u),r.sendRequest(a,...f)})}else return r.sendRequest(e,...n)}onRequest(e,n){let r=typeof e=="string"?e:e.method;this._requestHandlers.set(r,n);let i=this.activeConnection(),s;return i!==void 0?(this._requestDisposables.set(r,i.onRequest(e,n)),s={dispose:()=>{let o=this._requestDisposables.get(r);o!==void 0&&(o.dispose(),this._requestDisposables.delete(r))}}):(this._pendingRequestHandlers.set(r,n),s={dispose:()=>{this._pendingRequestHandlers.delete(r);let o=this._requestDisposables.get(r);o!==void 0&&(o.dispose(),this._requestDisposables.delete(r))}}),{dispose:()=>{this._requestHandlers.delete(r),s.dispose()}}}async sendNotification(e,n){if(this.$state===se.StartFailed||this.$state===se.Stopping||this.$state===se.Stopped)return Promise.reject(new O.ResponseError(O.ErrorCodes.ConnectionInactive,"Client is not running"));let r=this._didChangeTextDocumentFeature.syncKind===O.TextDocumentSyncKind.Full,i;r&&typeof e!="string"&&e.method===O.DidOpenTextDocumentNotification.method&&(i=n?.textDocument.uri,this._pendingOpenNotifications.add(i));let s=await this.$start();r&&await this.sendPendingFullTextDocumentChanges(s),i!==void 0&&this._pendingOpenNotifications.delete(i);let o=this._clientOptions.middleware?.sendNotification;return o?o(e,s.sendNotification.bind(s),n):s.sendNotification(e,n)}onNotification(e,n){let r=typeof e=="string"?e:e.method;this._notificationHandlers.set(r,n);let i=this.activeConnection(),s;return i!==void 0?(this._notificationDisposables.set(r,i.onNotification(e,n)),s={dispose:()=>{let o=this._notificationDisposables.get(r);o!==void 0&&(o.dispose(),this._notificationDisposables.delete(r))}}):(this._pendingNotificationHandlers.set(r,n),s={dispose:()=>{this._pendingNotificationHandlers.delete(r);let o=this._notificationDisposables.get(r);o!==void 0&&(o.dispose(),this._notificationDisposables.delete(r))}}),{dispose:()=>{this._notificationHandlers.delete(r),s.dispose()}}}async sendProgress(e,n,r){if(this.$state===se.StartFailed||this.$state===se.Stopping||this.$state===se.Stopped)return Promise.reject(new O.ResponseError(O.ErrorCodes.ConnectionInactive,"Client is not running"));try{return(await this.$start()).sendProgress(e,n,r)}catch(i){throw this.error(`Sending progress for token ${n} failed.`,i),i}}onProgress(e,n,r){this._progressHandlers.set(n,{type:e,handler:r});let i=this.activeConnection(),s,o=this._clientOptions.middleware?.handleWorkDoneProgress,a=O.WorkDoneProgress.is(e)&&o!==void 0?c=>{o(n,c,()=>r(c))}:r;return i!==void 0?(this._progressDisposables.set(n,i.onProgress(e,n,a)),s={dispose:()=>{let c=this._progressDisposables.get(n);c!==void 0&&(c.dispose(),this._progressDisposables.delete(n))}}):(this._pendingProgressHandlers.set(n,{type:e,handler:r}),s={dispose:()=>{this._pendingProgressHandlers.delete(n);let c=this._progressDisposables.get(n);c!==void 0&&(c.dispose(),this._progressDisposables.delete(n))}}),{dispose:()=>{this._progressHandlers.delete(n),s.dispose()}}}createDefaultErrorHandler(e){if(e!==void 0&&e<0)throw new Error(`Invalid maxRestartCount: ${e}`);return new cg(this,e??4)}async setTrace(e){this._trace=e;let n=this.activeConnection();n!==void 0&&await n.trace(this._trace,this._tracer,{sendNotification:!1,traceFormat:this._traceFormat})}data2String(e){if(e instanceof O.ResponseError){let n=e;return`  Message: ${n.message}
  Code: ${n.code} ${n.data?`
`+n.data.toString():""}`}return e instanceof Error?kn.string(e.stack)?e.stack:e.message:kn.string(e)?e:e.toString()}debug(e,n,r=!0){this.logOutputMessage(O.MessageType.Debug,mi.Debug,"Debug",e,n,r)}info(e,n,r=!0){this.logOutputMessage(O.MessageType.Info,mi.Info,"Info",e,n,r)}warn(e,n,r=!0){this.logOutputMessage(O.MessageType.Warning,mi.Warn,"Warn",e,n,r)}error(e,n,r=!0){this.logOutputMessage(O.MessageType.Error,mi.Error,"Error",e,n,r)}logOutputMessage(e,n,r,i,s,o){this.outputChannel.appendLine(`[${r.padEnd(5)} - ${new Date().toLocaleTimeString()}] ${i}`),s!=null&&this.outputChannel.appendLine(this.data2String(s)),(o==="force"||o&&this._clientOptions.revealOutputChannelOn<=n)&&this.showNotificationMessage(e,i)}showNotificationMessage(e,n){n=n??"A request has failed. See the output for more information.",(e===O.MessageType.Error?be.window.showErrorMessage:e===O.MessageType.Warning?be.window.showWarningMessage:be.window.showInformationMessage)(n,"Go to output").then(i=>{i!==void 0&&this.outputChannel.show(!0)})}logTrace(e,n){this.traceOutputChannel.appendLine(`[Trace - ${new Date().toLocaleTimeString()}] ${e}`),n&&this.traceOutputChannel.appendLine(this.data2String(n))}logObjectTrace(e){e.isLSPMessage&&e.type?this.traceOutputChannel.append(`[LSP   - ${new Date().toLocaleTimeString()}] `):this.traceOutputChannel.append(`[Trace - ${new Date().toLocaleTimeString()}] `),e&&this.traceOutputChannel.appendLine(`${JSON.stringify(e)}`)}needsStart(){return this.$state===se.Initial||this.$state===se.Stopping||this.$state===se.Stopped}needsStop(){return this.$state===se.Starting||this.$state===se.Running}activeConnection(){return this.$state===se.Running&&this._connection!==void 0?this._connection:void 0}isRunning(){return this.$state===se.Running}async start(){if(this._disposed==="disposing"||this._disposed==="disposed")throw new Error("Client got disposed and can't be restarted.");if(this.$state===se.Stopping)throw new Error("Client is currently stopping. Can only restart a full stopped client");if(this._onStart!==void 0)return this._onStart;let[e,n,r]=this.createOnStartPromise();this._onStart=e,this._diagnostics===void 0&&(this._diagnostics=this._clientOptions.diagnosticCollectionName?be.languages.createDiagnosticCollection(this._clientOptions.diagnosticCollectionName):be.languages.createDiagnosticCollection());for(let[i,s]of this._notificationHandlers)this._pendingNotificationHandlers.has(i)||this._pendingNotificationHandlers.set(i,s);for(let[i,s]of this._requestHandlers)this._pendingRequestHandlers.has(i)||this._pendingRequestHandlers.set(i,s);for(let[i,s]of this._progressHandlers)this._pendingProgressHandlers.has(i)||this._pendingProgressHandlers.set(i,s);this.$state=se.Starting;try{let i=await this.createConnection();i.onNotification(O.LogMessageNotification.type,s=>{switch(s.type){case O.MessageType.Error:this.error(s.message,void 0,!1);break;case O.MessageType.Warning:this.warn(s.message,void 0,!1);break;case O.MessageType.Info:this.info(s.message,void 0,!1);break;case O.MessageType.Debug:this.debug(s.message,void 0,!1);break;default:this.outputChannel.appendLine(s.message)}}),i.onNotification(O.ShowMessageNotification.type,s=>{switch(s.type){case O.MessageType.Error:be.window.showErrorMessage(s.message);break;case O.MessageType.Warning:be.window.showWarningMessage(s.message);break;case O.MessageType.Info:be.window.showInformationMessage(s.message);break;default:be.window.showInformationMessage(s.message)}}),i.onRequest(O.ShowMessageRequest.type,s=>{let o;switch(s.type){case O.MessageType.Error:o=be.window.showErrorMessage;break;case O.MessageType.Warning:o=be.window.showWarningMessage;break;case O.MessageType.Info:o=be.window.showInformationMessage;break;default:o=be.window.showInformationMessage}let a=s.actions||[];return o(s.message,...a)}),i.onNotification(O.TelemetryEventNotification.type,s=>{this._telemetryEmitter.fire(s)}),i.onRequest(O.ShowDocumentRequest.type,async s=>{let o=async c=>{let u=this.protocol2CodeConverter.asUri(c.uri);try{if(c.external===!0)return{success:await be.env.openExternal(u)};{let f={};return c.selection!==void 0&&(f.selection=this.protocol2CodeConverter.asRange(c.selection)),c.takeFocus===void 0||c.takeFocus===!1?f.preserveFocus=!0:c.takeFocus===!0&&(f.preserveFocus=!1),await be.window.showTextDocument(u,f),{success:!0}}}catch{return{success:!1}}},a=this._clientOptions.middleware.window?.showDocument;return a!==void 0?a(s,o):o(s)}),i.listen(),await this.initialize(i),n()}catch(i){this.$state=se.StartFailed,this.error(`${this._name} client: couldn't create connection to server.`,i,"force"),r(i)}return this._onStart}createOnStartPromise(){let e,n;return[new Promise((i,s)=>{e=i,n=s}),e,n]}async initialize(e){this.refreshTrace(e,!1);let n=this._clientOptions.initializationOptions,[r,i]=this._clientOptions.workspaceFolder!==void 0?[this._clientOptions.workspaceFolder.uri.fsPath,[{uri:this._c2p.asUri(this._clientOptions.workspaceFolder.uri),name:this._clientOptions.workspaceFolder.name}]]:[this._clientGetRootPath(),null],s={processId:null,clientInfo:{name:be.env.appName,version:be.version},locale:this.getLocale(),rootPath:r||null,rootUri:r?this._c2p.asUri(be.Uri.file(r)):null,capabilities:this.computeClientCapabilities(),initializationOptions:kn.func(n)?n():n,trace:O.Trace.toString(this._trace),workspaceFolders:i};if(this.fillInitializeParams(s),this._clientOptions.progressOnInitialization){let o=E_.generateUuid(),a=new l0.ProgressPart(e,o);s.workDoneToken=o;try{let c=await this.doInitialize(e,s);return a.done(),c}catch(c){throw a.cancel(),c}}else return this.doInitialize(e,s)}async doInitialize(e,n){try{let r=await e.initialize(n);if(r.capabilities.positionEncoding!==void 0&&r.capabilities.positionEncoding!==O.PositionEncodingKind.UTF16)throw new Error(`Unsupported position encoding (${r.capabilities.positionEncoding}) received from server ${this.name}`);this._initializeResult=r,this.$state=se.Running;let i;kn.number(r.capabilities.textDocumentSync)?r.capabilities.textDocumentSync===O.TextDocumentSyncKind.None?i={openClose:!1,change:O.TextDocumentSyncKind.None,save:void 0}:i={openClose:!0,change:r.capabilities.textDocumentSync,save:{includeText:!1}}:r.capabilities.textDocumentSync!==void 0&&r.capabilities.textDocumentSync!==null&&(i=r.capabilities.textDocumentSync),this._capabilities=Object.assign({},r.capabilities,{resolvedTextDocumentSync:i}),e.onNotification(O.PublishDiagnosticsNotification.type,s=>this.handleDiagnostics(s)),e.onRequest(O.RegistrationRequest.type,s=>this.handleRegistrationRequest(s)),e.onRequest("client/registerFeature",s=>this.handleRegistrationRequest(s)),e.onRequest(O.UnregistrationRequest.type,s=>this.handleUnregistrationRequest(s)),e.onRequest("client/unregisterFeature",s=>this.handleUnregistrationRequest(s)),e.onRequest(O.ApplyWorkspaceEditRequest.type,s=>this.handleApplyWorkspaceEdit(s));for(let[s,o]of this._pendingNotificationHandlers)this._notificationDisposables.set(s,e.onNotification(s,o));this._pendingNotificationHandlers.clear();for(let[s,o]of this._pendingRequestHandlers)this._requestDisposables.set(s,e.onRequest(s,o));this._pendingRequestHandlers.clear();for(let[s,o]of this._pendingProgressHandlers)this._progressDisposables.set(s,e.onProgress(o.type,s,o.handler));return this._pendingProgressHandlers.clear(),await e.sendNotification(O.InitializedNotification.type,{}),this.hookFileEvents(e),this.hookConfigurationChanged(e),this.initializeFeatures(e),r}catch(r){throw this._clientOptions.initializationFailedHandler?this._clientOptions.initializationFailedHandler(r)?this.initialize(e):this.stop():r instanceof O.ResponseError&&r.data&&r.data.retry?be.window.showErrorMessage(r.message,{title:"Retry",id:"retry"}).then(i=>{i&&i.id==="retry"?this.initialize(e):this.stop()}):(r&&r.message&&be.window.showErrorMessage(r.message),this.error("Server initialization failed.",r),this.stop()),r}}_clientGetRootPath(){let e=be.workspace.workspaceFolders;if(!e||e.length===0)return;let n=e[0];if(n.uri.scheme==="file")return n.uri.fsPath}stop(e=2e3){return this.shutdown("stop",e)}dispose(e=2e3){try{return this._disposed="disposing",this.stop(e)}finally{this._disposed="disposed"}}async shutdown(e,n){if(this.$state===se.Stopped||this.$state===se.Initial)return;if(this.$state===se.Stopping){if(this._onStop!==void 0)return this._onStop;throw new Error("Client is stopping but no stop promise available.")}let r=this.activeConnection();if(r===void 0||this.$state!==se.Running)throw new Error(`Client is not running and can't be stopped. It's current state is: ${this.$state}`);this._initializeResult=void 0,this.$state=se.Stopping,this.cleanUp(e);let i=new Promise(o=>{(0,O.RAL)().timer.setTimeout(o,n)}),s=(async o=>(await o.shutdown(),await o.exit(),o))(r);return this._onStop=Promise.race([i,s]).then(o=>{if(o!==void 0)o.end(),o.dispose();else throw this.error("Stopping server timed out",void 0,!1),new Error("Stopping the server timed out")},o=>{throw this.error("Stopping server failed",o,!1),o}).finally(()=>{this.$state=se.Stopped,e==="stop"&&this.cleanUpChannel(),this._onStart=void 0,this._onStop=void 0,this._connection=void 0,this._ignoredRegistrations.clear()})}cleanUp(e){this._fileEvents=[],this._fileEventDelayer.cancel();let n=this._listeners.splice(0,this._listeners.length);for(let r of n)r.dispose();this._syncedDocuments&&this._syncedDocuments.clear();for(let r of Array.from(this._features.entries()).map(i=>i[1]).reverse())r.clear();e==="stop"&&this._diagnostics!==void 0&&(this._diagnostics.dispose(),this._diagnostics=void 0),this._idleInterval!==void 0&&(this._idleInterval.dispose(),this._idleInterval=void 0)}cleanUpChannel(){this._outputChannel!==void 0&&this._disposeOutputChannel&&(this._outputChannel.dispose(),this._outputChannel=void 0)}notifyFileEvent(e){let n=this;async function r(s){return n._fileEvents.push(s),n._fileEventDelayer.trigger(async()=>{await n.sendNotification(O.DidChangeWatchedFilesNotification.type,{changes:n._fileEvents}),n._fileEvents=[]})}let i=this.clientOptions.middleware?.workspace;(i?.didChangeWatchedFile?i.didChangeWatchedFile(e,r):r(e)).catch(s=>{n.error("Notify file events failed.",s)})}async sendPendingFullTextDocumentChanges(e){return this._pendingChangeSemaphore.lock(async()=>{try{let n=this._didChangeTextDocumentFeature.getPendingDocumentChanges(this._pendingOpenNotifications);if(n.length===0)return;for(let r of n){let i=this.code2ProtocolConverter.asChangeTextDocumentParams(r);await e.sendNotification(O.DidChangeTextDocumentNotification.type,i),this._didChangeTextDocumentFeature.notificationSent(r,O.DidChangeTextDocumentNotification.type,i)}}catch(n){throw this.error("Sending pending changes failed",n,!1),n}})}triggerPendingChangeDelivery(){this._pendingChangeDelayer.trigger(async()=>{let e=this.activeConnection();if(e===void 0){this.triggerPendingChangeDelivery();return}await this.sendPendingFullTextDocumentChanges(e)}).catch(e=>this.error("Delivering pending changes failed",e,!1))}handleDiagnostics(e){if(!this._diagnostics)return;let n=e.uri;this._diagnosticQueueState.state==="busy"&&this._diagnosticQueueState.document===n&&this._diagnosticQueueState.tokenSource.cancel(),this._diagnosticQueue.set(e.uri,e.diagnostics),this.triggerDiagnosticQueue()}triggerDiagnosticQueue(){(0,O.RAL)().timer.setImmediate(()=>{this.workDiagnosticQueue()})}workDiagnosticQueue(){if(this._diagnosticQueueState.state==="busy")return;let e=this._diagnosticQueue.entries().next();if(e.done===!0)return;let[n,r]=e.value;this._diagnosticQueue.delete(n);let i=new be.CancellationTokenSource;this._diagnosticQueueState={state:"busy",document:n,tokenSource:i},this._p2c.asDiagnostics(r,i.token).then(s=>{if(!i.token.isCancellationRequested){let o=this._p2c.asUri(n),a=this.clientOptions.middleware;a.handleDiagnostics?a.handleDiagnostics(o,s,(c,u)=>this.setDiagnostics(c,u)):this.setDiagnostics(o,s)}}).finally(()=>{this._diagnosticQueueState={state:"idle"},this.triggerDiagnosticQueue()})}setDiagnostics(e,n){this._diagnostics&&this._diagnostics.set(e,n)}getLocale(){return be.env.language}async $start(){if(this.$state===se.StartFailed)throw new Error("Previous start failed. Can't restart server.");await this.start();let e=this.activeConnection();if(e===void 0)throw new Error("Starting server failed");return e}async createConnection(){let e=(i,s,o)=>{this.handleConnectionError(i,s,o).catch(a=>this.error("Handling connection error failed",a))},n=()=>{this.handleConnectionClosed().catch(i=>this.error("Handling connection close failed",i))},r=await this.createMessageTransports(this._clientOptions.stdioEncoding||"utf8");return this._connection=H0(r.reader,r.writer,e,n,this._clientOptions.connectionOptions),this._connection}async handleConnectionClosed(){if(this.$state===se.Stopped)return;try{this._connection!==void 0&&this._connection.dispose()}catch{}let e={action:Fr.DoNotRestart};if(this.$state!==se.Stopping)try{e=await this._clientOptions.errorHandler.closed()}catch{}this._connection=void 0,e.action===Fr.DoNotRestart?(this.error(e.message??"Connection to server got closed. Server will not be restarted.",void 0,e.handled===!0?!1:"force"),this.cleanUp("stop"),this.$state===se.Starting?this.$state=se.StartFailed:this.$state=se.Stopped,this._onStop=Promise.resolve(),this._onStart=void 0):e.action===Fr.Restart&&(this.info(e.message??"Connection to server got closed. Server will restart.",!e.handled),this.cleanUp("restart"),this.$state=se.Initial,this._onStop=Promise.resolve(),this._onStart=void 0,this.start().catch(n=>this.error("Restarting server failed",n,"force")))}async handleConnectionError(e,n,r){let i=await this._clientOptions.errorHandler.error(e,n,r);i.action===_o.Shutdown?(this.error(i.message??`Client ${this._name}: connection to server is erroring.
${e.message}
Shutting down server.`,void 0,i.handled===!0?!1:"force"),this.stop().catch(s=>{this.error("Stopping server failed",s,!1)})):this.error(i.message??`Client ${this._name}: connection to server is erroring.
${e.message}`,void 0,i.handled===!0?!1:"force")}hookConfigurationChanged(e){this._listeners.push(be.workspace.onDidChangeConfiguration(()=>{this.refreshTrace(e,!0)}))}refreshTrace(e,n=!1){let r=be.workspace.getConfiguration(this._id),i=O.Trace.Off,s=O.TraceFormat.Text;if(r){let o=r.get("trace.server","off");typeof o=="string"?i=O.Trace.fromString(o):(i=O.Trace.fromString(r.get("trace.server.verbosity","off")),s=O.TraceFormat.fromString(r.get("trace.server.format","text")))}this._trace=i,this._traceFormat=s,e.trace(this._trace,this._tracer,{sendNotification:n,traceFormat:this._traceFormat}).catch(o=>{this.error("Updating trace failed with error",o,!1)})}hookFileEvents(e){let n=this._clientOptions.synchronize.fileEvents;if(!n)return;let r;kn.array(n)?r=n:r=[n],r&&this._dynamicFeatures.get(O.DidChangeWatchedFilesNotification.type.method).registerRaw(E_.generateUuid(),r)}registerFeatures(e){for(let n of e)this.registerFeature(n)}registerFeature(e){if(this._features.push(e),qn.DynamicFeature.is(e)){let n=e.registrationType;this._dynamicFeatures.set(n.method,e)}}getFeature(e){return this._dynamicFeatures.get(e)}hasDedicatedTextSynchronizationFeature(e){let n=this.getFeature(O.NotebookDocumentSyncRegistrationType.method);return n===void 0||!(n instanceof x_.NotebookDocumentSyncFeature)?!1:n.handles(e)}registerBuiltinFeatures(){let e=new Map;this.registerFeature(new q_.ConfigurationFeature(this)),this.registerFeature(new ps.DidOpenTextDocumentFeature(this,this._syncedDocuments)),this._didChangeTextDocumentFeature=new ps.DidChangeTextDocumentFeature(this,e),this._didChangeTextDocumentFeature.onPendingChangeAdded(()=>{this.triggerPendingChangeDelivery()}),this.registerFeature(this._didChangeTextDocumentFeature),this.registerFeature(new ps.WillSaveFeature(this)),this.registerFeature(new ps.WillSaveWaitUntilFeature(this)),this.registerFeature(new ps.DidSaveTextDocumentFeature(this)),this.registerFeature(new ps.DidCloseTextDocumentFeature(this,this._syncedDocuments,e)),this.registerFeature(new R0.FileSystemWatcherFeature(this,n=>this.notifyFileEvent(n))),this.registerFeature(new f0.CompletionItemFeature(this)),this.registerFeature(new p0.HoverFeature(this)),this.registerFeature(new g0.SignatureHelpFeature(this)),this.registerFeature(new h0.DefinitionFeature(this)),this.registerFeature(new w0.ReferencesFeature(this)),this.registerFeature(new m0.DocumentHighlightFeature(this)),this.registerFeature(new v0.DocumentSymbolFeature(this)),this.registerFeature(new y0.WorkspaceSymbolFeature(this)),this.registerFeature(new b0.CodeActionFeature(this)),this.registerFeature(new C0.CodeLensFeature(this)),this.registerFeature(new og.DocumentFormattingFeature(this)),this.registerFeature(new og.DocumentRangeFormattingFeature(this)),this.registerFeature(new og.DocumentOnTypeFormattingFeature(this)),this.registerFeature(new _0.RenameFeature(this)),this.registerFeature(new S0.DocumentLinkFeature(this)),this.registerFeature(new D0.ExecuteCommandFeature(this)),this.registerFeature(new q_.SyncConfigurationFeature(this)),this.registerFeature(new E0.TypeDefinitionFeature(this)),this.registerFeature(new P0.ImplementationFeature(this)),this.registerFeature(new T0.ColorProviderFeature(this)),this.clientOptions.workspaceFolder===void 0&&this.registerFeature(new x0.WorkspaceFoldersFeature(this)),this.registerFeature(new q0.FoldingRangeFeature(this)),this.registerFeature(new k0.DeclarationFeature(this)),this.registerFeature(new O0.SelectionRangeFeature(this)),this.registerFeature(new I0.ProgressFeature(this)),this.registerFeature(new N0.CallHierarchyFeature(this)),this.registerFeature(new F0.SemanticTokensFeature(this)),this.registerFeature(new M0.LinkedEditingFeature(this)),this.registerFeature(new hs.DidCreateFilesFeature(this)),this.registerFeature(new hs.DidRenameFilesFeature(this)),this.registerFeature(new hs.DidDeleteFilesFeature(this)),this.registerFeature(new hs.WillCreateFilesFeature(this)),this.registerFeature(new hs.WillRenameFilesFeature(this)),this.registerFeature(new hs.WillDeleteFilesFeature(this)),this.registerFeature(new A0.TypeHierarchyFeature(this)),this.registerFeature(new L0.InlineValueFeature(this)),this.registerFeature(new j0.InlayHintsFeature(this)),this.registerFeature(new d0.DiagnosticFeature(this)),this.registerFeature(new x_.NotebookDocumentSyncFeature(this))}registerProposedFeatures(){this.registerFeatures(lg.createAll(this))}fillInitializeParams(e){for(let n of this._features)kn.func(n.fillInitializeParams)&&n.fillInitializeParams(e)}computeClientCapabilities(){let e={};(0,qn.ensure)(e,"workspace").applyEdit=!0;let n=(0,qn.ensure)((0,qn.ensure)(e,"workspace"),"workspaceEdit");n.documentChanges=!0,n.resourceOperations=[O.ResourceOperationKind.Create,O.ResourceOperationKind.Rename,O.ResourceOperationKind.Delete],n.failureHandling=O.FailureHandlingKind.TextOnlyTransactional,n.normalizesLineEndings=!0,n.changeAnnotationSupport={groupsOnLabel:!0};let r=(0,qn.ensure)((0,qn.ensure)(e,"textDocument"),"publishDiagnostics");r.relatedInformation=!0,r.versionSupport=!1,r.tagSupport={valueSet:[O.DiagnosticTag.Unnecessary,O.DiagnosticTag.Deprecated]},r.codeDescriptionSupport=!0,r.dataSupport=!0;let i=(0,qn.ensure)(e,"window"),s=(0,qn.ensure)(i,"showMessage");s.messageActionItem={additionalPropertiesSupport:!0};let o=(0,qn.ensure)(i,"showDocument");o.support=!0;let a=(0,qn.ensure)(e,"general");a.staleRequestSupport={cancel:!0,retryOnContentModified:Array.from(t.RequestsToCancelOnContentModified)},a.regularExpressions={engine:"ECMAScript",version:"ES2020"},a.markdown={parser:"marked",version:"1.1.0"},a.positionEncodings=["utf-16"],this._clientOptions.markdown.supportHtml&&(a.markdown.allowedTags=["ul","li","p","code","blockquote","ol","h1","h2","h3","h4","h5","h6","hr","em","pre","table","thead","tbody","tr","th","td","div","del","a","strong","br","img","span"]);for(let c of this._features)c.fillClientCapabilities(e);return e}initializeFeatures(e){let n=this._clientOptions.documentSelector;for(let r of this._features)kn.func(r.preInitialize)&&r.preInitialize(this._capabilities,n);for(let r of this._features)r.initialize(this._capabilities,n)}async handleRegistrationRequest(e){let n=this.clientOptions.middleware?.handleRegisterCapability;return n?n(e,r=>this.doRegisterCapability(r)):this.doRegisterCapability(e)}async doRegisterCapability(e){if(!this.isRunning()){for(let n of e.registrations)this._ignoredRegistrations.add(n.id);return}for(let n of e.registrations){let r=this._dynamicFeatures.get(n.method);if(r===void 0)return Promise.reject(new Error(`No feature implementation for ${n.method} found. Registration failed.`));let i=n.registerOptions??{};i.documentSelector=i.documentSelector??this._clientOptions.documentSelector;let s={id:n.id,registerOptions:i};try{r.register(s)}catch(o){return Promise.reject(o)}}}async handleUnregistrationRequest(e){let n=this.clientOptions.middleware?.handleUnregisterCapability;return n?n(e,r=>this.doUnregisterCapability(r)):this.doUnregisterCapability(e)}async doUnregisterCapability(e){for(let n of e.unregisterations){if(this._ignoredRegistrations.has(n.id))continue;let r=this._dynamicFeatures.get(n.method);if(!r)return Promise.reject(new Error(`No feature implementation for ${n.method} found. Unregistration failed.`));r.unregister(n.id)}}async handleApplyWorkspaceEdit(e){let n=e.edit,r=await this.workspaceEditLock.lock(()=>this._p2c.asWorkspaceEdit(n)),i=new Map;be.workspace.textDocuments.forEach(o=>i.set(o.uri.toString(),o));let s=!1;if(n.documentChanges){for(let o of n.documentChanges)if(O.TextDocumentEdit.is(o)&&o.textDocument.version&&o.textDocument.version>=0){let a=this._p2c.asUri(o.textDocument.uri).toString(),c=i.get(a);if(c&&c.version!==o.textDocument.version){s=!0;break}}}return s?Promise.resolve({applied:!1}):kn.asPromise(be.workspace.applyEdit(r).then(o=>({applied:o})))}handleFailedRequest(e,n,r,i,s=!0){if(r instanceof O.ResponseError){if(r.code===O.ErrorCodes.PendingResponseRejected||r.code===O.ErrorCodes.ConnectionInactive)return i;if(r.code===O.LSPErrorCodes.RequestCancelled||r.code===O.LSPErrorCodes.ServerCancelled){if(n!==void 0&&n.isCancellationRequested)return i;throw r.data!==void 0?new qn.LSPCancellationError(r.data):new be.CancellationError}else if(r.code===O.LSPErrorCodes.ContentModified){if(t.RequestsToCancelOnContentModified.has(e.method)||t.CancellableResolveCalls.has(e.method))throw new be.CancellationError;return i}}throw this.error(`Request ${e.method} failed.`,r,s),r}};ct.BaseLanguageClient=So;So.RequestsToCancelOnContentModified=new Set([O.SemanticTokensRequest.method,O.SemanticTokensRangeRequest.method,O.SemanticTokensDeltaRequest.method]);So.CancellableResolveCalls=new Set([O.CompletionResolveRequest.method,O.CodeLensResolveRequest.method,O.CodeActionResolveRequest.method,O.InlayHintResolveRequest.method,O.DocumentLinkResolveRequest.method,O.WorkspaceSymbolResolveRequest.method]);var ug=class{error(e){(0,O.RAL)().console.error(e)}warn(e){(0,O.RAL)().console.warn(e)}info(e){(0,O.RAL)().console.info(e)}log(e){(0,O.RAL)().console.log(e)}};function H0(t,e,n,r,i){let s=new ug,o=(0,O.createProtocolConnection)(t,e,s,i);return o.onError(c=>{n(c[0],c[1],c[2])}),o.onClose(r),{listen:()=>o.listen(),sendRequest:o.sendRequest,onRequest:o.onRequest,hasPendingResponse:o.hasPendingResponse,sendNotification:o.sendNotification,onNotification:o.onNotification,onProgress:o.onProgress,sendProgress:o.sendProgress,trace:(c,u,f)=>{let d={sendNotification:!1,traceFormat:O.TraceFormat.Text};return f===void 0?o.trace(c,u,d):(kn.boolean(f),o.trace(c,u,f))},initialize:c=>o.sendRequest(O.InitializeRequest.type,c),shutdown:()=>o.sendRequest(O.ShutdownRequest.type,void 0),exit:()=>o.sendNotification(O.ExitNotification.type),end:()=>o.end(),dispose:()=>o.dispose()}}var lg;(function(t){function e(n){return[new $0.InlineCompletionItemFeature(n)]}t.createAll=e})(lg||(ct.ProposedFeatures=lg={}))});var N_=b(Lc=>{"use strict";Object.defineProperty(Lc,"__esModule",{value:!0});Lc.terminate=void 0;var I_=require("child_process"),U0=require("path"),V0=process.platform==="win32",W0=process.platform==="darwin",K0=process.platform==="linux";function z0(t,e){if(V0)try{let i={stdio:["pipe","pipe","ignore"]};return e&&(i.cwd=e),I_.execFileSync("taskkill",["/T","/F","/PID",t.pid.toString()],i),!0}catch{return!1}else if(K0||W0)try{var n=(0,U0.join)(__dirname,"terminateProcess.sh"),r=I_.spawnSync(n,[t.pid.toString()]);return!r.error}catch{return!1}else return t.kill("SIGKILL"),!0}Lc.terminate=z0});var fg=b((YM,F_)=>{"use strict";F_.exports=J()});var Do=b((QM,M_)=>{"use strict";var B0=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...t)=>console.error("SEMVER",...t):()=>{};M_.exports=B0});var Ro=b((ZM,A_)=>{"use strict";var G0="2.0.0",X0=Number.MAX_SAFE_INTEGER||9007199254740991,J0=16,Y0=250,Q0=["major","premajor","minor","preminor","patch","prepatch","prerelease"];A_.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:J0,MAX_SAFE_BUILD_LENGTH:Y0,MAX_SAFE_INTEGER:X0,RELEASE_TYPES:Q0,SEMVER_SPEC_VERSION:G0,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var gs=b((ir,L_)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:pg,MAX_SAFE_BUILD_LENGTH:Z0,MAX_LENGTH:eq}=Ro(),tq=Do();ir=L_.exports={};var nq=ir.re=[],rq=ir.safeRe=[],H=ir.src=[],U=ir.t={},iq=0,hg="[a-zA-Z0-9-]",sq=[["\\s",1],["\\d",eq],[hg,Z0]],oq=t=>{for(let[e,n]of sq)t=t.split(`${e}*`).join(`${e}{0,${n}}`).split(`${e}+`).join(`${e}{1,${n}}`);return t},ne=(t,e,n)=>{let r=oq(e),i=iq++;tq(t,i,e),U[t]=i,H[i]=e,nq[i]=new RegExp(e,n?"g":void 0),rq[i]=new RegExp(r,n?"g":void 0)};ne("NUMERICIDENTIFIER","0|[1-9]\\d*");ne("NUMERICIDENTIFIERLOOSE","\\d+");ne("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${hg}*`);ne("MAINVERSION",`(${H[U.NUMERICIDENTIFIER]})\\.(${H[U.NUMERICIDENTIFIER]})\\.(${H[U.NUMERICIDENTIFIER]})`);ne("MAINVERSIONLOOSE",`(${H[U.NUMERICIDENTIFIERLOOSE]})\\.(${H[U.NUMERICIDENTIFIERLOOSE]})\\.(${H[U.NUMERICIDENTIFIERLOOSE]})`);ne("PRERELEASEIDENTIFIER",`(?:${H[U.NUMERICIDENTIFIER]}|${H[U.NONNUMERICIDENTIFIER]})`);ne("PRERELEASEIDENTIFIERLOOSE",`(?:${H[U.NUMERICIDENTIFIERLOOSE]}|${H[U.NONNUMERICIDENTIFIER]})`);ne("PRERELEASE",`(?:-(${H[U.PRERELEASEIDENTIFIER]}(?:\\.${H[U.PRERELEASEIDENTIFIER]})*))`);ne("PRERELEASELOOSE",`(?:-?(${H[U.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${H[U.PRERELEASEIDENTIFIERLOOSE]})*))`);ne("BUILDIDENTIFIER",`${hg}+`);ne("BUILD",`(?:\\+(${H[U.BUILDIDENTIFIER]}(?:\\.${H[U.BUILDIDENTIFIER]})*))`);ne("FULLPLAIN",`v?${H[U.MAINVERSION]}${H[U.PRERELEASE]}?${H[U.BUILD]}?`);ne("FULL",`^${H[U.FULLPLAIN]}$`);ne("LOOSEPLAIN",`[v=\\s]*${H[U.MAINVERSIONLOOSE]}${H[U.PRERELEASELOOSE]}?${H[U.BUILD]}?`);ne("LOOSE",`^${H[U.LOOSEPLAIN]}$`);ne("GTLT","((?:<|>)?=?)");ne("XRANGEIDENTIFIERLOOSE",`${H[U.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);ne("XRANGEIDENTIFIER",`${H[U.NUMERICIDENTIFIER]}|x|X|\\*`);ne("XRANGEPLAIN",`[v=\\s]*(${H[U.XRANGEIDENTIFIER]})(?:\\.(${H[U.XRANGEIDENTIFIER]})(?:\\.(${H[U.XRANGEIDENTIFIER]})(?:${H[U.PRERELEASE]})?${H[U.BUILD]}?)?)?`);ne("XRANGEPLAINLOOSE",`[v=\\s]*(${H[U.XRANGEIDENTIFIERLOOSE]})(?:\\.(${H[U.XRANGEIDENTIFIERLOOSE]})(?:\\.(${H[U.XRANGEIDENTIFIERLOOSE]})(?:${H[U.PRERELEASELOOSE]})?${H[U.BUILD]}?)?)?`);ne("XRANGE",`^${H[U.GTLT]}\\s*${H[U.XRANGEPLAIN]}$`);ne("XRANGELOOSE",`^${H[U.GTLT]}\\s*${H[U.XRANGEPLAINLOOSE]}$`);ne("COERCEPLAIN",`(^|[^\\d])(\\d{1,${pg}})(?:\\.(\\d{1,${pg}}))?(?:\\.(\\d{1,${pg}}))?`);ne("COERCE",`${H[U.COERCEPLAIN]}(?:$|[^\\d])`);ne("COERCEFULL",H[U.COERCEPLAIN]+`(?:${H[U.PRERELEASE]})?(?:${H[U.BUILD]})?(?:$|[^\\d])`);ne("COERCERTL",H[U.COERCE],!0);ne("COERCERTLFULL",H[U.COERCEFULL],!0);ne("LONETILDE","(?:~>?)");ne("TILDETRIM",`(\\s*)${H[U.LONETILDE]}\\s+`,!0);ir.tildeTrimReplace="$1~";ne("TILDE",`^${H[U.LONETILDE]}${H[U.XRANGEPLAIN]}$`);ne("TILDELOOSE",`^${H[U.LONETILDE]}${H[U.XRANGEPLAINLOOSE]}$`);ne("LONECARET","(?:\\^)");ne("CARETTRIM",`(\\s*)${H[U.LONECARET]}\\s+`,!0);ir.caretTrimReplace="$1^";ne("CARET",`^${H[U.LONECARET]}${H[U.XRANGEPLAIN]}$`);ne("CARETLOOSE",`^${H[U.LONECARET]}${H[U.XRANGEPLAINLOOSE]}$`);ne("COMPARATORLOOSE",`^${H[U.GTLT]}\\s*(${H[U.LOOSEPLAIN]})$|^$`);ne("COMPARATOR",`^${H[U.GTLT]}\\s*(${H[U.FULLPLAIN]})$|^$`);ne("COMPARATORTRIM",`(\\s*)${H[U.GTLT]}\\s*(${H[U.LOOSEPLAIN]}|${H[U.XRANGEPLAIN]})`,!0);ir.comparatorTrimReplace="$1$2$3";ne("HYPHENRANGE",`^\\s*(${H[U.XRANGEPLAIN]})\\s+-\\s+(${H[U.XRANGEPLAIN]})\\s*$`);ne("HYPHENRANGELOOSE",`^\\s*(${H[U.XRANGEPLAINLOOSE]})\\s+-\\s+(${H[U.XRANGEPLAINLOOSE]})\\s*$`);ne("STAR","(<|>)?=?\\s*\\*");ne("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");ne("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var jc=b((eA,j_)=>{"use strict";var aq=Object.freeze({loose:!0}),cq=Object.freeze({}),uq=t=>t?typeof t!="object"?aq:t:cq;j_.exports=uq});var gg=b((tA,U_)=>{"use strict";var $_=/^[0-9]+$/,H_=(t,e)=>{let n=$_.test(t),r=$_.test(e);return n&&r&&(t=+t,e=+e),t===e?0:n&&!r?-1:r&&!n?1:t<e?-1:1},lq=(t,e)=>H_(e,t);U_.exports={compareIdentifiers:H_,rcompareIdentifiers:lq}});var Ct=b((nA,z_)=>{"use strict";var $c=Do(),{MAX_LENGTH:V_,MAX_SAFE_INTEGER:Hc}=Ro(),{safeRe:W_,t:K_}=gs(),dq=jc(),{compareIdentifiers:ms}=gg(),mg=class t{constructor(e,n){if(n=dq(n),e instanceof t){if(e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease)return e;e=e.version}else if(typeof e!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>V_)throw new TypeError(`version is longer than ${V_} characters`);$c("SemVer",e,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;let r=e.trim().match(n.loose?W_[K_.LOOSE]:W_[K_.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>Hc||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Hc||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Hc||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(i=>{if(/^[0-9]+$/.test(i)){let s=+i;if(s>=0&&s<Hc)return s}return i}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if($c("SemVer.compare",this.version,this.options,e),!(e instanceof t)){if(typeof e=="string"&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof t||(e=new t(e,this.options)),ms(this.major,e.major)||ms(this.minor,e.minor)||ms(this.patch,e.patch)}comparePre(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let n=0;do{let r=this.prerelease[n],i=e.prerelease[n];if($c("prerelease compare",n,r,i),r===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(r===void 0)return-1;if(r===i)continue;return ms(r,i)}while(++n)}compareBuild(e){e instanceof t||(e=new t(e,this.options));let n=0;do{let r=this.build[n],i=e.build[n];if($c("build compare",n,r,i),r===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(r===void 0)return-1;if(r===i)continue;return ms(r,i)}while(++n)}inc(e,n,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,r),this.inc("pre",n,r);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,r),this.inc("pre",n,r);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let i=Number(r)?1:0;if(!n&&r===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[i];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&r===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(i)}}if(n){let s=[n,i];r===!1&&(s=[n]),ms(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};z_.exports=mg});var Mr=b((rA,G_)=>{"use strict";var B_=Ct(),fq=(t,e,n=!1)=>{if(t instanceof B_)return t;try{return new B_(t,e)}catch(r){if(!n)return null;throw r}};G_.exports=fq});var J_=b((iA,X_)=>{"use strict";var vg=class{constructor(){this.max=1e3,this.map=new Map}get(e){let n=this.map.get(e);if(n!==void 0)return this.map.delete(e),this.map.set(e,n),n}delete(e){return this.map.delete(e)}set(e,n){if(!this.delete(e)&&n!==void 0){if(this.map.size>=this.max){let i=this.map.keys().next().value;this.delete(i)}this.map.set(e,n)}return this}};X_.exports=vg});var gn=b((sA,Q_)=>{"use strict";var Y_=Ct(),pq=(t,e,n)=>new Y_(t,n).compare(new Y_(e,n));Q_.exports=pq});var yg=b((oA,Z_)=>{"use strict";var hq=gn(),gq=(t,e,n)=>hq(t,e,n)===0;Z_.exports=gq});var wg=b((aA,eS)=>{"use strict";var mq=gn(),vq=(t,e,n)=>mq(t,e,n)!==0;eS.exports=vq});var To=b((cA,tS)=>{"use strict";var yq=gn(),wq=(t,e,n)=>yq(t,e,n)>0;tS.exports=wq});var Uc=b((uA,nS)=>{"use strict";var bq=gn(),Cq=(t,e,n)=>bq(t,e,n)>=0;nS.exports=Cq});var Vc=b((lA,rS)=>{"use strict";var _q=gn(),Sq=(t,e,n)=>_q(t,e,n)<0;rS.exports=Sq});var Wc=b((dA,iS)=>{"use strict";var Dq=gn(),Rq=(t,e,n)=>Dq(t,e,n)<=0;iS.exports=Rq});var bg=b((fA,sS)=>{"use strict";var Tq=yg(),Pq=wg(),Eq=To(),xq=Uc(),qq=Vc(),kq=Wc(),Oq=(t,e,n,r)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof n=="object"&&(n=n.version),t===n;case"!==":return typeof t=="object"&&(t=t.version),typeof n=="object"&&(n=n.version),t!==n;case"":case"=":case"==":return Tq(t,n,r);case"!=":return Pq(t,n,r);case">":return Eq(t,n,r);case">=":return xq(t,n,r);case"<":return qq(t,n,r);case"<=":return kq(t,n,r);default:throw new TypeError(`Invalid operator: ${e}`)}};sS.exports=Oq});var Eo=b((pA,dS)=>{"use strict";var Po=Symbol("SemVer ANY"),Sg=class t{static get ANY(){return Po}constructor(e,n){if(n=oS(n),e instanceof t){if(e.loose===!!n.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),_g("comparator",e,n),this.options=n,this.loose=!!n.loose,this.parse(e),this.semver===Po?this.value="":this.value=this.operator+this.semver.version,_g("comp",this)}parse(e){let n=this.options.loose?aS[cS.COMPARATORLOOSE]:aS[cS.COMPARATOR],r=e.match(n);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=r[1]!==void 0?r[1]:"",this.operator==="="&&(this.operator=""),r[2]?this.semver=new uS(r[2],this.options.loose):this.semver=Po}toString(){return this.value}test(e){if(_g("Comparator.test",e,this.options.loose),this.semver===Po||e===Po)return!0;if(typeof e=="string")try{e=new uS(e,this.options)}catch{return!1}return Cg(e,this.operator,this.semver,this.options)}intersects(e,n){if(!(e instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new lS(e.value,n).test(this.value):e.operator===""?e.value===""?!0:new lS(this.value,n).test(e.semver):(n=oS(n),n.includePrerelease&&(this.value==="<0.0.0-0"||e.value==="<0.0.0-0")||!n.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||Cg(this.semver,"<",e.semver,n)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||Cg(this.semver,">",e.semver,n)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}};dS.exports=Sg;var oS=jc(),{safeRe:aS,t:cS}=gs(),Cg=bg(),_g=Do(),uS=Ct(),lS=mn()});var mn=b((hA,gS)=>{"use strict";var Iq=/\s+/g,Dg=class t{constructor(e,n){if(n=Fq(n),e instanceof t)return e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease?e:new t(e.raw,n);if(e instanceof Rg)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease,this.raw=e.trim().replace(Iq," "),this.set=this.raw.split("||").map(r=>this.parseRange(r.trim())).filter(r=>r.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let r=this.set[0];if(this.set=this.set.filter(i=>!pS(i[0])),this.set.length===0)this.set=[r];else if(this.set.length>1){for(let i of this.set)if(i.length===1&&Uq(i[0])){this.set=[i];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let n=this.set[e];for(let r=0;r<n.length;r++)r>0&&(this.formatted+=" "),this.formatted+=n[r].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let r=((this.options.includePrerelease&&$q)|(this.options.loose&&Hq))+":"+e,i=fS.get(r);if(i)return i;let s=this.options.loose,o=s?Wt[Pt.HYPHENRANGELOOSE]:Wt[Pt.HYPHENRANGE];e=e.replace(o,Qq(this.options.includePrerelease)),Fe("hyphen replace",e),e=e.replace(Wt[Pt.COMPARATORTRIM],Aq),Fe("comparator trim",e),e=e.replace(Wt[Pt.TILDETRIM],Lq),Fe("tilde trim",e),e=e.replace(Wt[Pt.CARETTRIM],jq),Fe("caret trim",e);let a=e.split(" ").map(d=>Vq(d,this.options)).join(" ").split(/\s+/).map(d=>Yq(d,this.options));s&&(a=a.filter(d=>(Fe("loose invalid filter",d,this.options),!!d.match(Wt[Pt.COMPARATORLOOSE])))),Fe("range list",a);let c=new Map,u=a.map(d=>new Rg(d,this.options));for(let d of u){if(pS(d))return[d];c.set(d.value,d)}c.size>1&&c.has("")&&c.delete("");let f=[...c.values()];return fS.set(r,f),f}intersects(e,n){if(!(e instanceof t))throw new TypeError("a Range is required");return this.set.some(r=>hS(r,n)&&e.set.some(i=>hS(i,n)&&r.every(s=>i.every(o=>s.intersects(o,n)))))}test(e){if(!e)return!1;if(typeof e=="string")try{e=new Mq(e,this.options)}catch{return!1}for(let n=0;n<this.set.length;n++)if(Zq(this.set[n],e,this.options))return!0;return!1}};gS.exports=Dg;var Nq=J_(),fS=new Nq,Fq=jc(),Rg=Eo(),Fe=Do(),Mq=Ct(),{safeRe:Wt,t:Pt,comparatorTrimReplace:Aq,tildeTrimReplace:Lq,caretTrimReplace:jq}=gs(),{FLAG_INCLUDE_PRERELEASE:$q,FLAG_LOOSE:Hq}=Ro(),pS=t=>t.value==="<0.0.0-0",Uq=t=>t.value==="",hS=(t,e)=>{let n=!0,r=t.slice(),i=r.pop();for(;n&&r.length;)n=r.every(s=>i.intersects(s,e)),i=r.pop();return n},Vq=(t,e)=>(Fe("comp",t,e),t=zq(t,e),Fe("caret",t),t=Wq(t,e),Fe("tildes",t),t=Gq(t,e),Fe("xrange",t),t=Jq(t,e),Fe("stars",t),t),Et=t=>!t||t.toLowerCase()==="x"||t==="*",Wq=(t,e)=>t.trim().split(/\s+/).map(n=>Kq(n,e)).join(" "),Kq=(t,e)=>{let n=e.loose?Wt[Pt.TILDELOOSE]:Wt[Pt.TILDE];return t.replace(n,(r,i,s,o,a)=>{Fe("tilde",t,r,i,s,o,a);let c;return Et(i)?c="":Et(s)?c=`>=${i}.0.0 <${+i+1}.0.0-0`:Et(o)?c=`>=${i}.${s}.0 <${i}.${+s+1}.0-0`:a?(Fe("replaceTilde pr",a),c=`>=${i}.${s}.${o}-${a} <${i}.${+s+1}.0-0`):c=`>=${i}.${s}.${o} <${i}.${+s+1}.0-0`,Fe("tilde return",c),c})},zq=(t,e)=>t.trim().split(/\s+/).map(n=>Bq(n,e)).join(" "),Bq=(t,e)=>{Fe("caret",t,e);let n=e.loose?Wt[Pt.CARETLOOSE]:Wt[Pt.CARET],r=e.includePrerelease?"-0":"";return t.replace(n,(i,s,o,a,c)=>{Fe("caret",t,i,s,o,a,c);let u;return Et(s)?u="":Et(o)?u=`>=${s}.0.0${r} <${+s+1}.0.0-0`:Et(a)?s==="0"?u=`>=${s}.${o}.0${r} <${s}.${+o+1}.0-0`:u=`>=${s}.${o}.0${r} <${+s+1}.0.0-0`:c?(Fe("replaceCaret pr",c),s==="0"?o==="0"?u=`>=${s}.${o}.${a}-${c} <${s}.${o}.${+a+1}-0`:u=`>=${s}.${o}.${a}-${c} <${s}.${+o+1}.0-0`:u=`>=${s}.${o}.${a}-${c} <${+s+1}.0.0-0`):(Fe("no pr"),s==="0"?o==="0"?u=`>=${s}.${o}.${a}${r} <${s}.${o}.${+a+1}-0`:u=`>=${s}.${o}.${a}${r} <${s}.${+o+1}.0-0`:u=`>=${s}.${o}.${a} <${+s+1}.0.0-0`),Fe("caret return",u),u})},Gq=(t,e)=>(Fe("replaceXRanges",t,e),t.split(/\s+/).map(n=>Xq(n,e)).join(" ")),Xq=(t,e)=>{t=t.trim();let n=e.loose?Wt[Pt.XRANGELOOSE]:Wt[Pt.XRANGE];return t.replace(n,(r,i,s,o,a,c)=>{Fe("xRange",t,r,i,s,o,a,c);let u=Et(s),f=u||Et(o),d=f||Et(a),p=d;return i==="="&&p&&(i=""),c=e.includePrerelease?"-0":"",u?i===">"||i==="<"?r="<0.0.0-0":r="*":i&&p?(f&&(o=0),a=0,i===">"?(i=">=",f?(s=+s+1,o=0,a=0):(o=+o+1,a=0)):i==="<="&&(i="<",f?s=+s+1:o=+o+1),i==="<"&&(c="-0"),r=`${i+s}.${o}.${a}${c}`):f?r=`>=${s}.0.0${c} <${+s+1}.0.0-0`:d&&(r=`>=${s}.${o}.0${c} <${s}.${+o+1}.0-0`),Fe("xRange return",r),r})},Jq=(t,e)=>(Fe("replaceStars",t,e),t.trim().replace(Wt[Pt.STAR],"")),Yq=(t,e)=>(Fe("replaceGTE0",t,e),t.trim().replace(Wt[e.includePrerelease?Pt.GTE0PRE:Pt.GTE0],"")),Qq=t=>(e,n,r,i,s,o,a,c,u,f,d,p)=>(Et(r)?n="":Et(i)?n=`>=${r}.0.0${t?"-0":""}`:Et(s)?n=`>=${r}.${i}.0${t?"-0":""}`:o?n=`>=${n}`:n=`>=${n}${t?"-0":""}`,Et(u)?c="":Et(f)?c=`<${+u+1}.0.0-0`:Et(d)?c=`<${u}.${+f+1}.0-0`:p?c=`<=${u}.${f}.${d}-${p}`:t?c=`<${u}.${f}.${+d+1}-0`:c=`<=${c}`,`${n} ${c}`.trim()),Zq=(t,e,n)=>{for(let r=0;r<t.length;r++)if(!t[r].test(e))return!1;if(e.prerelease.length&&!n.includePrerelease){for(let r=0;r<t.length;r++)if(Fe(t[r].semver),t[r].semver!==Rg.ANY&&t[r].semver.prerelease.length>0){let i=t[r].semver;if(i.major===e.major&&i.minor===e.minor&&i.patch===e.patch)return!0}return!1}return!0}});var vs=b((gA,mS)=>{"use strict";var ek=mn(),tk=(t,e,n)=>{try{e=new ek(e,n)}catch{return!1}return e.test(t)};mS.exports=tk});var yS=b(en=>{"use strict";var nk=en&&en.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Tg=en&&en.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&nk(e,t,n)};Object.defineProperty(en,"__esModule",{value:!0});en.DiagnosticPullMode=en.vsdiag=void 0;Tg(J(),en);Tg(de(),en);var vS=qp();Object.defineProperty(en,"vsdiag",{enumerable:!0,get:function(){return vS.vsdiag}});Object.defineProperty(en,"DiagnosticPullMode",{enumerable:!0,get:function(){return vS.DiagnosticPullMode}});Tg(dg(),en)});var Fg=b(Kt=>{"use strict";var rk=Kt&&Kt.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),CS=Kt&&Kt.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&rk(e,t,n)};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.SettingMonitor=Kt.LanguageClient=Kt.TransportKind=void 0;var sr=require("child_process"),Pg=require("fs"),Eg=require("path"),Lr=require("vscode"),$e=ln(),wS=dg(),ik=N_(),Ve=fg(),sk=Mr(),ok=vs();CS(fg(),Kt);CS(yS(),Kt);var bS="^1.82.0",We;(function(t){t[t.stdio=0]="stdio",t[t.ipc=1]="ipc",t[t.pipe=2]="pipe",t[t.socket=3]="socket"})(We||(Kt.TransportKind=We={}));var Ar;(function(t){function e(n){let r=n;return r&&r.kind===We.socket&&$e.number(r.port)}t.isSocket=e})(Ar||(Ar={}));var xg;(function(t){function e(n){return $e.string(n.command)}t.is=e})(xg||(xg={}));var qg;(function(t){function e(n){return $e.string(n.module)}t.is=e})(qg||(qg={}));var kg;(function(t){function e(n){let r=n;return r&&r.writer!==void 0&&r.reader!==void 0}t.is=e})(kg||(kg={}));var Og;(function(t){function e(n){let r=n;return r&&r.process!==void 0&&typeof r.detached=="boolean"}t.is=e})(Og||(Og={}));var Ig=class extends wS.BaseLanguageClient{constructor(e,n,r,i,s){let o,a,c,u,f;$e.string(n)?(o=e,a=n,c=r,u=i,f=!!s):(o=e.toLowerCase(),a=e,c=n,u=r,f=i),f===void 0&&(f=!1),super(o,a,u),this._serverOptions=c,this._forceDebug=f,this._isInDebugMode=f;try{this.checkVersion()}catch(d){throw $e.string(d.message)&&this.outputChannel.appendLine(d.message),d}}checkVersion(){let e=sk(Lr.version);if(!e)throw new Error(`No valid VS Code version detected. Version string is: ${Lr.version}`);if(e.prerelease&&e.prerelease.length>0&&(e.prerelease=[]),!ok(e,bS))throw new Error(`The language client requires VS Code version ${bS} but received version ${Lr.version}`)}get isInDebugMode(){return this._isInDebugMode}async restart(){await this.stop(),this.isInDebugMode?(await new Promise(e=>setTimeout(e,1e3)),await this.start()):await this.start()}stop(e=2e3){return super.stop(e).finally(()=>{if(this._serverProcess){let n=this._serverProcess;this._serverProcess=void 0,(this._isDetached===void 0||!this._isDetached)&&this.checkProcessDied(n),this._isDetached=void 0}})}checkProcessDied(e){!e||e.pid===void 0||setTimeout(()=>{try{e.pid!==void 0&&(process.kill(e.pid,0),(0,ik.terminate)(e))}catch{}},2e3)}handleConnectionClosed(){return this._serverProcess=void 0,super.handleConnectionClosed()}fillInitializeParams(e){super.fillInitializeParams(e),e.processId===null&&(e.processId=process.pid)}createMessageTransports(e){function n(f,d){if(!f&&!d)return;let p=Object.create(null);return Object.keys(process.env).forEach(g=>p[g]=process.env[g]),d&&(p.ELECTRON_RUN_AS_NODE="1",p.ELECTRON_NO_ASAR="1"),f&&Object.keys(f).forEach(g=>p[g]=f[g]),p}let r=["--debug=","--debug-brk=","--inspect=","--inspect-brk="],i=["--debug","--debug-brk","--inspect","--inspect-brk"];function s(){let f=process.execArgv;return f?f.some(d=>r.some(p=>d.startsWith(p))||i.some(p=>d===p)):!1}function o(f){if(f.stdin===null||f.stdout===null||f.stderr===null)throw new Error("Process created without stdio streams")}let a=this._serverOptions;if($e.func(a))return a().then(f=>{if(wS.MessageTransports.is(f))return this._isDetached=!!f.detached,f;if(kg.is(f))return this._isDetached=!!f.detached,{reader:new Ve.StreamMessageReader(f.reader),writer:new Ve.StreamMessageWriter(f.writer)};{let d;return Og.is(f)?(d=f.process,this._isDetached=f.detached):(d=f,this._isDetached=!1),d.stderr.on("data",p=>this.outputChannel.append($e.string(p)?p:p.toString(e))),{reader:new Ve.StreamMessageReader(d.stdout),writer:new Ve.StreamMessageWriter(d.stdin)}}});let c,u=a;return u.run||u.debug?this._forceDebug||s()?(c=u.debug,this._isInDebugMode=!0):(c=u.run,this._isInDebugMode=!1):c=a,this._getServerWorkingDir(c.options).then(f=>{if(qg.is(c)&&c.module){let d=c,p=d.transport||We.stdio;if(d.runtime){let g=[],m=d.options??Object.create(null);m.execArgv&&m.execArgv.forEach(k=>g.push(k)),g.push(d.module),d.args&&d.args.forEach(k=>g.push(k));let S=Object.create(null);S.cwd=f,S.env=n(m.env,!1);let D=this._getRuntimePath(d.runtime,f),P;if(p===We.ipc?(S.stdio=[null,null,null,"ipc"],g.push("--node-ipc")):p===We.stdio?g.push("--stdio"):p===We.pipe?(P=(0,Ve.generateRandomPipeName)(),g.push(`--pipe=${P}`)):Ar.isSocket(p)&&g.push(`--socket=${p.port}`),g.push(`--clientProcessId=${process.pid.toString()}`),p===We.ipc||p===We.stdio){let k=sr.spawn(D,g,S);return!k||!k.pid?ys(k,`Launching server using runtime ${D} failed.`):(this._serverProcess=k,k.stderr.on("data",x=>this.outputChannel.append($e.string(x)?x:x.toString(e))),p===We.ipc?(k.stdout.on("data",x=>this.outputChannel.append($e.string(x)?x:x.toString(e))),Promise.resolve({reader:new Ve.IPCMessageReader(k),writer:new Ve.IPCMessageWriter(k)})):Promise.resolve({reader:new Ve.StreamMessageReader(k.stdout),writer:new Ve.StreamMessageWriter(k.stdin)}))}else{if(p===We.pipe)return(0,Ve.createClientPipeTransport)(P).then(k=>{let x=sr.spawn(D,g,S);return!x||!x.pid?ys(x,`Launching server using runtime ${D} failed.`):(this._serverProcess=x,x.stderr.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),x.stdout.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),k.onConnected().then(I=>({reader:I[0],writer:I[1]})))});if(Ar.isSocket(p))return(0,Ve.createClientSocketTransport)(p.port).then(k=>{let x=sr.spawn(D,g,S);return!x||!x.pid?ys(x,`Launching server using runtime ${D} failed.`):(this._serverProcess=x,x.stderr.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),x.stdout.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),k.onConnected().then(I=>({reader:I[0],writer:I[1]})))})}}else{let g;return new Promise((m,S)=>{let D=(d.args&&d.args.slice())??[];p===We.ipc?D.push("--node-ipc"):p===We.stdio?D.push("--stdio"):p===We.pipe?(g=(0,Ve.generateRandomPipeName)(),D.push(`--pipe=${g}`)):Ar.isSocket(p)&&D.push(`--socket=${p.port}`),D.push(`--clientProcessId=${process.pid.toString()}`);let P=d.options??Object.create(null);if(P.env=n(P.env,!0),P.execArgv=P.execArgv||[],P.cwd=f,P.silent=!0,p===We.ipc||p===We.stdio){let k=sr.fork(d.module,D||[],P);o(k),this._serverProcess=k,k.stderr.on("data",x=>this.outputChannel.append($e.string(x)?x:x.toString(e))),p===We.ipc?(k.stdout.on("data",x=>this.outputChannel.append($e.string(x)?x:x.toString(e))),m({reader:new Ve.IPCMessageReader(this._serverProcess),writer:new Ve.IPCMessageWriter(this._serverProcess)})):m({reader:new Ve.StreamMessageReader(k.stdout),writer:new Ve.StreamMessageWriter(k.stdin)})}else p===We.pipe?(0,Ve.createClientPipeTransport)(g).then(k=>{let x=sr.fork(d.module,D||[],P);o(x),this._serverProcess=x,x.stderr.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),x.stdout.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),k.onConnected().then(I=>{m({reader:I[0],writer:I[1]})},S)},S):Ar.isSocket(p)&&(0,Ve.createClientSocketTransport)(p.port).then(k=>{let x=sr.fork(d.module,D||[],P);o(x),this._serverProcess=x,x.stderr.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),x.stdout.on("data",I=>this.outputChannel.append($e.string(I)?I:I.toString(e))),k.onConnected().then(I=>{m({reader:I[0],writer:I[1]})},S)},S)})}}else if(xg.is(c)&&c.command){let d=c,p=c.args!==void 0?c.args.slice(0):[],g,m=c.transport;if(m===We.stdio)p.push("--stdio");else if(m===We.pipe)g=(0,Ve.generateRandomPipeName)(),p.push(`--pipe=${g}`);else if(Ar.isSocket(m))p.push(`--socket=${m.port}`);else if(m===We.ipc)throw new Error("Transport kind ipc is not support for command executable");let S=Object.assign({},d.options);if(S.cwd=S.cwd||f,m===void 0||m===We.stdio){let D=sr.spawn(d.command,p,S);return!D||!D.pid?ys(D,`Launching server using command ${d.command} failed.`):(D.stderr.on("data",P=>this.outputChannel.append($e.string(P)?P:P.toString(e))),this._serverProcess=D,this._isDetached=!!S.detached,Promise.resolve({reader:new Ve.StreamMessageReader(D.stdout),writer:new Ve.StreamMessageWriter(D.stdin)}))}else{if(m===We.pipe)return(0,Ve.createClientPipeTransport)(g).then(D=>{let P=sr.spawn(d.command,p,S);return!P||!P.pid?ys(P,`Launching server using command ${d.command} failed.`):(this._serverProcess=P,this._isDetached=!!S.detached,P.stderr.on("data",k=>this.outputChannel.append($e.string(k)?k:k.toString(e))),P.stdout.on("data",k=>this.outputChannel.append($e.string(k)?k:k.toString(e))),D.onConnected().then(k=>({reader:k[0],writer:k[1]})))});if(Ar.isSocket(m))return(0,Ve.createClientSocketTransport)(m.port).then(D=>{let P=sr.spawn(d.command,p,S);return!P||!P.pid?ys(P,`Launching server using command ${d.command} failed.`):(this._serverProcess=P,this._isDetached=!!S.detached,P.stderr.on("data",k=>this.outputChannel.append($e.string(k)?k:k.toString(e))),P.stdout.on("data",k=>this.outputChannel.append($e.string(k)?k:k.toString(e))),D.onConnected().then(k=>({reader:k[0],writer:k[1]})))})}}return Promise.reject(new Error("Unsupported server configuration "+JSON.stringify(a,null,4)))}).finally(()=>{this._serverProcess!==void 0&&this._serverProcess.on("exit",(f,d)=>{f!==null&&this.error(`Server process exited with code ${f}.`,void 0,!1),d!==null&&this.error(`Server process exited with signal ${d}.`,void 0,!1)})})}_getRuntimePath(e,n){if(Eg.isAbsolute(e))return e;let r=this._mainGetRootPath();if(r!==void 0){let i=Eg.join(r,e);if(Pg.existsSync(i))return i}if(n!==void 0){let i=Eg.join(n,e);if(Pg.existsSync(i))return i}return e}_mainGetRootPath(){let e=Lr.workspace.workspaceFolders;if(!e||e.length===0)return;let n=e[0];if(n.uri.scheme==="file")return n.uri.fsPath}_getServerWorkingDir(e){let n=e&&e.cwd;return n||(n=this.clientOptions.workspaceFolder?this.clientOptions.workspaceFolder.uri.fsPath:this._mainGetRootPath()),n?new Promise(r=>{Pg.lstat(n,(i,s)=>{r(!i&&s.isDirectory()?n:void 0)})}):Promise.resolve(void 0)}};Kt.LanguageClient=Ig;var Ng=class{constructor(e,n){this._client=e,this._setting=n,this._listeners=[]}start(){return Lr.workspace.onDidChangeConfiguration(this.onDidChangeConfiguration,this,this._listeners),this.onDidChangeConfiguration(),new Lr.Disposable(()=>{this._client.needsStop()&&this._client.stop()})}onDidChangeConfiguration(){let e=this._setting.indexOf("."),n=e>=0?this._setting.substr(0,e):this._setting,r=e>=0?this._setting.substr(e+1):void 0,i=r?Lr.workspace.getConfiguration(n).get(r,!1):Lr.workspace.getConfiguration(n);i&&this._client.needsStart()?this._client.start().catch(s=>this._client.error("Start failed after configuration change",s,"force")):!i&&this._client.needsStop()&&this._client.stop().catch(s=>this._client.error("Stop failed after configuration change",s,"force"))}};Kt.SettingMonitor=Ng;function ys(t,e){return t===null?Promise.reject(e):new Promise((n,r)=>{t.on("error",i=>{r(`${e} ${i}`)}),setImmediate(()=>r(e))})}});var vi=b(Ce=>{"use strict";var ak=Ce&&Ce.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),ck=Ce&&Ce.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&ak(e,t,n)};Object.defineProperty(Ce,"__esModule",{value:!0});Ce.currentLabsVersion=Ce.middleware=Ce.getTsdk=Ce.activateTsVersionStatusItem=Ce.activateTsConfigStatusItem=Ce.activateReloadProjects=Ce.activateFindFileReferences=Ce.activateWriteVirtualFiles=Ce.activateDocumentDropEdit=Ce.activateAutoInsertion=void 0;Ce.parseServerCommand=ws;Ce.createLabsInfo=gk;var or=require("vscode"),uk=Tb();Object.defineProperty(Ce,"activateAutoInsertion",{enumerable:!0,get:function(){return uk.activate}});var lk=Pb();Object.defineProperty(Ce,"activateDocumentDropEdit",{enumerable:!0,get:function(){return lk.activate}});var dk=Eb();Object.defineProperty(Ce,"activateWriteVirtualFiles",{enumerable:!0,get:function(){return dk.activate}});var fk=Ab();Object.defineProperty(Ce,"activateFindFileReferences",{enumerable:!0,get:function(){return fk.activate}});var pk=Lb();Object.defineProperty(Ce,"activateReloadProjects",{enumerable:!0,get:function(){return pk.activate}});var hk=Hb();Object.defineProperty(Ce,"activateTsConfigStatusItem",{enumerable:!0,get:function(){return hk.activate}});var _S=Bb();Object.defineProperty(Ce,"activateTsVersionStatusItem",{enumerable:!0,get:function(){return _S.activate}});Object.defineProperty(Ce,"getTsdk",{enumerable:!0,get:function(){return _S.getTsdk}});ck(Fg(),Ce);Ce.middleware={async provideCodeActions(t,e,n,r,i){let s=await i(t,e,n,r);return s=s?.map(o=>o instanceof or.CodeAction?(o.command&&(o.command=ws(o.command)),o):ws(o)),s},async resolveCodeAction(t,e,n){let r=await n(t,e);return r?.command&&(r.command=ws(r.command)),r},async provideCodeLenses(t,e,n){let r=await n(t,e);return r=r?.map(i=>(i.command&&(i.command=ws(i.command)),i)),r},async resolveCodeLens(t,e,n){let r=await n(t,e);return r?.command&&(r.command=ws(r.command)),r}};function ws(t){return t.command==="editor.action.rename"&&t.arguments?{...t,arguments:[[or.Uri.parse(t.arguments[0]),new or.Position(t.arguments[1].line,t.arguments[1].character)]]}:t.command==="editor.action.showReferences"&&t.arguments?{...t,arguments:[or.Uri.parse(t.arguments[0]),new or.Position(t.arguments[1].line,t.arguments[1].character),t.arguments[2].map(e=>new or.Location(or.Uri.parse(e.uri),new or.Range(e.range.start.line,e.range.start.character,e.range.end.line,e.range.end.character)))]}:t}Ce.currentLabsVersion="2.3.1";function gk(t){let e=new or.EventEmitter,n={volarLabs:{version:Ce.currentLabsVersion,languageClients:[],languageServerProtocol:t,onDidAddLanguageClient:e.event}};return{extensionExports:n,addLanguageClient(r){n.volarLabs.languageClients.push(r),e.fire(r)}}}});var DS=b((wA,SS)=>{"use strict";SS.exports=Fg()});var RS=b(yi=>{"use strict";var mk=yi&&yi.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),vk=yi&&yi.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&mk(e,t,n)};Object.defineProperty(yi,"__esModule",{value:!0});vk(DS(),yi)});var TS=b(wi=>{"use strict";var yk=wi&&wi.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),wk=wi&&wi.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&yk(e,t,n)};Object.defineProperty(wi,"__esModule",{value:!0});wk(qr(),wi)});var eD=b(z=>{"use strict";Object.defineProperty(z,Symbol.toStringTag,{value:"Module"});function bk(t){let e=Object.create(null);for(let n of t.split(","))e[n]=1;return n=>n in e}var xS={},qS=()=>{},Jg=Object.assign,Ck=(t,e)=>{let n=t.indexOf(e);n>-1&&t.splice(n,1)},_k=Object.prototype.hasOwnProperty,Xc=(t,e)=>_k.call(t,e),Wn=Array.isArray,ko=t=>su(t)==="[object Map]",Sk=t=>su(t)==="[object Set]",dr=t=>typeof t=="function",Dk=t=>typeof t=="string",Mo=t=>typeof t=="symbol",_i=t=>t!==null&&typeof t=="object",Rk=t=>(_i(t)||dr(t))&&dr(t.then)&&dr(t.catch),Tk=Object.prototype.toString,su=t=>Tk.call(t),Pk=t=>su(t).slice(8,-1),Ek=t=>su(t)==="[object Object]",Yg=t=>Dk(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,xk=t=>{let e=Object.create(null);return n=>e[n]||(e[n]=t(n))},_A=xk(t=>t.charAt(0).toUpperCase()+t.slice(1)),$r=(t,e)=>!Object.is(t,e),qk=(t,e,n,r=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:r,value:n})};function kk(t,...e){console.warn(`[Vue warn] ${t}`,...e)}var xt,Jc=class{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=xt,!e&&xt&&(this.index=(xt.scopes||(xt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,n;if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].pause();for(e=0,n=this.effects.length;e<n;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,n;if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].resume();for(e=0,n=this.effects.length;e<n;e++)this.effects[e].resume()}}run(e){if(this._active){let n=xt;try{return xt=this,e()}finally{xt=n}}}on(){xt=this}off(){xt=this.parent}stop(e){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}};function Ok(t){return new Jc(t)}function kS(){return xt}function Ik(t,e=!1){xt&&xt.cleanups.push(t)}var Pe,Nk={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED"},Mg=new WeakSet,Ci=class{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,xt&&xt.active&&xt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Mg.has(this)&&(Mg.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||IS(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,PS(this),NS(this);let e=Pe,n=tn;Pe=this,tn=!0;try{return this.fn()}finally{FS(this),Pe=e,tn=n,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)em(e);this.deps=this.depsTail=void 0,PS(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Mg.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){jg(this)&&this.run()}get dirty(){return jg(this)}},OS=0,Oo,Io;function IS(t,e=!1){if(t.flags|=8,e){t.next=Io,Io=t;return}t.next=Oo,Oo=t}function Qg(){OS++}function Zg(){if(--OS>0)return;if(Io){let e=Io;for(Io=void 0;e;){let n=e.next;e.next=void 0,e.flags&=-9,e=n}}let t;for(;Oo;){let e=Oo;for(Oo=void 0;e;){let n=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger()}catch(r){t||(t=r)}e=n}}if(t)throw t}function NS(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e}function FS(t){let e,n=t.depsTail,r=n;for(;r;){let i=r.prevDep;r.version===-1?(r===n&&(n=i),em(r),Fk(r)):e=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=i}t.deps=e,t.depsTail=n}function jg(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&(MS(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty}function MS(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===No))return;t.globalVersion=No;let e=t.dep;if(t.flags|=2,e.version>0&&!t.isSSR&&t.deps&&!jg(t)){t.flags&=-3;return}let n=Pe,r=tn;Pe=t,tn=!0;try{NS(t);let i=t.fn(t._value);(e.version===0||$r(i,t._value))&&(t._value=i,e.version++)}catch(i){throw e.version++,i}finally{Pe=n,tn=r,FS(t),t.flags&=-3}}function em(t,e=!1){let{dep:n,prevSub:r,nextSub:i}=t;if(r&&(r.nextSub=i,t.prevSub=void 0),i&&(i.prevSub=r,t.nextSub=void 0),n.subs===t&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)em(s,!0)}!e&&!--n.sc&&n.map&&n.map.delete(n.key)}function Fk(t){let{prevDep:e,nextDep:n}=t;e&&(e.nextDep=n,t.prevDep=void 0),n&&(n.prevDep=e,t.nextDep=void 0)}function Mk(t,e){t.effect instanceof Ci&&(t=t.effect.fn);let n=new Ci(t);e&&Jg(n,e);try{n.run()}catch(i){throw n.stop(),i}let r=n.run.bind(n);return r.effect=n,r}function Ak(t){t.effect.stop()}var tn=!0,tm=[];function nm(){tm.push(tn),tn=!1}function Lk(){tm.push(tn),tn=!0}function rm(){let t=tm.pop();tn=t===void 0?!0:t}function jk(t,e=!1){Pe instanceof Ci&&(Pe.cleanup=t)}function PS(t){let{cleanup:e}=t;if(t.cleanup=void 0,e){let n=Pe;Pe=void 0;try{e()}finally{Pe=n}}}var No=0,$g=class{constructor(e,n){this.sub=e,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}},_s=class{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!Pe||!tn||Pe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Pe)n=this.activeLink=new $g(Pe,this),Pe.deps?(n.prevDep=Pe.depsTail,Pe.depsTail.nextDep=n,Pe.depsTail=n):Pe.deps=Pe.depsTail=n,AS(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){let r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Pe.depsTail,n.nextDep=void 0,Pe.depsTail.nextDep=n,Pe.depsTail=n,Pe.deps===n&&(Pe.deps=r)}return n}trigger(e){this.version++,No++,this.notify(e)}notify(e){Qg();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Zg()}}};function AS(t){if(t.dep.sc++,t.sub.flags&4){let e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let r=e.deps;r;r=r.nextDep)AS(r)}let n=t.dep.subs;n!==t&&(t.prevSub=n,n&&(n.nextSub=t)),t.dep.subs=t}}var Yc=new WeakMap,Hr=Symbol(""),Qc=Symbol(""),Ss=Symbol("");function qt(t,e,n){if(tn&&Pe){let r=Yc.get(t);r||Yc.set(t,r=new Map);let i=r.get(n);i||(r.set(n,i=new _s),i.map=r,i.key=n),i.track()}}function ur(t,e,n,r,i,s){let o=Yc.get(t);if(!o){No++;return}let a=c=>{c&&c.trigger()};if(Qg(),e==="clear")o.forEach(a);else{let c=Wn(t),u=c&&Yg(n);if(c&&n==="length"){let f=Number(r);o.forEach((d,p)=>{(p==="length"||p===Ss||!Mo(p)&&p>=f)&&a(d)})}else switch((n!==void 0||o.has(void 0))&&a(o.get(n)),u&&a(o.get(Ss)),e){case"add":c?u&&a(o.get("length")):(a(o.get(Hr)),ko(t)&&a(o.get(Qc)));break;case"delete":c||(a(o.get(Hr)),ko(t)&&a(o.get(Qc)));break;case"set":ko(t)&&a(o.get(Hr));break}}Zg()}function $k(t,e){let n=Yc.get(t);return n&&n.get(e)}function bi(t){let e=De(t);return e===t?e:(qt(e,"iterate",Ss),yn(t)?e:e.map(_t))}function ou(t){return qt(t=De(t),"iterate",Ss),t}var Hk={__proto__:null,[Symbol.iterator](){return Ag(this,Symbol.iterator,_t)},concat(...t){return bi(this).concat(...t.map(e=>Wn(e)?bi(e):e))},entries(){return Ag(this,"entries",t=>(t[1]=_t(t[1]),t))},every(t,e){return ar(this,"every",t,e,void 0,arguments)},filter(t,e){return ar(this,"filter",t,e,n=>n.map(_t),arguments)},find(t,e){return ar(this,"find",t,e,_t,arguments)},findIndex(t,e){return ar(this,"findIndex",t,e,void 0,arguments)},findLast(t,e){return ar(this,"findLast",t,e,_t,arguments)},findLastIndex(t,e){return ar(this,"findLastIndex",t,e,void 0,arguments)},forEach(t,e){return ar(this,"forEach",t,e,void 0,arguments)},includes(...t){return Lg(this,"includes",t)},indexOf(...t){return Lg(this,"indexOf",t)},join(t){return bi(this).join(t)},lastIndexOf(...t){return Lg(this,"lastIndexOf",t)},map(t,e){return ar(this,"map",t,e,void 0,arguments)},pop(){return xo(this,"pop")},push(...t){return xo(this,"push",t)},reduce(t,...e){return ES(this,"reduce",t,e)},reduceRight(t,...e){return ES(this,"reduceRight",t,e)},shift(){return xo(this,"shift")},some(t,e){return ar(this,"some",t,e,void 0,arguments)},splice(...t){return xo(this,"splice",t)},toReversed(){return bi(this).toReversed()},toSorted(t){return bi(this).toSorted(t)},toSpliced(...t){return bi(this).toSpliced(...t)},unshift(...t){return xo(this,"unshift",t)},values(){return Ag(this,"values",_t)}};function Ag(t,e,n){let r=ou(t),i=r[e]();return r!==t&&!yn(t)&&(i._next=i.next,i.next=()=>{let s=i._next();return s.value&&(s.value=n(s.value)),s}),i}var Uk=Array.prototype;function ar(t,e,n,r,i,s){let o=ou(t),a=o!==t&&!yn(t),c=o[e];if(c!==Uk[e]){let d=c.apply(t,s);return a?_t(d):d}let u=n;o!==t&&(a?u=function(d,p){return n.call(this,_t(d),p,t)}:n.length>2&&(u=function(d,p){return n.call(this,d,p,t)}));let f=c.call(o,u,r);return a&&i?i(f):f}function ES(t,e,n,r){let i=ou(t),s=n;return i!==t&&(yn(t)?n.length>3&&(s=function(o,a,c){return n.call(this,o,a,c,t)}):s=function(o,a,c){return n.call(this,o,_t(a),c,t)}),i[e](s,...r)}function Lg(t,e,n){let r=De(t);qt(r,"iterate",Ss);let i=r[e](...n);return(i===-1||i===!1)&&VS(n[0])?(n[0]=De(n[0]),r[e](...n)):i}function xo(t,e,n=[]){nm(),Qg();let r=De(t)[e].apply(t,n);return Zg(),rm(),r}var Vk=bk("__proto__,__v_isRef,__isVue"),LS=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter(Mo));function Wk(t){Mo(t)||(t=String(t));let e=De(this);return qt(e,"has",t),e.hasOwnProperty(t)}var Zc=class{constructor(e=!1,n=!1){this._isReadonly=e,this._isShallow=n}get(e,n,r){if(n==="__v_skip")return e.__v_skip;let i=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return s;if(n==="__v_raw")return r===(i?s?US:HS:s?$S:jS).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(r)?e:void 0;let o=Wn(e);if(!i){let c;if(o&&(c=Hk[n]))return c;if(n==="hasOwnProperty")return Wk}let a=Reflect.get(e,n,kt(e)?e:r);return(Mo(n)?LS.has(n):Vk(n))||(i||qt(e,"get",n),s)?a:kt(a)?o&&Yg(n)?a:a.value:_i(a)?i?sm(a):im(a):a}},eu=class extends Zc{constructor(e=!1){super(!1,e)}set(e,n,r,i){let s=e[n];if(!this._isShallow){let c=Ur(s);if(!yn(r)&&!Ur(r)&&(s=De(s),r=De(r)),!Wn(e)&&kt(s)&&!kt(r))return c?!1:(s.value=r,!0)}let o=Wn(e)&&Yg(n)?Number(n)<e.length:Xc(e,n),a=Reflect.set(e,n,r,kt(e)?e:i);return e===De(i)&&(o?$r(r,s)&&ur(e,"set",n,r,s):ur(e,"add",n,r)),a}deleteProperty(e,n){let r=Xc(e,n),i=e[n],s=Reflect.deleteProperty(e,n);return s&&r&&ur(e,"delete",n,void 0,i),s}has(e,n){let r=Reflect.has(e,n);return(!Mo(n)||!LS.has(n))&&qt(e,"has",n),r}ownKeys(e){return qt(e,"iterate",Wn(e)?"length":Hr),Reflect.ownKeys(e)}},tu=class extends Zc{constructor(e=!1){super(!0,e)}set(e,n){return!0}deleteProperty(e,n){return!0}},Kk=new eu,zk=new tu,Bk=new eu(!0),Gk=new tu(!0),Hg=t=>t,Kc=t=>Reflect.getPrototypeOf(t);function Xk(t,e,n){return function(...r){let i=this.__v_raw,s=De(i),o=ko(s),a=t==="entries"||t===Symbol.iterator&&o,c=t==="keys"&&o,u=i[t](...r),f=n?Hg:e?nu:_t;return!e&&qt(s,"iterate",c?Qc:Hr),{next(){let{value:d,done:p}=u.next();return p?{value:d,done:p}:{value:a?[f(d[0]),f(d[1])]:f(d),done:p}},[Symbol.iterator](){return this}}}}function zc(t){return function(...e){return t==="delete"?!1:t==="clear"?void 0:this}}function Jk(t,e){let n={get(i){let s=this.__v_raw,o=De(s),a=De(i);t||($r(i,a)&&qt(o,"get",i),qt(o,"get",a));let{has:c}=Kc(o),u=e?Hg:t?nu:_t;if(c.call(o,i))return u(s.get(i));if(c.call(o,a))return u(s.get(a));s!==o&&s.get(i)},get size(){let i=this.__v_raw;return!t&&qt(De(i),"iterate",Hr),Reflect.get(i,"size",i)},has(i){let s=this.__v_raw,o=De(s),a=De(i);return t||($r(i,a)&&qt(o,"has",i),qt(o,"has",a)),i===a?s.has(i):s.has(i)||s.has(a)},forEach(i,s){let o=this,a=o.__v_raw,c=De(a),u=e?Hg:t?nu:_t;return!t&&qt(c,"iterate",Hr),a.forEach((f,d)=>i.call(s,u(f),u(d),o))}};return Jg(n,t?{add:zc("add"),set:zc("set"),delete:zc("delete"),clear:zc("clear")}:{add(i){!e&&!yn(i)&&!Ur(i)&&(i=De(i));let s=De(this);return Kc(s).has.call(s,i)||(s.add(i),ur(s,"add",i,i)),this},set(i,s){!e&&!yn(s)&&!Ur(s)&&(s=De(s));let o=De(this),{has:a,get:c}=Kc(o),u=a.call(o,i);u||(i=De(i),u=a.call(o,i));let f=c.call(o,i);return o.set(i,s),u?$r(s,f)&&ur(o,"set",i,s,f):ur(o,"add",i,s),this},delete(i){let s=De(this),{has:o,get:a}=Kc(s),c=o.call(s,i);c||(i=De(i),c=o.call(s,i));let u=a?a.call(s,i):void 0,f=s.delete(i);return c&&ur(s,"delete",i,void 0,u),f},clear(){let i=De(this),s=i.size!==0,o=void 0,a=i.clear();return s&&ur(i,"clear",void 0,void 0,o),a}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Xk(i,t,e)}),n}function au(t,e){let n=Jk(t,e);return(r,i,s)=>i==="__v_isReactive"?!t:i==="__v_isReadonly"?t:i==="__v_raw"?r:Reflect.get(Xc(n,i)&&i in r?n:r,i,s)}var Yk={get:au(!1,!1)},Qk={get:au(!1,!0)},Zk={get:au(!0,!1)},eO={get:au(!0,!0)},jS=new WeakMap,$S=new WeakMap,HS=new WeakMap,US=new WeakMap;function tO(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function nO(t){return t.__v_skip||!Object.isExtensible(t)?0:tO(Pk(t))}function im(t){return Ur(t)?t:cu(t,!1,Kk,Yk,jS)}function rO(t){return cu(t,!1,Bk,Qk,$S)}function sm(t){return cu(t,!0,zk,Zk,HS)}function iO(t){return cu(t,!0,Gk,eO,US)}function cu(t,e,n,r,i){if(!_i(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;let s=i.get(t);if(s)return s;let o=nO(t);if(o===0)return t;let a=new Proxy(t,o===2?r:n);return i.set(t,a),a}function Cs(t){return Ur(t)?Cs(t.__v_raw):!!(t&&t.__v_isReactive)}function Ur(t){return!!(t&&t.__v_isReadonly)}function yn(t){return!!(t&&t.__v_isShallow)}function VS(t){return t?!!t.__v_raw:!1}function De(t){let e=t&&t.__v_raw;return e?De(e):t}function sO(t){return!Xc(t,"__v_skip")&&Object.isExtensible(t)&&qk(t,"__v_skip",!0),t}var _t=t=>_i(t)?im(t):t,nu=t=>_i(t)?sm(t):t;function kt(t){return t?t.__v_isRef===!0:!1}function WS(t){return KS(t,!1)}function oO(t){return KS(t,!0)}function KS(t,e){return kt(t)?t:new Ug(t,e)}var Ug=class{constructor(e,n){this.dep=new _s,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?e:De(e),this._value=n?e:_t(e),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(e){let n=this._rawValue,r=this.__v_isShallow||yn(e)||Ur(e);e=r?e:De(e),$r(e,n)&&(this._rawValue=e,this._value=r?e:_t(e),this.dep.trigger())}};function aO(t){t.dep&&t.dep.trigger()}function om(t){return kt(t)?t.value:t}function cO(t){return dr(t)?t():om(t)}var uO={get:(t,e,n)=>e==="__v_raw"?t:om(Reflect.get(t,e,n)),set:(t,e,n,r)=>{let i=t[e];return kt(i)&&!kt(n)?(i.value=n,!0):Reflect.set(t,e,n,r)}};function lO(t){return Cs(t)?t:new Proxy(t,uO)}var Vg=class{constructor(e){this.__v_isRef=!0,this._value=void 0;let n=this.dep=new _s,{get:r,set:i}=e(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=i}get value(){return this._value=this._get()}set value(e){this._set(e)}};function dO(t){return new Vg(t)}function fO(t){let e=Wn(t)?new Array(t.length):{};for(let n in t)e[n]=zS(t,n);return e}var Wg=class{constructor(e,n,r){this._object=e,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return $k(De(this._object),this._key)}},Kg=class{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}};function pO(t,e,n){return kt(t)?t:dr(t)?new Kg(t):_i(t)&&arguments.length>1?zS(t,e,n):WS(t)}function zS(t,e,n){let r=t[e];return kt(r)?r:new Wg(t,e,n)}var zg=class{constructor(e,n,r){this.fn=e,this.setter=n,this._value=void 0,this.dep=new _s(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=No-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Pe!==this)return IS(this,!0),!0}get value(){let e=this.dep.track();return MS(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}};function hO(t,e,n=!1){let r,i;return dr(t)?r=t:(r=t.get,i=t.set),new zg(r,i,n)}var gO={GET:"get",HAS:"has",ITERATE:"iterate"},mO={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},vO={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},yO={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},Bc={},ru=new WeakMap,jr;function wO(){return jr}function BS(t,e=!1,n=jr){if(n){let r=ru.get(n);r||ru.set(n,r=[]),r.push(t)}}function bO(t,e,n=xS){let{immediate:r,deep:i,once:s,scheduler:o,augmentJob:a,call:c}=n,u=M=>{(n.onWarn||kk)("Invalid watch source: ",M,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},f=M=>i?M:yn(M)||i===!1||i===0?lr(M,1):lr(M),d,p,g,m,S=!1,D=!1;if(kt(t)?(p=()=>t.value,S=yn(t)):Cs(t)?(p=()=>f(t),S=!0):Wn(t)?(D=!0,S=t.some(M=>Cs(M)||yn(M)),p=()=>t.map(M=>{if(kt(M))return M.value;if(Cs(M))return f(M);if(dr(M))return c?c(M,2):M()})):dr(t)?e?p=c?()=>c(t,2):t:p=()=>{if(g){nm();try{g()}finally{rm()}}let M=jr;jr=d;try{return c?c(t,3,[m]):t(m)}finally{jr=M}}:p=qS,e&&i){let M=p,Y=i===!0?1/0:i;p=()=>lr(M(),Y)}let P=kS(),k=()=>{d.stop(),P&&P.active&&Ck(P.effects,d)};if(s&&e){let M=e;e=(...Y)=>{M(...Y),k()}}let x=D?new Array(t.length).fill(Bc):Bc,I=M=>{if(!(!(d.flags&1)||!d.dirty&&!M))if(e){let Y=d.run();if(i||S||(D?Y.some((B,Z)=>$r(B,x[Z])):$r(Y,x))){g&&g();let B=jr;jr=d;try{let Z=[Y,x===Bc?void 0:D&&x[0]===Bc?[]:x,m];c?c(e,3,Z):e(...Z),x=Y}finally{jr=B}}}else d.run()};return a&&a(I),d=new Ci(p),d.scheduler=o?()=>o(I,!1):I,m=M=>BS(M,!1,d),g=d.onStop=()=>{let M=ru.get(d);if(M){if(c)c(M,4);else for(let Y of M)Y();ru.delete(d)}},e?r?I(!0):x=d.run():o?o(I.bind(null,!0),!0):d.run(),k.pause=d.pause.bind(d),k.resume=d.resume.bind(d),k.stop=k,k}function lr(t,e=1/0,n){if(e<=0||!_i(t)||t.__v_skip||(n=n||new Set,n.has(t)))return t;if(n.add(t),e--,kt(t))lr(t.value,e,n);else if(Wn(t))for(let r=0;r<t.length;r++)lr(t[r],e,n);else if(Sk(t)||ko(t))t.forEach(r=>{lr(r,e,n)});else if(Ek(t)){for(let r in t)lr(t[r],e,n);for(let r of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,r)&&lr(t[r],e,n)}return t}var Bg=(t=>(t.ACTIVATED="a",t.DEACTIVATED="da",t))(Bg||{}),GS=(t=>(t[t.WATCH_GETTER=0]="WATCH_GETTER",t[t.WATCH_CALLBACK=1]="WATCH_CALLBACK",t[t.WATCH_CLEANUP=2]="WATCH_CLEANUP",t[t.APP_ERROR_HANDLER=3]="APP_ERROR_HANDLER",t[t.SCHEDULER=4]="SCHEDULER",t))(GS||{}),SA={[Bg.ACTIVATED]:"activated hook",[Bg.DEACTIVATED]:"deactivated hook",0:"watcher getter",1:"watcher callback",2:"watcher cleanup function",3:"app errorHandler",4:"scheduler flush"};function XS(t,e,n,r){try{return r?t(...r):t()}catch(i){YS(i,e,n)}}function JS(t,e,n,r){if(dr(t)){let i=XS(t,e,n,r);return i&&Rk(i)&&i.catch(s=>{YS(s,e,n)}),i}if(Wn(t)){let i=[];for(let s=0;s<t.length;s++)i.push(JS(t[s],e,n,r));return i}}function YS(t,e,n,r=!0){CO(t,n,null,r)}function CO(t,e,n,r=!0){console.error(t)}var Gg=(t=>(t[t.QUEUED=1]="QUEUED",t[t.PRE=2]="PRE",t[t.ALLOW_RECURSE=4]="ALLOW_RECURSE",t[t.DISPOSED=8]="DISPOSED",t))(Gg||{}),iu=!1,Xg=!1,vn=[],cr=0,Gc=[],bs=null,qo=0,QS=Promise.resolve(),am=null;function _O(t){let e=am||QS;return t?e.then(this?t.bind(this):t):e}function SO(t){let e=iu?cr+1:0,n=vn.length;for(;e<n;){let r=e+n>>>1,i=vn[r],s=Fo(i);s<t||s===t&&i.flags&2?e=r+1:n=r}return e}function DO(t){if(!(t.flags&1)){let e=Fo(t),n=vn[vn.length-1];!n||!(t.flags&2)&&e>=Fo(n)?vn.push(t):vn.splice(SO(e),0,t),t.flags|=1,RO()}}function RO(){!iu&&!Xg&&(Xg=!0,am=QS.then(ZS))}function TO(t){if(Gc.length){let e=[...new Set(Gc)].sort((n,r)=>Fo(n)-Fo(r));if(Gc.length=0,bs){bs.push(...e);return}for(bs=e,qo=0;qo<bs.length;qo++){let n=bs[qo];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}bs=null,qo=0}}var Fo=t=>t.id==null?t.flags&2?-1:1/0:t.id;function ZS(t){Xg=!1,iu=!0;let e=qS;try{for(cr=0;cr<vn.length;cr++){let n=vn[cr];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),XS(n,null,GS.SCHEDULER),n.flags&4||(n.flags&=-2))}}finally{for(;cr<vn.length;cr++){let n=vn[cr];n&&(n.flags&=-2)}cr=0,vn.length=0,TO(t),iu=!1,am=null,(vn.length||Gc.length)&&ZS(t)}}function PO(t,e){return cm(t,null,e)}function EO(t,e){return cm(t,null,{flush:"sync"})}function xO(t,e,n){return cm(t,e,n)}function cm(t,e,n=xS){let{immediate:r,deep:i,flush:s,once:o}=n,a=Jg({},n);a.call=(f,d,p)=>JS(f,null,d,p);let c=!1;return s!=="sync"&&(c=!0,a.scheduler=(f,d)=>{d?f():DO(f)}),a.augmentJob=f=>{e&&(f.flags|=Gg.ALLOW_RECURSE),c&&(f.flags|=Gg.PRE)},bO(t,e,a)}z.ARRAY_ITERATE_KEY=Ss;z.EffectFlags=Nk;z.EffectScope=Jc;z.ITERATE_KEY=Hr;z.MAP_KEY_ITERATE_KEY=Qc;z.ReactiveEffect=Ci;z.ReactiveFlags=vO;z.TrackOpTypes=gO;z.TriggerOpTypes=mO;z.WatchErrorCodes=yO;z.computed=hO;z.customRef=dO;z.effect=Mk;z.effectScope=Ok;z.enableTracking=Lk;z.getCurrentScope=kS;z.getCurrentWatcher=wO;z.isProxy=VS;z.isReactive=Cs;z.isReadonly=Ur;z.isRef=kt;z.isShallow=yn;z.markRaw=sO;z.nextTick=_O;z.onEffectCleanup=jk;z.onScopeDispose=Ik;z.onWatcherCleanup=BS;z.pauseTracking=nm;z.proxyRefs=lO;z.reactive=im;z.reactiveReadArray=bi;z.readonly=sm;z.ref=WS;z.resetTracking=rm;z.shallowReactive=rO;z.shallowReadArray=ou;z.shallowReadonly=iO;z.shallowRef=oO;z.stop=Ak;z.toRaw=De;z.toReactive=_t;z.toReadonly=nu;z.toRef=pO;z.toRefs=fO;z.toValue=cO;z.track=qt;z.traverse=lr;z.trigger=ur;z.triggerRef=aO;z.unref=om;z.watch=xO;z.watchEffect=PO;z.watchSyncEffect=EO});var Kn=b(L=>{"use strict";Object.defineProperty(L,Symbol.toStringTag,{value:"Module"});var C=eD(),j=require("vscode");function tD(t){return C.getCurrentScope()?(C.onScopeDispose(t),!0):!1}function um(t,e){let n=new Map;return(...r)=>{let i=e(...r),s=n.get(i);return s?s.refCount++:(s={data:t(...r),refCount:1},n.set(i,s)),tD(()=>{--s.refCount===0&&n.delete(i)}),s.data}}function pt(t){let e;return()=>e!==void 0?e:e=C.effectScope(!0).run(t)}var lm=[];function qO(t){lm.push(t)}var Ds=C.shallowRef(null),uu=C.effectScope();function kO(t){return{activate:e=>(Ds.value=e,uu.run(()=>(dm.map(n=>n(e)),t(e)))),deactivate:()=>{lm.map(e=>e()),uu.stop()}}}var dm=[];function fm(t){Ds.value?t(Ds.value):dm.push(t)}function nD(t,e,n){let r=!t,i=j.workspace.getConfiguration(r?void 0:t,n);function s(a,c){let u=C.shallowRef(c),f=C.computed({get:()=>u.value,set:d=>{u.value=d,i.update(a,d)}});return f.update=async(d,p,g)=>{await i.update(a,d,p,g),f.value=d},f.set=d=>{u.value=d},f}let o=Object.fromEntries(Object.keys(e).map(a=>[a,s(a,i.get(a))]));return fm(()=>{re(j.workspace.onDidChangeConfiguration(r?a=>{let c=j.workspace.getConfiguration();for(let u in e)a.affectsConfiguration(u)&&o[u].set(c.get(u))}:a=>{if(!a.affectsConfiguration(t))return;let c=j.workspace.getConfiguration(t);for(let u in e)a.affectsConfiguration(`${t}.${u}`)&&o[u].set(c.get(u))}))}),o}function OO(t,e,n){let r=nD(t,e,n),i={},s=C.shallowReactive({$update(o,a,c,u){return r[o].update(a,c,u)},$set(o,a){return r[o].set(a)}});for(let o in e){let a=o.split("."),c="",u=i,f=s;for(let p of a.slice(0,-1)){c=c?`${c}.${p}`:p;let g=u=u[p]||(u[p]={});if(f[p])f=f[p];else{let m=C.shallowReactive({});Object.defineProperty(f,p,{enumerable:!0,get(){return m},set(S){function D(P,k,x){if(P)for(let I in P)D(P[I],k?`${k}.${I}`:I,x?.[I]);else r[k].value=x}D(g,c,S)}}),f=m}}let d=a[a.length-1];u[d]=null,Object.defineProperty(f,d,{enumerable:!0,get(){return r[o].value},set(p){r[o].value=p}})}return s}function IO(t,e){let n=C.shallowRef(null),r=[],i=s=>(...o)=>n.value?n.value[s](...o):(r.push([s,o]),null);return fm(()=>{n.value=uD(t,e);for(let[s,o]of r)n.value[s](...o)}),{logger:n,outputChannel:C.computed(()=>{var s;return(s=n.value)==null?void 0:s.outputChannel}),info:i("info"),warn:i("warn"),error:i("error"),append:i("append"),appendLine:i("appendLine"),replace:i("replace"),clear:i("clear"),show:i("show"),hide:i("hide")}}function NO(t,...e){return j.commands.executeCommand(t,...e)}function FO(t,e=!1){let n=Ds.value;if(!n&&!e)throw new Error("Cannot get absolute path because the extension is not activated yet");return n?.asAbsolutePath(t)}function MO(t,e=!1){return C.computed(()=>FO(C.toValue(t),e))}function re(t){return(C.getCurrentScope()??uu).cleanups.push(t.dispose.bind(t)),t}var rD=pt(()=>{let t=C.shallowRef(j.window.activeColorTheme);return re(j.window.onDidChangeActiveColorTheme(e=>{t.value=e})),t}),AO=pt(()=>{let t=C.shallowRef(j.debug.activeDebugSession);return re(j.debug.onDidChangeActiveDebugSession(e=>{t.value=e})),C.computed(()=>t.value)}),iD=pt(()=>{let t=C.shallowRef(j.window.activeTextEditor);return re(j.window.onDidChangeActiveTextEditor(e=>{t.value=e})),t});function sD(t){var e;let n=C.shallowRef((e=C.toValue(t))==null?void 0:e.getText());return C.watchEffect(()=>{var r;n.value=(r=C.toValue(t))==null?void 0:r.getText()}),re(j.workspace.onDidChangeTextDocument(r=>{r.document===C.toValue(t)&&(n.value=r.document.getText())})),n}function oD(t,e,n,r={}){let{updateOn:i=["effect","documentChanged"]}=r,s="key"in e?e:re(j.window.createTextEditorDecorationType(e)),o=async()=>{let c=C.toValue(t);c&&c.setDecorations(s,typeof n=="function"?await n(c):C.toValue(n))},a=i.includes("documentChanged")?sD(()=>{var c;return(c=C.toValue(t))==null?void 0:c.document}):null;return i.includes("effect")?C.watchEffect(async()=>{a?.value,await o()}):a&&C.watch(a,o),{update:o}}function LO(t,e){let n=iD();oD(n,t,e)}var jO=pt(()=>{let t=C.shallowRef(j.window.activeNotebookEditor);return re(j.window.onDidChangeActiveNotebookEditor(e=>{t.value=e})),t}),$O=pt(()=>{let t=C.shallowRef(j.window.activeTerminal);return re(j.window.onDidChangeActiveTerminal(e=>{t.value=e})),t}),HO=pt(()=>{let t=C.shallowRef(j.extensions.all);return re(j.extensions.onDidChange(()=>{t.value=j.extensions.all})),C.computed(()=>t.value)});function aD(t,e){re(j.commands.registerCommand(t,e))}function UO(t){for(let[e,n]of Object.entries(t))n&&aD(e,n)}function VO(t,e){return re(j.comments.createCommentController(t,e))}function pm(t){var e;let n=C.shallowRef((e=C.toValue(t))==null?void 0:e.state);return C.watch(t,()=>{var r;n.value=(r=C.toValue(t))==null?void 0:r.state}),re(j.window.onDidChangeTerminalState(r=>{r===C.toValue(t)&&(n.value=r.state)})),C.computed(()=>n.value)}function WO(...t){let e=C.ref(null);function n(){return!!e.value&&e.value.exitStatus==null}function r(){return n()?e.value:e.value=j.window.createTerminal(...t)}function i(a){r().sendText(a)}function s(){r().show()}function o(){n()&&(e.value.sendText(""),e.value.dispose(),e.value=null)}return C.onScopeDispose(o),{terminal:e,getIsActive:n,show:s,sendText:i,close:o,state:pm(e)}}var KO=pt(()=>{let t=C.shallowRef(j.env.shell);return re(j.env.onDidChangeShell(e=>{t.value=e})),C.computed(()=>t.value)});function Vr(t,e){let n=(r,i,s)=>{re(t(r,i,s))};return e?.forEach(r=>n(r)),n}function Ao(t,e=[]){let n=Array.isArray(t)?t:e??[],r=re(Array.isArray(t)||t==null?new j.EventEmitter:t),i=Vr(r.event,n);for(let s of n)i(s);return{event:r.event,fire:r.fire.bind(r),addListener:i}}async function zO(t){let e=Ds.value.secrets,n=C.ref(await e.get(t));return n.set=async r=>{n.value=r,await e.store(t,r)},n.remove=async()=>{n.value=void 0,await e.delete(t)},re(e.onDidChange(async r=>{r.key===t&&(n.value=await e.get(t))})),C.watch(n,r=>{r===void 0?e.delete(t):e.store(t,r)}),n}function BO(t){return C.computed(()=>j.tasks.fetchTasks(C.toValue(t)))}function GO(t){return C.computed(()=>j.Uri.file(C.toValue(t)))}function XO(t,e){let n=new j.EventEmitter,r=C.shallowRef();C.watchEffect(()=>{r.value&&n.fire(),r.value=C.toValue(e)}),re(j.languages.registerFoldingRangeProvider(t,{onDidChangeFoldingRanges:n.event,provideFoldingRanges(i,s,o){var a;return(a=r.value)==null?void 0:a.call(r,i,s,o)}}))}function JO(t,e,n,r){let i=C.shallowReactive(new Map),s=Ao(),o=Ao(),a=Ao(),c=C.computed(()=>{let d=C.toValue(t);return Array.isArray(d)?d:d instanceof Set?Array.from(d):[d]});function u(){let d=c.value;for(let[p,g]of i)d.includes(p)||(g.dispose(),i.delete(p));for(let p of d)if(!i.has(p)){let g=j.workspace.createFileSystemWatcher(p,C.toValue(e)||!1,C.toValue(n)||!1,C.toValue(r)||!1);g.onDidCreate(s.fire),g.onDidChange(o.fire),g.onDidDelete(a.fire),i.set(p,g)}}function f(){for(let d of i.values())d.dispose();i.clear()}return u(),C.watch(c,u),C.watch(()=>[C.toValue(e),C.toValue(n),C.toValue(r)],()=>{f(),u()}),C.onScopeDispose(f),{watchers:i,onDidCreate:s.event,onDidChange:o.event,onDidDelete:a.event}}var YO=pt(()=>{let t=rD();return C.computed(()=>t.value.kind===j.ColorThemeKind.Dark||t.value.kind===j.ColorThemeKind.HighContrast)}),QO=pt(()=>{let t=C.shallowRef(j.env.isTelemetryEnabled);return re(j.env.onDidChangeTelemetryEnabled(e=>{t.value=e})),C.computed(()=>t.value)});function ZO(t,...e){return C.computed(()=>typeof e[0]=="object"?j.l10n.t(C.toValue(t),C.toRaw(e[0])):j.l10n.t(C.toValue(t),...e.map(C.toValue)))}function cD(t,e){return re(j.window.createOutputChannel(t,e))}function eI(t){let e=new Date,n=String(e.getFullYear()).padStart(4,"0"),r=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),o=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0"),c=String(e.getMilliseconds()).padStart(3,"0");return`${n}-${r}-${i} ${s}:${o}:${a}.${c} [${t}] `}function uD(t,e={}){let n=e.outputChannel??cD(t),r=i=>(...s)=>{var o;n.appendLine((((o=e.getPrefix)==null?void 0:o.call(e,i))??"")+s.join(" "))};return{outputChannel:n,createLoggerFunc:r,info:r("INFO"),warn:r("WARN"),error:r("ERROR"),append:n.append.bind(n),appendLine:n.appendLine.bind(n),replace:n.replace.bind(n),clear:n.clear.bind(n),show:n.show.bind(n),hide:n.hide.bind(n)}}var tI=pt(()=>{let t=C.shallowRef(j.env.logLevel);return re(j.env.onDidChangeLogLevel(e=>{t.value=e})),C.computed(()=>t.value)});function lD(t){var e;let n=C.shallowRef(((e=C.toValue(t))==null?void 0:e.selections)??[]);return C.watch(t,()=>{var r;n.value=((r=C.toValue(t))==null?void 0:r.selections)??[]}),re(j.window.onDidChangeNotebookEditorSelection(r=>{r.notebookEditor===C.toValue(t)&&(n.value=r.selections)})),C.computed({get(){return n.value},set(r){n.value=r;let i=C.toValue(t);i&&(i.selections=r)}})}function nI(t){let e=lD(t);return C.computed({get(){return e.value[0]},set(n){e.value=e.value.toSpliced(0,1,n)}})}function rI(t){var e;let n=C.shallowRef(((e=C.toValue(t))==null?void 0:e.visibleRanges)??[]);return C.watch(t,()=>{var r;n.value=((r=C.toValue(t))==null?void 0:r.visibleRanges)??[]}),re(j.window.onDidChangeNotebookEditorVisibleRanges(r=>{r.notebookEditor===C.toValue(t)&&(n.value=r.visibleRanges)})),C.computed(()=>n.value)}var iI=pt(()=>{let t=C.shallowRef(j.window.terminals);function e(){t.value=j.window.terminals}return re(j.window.onDidOpenTerminal(e)),re(j.window.onDidCloseTerminal(e)),t});function sI(t={}){let e=re(j.window.createQuickPick()),n=Vr(e.onDidChangeActive),r=Vr(e.onDidChangeSelection),i=Vr(e.onDidAccept),s=Vr(e.onDidHide),o=Vr(e.onDidTriggerButton),a=Vr(e.onDidChangeValue);["items","buttons","title","step","totalSteps","enabled","busy","ignoreFocusOut","placeholder","canSelectMany","matchOnDescription","matchOnDetail","keepScrollPosition"].forEach(d=>{t[d]&&C.watchEffect(()=>e[d]=C.toValue(t[d]))}),t.value&&(e.value=t.value);let c=C.shallowRef(e.value);a(d=>c.value=d),t.activeItems&&(e.activeItems=t.activeItems);let u=C.shallowRef(e.activeItems);n(d=>u.value=d),t.selectedItems&&(e.selectedItems=t.selectedItems);let f=C.shallowRef(e.selectedItems);return r(d=>f.value=d),{...e,onDidChangeActive:n,onDidChangeSelection:r,onDidAccept:i,onDidHide:s,onDidTriggerButton:o,onDidChangeValue:a,value:c,activeItems:u,selectedItems:f}}function oI(t){let e=re(t.id?j.window.createStatusBarItem(t.id,t.alignment,t.priority):j.window.createStatusBarItem(t.alignment,t.priority));function n(r){let i=t[r];i!=null&&C.watchEffect(()=>e[r]=C.toValue(i))}return["name","text","tooltip","color","backgroundColor","command","accessibilityInformation"].forEach(n),e}var aI=pt(()=>{let t=C.shallowRef(j.tasks.taskExecutions);function e(){t.value=j.tasks.taskExecutions}return re(j.tasks.onDidStartTask(e)),re(j.tasks.onDidEndTask(e)),C.computed(()=>t.value)});function cI(...t){let e=re(j.window.createTerminal(...t));return{terminal:e,get name(){return e.name},get processId(){return e.processId},get creationOptions(){return e.creationOptions},get exitStatus(){return e.exitStatus},get shellIntegration(){return e.shellIntegration},sendText:e.sendText.bind(e),show:e.show.bind(e),hide:e.hide.bind(e),state:pm(e)}}function dD(t,e){re(j.commands.registerTextEditorCommand(t,e))}function uI(t){for(let[e,n]of Object.entries(t))dD(e,n)}function fD(t,e){var n;let r=C.shallowRef(((n=C.toValue(t))==null?void 0:n.selections)??[]);return C.watch(t,()=>{var i;r.value=((i=C.toValue(t))==null?void 0:i.selections)??[]}),re(j.window.onDidChangeTextEditorSelection(i=>{let s=C.toValue(t),o=C.toValue(e);i.textEditor===s&&(!o||o.includes(i.kind))&&(r.value=i.selections)})),C.computed({get(){return r.value},set(i){r.value=i;let s=C.toValue(t);s&&(s.selections=i)}})}function lI(t,e){let n=fD(t,e);return C.computed({get(){return n.value[0]},set(r){n.value=n.value.toSpliced(0,1,r)}})}function dI(t){var e;let n=C.shallowRef((e=C.toValue(t))==null?void 0:e.viewColumn);return C.watch(t,()=>{var r;n.value=(r=C.toValue(t))==null?void 0:r.viewColumn}),re(j.window.onDidChangeTextEditorViewColumn(r=>{r.textEditor===C.toValue(t)&&(n.value=r.viewColumn)})),C.computed(()=>n.value)}function fI(t){var e;let n=C.shallowRef(((e=C.toValue(t))==null?void 0:e.visibleRanges)??[]);return C.watch(t,()=>{var r;n.value=((r=C.toValue(t))==null?void 0:r.visibleRanges)??[]}),re(j.window.onDidChangeTextEditorVisibleRanges(r=>{r.textEditor===C.toValue(t)&&(n.value=r.visibleRanges)})),C.computed(()=>n.value)}function hm(t,e){C.watchEffect(()=>{let n=C.toValue(t);n&&(n.badge=C.toValue(e))})}function lu(t,e){C.watchEffect(()=>{let n=C.toValue(t);n&&(n.title=C.toValue(e))})}var pI=um((t,e,n)=>{let r=Ao();C.watch(e,()=>r.fire()),n!=null&&n.watchSource&&C.watch(n.watchSource,()=>r.fire());let i=new WeakMap,s=re(j.window.createTreeView(t,{...n,treeDataProvider:{...n,onDidChangeTreeData:r.event,getTreeItem(o){return o.treeItem},getChildren(o){var a;return o?((a=o.children)==null||a.forEach(c=>i.set(c,o)),o.children):C.toValue(e)},getParent(o){return i.get(o)}}}));return n!=null&&n.title&&lu(s,n.title),n!=null&&n.badge&&hm(s,n.badge),s},t=>t);function hI(t){var e;let n=C.ref((e=C.toValue(t))==null?void 0:e.visible);function r(){var i;n.value=(i=C.toValue(t))==null?void 0:i.visible}return C.watchEffect(i=>{let s=C.toValue(t);if(s){let o=s.onDidChangeVisibility(r);i(()=>o.dispose())}}),C.watchEffect(r),C.computed(()=>!!n.value)}var gI=pt(()=>{let t=C.shallowRef(j.window.visibleNotebookEditors);return re(j.window.onDidChangeVisibleNotebookEditors(e=>{t.value=e})),t}),mI=pt(()=>{let t=C.shallowRef(j.window.visibleTextEditors);return re(j.window.onDidChangeVisibleTextEditors(e=>{t.value=e})),t});function vI(t,e,n=!0){let r=C.isRef(e)?e:typeof e=="function"?C.computed(e):C.ref(e);return C.watchEffect(()=>{C.toValue(n)&&j.commands.executeCommand("setContext",t,r.value)}),r}function yI(t,e,n,r,i){let s=C.shallowRef(),o=re(j.window.createWebviewPanel(t,C.toValue(e),r,{enableFindWidget:i?.enableFindWidget,retainContextWhenHidden:i?.retainContextWhenHidden,...C.toValue(i?.webviewOptions)}));i!=null&&i.onDidReceiveMessage&&o.webview.onDidReceiveMessage(i.onDidReceiveMessage);let a=C.ref(0);function c(){a.value++}if(C.watchEffect(()=>{o.webview.html=`${C.toValue(n)}<!--${a.value}-->`}),i!=null&&i.webviewOptions){let p=i.webviewOptions;C.watchEffect(()=>{o.webview.options=C.toValue(p)})}lu(o,e);function u(p){return o.webview.postMessage(p)}let f=C.ref(o.active),d=C.ref(o.visible);return re(o.onDidChangeViewState(()=>{f.value=o.active,d.value=o.visible})),{panel:o,context:s,active:f,visible:d,postMessage:u,forceRefresh:c}}var wI=um((t,e,n)=>{let r=C.shallowRef(),i=C.shallowRef();re(j.window.registerWebviewViewProvider(t,{resolveWebviewView(c,u){r.value=c,i.value=u,n!=null&&n.onDidReceiveMessage&&c.webview.onDidReceiveMessage(n.onDidReceiveMessage)}},{webviewOptions:{retainContextWhenHidden:n?.retainContextWhenHidden}}));let s=C.ref(0);function o(){s.value++}if(C.watchEffect(()=>{r.value&&(r.value.webview.html=`${C.toValue(e)}<!--${s.value}-->`)}),n!=null&&n.webviewOptions){let c=n.webviewOptions;C.watchEffect(()=>{r.value&&(r.value.webview.options=C.toValue(c))})}n!=null&&n.title&&lu(r,n.title),n!=null&&n.badge&&hm(r,n.badge);function a(c){var u;return(u=r.value)==null?void 0:u.webview.postMessage(c)}return{view:r,context:i,postMessage:a,forceRefresh:o}},t=>t),bI=pt(()=>{let t=C.shallowRef(j.window.state);return re(j.window.onDidChangeWindowState(e=>{t.value=e})),{focused:C.computed(()=>t.value.focused),active:C.computed(()=>t.value.active)}}),CI=pt(()=>{let t=C.shallowRef(j.workspace.workspaceFolders);return re(j.workspace.onDidChangeWorkspaceFolders(()=>{t.value=j.workspace.workspaceFolders})),C.computed(()=>t.value)});L.activateCbs=dm;L.createKeyedComposable=um;L.createSingletonComposable=pt;L.deactivateCbs=lm;L.defineConfigObject=OO;L.defineConfigs=nD;L.defineExtension=kO;L.defineLogger=IO;L.executeCommand=NO;L.extensionContext=Ds;L.extensionScope=uu;L.getDefaultLoggerPrefix=eI;L.onActivate=fm;L.onDeactivate=qO;L.tryOnScopeDispose=tD;L.useAbsolutePath=MO;L.useActiveColorTheme=rD;L.useActiveDebugSession=AO;L.useActiveEditorDecorations=LO;L.useActiveNotebookEditor=jO;L.useActiveTerminal=$O;L.useActiveTextEditor=iD;L.useAllExtensions=HO;L.useCommand=aD;L.useCommands=UO;L.useCommentController=VO;L.useControlledTerminal=WO;L.useDefaultShell=KO;L.useDisposable=re;L.useDocumentText=sD;L.useEditorDecorations=oD;L.useEvent=Vr;L.useEventEmitter=Ao;L.useExtensionSecret=zO;L.useFetchTasks=BO;L.useFileUri=GO;L.useFoldingRangeProvider=XO;L.useFsWatcher=JO;L.useIsDarkTheme=YO;L.useIsTelemetryEnabled=QO;L.useL10nText=ZO;L.useLogLevel=tI;L.useLogger=uD;L.useNotebookEditorSelection=nI;L.useNotebookEditorSelections=lD;L.useNotebookEditorVisibleRanges=rI;L.useOpenedTerminals=iI;L.useOutputChannel=cD;L.useQuickPick=sI;L.useStatusBarItem=oI;L.useTaskExecutions=aI;L.useTerminal=cI;L.useTerminalState=pm;L.useTextEditorCommand=dD;L.useTextEditorCommands=uI;L.useTextEditorSelection=lI;L.useTextEditorSelections=fD;L.useTextEditorViewColumn=dI;L.useTextEditorVisibleRanges=fI;L.useTreeView=pI;L.useViewBadge=hm;L.useViewTitle=lu;L.useViewVisibility=hI;L.useVisibleNotebookEditors=gI;L.useVisibleTextEditors=mI;L.useVscodeContext=vI;L.useWebviewPanel=yI;L.useWebviewView=wI;L.useWindowState=bI;L.useWorkspaceFolders=CI;Object.keys(C).forEach(t=>{t!=="default"&&!Object.prototype.hasOwnProperty.call(L,t)&&Object.defineProperty(L,t,{enumerable:!0,get:()=>C[t]})})});var pD=b(Xe=>{"use strict";Object.defineProperty(Xe,"__esModule",{value:!0});Xe.scopedConfigs=Xe.configs=Xe.commands=Xe.extensionId=Xe.description=Xe.displayName=Xe.version=Xe.name=Xe.publisher=void 0;Xe.publisher="Vue";Xe.name="volar";Xe.version="2.2.10";Xe.displayName="Vue - Official";Xe.description="Language Support for Vue";Xe.extensionId=`${Xe.publisher}.${Xe.name}`;Xe.commands={actionRestartServer:"vue.action.restartServer",actionDoctor:"vue.action.doctor",actionWriteVirtualFiles:"vue.action.writeVirtualFiles",actionSplitEditors:"vue.action.splitEditors",findAllFileReferences:"vue.findAllFileReferences"};Xe.configs={traceServer:{key:"vue.trace.server",default:"off"},serverHybridMode:{key:"vue.server.hybridMode",default:"auto"},serverCompatibleExtensions:{key:"vue.server.compatibleExtensions",default:[]},serverIncludeLanguages:{key:"vue.server.includeLanguages",default:["vue"]},serverMaxOldSpaceSize:{key:"vue.server.maxOldSpaceSize",default:null},doctorStatus:{key:"vue.doctor.status",default:!0},splitEditorsIcon:{key:"vue.splitEditors.icon",default:!1},splitEditorsLayoutLeft:{key:"vue.splitEditors.layout.left",default:["script","scriptSetup","styles"]},splitEditorsLayoutRight:{key:"vue.splitEditors.layout.right",default:["template","customBlocks"]},updateImportsOnFileMoveEnabled:{key:"vue.updateImportsOnFileMove.enabled",default:!0},codeActionsEnabled:{key:"vue.codeActions.enabled",default:!0},codeActionsAskNewComponentName:{key:"vue.codeActions.askNewComponentName",default:!0},codeLensEnabled:{key:"vue.codeLens.enabled",default:!0},completeCasingTags:{key:"vue.complete.casing.tags",default:"autoPascal"},completeCasingProps:{key:"vue.complete.casing.props",default:"autoKebab"},completeDefineAssignment:{key:"vue.complete.defineAssignment",default:!0},autoInsertDotValue:{key:"vue.autoInsert.dotValue",default:!1},autoInsertBracketSpacing:{key:"vue.autoInsert.bracketSpacing",default:!0},inlayHintsDestructuredProps:{key:"vue.inlayHints.destructuredProps",default:!1},inlayHintsMissingProps:{key:"vue.inlayHints.missingProps",default:!1},inlayHintsInlineHandlerLeading:{key:"vue.inlayHints.inlineHandlerLeading",default:!1},inlayHintsOptionsWrapper:{key:"vue.inlayHints.optionsWrapper",default:!1},inlayHintsVBindShorthand:{key:"vue.inlayHints.vBindShorthand",default:!1},formatTemplateInitialIndent:{key:"vue.format.template.initialIndent",default:!0},formatStyleInitialIndent:{key:"vue.format.style.initialIndent",default:!1},formatScriptInitialIndent:{key:"vue.format.script.initialIndent",default:!1},formatWrapAttributes:{key:"vue.format.wrapAttributes",default:"auto"}};Xe.scopedConfigs={scope:"vue",defaults:{"trace.server":"off","server.hybridMode":"auto","server.compatibleExtensions":[],"server.includeLanguages":["vue"],"server.maxOldSpaceSize":null,"doctor.status":!0,"splitEditors.icon":!1,"splitEditors.layout.left":["script","scriptSetup","styles"],"splitEditors.layout.right":["template","customBlocks"],"updateImportsOnFileMove.enabled":!0,"codeActions.enabled":!0,"codeActions.askNewComponentName":!0,"codeLens.enabled":!0,"complete.casing.tags":"autoPascal","complete.casing.props":"autoKebab","complete.defineAssignment":!0,"autoInsert.dotValue":!1,"autoInsert.bracketSpacing":!0,"inlayHints.destructuredProps":!1,"inlayHints.missingProps":!1,"inlayHints.inlineHandlerLeading":!1,"inlayHints.optionsWrapper":!1,"inlayHints.vBindShorthand":!1,"format.template.initialIndent":!0,"format.style.initialIndent":!1,"format.script.initialIndent":!1,"format.wrapAttributes":"auto"}}});var fr=b(du=>{"use strict";Object.defineProperty(du,"__esModule",{value:!0});du.config=void 0;var _I=Kn(),hD=pD();du.config=(0,_I.defineConfigObject)(hD.scopedConfigs.scope,hD.scopedConfigs.defaults)});var mD=b((EA,gD)=>{"use strict";var SI=Mr(),DI=(t,e)=>{let n=SI(t,e);return n?n.version:null};gD.exports=DI});var yD=b((xA,vD)=>{"use strict";var RI=Mr(),TI=(t,e)=>{let n=RI(t.trim().replace(/^[=v]+/,""),e);return n?n.version:null};vD.exports=TI});var CD=b((qA,bD)=>{"use strict";var wD=Ct(),PI=(t,e,n,r,i)=>{typeof n=="string"&&(i=r,r=n,n=void 0);try{return new wD(t instanceof wD?t.version:t,n).inc(e,r,i).version}catch{return null}};bD.exports=PI});var DD=b((kA,SD)=>{"use strict";var _D=Mr(),EI=(t,e)=>{let n=_D(t,null,!0),r=_D(e,null,!0),i=n.compare(r);if(i===0)return null;let s=i>0,o=s?n:r,a=s?r:n,c=!!o.prerelease.length;if(!!a.prerelease.length&&!c)return!a.patch&&!a.minor?"major":o.patch?"patch":o.minor?"minor":"major";let f=c?"pre":"";return n.major!==r.major?f+"major":n.minor!==r.minor?f+"minor":n.patch!==r.patch?f+"patch":"prerelease"};SD.exports=EI});var TD=b((OA,RD)=>{"use strict";var xI=Ct(),qI=(t,e)=>new xI(t,e).major;RD.exports=qI});var ED=b((IA,PD)=>{"use strict";var kI=Ct(),OI=(t,e)=>new kI(t,e).minor;PD.exports=OI});var qD=b((NA,xD)=>{"use strict";var II=Ct(),NI=(t,e)=>new II(t,e).patch;xD.exports=NI});var OD=b((FA,kD)=>{"use strict";var FI=Mr(),MI=(t,e)=>{let n=FI(t,e);return n&&n.prerelease.length?n.prerelease:null};kD.exports=MI});var ND=b((MA,ID)=>{"use strict";var AI=gn(),LI=(t,e,n)=>AI(e,t,n);ID.exports=LI});var MD=b((AA,FD)=>{"use strict";var jI=gn(),$I=(t,e)=>jI(t,e,!0);FD.exports=$I});var fu=b((LA,LD)=>{"use strict";var AD=Ct(),HI=(t,e,n)=>{let r=new AD(t,n),i=new AD(e,n);return r.compare(i)||r.compareBuild(i)};LD.exports=HI});var $D=b((jA,jD)=>{"use strict";var UI=fu(),VI=(t,e)=>t.sort((n,r)=>UI(n,r,e));jD.exports=VI});var UD=b(($A,HD)=>{"use strict";var WI=fu(),KI=(t,e)=>t.sort((n,r)=>WI(r,n,e));HD.exports=KI});var WD=b((HA,VD)=>{"use strict";var zI=Ct(),BI=Mr(),{safeRe:pu,t:hu}=gs(),GI=(t,e)=>{if(t instanceof zI)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let n=null;if(!e.rtl)n=t.match(e.includePrerelease?pu[hu.COERCEFULL]:pu[hu.COERCE]);else{let c=e.includePrerelease?pu[hu.COERCERTLFULL]:pu[hu.COERCERTL],u;for(;(u=c.exec(t))&&(!n||n.index+n[0].length!==t.length);)(!n||u.index+u[0].length!==n.index+n[0].length)&&(n=u),c.lastIndex=u.index+u[1].length+u[2].length;c.lastIndex=-1}if(n===null)return null;let r=n[2],i=n[3]||"0",s=n[4]||"0",o=e.includePrerelease&&n[5]?`-${n[5]}`:"",a=e.includePrerelease&&n[6]?`+${n[6]}`:"";return BI(`${r}.${i}.${s}${o}${a}`,e)};VD.exports=GI});var zD=b((UA,KD)=>{"use strict";var XI=mn(),JI=(t,e)=>new XI(t,e).set.map(n=>n.map(r=>r.value).join(" ").trim().split(" "));KD.exports=JI});var GD=b((VA,BD)=>{"use strict";var YI=Ct(),QI=mn(),ZI=(t,e,n)=>{let r=null,i=null,s=null;try{s=new QI(e,n)}catch{return null}return t.forEach(o=>{s.test(o)&&(!r||i.compare(o)===-1)&&(r=o,i=new YI(r,n))}),r};BD.exports=ZI});var JD=b((WA,XD)=>{"use strict";var eN=Ct(),tN=mn(),nN=(t,e,n)=>{let r=null,i=null,s=null;try{s=new tN(e,n)}catch{return null}return t.forEach(o=>{s.test(o)&&(!r||i.compare(o)===1)&&(r=o,i=new eN(r,n))}),r};XD.exports=nN});var ZD=b((KA,QD)=>{"use strict";var gm=Ct(),rN=mn(),YD=To(),iN=(t,e)=>{t=new rN(t,e);let n=new gm("0.0.0");if(t.test(n)||(n=new gm("0.0.0-0"),t.test(n)))return n;n=null;for(let r=0;r<t.set.length;++r){let i=t.set[r],s=null;i.forEach(o=>{let a=new gm(o.semver.version);switch(o.operator){case">":a.prerelease.length===0?a.patch++:a.prerelease.push(0),a.raw=a.format();case"":case">=":(!s||YD(a,s))&&(s=a);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${o.operator}`)}}),s&&(!n||YD(n,s))&&(n=s)}return n&&t.test(n)?n:null};QD.exports=iN});var tR=b((zA,eR)=>{"use strict";var sN=mn(),oN=(t,e)=>{try{return new sN(t,e).range||"*"}catch{return null}};eR.exports=oN});var gu=b((BA,sR)=>{"use strict";var aN=Ct(),iR=Eo(),{ANY:cN}=iR,uN=mn(),lN=vs(),nR=To(),rR=Vc(),dN=Wc(),fN=Uc(),pN=(t,e,n,r)=>{t=new aN(t,r),e=new uN(e,r);let i,s,o,a,c;switch(n){case">":i=nR,s=dN,o=rR,a=">",c=">=";break;case"<":i=rR,s=fN,o=nR,a="<",c="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(lN(t,e,r))return!1;for(let u=0;u<e.set.length;++u){let f=e.set[u],d=null,p=null;if(f.forEach(g=>{g.semver===cN&&(g=new iR(">=0.0.0")),d=d||g,p=p||g,i(g.semver,d.semver,r)?d=g:o(g.semver,p.semver,r)&&(p=g)}),d.operator===a||d.operator===c||(!p.operator||p.operator===a)&&s(t,p.semver))return!1;if(p.operator===c&&o(t,p.semver))return!1}return!0};sR.exports=pN});var aR=b((GA,oR)=>{"use strict";var hN=gu(),gN=(t,e,n)=>hN(t,e,">",n);oR.exports=gN});var uR=b((XA,cR)=>{"use strict";var mN=gu(),vN=(t,e,n)=>mN(t,e,"<",n);cR.exports=vN});var fR=b((JA,dR)=>{"use strict";var lR=mn(),yN=(t,e,n)=>(t=new lR(t,n),e=new lR(e,n),t.intersects(e,n));dR.exports=yN});var hR=b((YA,pR)=>{"use strict";var wN=vs(),bN=gn();pR.exports=(t,e,n)=>{let r=[],i=null,s=null,o=t.sort((f,d)=>bN(f,d,n));for(let f of o)wN(f,e,n)?(s=f,i||(i=f)):(s&&r.push([i,s]),s=null,i=null);i&&r.push([i,null]);let a=[];for(let[f,d]of r)f===d?a.push(f):!d&&f===o[0]?a.push("*"):d?f===o[0]?a.push(`<=${d}`):a.push(`${f} - ${d}`):a.push(`>=${f}`);let c=a.join(" || "),u=typeof e.raw=="string"?e.raw:String(e);return c.length<u.length?c:e}});var bR=b((QA,wR)=>{"use strict";var gR=mn(),vm=Eo(),{ANY:mm}=vm,Lo=vs(),ym=gn(),CN=(t,e,n={})=>{if(t===e)return!0;t=new gR(t,n),e=new gR(e,n);let r=!1;e:for(let i of t.set){for(let s of e.set){let o=SN(i,s,n);if(r=r||o!==null,o)continue e}if(r)return!1}return!0},_N=[new vm(">=0.0.0-0")],mR=[new vm(">=0.0.0")],SN=(t,e,n)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===mm){if(e.length===1&&e[0].semver===mm)return!0;n.includePrerelease?t=_N:t=mR}if(e.length===1&&e[0].semver===mm){if(n.includePrerelease)return!0;e=mR}let r=new Set,i,s;for(let g of t)g.operator===">"||g.operator===">="?i=vR(i,g,n):g.operator==="<"||g.operator==="<="?s=yR(s,g,n):r.add(g.semver);if(r.size>1)return null;let o;if(i&&s){if(o=ym(i.semver,s.semver,n),o>0)return null;if(o===0&&(i.operator!==">="||s.operator!=="<="))return null}for(let g of r){if(i&&!Lo(g,String(i),n)||s&&!Lo(g,String(s),n))return null;for(let m of e)if(!Lo(g,String(m),n))return!1;return!0}let a,c,u,f,d=s&&!n.includePrerelease&&s.semver.prerelease.length?s.semver:!1,p=i&&!n.includePrerelease&&i.semver.prerelease.length?i.semver:!1;d&&d.prerelease.length===1&&s.operator==="<"&&d.prerelease[0]===0&&(d=!1);for(let g of e){if(f=f||g.operator===">"||g.operator===">=",u=u||g.operator==="<"||g.operator==="<=",i){if(p&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===p.major&&g.semver.minor===p.minor&&g.semver.patch===p.patch&&(p=!1),g.operator===">"||g.operator===">="){if(a=vR(i,g,n),a===g&&a!==i)return!1}else if(i.operator===">="&&!Lo(i.semver,String(g),n))return!1}if(s){if(d&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===d.major&&g.semver.minor===d.minor&&g.semver.patch===d.patch&&(d=!1),g.operator==="<"||g.operator==="<="){if(c=yR(s,g,n),c===g&&c!==s)return!1}else if(s.operator==="<="&&!Lo(s.semver,String(g),n))return!1}if(!g.operator&&(s||i)&&o!==0)return!1}return!(i&&u&&!s&&o!==0||s&&f&&!i&&o!==0||p||d)},vR=(t,e,n)=>{if(!t)return e;let r=ym(t.semver,e.semver,n);return r>0?t:r<0||e.operator===">"&&t.operator===">="?e:t},yR=(t,e,n)=>{if(!t)return e;let r=ym(t.semver,e.semver,n);return r<0?t:r>0||e.operator==="<"&&t.operator==="<="?e:t};wR.exports=CN});var mu=b((ZA,SR)=>{"use strict";var wm=gs(),CR=Ro(),DN=Ct(),_R=gg(),RN=Mr(),TN=mD(),PN=yD(),EN=CD(),xN=DD(),qN=TD(),kN=ED(),ON=qD(),IN=OD(),NN=gn(),FN=ND(),MN=MD(),AN=fu(),LN=$D(),jN=UD(),$N=To(),HN=Vc(),UN=yg(),VN=wg(),WN=Uc(),KN=Wc(),zN=bg(),BN=WD(),GN=Eo(),XN=mn(),JN=vs(),YN=zD(),QN=GD(),ZN=JD(),eF=ZD(),tF=tR(),nF=gu(),rF=aR(),iF=uR(),sF=fR(),oF=hR(),aF=bR();SR.exports={parse:RN,valid:TN,clean:PN,inc:EN,diff:xN,major:qN,minor:kN,patch:ON,prerelease:IN,compare:NN,rcompare:FN,compareLoose:MN,compareBuild:AN,sort:LN,rsort:jN,gt:$N,lt:HN,eq:UN,neq:VN,gte:WN,lte:KN,cmp:zN,coerce:BN,Comparator:GN,Range:XN,satisfies:JN,toComparators:YN,maxSatisfying:QN,minSatisfying:ZN,minVersion:eF,validRange:tF,outside:nF,gtr:rF,ltr:iF,intersects:sF,simplifyRange:oF,subset:aF,SemVer:DN,re:wm.re,src:wm.src,tokens:wm.t,SEMVER_SPEC_VERSION:CR.SEMVER_SPEC_VERSION,RELEASE_TYPES:CR.RELEASE_TYPES,compareIdentifiers:_R.compareIdentifiers,rcompareIdentifiers:_R.rcompareIdentifiers}});var TR=b(Rs=>{"use strict";Object.defineProperty(Rs,"__esModule",{value:!0});Rs.unknownExtensions=Rs.incompatibleExtensions=void 0;var bm=Kn(),cF=mu(),uF=require("vscode"),lF=fr(),dF=new Set(["astro-build.astro-vscode","bierner.lit-html","Divlo.vscode-styled-jsx-languageserver","GitHub.copilot-chat","ije.esm-vscode","jenkey2011.string-highlight","johnsoncodehk.vscode-tsslint","kimuson.ts-type-expand","miaonster.vscode-tsx-arrow-definition","ms-dynamics-smb.al","mxsdev.typescript-explorer","nrwl.angular-console","p42ai.refactor","runem.lit-plugin","ShenQingchuan.vue-vine-extension","styled-components.vscode-styled-components","unifiedjs.vscode-mdx","VisualStudioExptTeam.vscodeintellicode","Vue.volar"]),DR=(0,bm.useAllExtensions)();Rs.incompatibleExtensions=(0,bm.computed)(()=>DR.value.filter(t=>RR(t)===!1).map(t=>t.id));Rs.unknownExtensions=(0,bm.computed)(()=>DR.value.filter(t=>RR(t)===void 0&&!!t.packageJSON?.contributes?.typescriptServerPlugins).map(t=>t.id));function RR(t){if(dF.has(t.id)||lF.config.server.compatibleExtensions.includes(t.id))return!0;if(t.id==="denoland.vscode-deno")return!uF.workspace.getConfiguration("deno").get("enable");if(t.id==="svelte.svelte-vscode")return cF.gte(t.packageJSON.version,"108.4.0")}});var _m=b(bn=>{"use strict";Object.defineProperty(bn,"__esModule",{value:!0});bn.enabledTypeScriptPlugin=bn.enabledHybridMode=void 0;bn.useHybridModeTips=hF;bn.useHybridModeStatusItem=gF;var fF=require("node:fs"),PR=require("node:path"),zn=Kn(),vu=mu(),wn=require("vscode"),Wr=TR(),Kr=fr(),pF=(0,zn.useAllExtensions)();bn.enabledHybridMode=(0,zn.computed)(()=>Kr.config.server.hybridMode==="typeScriptPluginOnly"?!1:Kr.config.server.hybridMode==="auto"?Wr.incompatibleExtensions.value.length||Wr.unknownExtensions.value.length?!1:!(jo.value&&!vu.gte(jo.value,"5.3.0")||Ts.value&&!vu.gte(Ts.value,"5.3.0")):Kr.config.server.hybridMode);bn.enabledTypeScriptPlugin=(0,zn.computed)(()=>bn.enabledHybridMode.value||Kr.config.server.hybridMode==="typeScriptPluginOnly");var jo=(0,zn.computed)(()=>{let t=pF.value.find(({id:e})=>e==="ms-vscode.vscode-typescript-next");if(t){let e=PR.join(t.extensionPath.replace(/\\/g,"/"),"node_modules/typescript/lib");return Cm(e)}if(wn.env.appRoot){let e=PR.join(wn.env.appRoot.replace(/\\/g,"/"),"extensions/node_modules/typescript/lib");return Cm(e)}}),Ts=(0,zn.computed)(()=>{let t=wn.workspace.getConfiguration("typescript").get("tsdk")?.replace(/\\/g,"/");if(t)return Cm(t)});function hF(){(0,zn.useVscodeContext)("vueHybridMode",bn.enabledHybridMode),(0,zn.watchEffect)(()=>{if(Kr.config.server.hybridMode==="auto"){if(Wr.incompatibleExtensions.value.length||Wr.unknownExtensions.value.length)wn.window.showInformationMessage(`Hybrid Mode is disabled automatically because there is a potentially incompatible ${[...Wr.incompatibleExtensions.value,...Wr.unknownExtensions.value].join(", ")} TypeScript plugin installed.`,"Open Settings","Report a false positive").then(t=>{t==="Open Settings"?(0,zn.executeCommand)("workbench.action.openSettings","vue.server.hybridMode"):t=="Report a false positive"&&wn.env.openExternal(wn.Uri.parse("https://github.com/vuejs/language-tools/pull/4206"))});else if(jo.value&&!vu.gte(jo.value,"5.3.0")||Ts.value&&!vu.gte(Ts.value,"5.3.0")){let t=`Hybrid Mode is disabled automatically because TSDK >= 5.3.0 is required (VSCode TSDK: ${jo.value}`;Ts.value&&(t+=`, Workspace TSDK: ${Ts.value}`),t+=").",wn.window.showInformationMessage(t,"Open Settings").then(e=>{e==="Open Settings"&&(0,zn.executeCommand)("workbench.action.openSettings","vue.server.hybridMode")})}}else Kr.config.server.hybridMode&&Wr.incompatibleExtensions.value.length&&wn.window.showWarningMessage(`You have explicitly enabled Hybrid Mode, but you have installed known incompatible extensions: ${Wr.incompatibleExtensions.value.join(", ")}. You may want to change vue.server.hybridMode to "auto" to avoid compatibility issues.`,"Open Settings","Report a false positive").then(t=>{t==="Open Settings"?(0,zn.executeCommand)("workbench.action.openSettings","vue.server.hybridMode"):t=="Report a false positive"&&wn.env.openExternal(wn.Uri.parse("https://github.com/vuejs/language-tools/pull/4206"))})})}function gF(){let t=wn.languages.createLanguageStatusItem("vue-hybrid-mode",Kr.config.server.includeLanguages);t.text="Hybrid Mode",t.detail=(bn.enabledHybridMode.value?"Enabled":"Disabled")+(Kr.config.server.hybridMode==="auto"?" (Auto)":""),t.command={title:"Open Setting",command:"workbench.action.openSettings",arguments:["vue.server.hybridMode"]},bn.enabledHybridMode.value||(t.severity=wn.LanguageStatusSeverity.Warning)}function Cm(t){try{let i=t.toString().split("/").slice(0,-1).join("/")+"/package.json",s=fF.readFileSync(i,"utf-8");if(s===void 0)return;let o=null;try{o=JSON.parse(s)}catch{return}return!o||!o.version?void 0:o.version}catch{}}});var xR=b(ER=>{"use strict";Object.defineProperty(ER,"__esModule",{value:!0})});var kR=b(yu=>{"use strict";Object.defineProperty(yu,"__esModule",{value:!0});yu.FileType=void 0;var qR;(function(t){t[t.Unknown=0]="Unknown",t[t.File=1]="File",t[t.Directory=2]="Directory",t[t.SymbolicLink=64]="SymbolicLink"})(qR||(yu.FileType=qR={}))});var OR=b(wu=>{"use strict";Object.defineProperty(wu,"__esModule",{value:!0});wu.validVersions=void 0;wu.validVersions=[2,2.1]});var MR=b(zt=>{"use strict";var mF=zt&&zt.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),FR=zt&&zt.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&mF(e,t,n)};Object.defineProperty(zt,"__esModule",{value:!0});zt.commands=zt.AttrNameCasing=zt.TagNameCasing=void 0;var IR;(function(t){t[t.Kebab=0]="Kebab",t[t.Pascal=1]="Pascal"})(IR||(zt.TagNameCasing=IR={}));var NR;(function(t){t[t.Kebab=0]="Kebab",t[t.Camel=1]="Camel"})(NR||(zt.AttrNameCasing=NR={}));zt.commands={parseSfc:"vue.parseSfc",detectNameCasing:"vue.detectNameCasing",convertTagsToKebabCase:"vue.convertTagsToKebabCase",convertTagsToPascalCase:"vue.convertTagsToPascalCase",convertPropsToKebabCase:"vue.convertPropsToKebabCase",convertPropsToCamelCase:"vue.convertPropsToCamelCase"};FR(kR(),zt);FR(OR(),zt)});var $o=b(zr=>{"use strict";var vF=zr&&zr.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),AR=zr&&zr.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&vF(e,t,n)};Object.defineProperty(zr,"__esModule",{value:!0});AR(xR(),zr);AR(MR(),zr)});var HR=b(Sm=>{"use strict";Object.defineProperty(Sm,"__esModule",{value:!0});Sm.activate=bF;var LR=vi(),yF=$o(),Br=Kn(),Si=mu(),On=require("vscode"),wF=fr(),jR="vue-doctor",$R={postcss:["cpylua.language-postcss","vunguyentuan.vscode-postcss","csstools.postcss"],stylus:["sysoev.language-stylus"],sass:["Syler.sass-indented"]};async function bF(t){let e=(0,Br.useStatusBarItem)({alignment:On.StatusBarAlignment.Right,backgroundColor:new On.ThemeColor("statusBarItem.warningBackground"),command:"vue.action.doctor"}),n=(0,Br.useActiveTextEditor)(),r=(0,Br.useEventEmitter)();(0,Br.watchEffect)(s),(0,Br.useCommand)("vue.action.doctor",()=>{let a=n.value?.document;a&&(a.languageId==="vue"||a.uri.toString().endsWith(".vue"))&&a.uri.scheme==="file"&&(0,Br.executeCommand)("markdown.showPreviewToSide",i(a.uri))}),(0,Br.useDisposable)(On.workspace.registerTextDocumentContentProvider(jR,{onDidChange:r.event,async provideTextDocumentContent(a){let c=a.with({scheme:"file",path:a.path.slice(0,-10)}),u=await o(c),f=`# ${c.path.split("/").pop()} Doctor

`;for(let d of u)f+="## \u2757 "+d.title+`

`,f+=d.message+`

`;return f+=`---

`,f+=`> Have any questions about the report message? You can see how it is composed by inspecting the [source code](https://github.com/vuejs/language-tools/blob/master/extensions/vscode/src/features/doctor.ts).

`,f.trim()}}));function i(a){return a.with({scheme:jR,path:a.path+"/Doctor.md"})}async function s(){let a=n.value;if(wF.config.doctor.status&&a&&(a.document.languageId==="vue"||a.document.uri.toString().endsWith(".vue"))&&a.document.uri.scheme==="file"){let c=await o(a.document.uri);c.length&&On.window.activeTextEditor?.document===a.document&&(e.show(),e.text=c.length+(c.length===1?" known issue":" known issues"),r.fire(i(a.document.uri)))}else e.hide()}async function o(a){let c=On.workspace.textDocuments.find(k=>k.fileName===a.fsPath),u=c?await t.sendRequest(LR.ExecuteCommandRequest.type,{command:yF.commands.parseSfc,arguments:[c.getText()]}):void 0,f=bu(a.fsPath,"vue"),d=bu(a.fsPath,"@vue/runtime-dom"),p=[];f&&Si.lt(f.json.version,"2.7.0")&&!d?p.push({title:"`@vue/runtime-dom` missing for Vue 2",message:["Vue 2 does not have JSX types definitions, so template type checking will not work correctly. You can resolve this problem by installing `@vue/runtime-dom` and adding it to your project's `devDependencies`.","","- vue: "+f.path].join(`
`)}):f&&Si.gte(f.json.version,"2.7.0")&&Si.lt(f.json.version,"3.0.0")&&d&&p.push({title:"Unnecessary `@vue/runtime-dom`",message:["Vue 2.7 already includes JSX type definitions. You can remove the `@vue/runtime-dom` dependency from package.json.","","- vue: "+f.path,"- @vue/runtime-dom: "+d.path].join(`
`)});let g=bu(a.fsPath,"@types/node");g&&Si.gte(g.json.version,"18.8.1")&&Si.lte(g.json.version,"18.11.0")&&p.push({title:"Incompatible `@types/node` version",message:["`@types/node`'s version `"+g.json.version+"` is incompatible with Vue. It will cause broken DOM event types in template.","","You can update `@types/node` to `18.11.1` or later to resolve.","","- @types/node: "+g.path,"- Issue: https://github.com/vuejs/language-tools/issues/1985"].join(`
`)});let m=await bu(a.fsPath,"@vue/language-plugin-pug");if(u?.descriptor.template?.lang==="pug"&&!m&&p.push({title:"`@vue/language-plugin-pug` missing",message:['For `<template lang="pug">`, the `@vue/language-plugin-pug` plugin is required. Install it using `$ npm install -D @vue/language-plugin-pug` and add it to `vueCompilerOptions.plugins` to support TypeScript intellisense in Pug templates.',"","- package.json","```json",JSON.stringify({devDependencies:{"@vue/language-plugin-pug":"latest"}},void 0,2),"```","","- tsconfig.json / jsconfig.json","```jsonc",JSON.stringify({vueCompilerOptions:{plugins:["@vue/language-plugin-pug"]}},void 0,2),"```"].join(`
`)}),u?.descriptor.template?.lang==="pug"&&m&&!Si.gte(m.json.version,"2.0.5")&&p.push({title:"Outdated `@vue/language-plugin-pug`",message:["The version of `@vue/language-plugin-pug` is too low, it is required to upgrade to `2.0.5` or later.","","- @vue/language-plugin-pug: "+m.path].join(`
`)}),u){let k=[u.descriptor.template,u.descriptor.script,u.descriptor.scriptSetup,...u.descriptor.styles,...u.descriptor.customBlocks];for(let x of k)if(x&&x.lang&&x.lang in $R){let I=$R[x.lang];I.some(Y=>!!On.extensions.getExtension(Y))||p.push({title:"Syntax Highlighting for "+x.lang,message:`Did not find a valid syntax highlighter extension for ${x.lang} language block; you can choose to install one of the following:

`+I.map(Y=>`- [${Y}](https://marketplace.visualstudio.com/items?itemName=${Y})
`)})}}On.workspace.getConfiguration("emmet").get("includeLanguages")?.vue&&p.push({title:"Unnecessary `emmet.includeLanguages.vue`",message:"Vue language server already supports Emmet. You can remove `emmet.includeLanguages.vue` from `.vscode/settings.json`."}),On.workspace.getConfiguration("files").get("associations")?.["*.vue"]==="html"&&p.push({title:'Unnecessary `files.associations["*.vue"]`',message:'With `"files.associations": { "*.vue": html }`, language server cannot to recognize Vue files. You can remove `files.associations["*.vue"]` from `.vscode/settings.json`.'});let P=await(0,LR.getTsdk)(Br.extensionContext.value);return P.version&&!Si.gte(P.version,"5.0.0")&&p.push({title:"Requires TSDK 5.0 or higher",message:[`Extension >= 2.0 requires TSDK 5.0+. You are currently using TSDK ${P.version}, please upgrade to TSDK.`,"If you need to use TSDK 4.x, please downgrade the extension to v1."].join(`
`)}),(On.workspace.getConfiguration("vue").has("server.additionalExtensions")||On.workspace.getConfiguration("vue").has("server.petiteVue.supportHtmlFile")||On.workspace.getConfiguration("vue").has("server.vitePress.supportMdFile"))&&p.push({title:"Deprecated configuration",message:["`vue.server.additionalExtensions`, `vue.server.petiteVue.supportHtmlFile`, and `vue.server.vitePress.supportMdFile` are deprecated. Please remove them from your settings.","","- PR: https://github.com/vuejs/language-tools/pull/4321"].join(`
`)}),p}}function bu(t,e){try{let n=require.resolve(e+"/package.json",{paths:[t]});return{path:n,json:require(n)}}catch{}}});var Pm=b(Me=>{"use strict";Object.defineProperty(Me,"__esModule",{value:!0});Me.tagNameCasings=Me.attrNameCasings=void 0;Me.activate=_F;var Dm=vi(),CF=ja(),_e=$o(),pr=Kn(),Rm=require("vscode"),Tm=fr();Me.attrNameCasings=(0,pr.reactive)(new Map);Me.tagNameCasings=(0,pr.reactive)(new Map);async function _F(t,e){await t.start();let n=(0,pr.useActiveTextEditor)(),r=(0,pr.useDisposable)(Rm.languages.createLanguageStatusItem("vue-name-casing",e));r.command={title:"Open Menu",command:"vue.action.nameCasing"},(0,pr.watchEffect)(()=>{let c=n.value?.document;if(!c)return"";let u=Me.attrNameCasings.get(c.uri.toString()),f=Me.tagNameCasings.get(c.uri.toString()),d="<";f===_e.TagNameCasing.Kebab?d+="tag-name ":f===_e.TagNameCasing.Pascal?d+="TagName ":d+="? ",u===_e.AttrNameCasing.Kebab?d+="prop-name":u===_e.AttrNameCasing.Camel?d+="propName":d+="?",d+=" />",r.text=d}),(0,pr.watch)(n,()=>{i(n.value?.document)},{immediate:!0}),(0,pr.watch)(Tm.config.complete,()=>{Me.attrNameCasings.clear(),Me.tagNameCasings.clear(),i(n.value?.document)},{deep:!0}),(0,pr.useDisposable)(Rm.workspace.onDidCloseTextDocument(c=>{Me.attrNameCasings.delete(c.uri.toString()),Me.tagNameCasings.delete(c.uri.toString())})),(0,pr.useCommand)("vue.action.nameCasing",async()=>{if(!n.value?.document)return;let c=n.value.document,u=Me.attrNameCasings.get(c.uri.toString()),f=Me.tagNameCasings.get(c.uri.toString()),d=await(0,CF.quickPick)([{1:{label:(f===_e.TagNameCasing.Kebab?"\u2022 ":"")+"Component Name Using kebab-case"},2:{label:(f===_e.TagNameCasing.Pascal?"\u2022 ":"")+"Component Name Using PascalCase"},3:{label:"Convert Component Name to kebab-case"},4:{label:"Convert Component Name to PascalCase"}},{5:{label:(u===_e.AttrNameCasing.Kebab?"\u2022 ":"")+"Prop Name Using kebab-case"},6:{label:(u===_e.AttrNameCasing.Camel?"\u2022 ":"")+"Prop Name Using camelCase"},7:{label:"Convert Prop Name to kebab-case"},8:{label:"Convert Prop Name to camelCase"}}]);d!==void 0&&(d==="1"&&Me.tagNameCasings.set(c.uri.toString(),_e.TagNameCasing.Kebab),d==="2"&&Me.tagNameCasings.set(c.uri.toString(),_e.TagNameCasing.Pascal),d==="3"&&await s(n.value,_e.TagNameCasing.Kebab),d==="4"&&await s(n.value,_e.TagNameCasing.Pascal),d==="5"&&Me.attrNameCasings.set(c.uri.toString(),_e.AttrNameCasing.Kebab),d==="6"&&Me.attrNameCasings.set(c.uri.toString(),_e.AttrNameCasing.Camel),d==="7"&&await o(n.value,_e.AttrNameCasing.Kebab),d==="8"&&await o(n.value,_e.AttrNameCasing.Camel))});async function i(c){if(!c||!Rm.languages.match(e,c))return;let u,f=Me.attrNameCasings.get(c.uri.toString()),d=Me.tagNameCasings.get(c.uri.toString());if(!f){let p=Tm.config.complete.casing.props;p==="kebab"?f=_e.AttrNameCasing.Kebab:p==="camel"?f=_e.AttrNameCasing.Camel:(u??=await a(c),u?.attr.length===1?f=u.attr[0]:p==="autoCamel"?f=_e.AttrNameCasing.Camel:f=_e.AttrNameCasing.Kebab),Me.attrNameCasings.set(c.uri.toString(),f)}if(!d){let p=Tm.config.complete.casing.tags;p==="kebab"?d=_e.TagNameCasing.Kebab:p==="pascal"?d=_e.TagNameCasing.Pascal:(u??=await a(c),u?.tag.length===1?d=u.tag[0]:d=_e.TagNameCasing.Pascal),Me.tagNameCasings.set(c.uri.toString(),d)}}async function s(c,u){let f=await t.sendRequest(Dm.ExecuteCommandRequest.type,{command:u===_e.TagNameCasing.Kebab?_e.commands.convertTagsToKebabCase:_e.commands.convertTagsToPascalCase,arguments:[t.code2ProtocolConverter.asUri(c.document.uri)]}),d=await t.protocol2CodeConverter.asTextEdits(f);d&&c.edit(p=>{for(let g of d)p.replace(g.range,g.newText)}),Me.tagNameCasings.set(c.document.uri.toString(),u)}async function o(c,u){let f=await t.sendRequest(Dm.ExecuteCommandRequest.type,{command:u===_e.AttrNameCasing.Kebab?_e.commands.convertPropsToKebabCase:_e.commands.convertPropsToCamelCase,arguments:[t.code2ProtocolConverter.asUri(c.document.uri)]}),d=await t.protocol2CodeConverter.asTextEdits(f);d&&c.edit(p=>{for(let g of d)p.replace(g.range,g.newText)}),Me.attrNameCasings.set(c.document.uri.toString(),u)}function a(c){return t.sendRequest(Dm.ExecuteCommandRequest.type,{command:_e.commands.detectNameCasing,arguments:[t.code2ProtocolConverter.asUri(c.uri)]})}}});var VR=b(Cu=>{"use strict";Object.defineProperty(Cu,"__esModule",{value:!0});Cu.activate=TF;Cu.useDocDescriptor=UR;var SF=vi(),DF=$o(),Di=Kn(),Ho=require("vscode"),RF=fr();function TF(t){let e=(0,Di.useActiveTextEditor)(),n=UR(t);(0,Di.useCommand)("vue.action.splitEditors",async()=>{let r=e.value;if(!r)return;let i=RF.config.splitEditors.layout,s=r.document,o=(await n(s.getText()))?.descriptor;if(!o)return;let a=[],c=[];o.script&&(i.left.includes("script")&&a.push(o.script),i.right.includes("script")&&c.push(o.script)),o.scriptSetup&&(i.left.includes("scriptSetup")&&a.push(o.scriptSetup),i.right.includes("scriptSetup")&&c.push(o.scriptSetup)),o.template&&(i.left.includes("template")&&a.push(o.template),i.right.includes("template")&&c.push(o.template)),i.left.includes("styles")&&(a=a.concat(o.styles)),i.right.includes("styles")&&(c=c.concat(o.styles)),i.left.includes("customBlocks")&&(a=a.concat(o.customBlocks)),i.right.includes("customBlocks")&&(c=c.concat(o.customBlocks)),await(0,Di.executeCommand)("workbench.action.joinEditorInGroup"),e.value===r?(await u(a),await(0,Di.executeCommand)("workbench.action.toggleSplitEditorInGroup"),await u(c)):await(0,Di.executeCommand)("editor.unfoldAll");async function u(f){let d=e.value;if(!d)return;d.selections=f.length?f.map(g=>new Ho.Selection(s.positionAt(g.loc.start.offset),s.positionAt(g.loc.start.offset))):[new Ho.Selection(s.positionAt(s.getText().length),s.positionAt(s.getText().length))],await(0,Di.executeCommand)("editor.unfoldAll"),await(0,Di.executeCommand)("editor.foldLevel1");let p=f.sort((g,m)=>g.loc.start.offset-m.loc.start.offset)[0];p&&d.revealRange(new Ho.Range(s.positionAt(p.loc.start.offset),new Ho.Position(d.document.lineCount,0)),Ho.TextEditorRevealType.AtTop)}})}function UR(t){let e,n;return r;async function r(i){return i!==e&&(e=i,n=await t.sendRequest(SF.ExecuteCommandRequest.type,{command:DF.commands.parseSfc,arguments:[i]})),n}}});var KR=b(xm=>{"use strict";Object.defineProperty(xm,"__esModule",{value:!0});xm.useInsidersStatusItem=PF;var WR=ja(),Em=Kn(),Qe=require("vscode");function PF(t){let e=Qe.languages.createLanguageStatusItem("vue-insider","vue");e.command={title:"Fetch Versions",command:"vue-insiders.fetch"};let n="idle";(0,Em.useCommand)("vue-insiders.fetch",()=>{n==="idle"&&r()}),r();async function r(){e.busy=!0,e.text="Checking for Updates...",e.severity=Qe.LanguageStatusSeverity.Warning,n="pending";for(let s of["https://raw.githubusercontent.com/vuejs/language-tools/HEAD/insiders.json","https://cdn.jsdelivr.net/gh/vuejs/language-tools/insiders.json"])try{let o=new AbortController;setTimeout(()=>o.abort(),15e3);let a=await fetch(s,{signal:o.signal});i(await a.json()),n="success";break}catch{}e.busy=!1,n!=="success"&&(e.text="Failed to Fetch Versions",e.severity=Qe.LanguageStatusSeverity.Error,n="idle")}function i(s){e.detail=void 0,e.command={title:"Select Version",command:"vue-insiders.update"},s.versions.some(o=>o.version===t.extension.packageJSON.version)?(e.text="\u{1F680} Insiders Edition",e.severity=Qe.LanguageStatusSeverity.Information,t.extension.packageJSON.version!==s.latest&&(e.detail="New Version Available!",e.severity=Qe.LanguageStatusSeverity.Warning,Qe.window.showInformationMessage("New Insiders Version Available!","Download").then(o=>{o&&(0,Em.executeCommand)("vue-insiders.update")}))):(e.text="\u2728 Get Insiders Edition",e.severity=Qe.LanguageStatusSeverity.Warning),(0,Em.useCommand)("vue-insiders.update",async()=>{let o={};for(let{version:c,date:u}of s.versions){let f=u;t.extension.packageJSON.version===c&&(f+=" (current)"),o[c]={label:c,description:f}}let a=await(0,WR.quickPick)([o,{learnMore:{label:"Learn more about Insiders Edition"},joinViaGitHub:{label:"Join via GitHub Sponsors"},joinViaAFDIAN:{label:"Join via AFDIAN (\u7231\u53D1\u7535)"}}]);if(a==="learnMore")Qe.env.openExternal(Qe.Uri.parse("https://github.com/vuejs/language-tools/wiki/Get-Insiders-Edition"));else if(a==="joinViaGitHub")Qe.env.openExternal(Qe.Uri.parse("https://github.com/sponsors/johnsoncodehk"));else if(a==="joinViaAFDIAN")Qe.env.openExternal(Qe.Uri.parse("https://afdian.net/a/johnsoncodehk"));else{let c=s.versions.find(u=>u.version===a)?.downloads;if(c){let u={GitHub:{label:`${a} - GitHub Releases`,description:"Access via GitHub Sponsors",detail:c.GitHub},AFDIAN:{label:`${a} - Insiders \u7535\u5708`,description:"Access via AFDIAN (\u7231\u53D1\u7535)",detail:c.AFDIAN}},f={learnMore:{label:"Learn more about Insiders Edition"},joinViaGitHub:{label:"Join via GitHub Sponsors"},joinViaAFDIAN:{label:"Join via AFDIAN (\u7231\u53D1\u7535)"}},d=await(0,WR.quickPick)([u,f]);d==="learnMore"?Qe.env.openExternal(Qe.Uri.parse("https://github.com/vuejs/language-tools/wiki/Get-Insiders-Edition")):d==="joinViaGitHub"?Qe.env.openExternal(Qe.Uri.parse("https://github.com/sponsors/johnsoncodehk")):d==="joinViaAFDIAN"?Qe.env.openExternal(Qe.Uri.parse("https://afdian.net/a/johnsoncodehk")):d&&Qe.env.openExternal(Qe.Uri.parse(c[d]))}}})}}});var BR=b(Su=>{"use strict";Object.defineProperty(Su,"__esModule",{value:!0});Su.activate=IF;Su.deactivate=NF;var Ri=vi(),rn=Kn(),EF=require("vscode"),_u=fr(),xF=HR(),qF=Pm(),kF=VR(),hr=_m(),OF=KR(),nn;function IF(t,e){let n=(0,rn.useActiveTextEditor)(),r=(0,rn.useVisibleTextEditors)();(0,hr.useHybridModeTips)();let{stop:i}=(0,rn.watch)(n,()=>{r.value.some(s=>_u.config.server.includeLanguages.includes(s.document.languageId))&&(FF(t,e),(0,rn.nextTick)(()=>{i()}))},{immediate:!0})}function NF(){return nn?.stop()}async function FF(t,e){(0,rn.useVscodeContext)("vue.activated",!0);let n=(0,rn.useOutputChannel)("Vue Language Server"),r=_u.config.server.includeLanguages;nn=e("vue","Vue",r,await zR(t,hr.enabledHybridMode.value),6009,n),(0,rn.watch)([hr.enabledHybridMode,hr.enabledTypeScriptPlugin],(s,o)=>{s[0]!==o[0]?i(`Please restart extension host to ${s[0]?"enable":"disable"} Hybrid Mode.`):s[1]!==o[1]&&i(`Please restart extension host to ${s[1]?"enable":"disable"} Vue TypeScript Plugin.`)}),(0,rn.watch)(()=>_u.config.server.includeLanguages,()=>{hr.enabledHybridMode.value&&i("Please restart extension host to apply the new language settings.")}),(0,rn.watch)(_u.config.server,()=>{hr.enabledHybridMode.value||(0,rn.executeCommand)("vue.action.restartServer",!1)},{deep:!0}),(0,rn.useCommand)("vue.action.restartServer",async(s=!0)=>{s&&await(0,rn.executeCommand)("typescript.restartTsServer"),await nn.stop(),n.clear(),nn.clientOptions.initializationOptions=await zR(t,hr.enabledHybridMode.value),await nn.start()}),(0,xF.activate)(nn),(0,qF.activate)(nn,r),(0,kF.activate)(nn),Ri.activateAutoInsertion(r,nn),Ri.activateDocumentDropEdit(r,nn),Ri.activateWriteVirtualFiles("vue.action.writeVirtualFiles",nn),hr.enabledHybridMode.value||(Ri.activateTsConfigStatusItem(r,"vue.tsconfig",nn),Ri.activateTsVersionStatusItem(r,"vue.tsversion",t,s=>"TS "+s),Ri.activateFindFileReferences("vue.findAllFileReferences",nn)),(0,hr.useHybridModeStatusItem)(),(0,OF.useInsidersStatusItem)(t);async function i(s){await EF.window.showInformationMessage(s,"Restart Extension Host")&&(0,rn.executeCommand)("workbench.action.restartExtensionHost")}}async function zR(t,e){return{typescript:{tsdk:(await Ri.getTsdk(t)).tsdk},vue:{hybridMode:e}}}});var JR=b(Ru=>{"use strict";Object.defineProperty(Ru,"__esModule",{value:!0});Ru.middleware=void 0;var GR=vi(),Du=$o(),qm=require("vscode"),MF=fr(),XR=Pm();Ru.middleware={...GR.middleware,async resolveCodeAction(t,e,n){if(t.kind?.value==="refactor.move.newFile.dumb"&&MF.config.codeActions.askNewComponentName){let r=await qm.window.showInputBox({value:t.data.original.data.newName});if(!r)return t;t.data.original.data.newName=r}return await(GR.middleware.resolveCodeAction?.(t,e,n)??n(t,e))},workspace:{configuration(t,e,n){return t.items.some(r=>r.section==="vue.complete.casing.props"||r.section==="vue.complete.casing.tags")?t.items.map(r=>{if(r.scopeUri){if(r.section==="vue.complete.casing.tags"){let i=XR.tagNameCasings.get(r.scopeUri);if(i===Du.TagNameCasing.Kebab)return"kebab";if(i===Du.TagNameCasing.Pascal)return"pascal"}else if(r.section==="vue.complete.casing.props"){let i=XR.attrNameCasings.get(r.scopeUri);if(i===Du.AttrNameCasing.Kebab)return"kebab";if(i===Du.AttrNameCasing.Camel)return"camel"}}return qm.workspace.getConfiguration(r.section,r.scopeUri?qm.Uri.parse(r.scopeUri):void 0)}):n(t,e)}}}});var km;Object.defineProperty(exports,"__esModule",{value:!0});exports.deactivate=exports.activate=void 0;var AF=vi(),Om=RS(),LF=TS(),YR=require("node:fs"),Uo=Kn(),Ti=require("vscode"),Ps=fr(),QR=_m(),ZR=BR(),jF=JR();km=(0,Uo.defineExtension)(async()=>{let t=(0,AF.createLabsInfo)(LF),e=Ti.extensions.getExtension("vscode.typescript-language-features"),n=Ti.extensions.getExtension("Vue.vscode-typescript-vue-plugin");e?await e.activate():Ti.window.showWarningMessage('Takeover mode is no longer needed since v2. Please enable the "TypeScript and JavaScript Language Features" extension.',"Show Extension").then(i=>{i&&(0,Uo.executeCommand)("workbench.extensions.search","@builtin typescript-language-features")}),n&&Ti.window.showWarningMessage(`The "${n.packageJSON.displayName}" extension is no longer needed since v2. Please uninstall it.`,"Show Extension").then(i=>{i&&(0,Uo.executeCommand)("workbench.extensions.search",n.id)});let r=Uo.extensionContext.value;return(0,ZR.activate)(r,(i,s,o,a,c,u)=>{class f extends Om.LanguageClient{fillInitializeParams(k){k.locale=Ti.env.language}}let d=Ti.Uri.joinPath(r.extensionUri,"server.js"),p={};Ps.config.server.maxOldSpaceSize&&(p.execArgv??=[],p.execArgv.push("--max-old-space-size="+Ps.config.server.maxOldSpaceSize));let g={execArgv:["--nolazy","--inspect="+c]},m={run:{module:d.fsPath,transport:Om.TransportKind.ipc,options:p},debug:{module:d.fsPath,transport:Om.TransportKind.ipc,options:g}},S={middleware:jF.middleware,documentSelector:o,initializationOptions:a,markdown:{isTrusted:!0,supportHtml:!0},outputChannel:u},D=new f(i,s,m,S);return D.start(),t.addLanguageClient(D),$F(D),D}),(0,Uo.onDeactivate)(()=>{(0,ZR.deactivate)()}),t.extensionExports}),exports.activate=km.activate,exports.deactivate=km.deactivate;function $F(t){let e=t.initializeFeatures;t.initializeFeatures=(...n)=>{let r=t._capabilities;return Ps.config.codeActions.enabled||(r.codeActionProvider=void 0),Ps.config.codeLens.enabled||(r.codeLensProvider=void 0),!Ps.config.updateImportsOnFileMove.enabled&&r.workspace?.fileOperations?.willRename&&(r.workspace.fileOperations.willRename=void 0),e.call(t,...n)}}try{let t=Ti.extensions.getExtension("vscode.typescript-language-features"),e=YR.readFileSync,n=require.resolve("./dist/extension.js",{paths:[t.extensionPath]});YR.readFileSync=(...r)=>{if(r[0]===n){let i=e(...r);return QR.enabledTypeScriptPlugin.value?QR.enabledHybridMode.value&&(i=i.replace("languages:Array.isArray(e.languages)",["languages:",`e.name==='vue-typescript-plugin-pack'?[${Ps.config.server.includeLanguages.map(s=>`'${s}'`).join(",")}]`,":Array.isArray(e.languages)"].join("")),i=i.replace("t.$u=[t.$r,t.$s,t.$p,t.$q]",s=>s+'.concat("vue")'),i=i.replace(".languages.match([t.$p,t.$q,t.$r,t.$s]",s=>s+'.concat("vue")'),i=i.replace("t.jsTsLanguageModes=[t.javascript,t.javascriptreact,t.typescript,t.typescriptreact]",s=>s+'.concat("vue")'),i=i.replace(".languages.match([t.typescript,t.typescriptreact,t.javascript,t.javascriptreact]",s=>s+'.concat("vue")')):i=i.replace("for(const e of n.contributes.typescriptServerPlugins",s=>s+".filter(p=>p.name!=='vue-typescript-plugin-pack')"),i}return e(...r)}}catch{}
/*! Bundled license information:

@reactive-vscode/reactivity/dist/index.cjs:
  (**
  * @vue/shared v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
  (*! #__NO_SIDE_EFFECTS__ *)
  (**
  * @vue/reactivity v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
*/
