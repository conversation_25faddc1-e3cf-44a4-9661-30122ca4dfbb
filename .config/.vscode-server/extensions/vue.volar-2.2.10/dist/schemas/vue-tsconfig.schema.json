{"properties": {"vueCompilerOptions": {"type": "object", "additionalProperties": false, "properties": {"target": {"default": "auto", "enum": ["auto", 2, 2.7, 3, 3.3, 3.5], "markdownDescription": "Target version of Vue."}, "extensions": {"type": "array", "default": [".vue"], "markdownDescription": "Valid file extensions that should be considered as regular Vue SFC, please note that you should not use this option separately for additional file extensions IDE support, see https://github.com/vuejs/language-tools/tree/master/extensions/vscode/README.md#custom-file-extensions."}, "vitePressExtensions": {"type": "array", "default": [".md"], "markdownDescription": "Valid file extensions that should be considered as regular VitePress SFC."}, "petiteVueExtensions": {"type": "array", "default": [".html"], "markdownDescription": "Valid file extensions that should be considered as regular PetiteVue SFC."}, "lib": {"default": "vue", "markdownDescription": "Specify module name for import regular types."}, "jsxSlots": {"type": "boolean", "default": false, "markdownDescription": "Generate slots type for `JSX.ElementChildrenAttribute`."}, "strictTemplates": {"type": "boolean", "default": false, "markdownDescription": "Strict props, component type-checking in templates."}, "checkUnknownProps": {"type": "boolean", "default": false, "markdownDescription": "Check unknown props. If not set, uses the 'strictTemplates' value."}, "checkUnknownEvents": {"type": "boolean", "default": false, "markdownDescription": "Check unknown events. If not set, uses the 'strictTemplates' value."}, "checkUnknownDirectives": {"type": "boolean", "default": false, "markdownDescription": "Check unknown directives. If not set, uses the 'strictTemplates' value."}, "checkUnknownComponents": {"type": "boolean", "default": false, "markdownDescription": "Check unknown components. If not set, uses the 'strictTemplates' value."}, "inferComponentDollarEl": {"type": "boolean", "default": false, "markdownDescription": "Infer `$el` type on the component instance."}, "inferComponentDollarRefs": {"type": "boolean", "default": false, "markdownDescription": "Infer `$refs` type on the component instance."}, "inferTemplateDollarAttrs": {"type": "boolean", "default": false, "markdownDescription": "Infer `$attrs` type in the template and the return type of `useAttrs`."}, "inferTemplateDollarEl": {"type": "boolean", "default": false, "markdownDescription": "Infer `$el` type in the template."}, "inferTemplateDollarRefs": {"type": "boolean", "default": false, "markdownDescription": "Infer `$refs` type in the template."}, "inferTemplateDollarSlots": {"type": "boolean", "default": false, "markdownDescription": "Infer `$slots` type in the template and the return type of `useSlots`."}, "skipTemplateCodegen": {"type": "boolean", "default": false, "markdownDescription": "https://github.com/vuejs/language-tools/issues/577"}, "fallthroughAttributes": {"type": "boolean", "default": false, "markdownDescription": "Enable to support typed fallthrough attributes. Please note that enabling may significantly slow down type checking."}, "fallthroughComponentNames": {"type": "array", "default": ["Transition", "KeepAlive", "Teleport", "Suspense"], "markdownDescription": "Component names that will be transparent when collecting single root child nodes and fallthrough attributes."}, "dataAttributes": {"type": "array", "default": [], "markdownDescription": "A glob matcher array that should always avoid recognized as HTML Attributes and Component props."}, "htmlAttributes": {"type": "array", "default": ["aria-*"], "markdownDescription": "A glob matcher array that should always be recognizing as HTML Attributes rather than Component props. Attribute name will never convert to camelize case."}, "optionsWrapper": {"type": "array", "default": ["(await import('vue')).defineComponent(", ")"], "markdownDescription": "How to wrap option of `export default { ... }`? Default: `[\"(await import('vue')).default.extend(\", \")\"]` for target < 2.7, `[\"(await import('vue')).defineComponent(\", \")\"]` for target >= 2.7."}, "macros": {"type": "object", "default": {"defineProps": ["defineProps"], "defineModel": ["defineModel"], "defineEmits": ["defineEmits"], "defineSlots": ["defineSlots"], "defineExpose": ["defineExpose"], "defineOptions": ["defineOptions"], "withDefaults": ["with<PERSON><PERSON><PERSON><PERSON>"]}}, "composables": {"type": "object", "default": {"useAttrs": ["useAttrs"], "useCssModule": ["useCssModule"], "useSlots": ["useSlots"], "useTemplateRef": ["useTemplateRef", "templateRef"]}}, "plugins": {"type": "array", "default": [], "markdownDescription": "Plugins to be used in the SFC compiler."}, "experimentalResolveStyleCssClasses": {"enum": ["scoped", "always", "never"], "markdownDescription": "https://github.com/vuejs/language-tools/issues/1038, https://github.com/vuejs/language-tools/issues/1121"}, "experimentalModelPropName": {"type": "object", "default": {"": {"input": true}, "value": {"input": {"type": "text"}, "textarea": true, "select": true}}, "markdownDescription": "https://github.com/vuejs/language-tools/issues/1969"}, "experimentalDefinePropProposal": {"enum": ["kevin<PERSON><PERSON>", "johnsonE<PERSON>", false]}}}}}