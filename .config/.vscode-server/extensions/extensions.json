[{"identifier": {"id": "github.copilot-chat", "uuid": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f"}, "version": "0.28.3", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3", "scheme": "file"}, "relativeLocation": "github.copilot-chat-0.28.3", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751123138595, "pinned": false, "source": "gallery", "id": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "github.copilot", "uuid": "23c4aeee-f844-43cd-b53e-1113e483f1a6"}, "version": "1.338.0", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-1.338.0", "scheme": "file"}, "relativeLocation": "github.copilot-1.338.0", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751123138556, "pinned": false, "source": "gallery", "id": "23c4aeee-f844-43cd-b53e-1113e483f1a6", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "augment.vscode-augment", "uuid": "fc0e137d-e132-47ed-9455-c4636fa5b897"}, "version": "0.487.1", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1", "scheme": "file"}, "relativeLocation": "augment.vscode-augment-0.487.1", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751123138594, "pinned": false, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "Augment Computing", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "vue.volar"}, "version": "2.2.10", "location": {"$mid": 1, "fsPath": "/home/<USER>/.vscode-server/extensions/vue.volar-2.2.10", "external": "file:///home/<USER>/.vscode-server/extensions/vue.volar-2.2.10", "path": "/home/<USER>/.vscode-server/extensions/vue.volar-2.2.10", "scheme": "file"}, "relativeLocation": "vue.volar-2.2.10", "metadata": {"installedTimestamp": 1751125741758, "source": "gallery", "id": "a95ee795-1576-4ffa-acda-8d6e6a95c584", "publisherId": "49983e2f-38ad-4441-beea-d678b53d0549", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}]