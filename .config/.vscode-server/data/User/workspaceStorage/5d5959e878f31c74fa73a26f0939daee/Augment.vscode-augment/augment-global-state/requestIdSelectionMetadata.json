[["f61ce99f-4dd4-4cbf-9c25-1d8be6550943", {"value": {"selectedCode": "", "prefix": "from sqlalchemy import create_engine\n", "suffix": "from sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "path": "backend/app/core/database.py", "language": "python", "prefixBegin": 0, "suffixEnd": 18}}], ["4d974e86-b6d6-485b-a26c-be248c5ef194", {"value": {"selectedCode": "", "prefix": "from sqlalchemy import create_engine\nfrom sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "suffix": "", "path": "backend/app/core/database.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["5aa7b544-e500-4397-9c08-359870aecc15", {"value": {"selectedCode": "", "prefix": "\n# AI Services\n", "suffix": "ANTHROPIC_API_KEY=************************************************************************************************************\n\nPERPLEXITY_API_KEY=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        # For research -- Format: pplx-abcde (Optional, Highly Recommended)\n\nOPENAI_API_KEY=********************************************************************************************************************************************************************\n\n# Security\nSECRET_KEY=your-secret-key-change-in-production\n\n# Redis\nREDIS_URL=redis://localhost:6379\n\n# Email\nSMTP_HOST=smtp.gmail.com\nSMTP_PORT=587\nSMTP_USERNAME=<EMAIL>\nSMTP_PASSWORD=your-app-password\n\n# Optional - defaults shown\nMODEL=claude-4-sonnet  \nPERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)\nMAX_TOKENS=64000                  # Maximum tokens for model responses (Required)\nTEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)\nDEBUG=false                       # Enable debug logging (true/false)\nLOG_LEVEL=info                    # Log level (debug, info, warn, error)\nDEFAULT_SUBTASKS=5                # Default number of subtasks when expanding\nDEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)\nPROJECT_NAME=ExProject            # Project name for tasks.json metadata\n\nDATABASE_URL=ppostgresql://neondb_owner:<EMAIL>/neondb?sslmode=require", "path": "backend/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 125}}], ["ce9607b2-299e-4b66-bd36-3632d3913cb9", {"value": {"selectedCode": "", "prefix": "\n# AI Services\nANTHROPIC_API_KEY=************************************************************************************************************\n\nPERPLEXITY_API_KEY=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        # For research -- Format: pplx-abcde (Optional, Highly Recommended)\n\nOPENAI_API_KEY=********************************************************************************************************************************************************************\n\n# Security\nSECRET_KEY=your-secret-key-change-in-production\n\n# Redis\nREDIS_URL=redis://localhost:6379\n\n# Email\nSMTP_HOST=smtp.gmail.com\nSMTP_PORT=587\n", "suffix": "SMTP_USERNAME=<EMAIL>\nSMTP_PASSWORD=your-app-password\n\n# Optional - defaults shown\nMODEL=claude-4-sonnet  \nPERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)\nMAX_TOKENS=64000                  # Maximum tokens for model responses (Required)\nTEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)\nDEBUG=false                       # Enable debug logging (true/false)\nLOG_LEVEL=info                    # Log level (debug, info, warn, error)\nDEFAULT_SUBTASKS=5                # Default number of subtasks when expanding\nDEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)\nPROJECT_NAME=ExProject            # Project name for tasks.json metadata\n\nDATABASE_URL=ppostgresql://neondb_owner:<EMAIL>/neondb?sslmode=require", "path": "backend/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 125}}], ["6c0fe3bd-802a-4b2e-8b9d-82ede4b6bf15", {"value": {"selectedCode": "", "prefix": "# 🧪 **STRATEGIA TEST EXPROJECT**\n", "suffix": "\n## **📋 OVERVIEW**\n\nStrategia di test completa per garantire qualità e affidabilità del sistema ExProject.\nCopre backend API, frontend components, integrazione AI e workflow system.\n\n## **🏗️ STRUTTURA TEST**\n\n```\ntests/\n├── backend/                 # Test Backend (FastAPI + SQLAlchemy)\n│   ├── unit/               # Test unitari modelli e servizi\n│   ├── integration/        # Test integrazione API\n│   ├── e2e/               # Test end-to-end workflow\n│   └── fixtures/          # Dati di test\n├── frontend/              # Test Frontend (Vue.js + TypeScript)\n│   ├── unit/              # Test componenti Vue\n│   ├── integration/       # Test integrazione API\n│   └── e2e/              # Test Cypress/Playwright\n├── ai/                   # Test Servizi AI\n│   ├── unit/             # Test singoli servizi AI\n│   ├── integration/      # Test orchestrazione AI\n│   └── performance/      # Test performance AI\n└── shared/               # Utilities e helpers condivisi\n    ├── fixtures/         # Dati di test condivisi\n    ├── mocks/           # Mock services\n    └── utils/           # Utility di test\n```\n\n## **🎯 PRIORITÀ TEST**\n\n### **CRITICO (P0) - Da implementare SUBITO**\n1. **Backend API Core** - Autenticazione, CRUD progetti\n2. **Workflow Templates** - Validazione template JSON\n3. **Frontend Wizard** - Flusso creazione progetti\n4. **Database Models** - Integrità relazioni\n\n### **IMPORTANTE (P1) - Prossime 2 settimane**\n1. **AI Services** - Validazione e suggerimenti\n2. **Frontend Components** - Design system consistency\n3. **Integration Tests** - Frontend ↔ Backend\n4. **Performance** - Load testing API\n\n### **NICE TO HAVE (P2) - Futuro**\n1. **E2E Scenarios** - User journey completi\n2. **Accessibility** - WCAG compliance\n3. **Security** - Penetration testing\n4. **Multi-tenant** - Isolamento dati\n\n## **🔧 TOOLS & FRAMEWORK**\n\n### **Backend Testing**\n- **pytest** - Framework principale\n- **pytest-asyncio** - Test async/await\n- **httpx** - Client HTTP per test API\n- **factory-boy** - Generazione dati test\n- **pytest-cov** - Coverage reporting\n\n### **Frontend Testing**\n- **Vitest** - Test runner veloce (Vite-based)\n- **Vue Test Utils** - Testing utilities Vue 3\n- **@testing-library/vue** - Testing best practices\n- **Cypress** - E2E testing\n- **MSW** - Mock Service Worker per API\n\n### **AI Testing**\n- **pytest** - Framework base\n- **unittest.mock** - Mock servizi esterni\n- **responses** - Mock HTTP requests\n- **memory-profiler** - Performance monitoring\n\n## **📊 COVERAGE TARGETS**\n\n- **Backend API**: 90%+ coverage\n- **Frontend Components**: 80%+ coverage  \n- **AI Services**: 85%+ coverage\n- **Critical Paths**: 100% coverage\n\n## **🚀 QUICK START**\n\n### **Setup Test Environment**\n```bash\n# Backend tests\ncd backend\npip install -r requirements-test.txt\npytest tests/ -v\n\n# Frontend tests  \ncd frontend\nnpm install\nnpm run test:unit\nnpm run test:e2e\n\n# AI tests\ncd tests/ai\npython -m pytest test_ai_services.py -v\n```\n\n### **Run Specific Test Suites**\n```bash\n# Test critici (P0)\npytest tests/backend/critical/ -v\nnpm run test:critical\n\n# Test completi con coverage\npytest tests/ --cov=app --cov-report=html\nnpm run test:coverage\n\n# Test performance\npytest tests/performance/ -v --benchmark-only\n```\n\n## **📝 CONVENZIONI**\n\n### **Naming Convention**\n- **File**: `test_<module_name>.py` / `<Component>.test.ts`\n- **Classes**: `Test<ClassName>`\n- **Methods**: `test_<action>_<expected_result>`\n\n### **Test Structure (AAA Pattern)**\n```python\ndef test_create_project_success():\n    # Arrange\n    user = create_test_user()\n    project_data = {\"name\": \"Test Project\"}\n    \n    # Act\n    response = client.post(\"/api/projects/\", json=project_data)\n    \n    # Assert\n    assert response.status_code == 201\n    assert response.json()[\"name\"] == \"Test Project\"\n```\n\n### **Mock Strategy**\n- **External APIs**: Sempre mockati\n- **Database**: Test database separato\n- **AI Services**: Mock per unit test, real per integration\n- **File System**: Temporary directories\n\n## **🔄 CI/CD INTEGRATION**\n\n### **GitHub Actions Pipeline**\n```yaml\n# .github/workflows/test.yml\nname: Test Suite\non: [push, pull_request]\n\njobs:\n  backend-tests:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - name: Setup Python\n        uses: actions/setup-python@v4\n        with:\n          python-version: '3.11'\n      - name: Run Backend Tests\n        run: |\n          cd backend\n          pip install -r requirements-test.txt\n          pytest tests/ --cov=app --cov-fail-under=90\n  \n  frontend-tests:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - name: Setup Node.js\n        uses: actions/setup-node@v3\n        with:\n          node-version: '18'\n      - name: Run Frontend Tests\n        run: |\n          cd frontend\n          npm ci\n          npm run test:unit\n          npm run test:e2e:headless\n```\n\n## **📈 MONITORING & REPORTING**\n\n### **Test Reports**\n- **Coverage Reports**: HTML + Badge in README\n- **Performance Benchmarks**: Trend tracking\n- **Flaky Test Detection**: Automatic retry + reporting\n- **Test Duration**: Optimization tracking\n\n### **Quality Gates**\n- **PR Requirements**: All tests pass + 90% coverage\n- **Performance**: No regression > 10%\n- **Security**: No high/critical vulnerabilities\n- **Accessibility**: WCAG AA compliance\n\n## **🎯 ROADMAP TEST**\n\n### **Week 1-2: Foundation**\n- [ ] Setup test infrastructure\n- [ ] Backend API critical tests\n- [ ] Frontend component tests\n- [ ] CI/CD pipeline\n\n### **Week 3-4: Coverage**\n- [ ] AI services testing\n- [ ] Integration tests\n- [ ] Performance baselines\n- [ ] E2E critical paths\n\n### **Month 2: Advanced**\n- [ ] Security testing\n- [ ] Accessibility testing\n- [ ] Load testing\n- [ ] Multi-tenant testing\n\n### **Ongoing: Maintenance**\n- [ ] Test maintenance\n- [ ] Performance monitoring\n- [ ] Coverage improvement\n- [ ] Flaky test fixes\n", "path": "tests/README.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}]]