[["f61ce99f-4dd4-4cbf-9c25-1d8be6550943", {"value": {"selectedCode": "", "prefix": "from sqlalchemy import create_engine\n", "suffix": "from sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "path": "backend/app/core/database.py", "language": "python", "prefixBegin": 0, "suffixEnd": 18}}], ["4d974e86-b6d6-485b-a26c-be248c5ef194", {"value": {"selectedCode": "", "prefix": "from sqlalchemy import create_engine\nfrom sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "suffix": "", "path": "backend/app/core/database.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["5aa7b544-e500-4397-9c08-359870aecc15", {"value": {"selectedCode": "", "prefix": "\n# AI Services\n", "suffix": "ANTHROPIC_API_KEY=************************************************************************************************************\n\nPERPLEXITY_API_KEY=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        # For research -- Format: pplx-abcde (Optional, Highly Recommended)\n\nOPENAI_API_KEY=********************************************************************************************************************************************************************\n\n# Security\nSECRET_KEY=your-secret-key-change-in-production\n\n# Redis\nREDIS_URL=redis://localhost:6379\n\n# Email\nSMTP_HOST=smtp.gmail.com\nSMTP_PORT=587\nSMTP_USERNAME=<EMAIL>\nSMTP_PASSWORD=your-app-password\n\n# Optional - defaults shown\nMODEL=claude-4-sonnet  \nPERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)\nMAX_TOKENS=64000                  # Maximum tokens for model responses (Required)\nTEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)\nDEBUG=false                       # Enable debug logging (true/false)\nLOG_LEVEL=info                    # Log level (debug, info, warn, error)\nDEFAULT_SUBTASKS=5                # Default number of subtasks when expanding\nDEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)\nPROJECT_NAME=ExProject            # Project name for tasks.json metadata\n\nDATABASE_URL=ppostgresql://neondb_owner:<EMAIL>/neondb?sslmode=require", "path": "backend/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 125}}]]