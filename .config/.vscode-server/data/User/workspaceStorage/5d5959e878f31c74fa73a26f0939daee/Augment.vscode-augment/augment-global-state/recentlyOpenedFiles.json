[["/home/<USER>/workspace/backend/app/init_simple.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/init_simple.py"}}], ["/home/<USER>/workspace/backend/app/core/database.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}}], ["/home/<USER>/workspace/backend/.env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/.env.example"}}], ["/home/<USER>/workspace/frontend/src/views/admin/ProjectCreate.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/ProjectCreate.vue"}}], ["/home/<USER>/workspace/tests/test-ai-integration.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-ai-integration.md"}}]]