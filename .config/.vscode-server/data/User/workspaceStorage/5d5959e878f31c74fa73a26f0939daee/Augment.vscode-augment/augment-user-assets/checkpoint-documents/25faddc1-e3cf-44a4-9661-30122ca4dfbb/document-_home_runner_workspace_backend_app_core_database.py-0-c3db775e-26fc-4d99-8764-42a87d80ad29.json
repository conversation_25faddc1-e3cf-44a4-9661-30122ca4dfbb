{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}, "originalCode": "from sqlalchemy import create_engine\nfrom sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "modifiedCode": "from sqlalchemy import create_engine\nfrom sqlalchemy.ext.declarative import declarative_base\nfrom sqlalchemy.orm import sessionmaker\nfrom app.core.config import settings\nfrom app.models.user_simple import Base\n\n# Create database engine\nengine = create_engine(\n    settings.DATABASE_URL,\n    pool_pre_ping=True,\n    pool_recycle=300,\n    echo=settings.DEBUG\n)\n\n# Create SessionLocal class\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n\n# Create tables on startup  \nBase.metadata.create_all(bind=engine)\n\n# Dependency to get database session\ndef get_db():\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()"}