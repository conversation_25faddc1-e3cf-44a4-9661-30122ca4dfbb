{"id": "shard-25faddc1-e3cf-44a4-9661-30122ca4dfbb", "checkpoints": {"25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/core/database.py": [{"sourceToolCallRequestId": "c3db775e-26fc-4d99-8764-42a87d80ad29", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}}}, {"sourceToolCallRequestId": "0746a322-5aa2-4892-af86-c71cdf3aa9e2", "timestamp": 1751124330819, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}}}, {"sourceToolCallRequestId": "da46f6a2-32fd-46e8-853c-3578bf5797fb", "timestamp": 1751124331260, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}}}, {"sourceToolCallRequestId": "d9cbdb9e-0874-4aa4-8d10-7e072754f1ca", "timestamp": 1751124331260, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "61145ab0-630d-4b97-ac11-9c205b19b519", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/database.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/user_simple.py": [{"sourceToolCallRequestId": "a8b47604-4727-43d5-85e2-b46412a552df", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/user_simple.py"}}}, {"sourceToolCallRequestId": "e2fd26e3-fcd3-4f29-9fbe-d86648d8d08c", "timestamp": 1751124345822, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/user_simple.py"}}}, {"sourceToolCallRequestId": "7806d81c-2412-4c21-b5c6-a4604cd9fcc5", "timestamp": 1751124346284, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/user_simple.py"}}}, {"sourceToolCallRequestId": "d2978882-6518-4cf0-a144-429cec0b98d4", "timestamp": 1751124346284, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "61145ab0-630d-4b97-ac11-9c205b19b519", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/user_simple.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/enhanced_models.py": [{"sourceToolCallRequestId": "3703db71-c972-444e-b67f-661951a5b48f", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/enhanced_models.py"}}}, {"sourceToolCallRequestId": "15112619-5444-4993-aa34-cca3bcf9c283", "timestamp": 1751124358270, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/enhanced_models.py"}}}, {"sourceToolCallRequestId": "83f6d19c-5507-4f17-82dc-49e2780edd39", "timestamp": 1751124358664, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "61145ab0-630d-4b97-ac11-9c205b19b519", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/enhanced_models.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/workflow_models.py": [{"sourceToolCallRequestId": "5107cde4-9773-4a5a-b893-e620b0bc6bcf", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/workflow_models.py"}}}, {"sourceToolCallRequestId": "15112619-5444-4993-aa34-cca3bcf9c283", "timestamp": 1751124358818, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/workflow_models.py"}}}, {"sourceToolCallRequestId": "9f5dc8eb-3923-405f-8380-d5cc0d477cec", "timestamp": 1751124359210, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "61145ab0-630d-4b97-ac11-9c205b19b519", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/models/workflow_models.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/seed/seed_data.py": [{"sourceToolCallRequestId": "ef00bfc7-e894-47b8-a956-128e2c152292", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/seed/seed_data.py"}}}, {"sourceToolCallRequestId": "aa226168-b67a-4109-abdf-2e357daa8e13", "timestamp": 1751124368278, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/seed/seed_data.py"}}}, {"sourceToolCallRequestId": "c6c3d547-2795-4582-97f8-c0d85f37a8f1", "timestamp": 1751124368730, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/seed/seed_data.py"}}}, {"sourceToolCallRequestId": "26be02cb-1bde-4062-bdbc-91f97f5cdac0", "timestamp": 1751124368730, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "61145ab0-630d-4b97-ac11-9c205b19b519", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/seed/seed_data.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/core/config.py": [{"sourceToolCallRequestId": "a5594be5-5a26-4442-b7f4-6df8faca47f6", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "d0c182fc-e2eb-40a4-b2ad-e3c5a65a7ddb", "timestamp": 1751124728057, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "47ae97ad-43b3-4458-a964-2102bb286275", "timestamp": 1751124728472, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "64839411-7421-4eba-adbf-10f5bbe3dd7c", "timestamp": 1751124728472, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "934044d0-18a4-488f-aeda-438aa437cfdf", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "a0af276e-ce6c-416b-86f9-f3b0c7957447", "timestamp": 1751126368415, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "c496a693-0e61-432d-8fc7-5951e73e0779", "timestamp": 1751126368844, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}, {"sourceToolCallRequestId": "d8bd520f-13b8-4dad-ac8e-8a7887c76af5", "timestamp": 1751126368844, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/core/config.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/requirements.txt": [{"sourceToolCallRequestId": "02b6e313-a9bb-4b34-a4fe-f22bee4c52f2", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/requirements.txt"}}}, {"sourceToolCallRequestId": "c578f57f-c651-495f-aca4-a7c90878c86f", "timestamp": 1751124752643, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/requirements.txt"}}}, {"sourceToolCallRequestId": "0d6b494a-1e53-4fb2-965f-837aa0904595", "timestamp": 1751124753064, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/requirements.txt"}}}, {"sourceToolCallRequestId": "8d4a23a7-9444-48f9-889f-4648efe852e9", "timestamp": 1751124753064, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "934044d0-18a4-488f-aeda-438aa437cfdf", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/requirements.txt"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/api/search.py": [{"sourceToolCallRequestId": "df090295-890d-4e94-9263-db7b1fece8d1", "timestamp": 1751124789471, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/api/search.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/main.py": [{"sourceToolCallRequestId": "ebbf7088-9ffa-4abe-b25b-b05011735061", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "b2b5ca87-1194-4bc6-a15d-d78384bb832f", "timestamp": 1751124798779, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "5b449b35-2b62-43ad-8ba0-eca2dd1d182e", "timestamp": 1751124799240, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "90db87f1-9b17-4298-92a2-0843338aec30", "timestamp": 1751124799240, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "a672ebde-c8f0-4252-af42-9af3b3c75108", "timestamp": 1751124811666, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "58c30811-33da-4422-8633-3d8be44470a7", "timestamp": 1751124811828, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "6394263a-42d9-44bb-be5d-d2f685cb16f1", "timestamp": 1751124811828, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "7716df3c-7cd3-43c3-bea5-3c38f98949b2", "timestamp": 1751124825240, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "919fc4d0-e13f-4c7b-b62e-716a8dfa907e", "timestamp": 1751124825432, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}, {"sourceToolCallRequestId": "cbe7b18c-6002-4ae4-84a5-52e753cdfd95", "timestamp": 1751124825432, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "934044d0-18a4-488f-aeda-438aa437cfdf", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/main.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/frontend/src/assets/css/variables.css": [{"sourceToolCallRequestId": "95d4fcc9-fccf-4912-bd2c-914bc1403c8b", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "03d473c4-0799-457c-9196-e9b2f6d48d52", "timestamp": 1751125553610, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "5d0fcec3-eafd-4b64-817a-173181f00df1", "timestamp": 1751125554085, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "99bc3f93-bca8-45ca-87d7-6d9e94275839", "timestamp": 1751125554085, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "53ca66d8-98be-4d93-b845-e6f722435b0d", "timestamp": 1751125569648, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "2ef276a9-087e-45a4-ad96-234c861b1f68", "timestamp": 1751125569809, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}, {"sourceToolCallRequestId": "c85f70de-73b8-492b-ba72-a2a63d557407", "timestamp": 1751125569809, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "lastIncludedInRequestId": "6c0fe3bd-802a-4b2e-8b9d-82ede4b6bf15", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/variables.css"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/frontend/src/components/project/ProjectHeaderFixed.vue": [{"sourceToolCallRequestId": "4968a1b9-cbd5-4e17-bc29-e7fad57b320a", "timestamp": 1751125620491, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/project/ProjectHeaderFixed.vue"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/README.md": [{"sourceToolCallRequestId": "424fc6ce-f901-4e53-b37d-bd789578efc6", "timestamp": 1751125655625, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/README.md"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/backend/test_project_api.py": [{"sourceToolCallRequestId": "c9ba328f-dafe-4ab9-a303-75194d55305b", "timestamp": 1751125696534, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/backend/test_project_api.py"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/frontend/ProjectWizard.test.ts": [{"sourceToolCallRequestId": "fc8ae63f-4ca8-449f-8a15-7d6d92cdfdb2", "timestamp": 1751125744487, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/frontend/ProjectWizard.test.ts"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/.env": [{"sourceToolCallRequestId": "affde2e7-302f-4a82-8dc7-5e2e6bd59527", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/.env"}}}, {"sourceToolCallRequestId": "f873c2c4-2c92-4c10-96a2-a263bc87aa12", "timestamp": 1751126403642, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/.env"}}}, {"sourceToolCallRequestId": "5a8a1e13-c6fe-4983-b055-7f476259c5dd", "timestamp": 1751126404067, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/.env"}}}, {"sourceToolCallRequestId": "fdc68797-4d50-4698-b8a9-962e715cbaed", "timestamp": 1751126404067, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/.env"}}}], "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/api/workflow_templates.py": [{"sourceToolCallRequestId": "3348684c-4b2a-4645-9687-fe2a122979b3", "timestamp": 0, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/api/workflow_templates.py"}}}, {"sourceToolCallRequestId": "dde23af4-4b38-4bbf-a455-4b2ae943a965", "timestamp": 1751126499249, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/api/workflow_templates.py"}}}, {"sourceToolCallRequestId": "3df78636-1609-4a85-877a-8e2dc2c5d7fe", "timestamp": 1751126499856, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/api/workflow_templates.py"}}}, {"sourceToolCallRequestId": "0cc0347a-7023-4dc4-95d6-e6e6d0ec2d24", "timestamp": 1751126499856, "conversationId": "25faddc1-e3cf-44a4-9661-30122ca4dfbb", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app/api/workflow_templates.py"}}}]}, "metadata": {"checkpointDocumentIds": ["25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/core/database.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/user_simple.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/enhanced_models.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/models/workflow_models.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/seed/seed_data.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/core/config.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/requirements.txt", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/api/search.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/main.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/frontend/src/assets/css/variables.css", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/frontend/src/components/project/ProjectHeaderFixed.vue", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/README.md", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/backend/test_project_api.py", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/tests/frontend/ProjectWizard.test.ts", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/.env", "25faddc1-e3cf-44a4-9661-30122ca4dfbb:/home/<USER>/workspace/backend/app/api/workflow_templates.py"], "size": 717410, "checkpointCount": 59, "lastModified": 1751126500628}}