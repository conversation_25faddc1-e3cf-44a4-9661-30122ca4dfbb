
# AI Services
ANTHROPIC_API_KEY=************************************************************************************************************

PERPLEXITY_API_KEY=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        # For research -- Format: pplx-abcde (Optional, Highly Recommended)

OPENAI_API_KEY=********************************************************************************************************************************************************************

# Security
SECRET_KEY=your-secret-key-change-in-production

# Redis REMOVED - using manual refresh instead

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Optional - defaults shown
MODEL=claude-4-sonnet  
PERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)
MAX_TOKENS=64000                  # Maximum tokens for model responses (Required)
TEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)
DEBUG=false                       # Enable debug logging (true/false)
LOG_LEVEL=info                    # Log level (debug, info, warn, error)
DEFAULT_SUBTASKS=5                # Default number of subtasks when expanding
DEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)
PROJECT_NAME=ExProject            # Project name for tasks.json metadata

DATABASE_URL=ppostgresql://neondb_owner:<EMAIL>/neondb?sslmode=require