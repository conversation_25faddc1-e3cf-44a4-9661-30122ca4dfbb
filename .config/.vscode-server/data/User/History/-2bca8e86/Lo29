# Code Architecture & Data Handling
- User prefers clean code architecture.
- User wants to eliminate mock data in favor of real database usage.

# Product Quality & Testing
- User emphasizes building a high-quality product.
- User wants comprehensive testing strategy for both backend and frontend in /tests/ directory.
- User has identified UI issues in project creation modals and Tailwind classes that need improvement.

# Planning & Documentation
- User prefers comprehensive planning based on documentation.

# Background Jobs & Search
- User prefers to keep background jobs disabled.
- User wants to implement manual search functionality instead of automated background processing.

# Frontend Design
- Frontend must maintain professional design system with Tailwind CSS.
- Frontend must support tenant-specific customization.
- Frontend must preserve current elegant UI consistency for multi-tenant deployments.

# Workflow Templates
- Workflow templates in /backend/app/workflows/templates/ are critical and should not be modified - there's a README_CRITICAL_DO_NOT_MODIFY.md file that contains important warnings about this system.
`