/* CSS Variables - Design System ExProject */
:root {
  /* Colors - Unified with PrimeVue */
  --color-primary: #4F46E5;
  --color-primary-hover: #4338ca;
  --color-primary-light: #eef2ff;
  
  --color-secondary: #f3f4f6;
  --color-secondary-hover: #e5e7eb;
  
  --color-success: #10b981;
  --color-success-hover: #059669;
  --color-success-light: #f0fdf4;
  
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-warning-light: #fefbf2;
  
  --color-danger: #ef4444;
  --color-danger-hover: #dc2626;
  --color-danger-light: #fef2f2;

  /* AI-specific colors (extending design system) */
  --color-ai-primary: #a78bfa;
  --color-ai-primary-hover: #7c3aed;
  --color-ai-primary-light: #faf5ff;
  --color-ai-success: #34d399;
  --color-ai-warning: #fbbf24;
  --color-ai-background: #fefbff;
  
  --color-info: #6366f1;
  --color-info-hover: #4338ca;
  --color-info-light: #eff6ff;
  
  /* Grays */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  --color-white: #ffffff;
  --color-black: #000000;
  
  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Borders */
  --border-width: 1px;
  --border-color: var(--color-gray-200);
  --border-color-focus: var(--color-primary);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-modal: 1050;
  --z-tooltip: 1100;
  
  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}