"""
Seed data for ExProject database
Popola il database con dati realistici per validare le operazioni CRUD
"""

import os
import sys
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.user_simple import User
from app.models.project import Project
from app.models.procedure import Procedure, ProcedureStep
from app.models.property import Property
from app.models.notification import Notification, NotificationTemplate
from app.models.task import Task, TaskComment, DeadlineTracker
from app.core.security import get_password_hash
from app.core.config import settings
from app.core.database import Base

# settings already imported

def create_seed_data():
    """Create realistic seed data for the database"""
    
    # Create database engine and session
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create tables if they don't exist
        Base.metadata.create_all(bind=engine)
        
        # Clear existing data (optional - remove in production)
        print("Clearing existing data...")
        db.query(StepExecution).delete()
        db.query(ProcedureWorkflowState).delete()
        db.query(ProcedureStep).delete()
        db.query(Procedure).delete()
        db.query(Property).delete()
        db.query(Project).delete()
        # Don't delete users - keep existing admin
        
        # Create additional users with various roles
        print("Creating users...")
        users_data = [
            {
                "email": "<EMAIL>",
                "full_name": "Mario Rossi - RUP",
                "password": "rup123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>", 
                "full_name": "Giulia Bianchi - RPE",
                "password": "rpe123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Francesco Verdi - Dirigente",
                "password": "dirigente123", 
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Anna Neri - Tecnico GIS",
                "password": "tecnico123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Luca Gialli - Ufficio Legale",
                "password": "legale123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Sara Blu - Assistente Procedure",
                "password": "assistente123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Giuseppe Verdi - Cittadino",
                "password": "cittadino123",
                "is_admin": False
            },
            {
                "email": "<EMAIL>",
                "full_name": "Maria Rosa - Consulente Tecnico",
                "password": "consulente123",
                "is_admin": False
            }
        ]
        
        created_users = []
        for user_data in users_data:
            existing_user = db.query(User).filter(User.email == user_data["email"]).first()
            if not existing_user:
                user = User(
                    email=user_data["email"],
                    full_name=user_data["full_name"],
                    hashed_password=get_password_hash(user_data["password"]),
                    is_admin=user_data["is_admin"],
                    is_active=True
                )
                db.add(user)
                created_users.append(user)
        
        db.commit()
        
        # Get admin user
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        # Create realistic projects
        print("Creating projects...")
        projects_data = [
            {
                "name": "Ampliamento SS36 - Tratto Lecco-Colico",
                "description": "Progetto di ampliamento della Strada Statale 36 nel tratto tra Lecco e Colico, inclusa realizzazione di nuove corsie e svincoli.",
                "project_type": "infrastructure",
                "status": "planning",
                "contracting_authority": "ANAS S.p.A.",
                "municipality": "Lecco",
                "province": "LC",
                "total_budget": 45000000.00,
                "workflow_template_id": "decreto_esproprio"
            },
            {
                "name": "Metropolitana Leggera Milano-Monza",
                "description": "Realizzazione della tratta di metropolitana leggera che collegherà Milano a Monza, con 12 fermate intermedie.",
                "project_type": "transport", 
                "status": "feasibility",
                "contracting_authority": "Regione Lombardia",
                "municipality": "Milano",
                "province": "MI",
                "total_budget": 850000000.00,
                "workflow_template_id": "occupazione_urgenza"
            },
            {
                "name": "Parco Urbano Lambro Nord",
                "description": "Creazione di un nuovo parco urbano nella zona nord di Milano, con esproprio di aree private per uso pubblico.",
                "project_type": "public_utility",
                "status": "expropriation",
                "contracting_authority": "Comune di Milano",
                "municipality": "Milano", 
                "province": "MI",
                "total_budget": 12000000.00,
                "workflow_template_id": "decreto_esproprio"
            },
            {
                "name": "Tangenziale Est Bergamo",
                "description": "Realizzazione della tangenziale est di Bergamo per decongestionare il traffico del centro città.",
                "project_type": "infrastructure",
                "status": "execution", 
                "contracting_authority": "Provincia di Bergamo",
                "municipality": "Bergamo",
                "province": "BG", 
                "total_budget": 78000000.00,
                "workflow_template_id": "decreto_esproprio"
            },
            {
                "name": "Depuratore Acque Reflue Como Sud",
                "description": "Costruzione di nuovo impianto di depurazione acque reflue per i comuni dell'area sud di Como.",
                "project_type": "public_utility",
                "status": "completed",
                "contracting_authority": "ATO Como",
                "municipality": "Como",
                "province": "CO",
                "total_budget": 25000000.00,
                "workflow_template_id": "decreto_esproprio"
            }
        ]
        
        created_projects = []
        for project_data in projects_data:
            project = Project(
                name=project_data["name"],
                description=project_data["description"],
                project_type=project_data["project_type"],
                status=project_data["status"],
                contracting_authority=project_data["contracting_authority"],
                municipality=project_data["municipality"],
                province=project_data["province"],
                total_budget=project_data["total_budget"],
                workflow_template_id=project_data["workflow_template_id"],
                created_by_id=admin_user.id if admin_user else 1,
                created_at=datetime.now() - timedelta(days=len(created_projects) * 15)
            )
            db.add(project)
            created_projects.append(project)
        
        db.commit()
        
        # Create procedures for projects
        print("Creating procedures...")
        
        # Create procedures using actual project IDs
        for i, project in enumerate(created_projects[:4]):  # Only for first 4 projects
            procedure_configs = [
                ("decreto_esproprio", "active", "high"),
                ("occupazione_urgenza", "active", "high"), 
                ("decreto_esproprio", "pending", "medium"),
                ("decreto_esproprio", "completed", "low")
            ]
            
            procedure_titles = [
                "Decreto di Esproprio - Lotto A SS36",
                "Occupazione d'Urgenza - Stazione Sesto San Giovanni",
                "Decreto di Esproprio - Area Verde Lambro",
                "Procedura Completata - Svincolo A4"
            ]
            
            procedure_descriptions = [
                "Procedimento di esproprio per il lotto A del progetto SS36",
                "Procedimento di occupazione d'urgenza per la realizzazione della stazione di Sesto San Giovanni",
                "Esproprio dell'area destinata a verde pubblico nel parco Lambro",
                "Procedimento di esproprio completato per lo svincolo della A4"
            ]
            
            if i < len(procedure_configs):
                proc_type, status, priority = procedure_configs[i]
                procedure = Procedure(
                    title=procedure_titles[i],
                    description=procedure_descriptions[i],
                    procedure_type=proc_type,
                    status=status,
                    priority=priority,
                    workflow_template_id=proc_type,
                    project_id=project.id,
                    assigned_user_id=admin_user.id if admin_user else 1,
                    created_at=datetime.now() - timedelta(days=i * 10)
                )
                db.add(procedure)
        
        db.commit()
        
        # Create sample properties (particelle catastali) - using correct column names
        print("Creating properties...")
        properties_data = [
            # Progetto SS36 - Lecco
            {
                "comune": "Lecco",
                "sezione": "A",
                "foglio": "23",
                "particella": "456", 
                "subalterno": "1",
                "owner_name": "Rossi Mario",
                "owner_fiscal_code": "****************",
                "owner_address": "Via Roma 12, 23900 Lecco (LC)",
                "property_type": "agricultural",
                "surface_area": 2500.00,
                "cadastral_income": 1200.00,
                "estimated_value": 125000.00,
                "compensation_amount": 140000.00,
                "project_id": 1
            },
            {
                "comune": "Lecco",
                "sezione": "A",
                "foglio": "23",
                "particella": "457", 
                "subalterno": None,
                "owner_name": "Azienda Agricola Lecchese S.r.l.",
                "owner_fiscal_code": "02468135792",
                "owner_address": "Corso Matteotti 45, 23900 Lecco (LC)",
                "property_type": "agricultural",
                "surface_area": 4200.00,
                "cadastral_income": 1800.00,
                "estimated_value": 210000.00,
                "compensation_amount": 235000.00,
                "project_id": 1
            },
            # Progetto Metro Milano-Monza
            {
                "comune": "Milano",
                "sezione": "B",
                "foglio": "189",
                "particella": "012",
                "subalterno": "2",
                "owner_name": "Bianchi Giulia",
                "owner_fiscal_code": "****************",
                "owner_address": "Viale Monza 78, 20125 Milano (MI)",
                "property_type": "residential",
                "surface_area": 1800.00,
                "cadastral_income": 2800.00,
                "estimated_value": 540000.00,
                "compensation_amount": 610000.00,
                "project_id": 2
            },
            {
                "comune": "Sesto San Giovanni",
                "sezione": "C",
                "foglio": "45",
                "particella": "234",
                "subalterno": None,
                "owner_name": "Immobiliare Brianza S.p.A.",
                "owner_fiscal_code": "13579246801",
                "owner_address": "Via Garibaldi 156, 20099 Sesto San Giovanni (MI)",
                "property_type": "commercial",
                "surface_area": 3200.00,
                "cadastral_income": 4200.00,
                "estimated_value": 960000.00,
                "compensation_amount": 1080000.00,
                "project_id": 2
            },
            # Progetto Parco Lambro
            {
                "comune": "Milano",
                "sezione": "D",
                "foglio": "345",
                "particella": "678",
                "subalterno": None,
                "owner_name": "Verdi Francesco e Figli S.n.c.",
                "owner_fiscal_code": "05172839461",
                "owner_address": "Via Lambro 23, 20134 Milano (MI)",
                "property_type": "agricultural",
                "surface_area": 5200.00,
                "cadastral_income": 980.00,
                "estimated_value": 312000.00,
                "compensation_amount": 350000.00,
                "project_id": 3
            },
            {
                "comune": "Milano",
                "sezione": "D",
                "foglio": "345",
                "particella": "679",
                "subalterno": "1",
                "owner_name": "Condominio Parco Verde",
                "owner_fiscal_code": "97531864027",
                "owner_address": "Via Trentacoste 8, 20134 Milano (MI)",
                "property_type": "public",
                "surface_area": 1200.00,
                "cadastral_income": 450.00,
                "estimated_value": 72000.00,
                "compensation_amount": 80000.00,
                "project_id": 3
            },
            # Progetto Tangenziale Bergamo
            {
                "comune": "Bergamo",
                "sezione": "E",
                "foglio": "901",
                "particella": "234", 
                "subalterno": "1",
                "owner_name": "Società Immobiliare Nord S.r.l.",
                "owner_fiscal_code": "08642097531",
                "owner_address": "Viale Papa Giovanni XXIII 102, 24121 Bergamo (BG)",
                "property_type": "industrial",
                "surface_area": 8200.00,
                "cadastral_income": 3200.00,
                "estimated_value": 656000.00,
                "compensation_amount": 740000.00,
                "project_id": 4
            },
            {
                "comune": "Bergamo",
                "sezione": "E",
                "foglio": "901",
                "particella": "235",
                "subalterno": None,
                "owner_name": "Famiglia Neri - Comproprietà",
                "owner_fiscal_code": "****************",
                "owner_address": "Via Colleoni 45, 24129 Bergamo (BG)",
                "property_type": "residential",
                "surface_area": 2100.00,
                "cadastral_income": 1600.00,
                "estimated_value": 336000.00,
                "compensation_amount": 380000.00,
                "project_id": 4
            },
            # Progetto Depuratore Como
            {
                "comune": "Como",
                "sezione": "F",
                "foglio": "567",
                "particella": "890",
                "subalterno": None,
                "owner_name": "Comune di Como",
                "owner_fiscal_code": "00507990138",
                "owner_address": "Piazza San Fedele 1, 22100 Como (CO)",
                "property_type": "public",
                "surface_area": 6800.00,
                "cadastral_income": 0.00,
                "estimated_value": 340000.00,
                "compensation_amount": 340000.00,
                "project_id": 5
            }
        ]
        
        # Assign properties to actual project IDs
        for i, property_data in enumerate(properties_data):
            # Map property index to actual project
            project_index = property_data["project_id"] - 1  # Convert to 0-based index
            actual_project_id = created_projects[project_index].id if project_index < len(created_projects) else created_projects[0].id
            
            property_obj = Property(
                comune=property_data["comune"],
                sezione=property_data["sezione"],
                foglio=property_data["foglio"], 
                particella=property_data["particella"],
                subalterno=property_data["subalterno"],
                owner_name=property_data["owner_name"],
                owner_fiscal_code=property_data["owner_fiscal_code"],
                owner_address=property_data["owner_address"],
                property_type=property_data["property_type"],
                surface_area=property_data["surface_area"],
                cadastral_income=property_data["cadastral_income"],
                estimated_value=property_data["estimated_value"],
                compensation_amount=property_data["compensation_amount"],
                project_id=actual_project_id,
                created_at=datetime.now()
            )
            db.add(property_obj)
        
        db.commit()
        
        # Get created procedures for workflow states
        procedures = db.query(Procedure).all()
        
        # Create workflow states for procedures
        print("Creating workflow states...")
        for i, procedure in enumerate(procedures[:3]):  # Only for active procedures
            workflow_state = ProcedureWorkflowState(
                procedure_id=procedure.id,
                workflow_template_id=procedure.workflow_template_id,
                current_step_id="step_1_notifiche",
                current_step_name="Notifiche ai Proprietari",
                overall_status="in_progress" if i < 2 else "pending",
                completed_steps=["step_0_preparazione"] if i < 2 else [],
                failed_steps=[],
                step_data={
                    "step_0_preparazione": {
                        "completed_date": "2024-12-01",
                        "notes": "Documentazione preparata correttamente"
                    }
                } if i < 2 else {},
                started_at=datetime.now() - timedelta(days=15 + i*5),
                next_deadline=datetime.now() + timedelta(days=30 - i*10),
                created_at=datetime.now() - timedelta(days=15 + i*5)
            )
            db.add(workflow_state)
        
        db.commit()
        
        # Create step executions for the workflow states
        print("Creating step executions...")
        workflow_states = db.query(ProcedureWorkflowState).all()
        
        for i, state in enumerate(workflow_states):
            # Step 1: Preparazione (completed)
            step_exec_1 = StepExecution(
                workflow_state_id=state.id,
                step_id="step_0_preparazione",
                step_name="Preparazione Documentazione",
                step_type="data_collection",
                status="completed",
                assigned_user_id=admin_user.id if admin_user else 1,
                form_data={
                    "documents_collected": True,
                    "legal_review": "Completata",
                    "technical_survey": "Eseguita"
                },
                completion_notes="Documentazione preparata secondo normativa D.P.R. 327/2001",
                started_at=datetime.now() - timedelta(days=15 + i*5),
                completed_at=datetime.now() - timedelta(days=10 + i*3),
                legal_deadline=datetime.now() - timedelta(days=5 + i*2),
                output_documents=["DOC001", "DOC002"],
                notifications_sent=[]
            )
            db.add(step_exec_1)
            
            # Step 2: Notifiche (in progress for first two)
            if i < 2:
                step_exec_2 = StepExecution(
                    workflow_state_id=state.id,
                    step_id="step_1_notifiche",
                    step_name="Notifiche ai Proprietari",
                    step_type="notification",
                    status="in_progress" if i == 0 else "completed",
                    assigned_user_id=(created_users[1].id if len(created_users) > 1 else admin_user.id) if admin_user else 2,
                    form_data={
                        "notification_method": "PEC",
                        "properties_notified": 2 + i,
                        "total_properties": 3 + i
                    },
                    completion_notes="Notifiche inviate via PEC certificata" if i == 1 else None,
                    started_at=datetime.now() - timedelta(days=10 + i*3),
                    completed_at=datetime.now() - timedelta(days=5) if i == 1 else None,
                    legal_deadline=datetime.now() + timedelta(days=20 - i*10),
                    output_documents=["NOTIF001", "NOTIF002"],
                    notifications_sent=["PEC001", "PEC002"] if i == 1 else ["PEC001"]
                )
                db.add(step_exec_2)
        
        db.commit()
        
        # Create notification templates
        print("Creating notification templates...")
        templates_data = [
            {
                "name": "Notifica Avvio Procedimento Art. 11",
                "template_type": "pec",
                "procedure_type": "decreto_esproprio",
                "subject_template": "Avvio procedimento espropriativo - {project_name}",
                "body_template": "Con la presente si comunica l'avvio del procedimento espropriativo relativo al progetto {project_name} ai sensi dell'art. 11 del D.P.R. 327/2001.",
                "legal_required": True,
                "delivery_confirmation_required": True,
                "send_days_before_deadline": 7
            },
            {
                "name": "Richiesta Documentazione Tecnica",
                "template_type": "pec",
                "procedure_type": "decreto_esproprio",
                "subject_template": "Richiesta documentazione tecnica - {project_name}",
                "body_template": "Si richiede di fornire la documentazione tecnica necessaria per il completamento della fase {phase_name}.",
                "legal_required": False,
                "delivery_confirmation_required": True,
                "send_days_before_deadline": 3
            }
        ]
        
        created_templates = []
        for template_data in templates_data:
            template = NotificationTemplate(**template_data)
            db.add(template)
            created_templates.append(template)
        
        db.commit()
        
        # Create sample notifications
        print("Creating notifications...")
        notifications_data = [
            {
                "subject": "PEC da inviare per Linea ferroviaria Milano-Bergamo",
                "body": "Notifica Art. 16 in attesa di invio a 15 proprietari per il progetto di ampliamento della linea ferroviaria Milano-Bergamo. Scadenza procedimento: 30 giorni.",
                "recipient_type": "external",
                "recipient_email": "<EMAIL>",
                "recipient_pec": "<EMAIL>",
                "delivery_method": "pec",
                "status": "pending",
                "project_id": 1,
                "procedure_id": 1,
                "scheduled_send_date": datetime.now() + timedelta(days=1)
            },
            {
                "subject": "Documento richiesto per SS36",
                "body": "Relazione tecnica necessaria per completare la fase di dichiarazione di pubblica utilità per il progetto SS36 Lecco.",
                "recipient_type": "user",
                "recipient_user_id": 2,
                "delivery_method": "pec",
                "status": "pending", 
                "project_id": 1,
                "procedure_id": 1,
                "scheduled_send_date": datetime.now() + timedelta(hours=6)
            },
            {
                "subject": "Approvazione decreto esproprio necessaria",
                "body": "Decreto di esproprio per Bretella autostradale A4-A57 in attesa di firma del dirigente responsabile. Procedimento urgente.",
                "recipient_type": "user",
                "recipient_user_id": 1,
                "delivery_method": "pec",
                "status": "pending",
                "project_id": 2,
                "procedure_id": 2,
                "scheduled_send_date": datetime.now() + timedelta(hours=2)
            },
            {
                "subject": "Integrazione SISTER completata",
                "body": "Nuovi dati catastali disponibili per sincronizzazione dal sistema SISTER. 5 nuove particelle identificate per il progetto Parco Lambro.",
                "recipient_type": "user",
                "recipient_user_id": 1,
                "delivery_method": "pec",
                "status": "sent",
                "project_id": 3,
                "sent_at": datetime.now() - timedelta(hours=2)
            }
        ]
        
        created_notifications = []
        for notif_data in notifications_data:
            # Find actual project and procedure IDs
            if notif_data.get("project_id") and notif_data["project_id"] <= len(created_projects):
                notif_data["project_id"] = created_projects[notif_data["project_id"] - 1].id
            
            notification = Notification(**notif_data)
            db.add(notification)
            created_notifications.append(notification)
        
        db.commit()
        
        # Create sample tasks
        print("Creating tasks...")
        tasks_data = [
            {
                "title": "Inviare notifiche PEC Art. 16",
                "description": "Inviare le notifiche PEC di avvio procedimento ai 15 proprietari identificati per il progetto SS36",
                "task_type": "notification_send",
                "priority": "high",
                "status": "pending",
                "assigned_user_id": 2,
                "project_id": 1,
                "procedure_id": 1,
                "due_date": datetime.now() + timedelta(days=2),
                "legal_deadline": True,
                "escalation_rules": {"days_before_escalation": 1, "escalate_to_user_id": 1}
            },
            {
                "title": "Preparare relazione tecnica",
                "description": "Redigere la relazione tecnica per la fase di dichiarazione di pubblica utilità",
                "task_type": "document_upload",
                "priority": "medium",
                "status": "in_progress",
                "assigned_user_id": 3,
                "project_id": 1,
                "procedure_id": 1,
                "due_date": datetime.now() + timedelta(days=7),
                "started_at": datetime.now() - timedelta(days=2),
                "legal_deadline": False
            },
            {
                "title": "Approvazione decreto dirigente",
                "description": "Decreto di esproprio per Bretella A4-A57 necessita approvazione e firma del dirigente",
                "task_type": "approval_required",
                "priority": "urgent",
                "status": "pending",
                "assigned_user_id": 1,
                "project_id": 2,
                "procedure_id": 2,
                "due_date": datetime.now() + timedelta(days=1),
                "legal_deadline": True,
                "escalation_rules": {"days_before_escalation": 0.5, "escalate_to_user_id": 1}
            },
            {
                "title": "Sincronizzazione dati SISTER",
                "description": "Sincronizzare i nuovi dati catastali dal sistema SISTER per aggiornare l'elenco delle particelle",
                "task_type": "integration_sync",
                "priority": "low",
                "status": "completed",
                "assigned_user_id": 4,
                "project_id": 3,
                "due_date": datetime.now() - timedelta(days=1),
                "completed_at": datetime.now() - timedelta(hours=6),
                "completion_notes": "Sincronizzazione completata con successo. 5 nuove particelle aggiunte.",
                "legal_deadline": False
            },
            {
                "title": "Revisione legale contratti cessione",
                "description": "Revisione dei contratti di cessione volontaria per il progetto Tangenziale Bergamo",
                "task_type": "legal_review",
                "priority": "medium",
                "status": "pending",
                "assigned_user_id": 5,
                "project_id": 4,
                "due_date": datetime.now() + timedelta(days=10),
                "legal_deadline": False
            }
        ]
        
        created_tasks = []
        for i, task_data in enumerate(tasks_data):
            # Map to actual user and project IDs
            if task_data.get("assigned_user_id") and task_data["assigned_user_id"] <= len(created_users):
                task_data["assigned_user_id"] = created_users[task_data["assigned_user_id"] - 1].id
            
            if task_data.get("project_id") and task_data["project_id"] <= len(created_projects):
                task_data["project_id"] = created_projects[task_data["project_id"] - 1].id
                
            task_data["created_by_id"] = admin_user.id if admin_user else created_users[0].id
            
            task = Task(**task_data)
            db.add(task)
            created_tasks.append(task)
        
        db.commit()
        
        # Create task comments for some tasks
        print("Creating task comments...")
        comments_data = [
            {
                "task_id": 0,  # Will be updated to actual task ID
                "comment": "Avviata preparazione dell'elenco proprietari. Identificati 15 soggetti da notificare.",
                "comment_type": "status_change",
                "task_metadata": {"old_status": "pending", "new_status": "in_progress"}
            },
            {
                "task_id": 1,  # Will be updated to actual task ID
                "comment": "La relazione tecnica è al 60% di completamento. Mancano ancora le verifiche geologiche.",
                "comment_type": "comment",
                "task_metadata": {"completion_percentage": 60}
            },
            {
                "task_id": 3,  # Will be updated to actual task ID
                "comment": "Sincronizzazione SISTER completata con successo. Database aggiornato.",
                "comment_type": "status_change",
                "task_metadata": {"old_status": "in_progress", "new_status": "completed", "records_synced": 5}
            }
        ]
        
        for comment_data in comments_data:
            if comment_data["task_id"] < len(created_tasks):
                comment_data["task_id"] = created_tasks[comment_data["task_id"]].id
                comment_data["user_id"] = admin_user.id if admin_user else created_users[0].id
                
                comment = TaskComment(**comment_data)
                db.add(comment)
        
        db.commit()
        
        # Create deadline trackers
        print("Creating deadline trackers...")
        deadlines_data = [
            {
                "deadline_type": "legal",
                "title": "Scadenza notifiche Art. 16",
                "description": "Termine per l'invio delle notifiche di avvio procedimento espropriativo",
                "deadline_date": datetime.now() + timedelta(days=2),
                "alert_days_before": [7, 3, 1],
                "status": "upcoming",
                "project_id": 1,
                "procedure_id": 1,
                "responsible_user_id": 2
            },
            {
                "deadline_type": "legal", 
                "title": "Approvazione decreto esproprio",
                "description": "Termine massimo per l'approvazione del decreto di esproprio",
                "deadline_date": datetime.now() + timedelta(days=1),
                "alert_days_before": [3, 1],
                "status": "due",
                "escalation_level": 1,
                "project_id": 2,
                "procedure_id": 2,
                "responsible_user_id": 1
            },
            {
                "deadline_type": "internal",
                "title": "Completamento relazione tecnica",
                "description": "Termine interno per il completamento della relazione tecnica",
                "deadline_date": datetime.now() + timedelta(days=7),
                "alert_days_before": [7, 3, 1],
                "status": "upcoming",
                "project_id": 1,
                "procedure_id": 1,
                "responsible_user_id": 3
            }
        ]
        
        for deadline_data in deadlines_data:
            # Map to actual IDs
            if deadline_data.get("project_id") and deadline_data["project_id"] <= len(created_projects):
                deadline_data["project_id"] = created_projects[deadline_data["project_id"] - 1].id
                
            if deadline_data.get("responsible_user_id") and deadline_data["responsible_user_id"] <= len(created_users):
                deadline_data["responsible_user_id"] = created_users[deadline_data["responsible_user_id"] - 1].id
                
            deadline = DeadlineTracker(**deadline_data)
            db.add(deadline)
        
        db.commit()
        
        print("Seed data created successfully!")
        print(f"Created {len(created_users)} users")
        print(f"Created {len(created_projects)} projects") 
        print(f"Created {len(procedures)} procedures")
        print(f"Created {len(properties_data)} properties")
        print(f"Created {len(workflow_states)} workflow states")
        print(f"Created step executions for workflow tracking")
        print(f"Created {len(created_templates)} notification templates")
        print(f"Created {len(created_notifications)} notifications")
        print(f"Created {len(created_tasks)} tasks")
        print(f"Created deadline trackers")
        
    except Exception as e:
        print(f"Error creating seed data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_seed_data()