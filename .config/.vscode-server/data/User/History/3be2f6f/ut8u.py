from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.core.config import settings
from app.api.auth_simple import router as auth_router
from app.api.users import router as users_router
from app.api.procedures import router as procedures_router
from app.api.projects import router as projects_router
from app.api.properties import router as properties_router
from app.api.documents import router as documents_router
from app.api.reports import router as reports_router
from app.api.ai import router as ai_router
from app.api.ai_endpoints import router as ai_project_router
from app.api.workflow_templates import router as workflow_templates_router
from app.api.notifications import router as notifications_router
from app.api.tasks import router as tasks_router
from app.api.sister import router as sister_router
from app.api.search import router as search_router

# API Tags metadata
tags_metadata = [
    {
        "name": "authentication",
        "description": "Autenticazione e autorizzazione utenti",
    },
    {
        "name": "users",
        "description": "Gestione utenti e profili",
    },
    {
        "name": "projects", 
        "description": "Gestione progetti espropriativi",
    },
    {
        "name": "properties",
        "description": "Gestione particelle catastali e proprietà",
    },
    {
        "name": "sister",
        "description": "Integrazione con sistema SISTER (mock) per dati catastali",
    },
    {
        "name": "procedures",
        "description": "Gestione procedure espropriative secondo D.P.R. 327/2001",
    },
    {
        "name": "notifications",
        "description": "Sistema notifiche e comunicazioni PEC",
    },
    {
        "name": "tasks",
        "description": "Gestione task e scadenze procedimentali",
    },
    {
        "name": "documents",
        "description": "Gestione documenti e allegati",
    },
    {
        "name": "reports",
        "description": "Report e statistiche",
    },
    {
        "name": "workflow-templates",
        "description": "Template e configurazioni workflow",
    },
    {
        "name": "ai",
        "description": "Servizi AI per assistenza procedimentale e creazione progetti",
    },
]

# Create FastAPI application
app = FastAPI(
    title="ExProject - Sistema Gestione Espropri",
    description="WebApp SaaS per la gestione dei procedimenti espropriativi in Italia secondo il D.P.R. 327/2001",
    version="1.0.0",
    openapi_version="3.1.0",
    openapi_tags=tags_metadata,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    contact={
        "name": "ExProject Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Proprietary",
        "identifier": "Proprietary"
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Development server"
        },
        {
            "url": "https://api.exproject.it",
            "description": "Production server"
        }
    ]
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# Include routers
app.include_router(auth_router, prefix="/api/auth", tags=["authentication"])
app.include_router(users_router, prefix="/api/users", tags=["users"])
app.include_router(projects_router, prefix="/api/projects", tags=["projects"])
app.include_router(properties_router, prefix="/api/properties", tags=["properties"])
app.include_router(sister_router, prefix="/api/sister", tags=["sister"])
app.include_router(procedures_router, prefix="/api/procedures", tags=["procedures"])
app.include_router(notifications_router, prefix="/api/notifications", tags=["notifications"])
app.include_router(tasks_router, prefix="/api/tasks", tags=["tasks"])
app.include_router(documents_router, prefix="/api/documents", tags=["documents"])
app.include_router(reports_router, prefix="/api/reports", tags=["reports"])
app.include_router(workflow_templates_router, prefix="/api/workflow-templates", tags=["workflow-templates"])
app.include_router(ai_router, prefix="/api/ai", tags=["ai"])
app.include_router(ai_project_router, prefix="/api/ai/project", tags=["ai"])

@app.get("/")
async def root():
    return {"message": "ExProject API - Sistema Gestione Espropri"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ExProject API"}

@app.get("/api/health")
async def api_health_check():
    return {"status": "healthy", "service": "ExProject API"}