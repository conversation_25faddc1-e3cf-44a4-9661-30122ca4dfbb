from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # App configuration
    APP_NAME: str = "ExProject"
    DEBUG: bool = True
    VERSION: str = "1.0.0"
    
    # Server configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    CORS_ORIGINS: List[str] = ["*"]
    
    # Database configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://user:password@localhost/exproject")
    
    # Security configuration
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Redis configuration (DISABLED - using manual refresh instead)
    # REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # AI Services
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    ANTHROPIC_API_KEY: str = os.getenv("ANTHROPIC_API_KEY", "")
    PERPLEXITY_API_KEY: str = os.getenv("PERPLEXITY_API_KEY", "")
    
    # AI Model Configuration
    MODEL: str = os.getenv("MODEL", "claude-3-sonnet")
    PERPLEXITY_MODEL: str = os.getenv("PERPLEXITY_MODEL", "sonar-pro")
    MAX_TOKENS: int = int(os.getenv("MAX_TOKENS", "64000"))
    TEMPERATURE: float = float(os.getenv("TEMPERATURE", "0.2"))
    
    # Project Configuration
    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "ExProject")
    DEFAULT_SUBTASKS: int = int(os.getenv("DEFAULT_SUBTASKS", "5"))
    DEFAULT_PRIORITY: str = os.getenv("DEFAULT_PRIORITY", "medium")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "info")
    
    # File storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # Email configuration (for notifications)
    SMTP_HOST: str = os.getenv("SMTP_HOST", "")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()