2025-06-28 15:05:35.060 [info] 




2025-06-28 15:05:35.070 [info] Extension host agent started.
2025-06-28 15:05:35.387 [info] [<unknown>][1171114c][ManagementConnection] New connection established.
2025-06-28 15:05:35.422 [info] [<unknown>][ecf1f7b5][ExtensionHostConnection] New connection established.
2025-06-28 15:05:35.506 [info] [<unknown>][ecf1f7b5][ExtensionHostConnection] <3800> Launched Extension Host Process.
2025-06-28 15:05:35.736 [info] ComputeTargetPlatform: linux-x64
2025-06-28 15:05:37.234 [info] ComputeTargetPlatform: linux-x64
2025-06-28 15:05:38.448 [info] Getting Manifest... augment.vscode-augment
2025-06-28 15:05:38.449 [info] Getting Manifest... github.copilot
2025-06-28 15:05:38.449 [info] Getting Manifest... github.copilot-chat
2025-06-28 15:05:38.529 [info] Installing extension: github.copilot {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-28 15:05:38.552 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-28 15:05:38.569 [info] Installing extension: github.copilot-chat {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-28 15:05:40.933 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1825ms.
2025-06-28 15:05:40.951 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1758ms.
2025-06-28 15:05:40.970 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1843ms.
2025-06-28 15:05:41.686 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3: github.copilot-chat
2025-06-28 15:05:41.709 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3
2025-06-28 15:05:41.749 [info] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-28 15:05:41.752 [info] Marked extension as removed github.copilot-chat-0.28.1
2025-06-28 15:05:42.239 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.338.0: github.copilot
2025-06-28 15:05:42.290 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.338.0
2025-06-28 15:05:42.318 [info] Marked extension as removed github.copilot-1.336.0
2025-06-28 15:05:42.325 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-28 15:05:42.446 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1: augment.vscode-augment
2025-06-28 15:05:42.483 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1
2025-06-28 15:05:42.501 [info] Marked extension as removed augment.vscode-augment-0.482.1
2025-06-28 15:05:42.502 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-28 15:06:01.486 [info] [<unknown>][1171114c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-28 15:06:01.568 [info] [<unknown>][ecf1f7b5][ExtensionHostConnection] <3800> Extension Host Process exited with code: 0, signal: null.
2025-06-28 15:06:01.568 [info] Cancelling previous shutdown timeout
2025-06-28 15:06:01.568 [info] Last EH closed, waiting before shutting down
2025-06-28 15:06:03.414 [info] [<unknown>][30df160a][ManagementConnection] New connection established.
2025-06-28 15:06:03.415 [info] [<unknown>][d25cc1ab][ExtensionHostConnection] New connection established.
2025-06-28 15:06:03.419 [info] [<unknown>][d25cc1ab][ExtensionHostConnection] <4313> Launched Extension Host Process.
2025-06-28 15:11:01.569 [info] New EH opened, aborting shutdown
