2025-06-28 15:06:10.999 [info] [main] Log level: Info
2025-06-28 15:06:10.999 [info] [main] Validating found git in: "git"
2025-06-28 15:06:10.999 [info] [main] Using git "2.47.2" from "git"
2025-06-28 15:06:10.999 [info] [Model][doInitialScan] Initial repository scan started
2025-06-28 15:06:10.999 [info] > git rev-parse --show-toplevel [9ms]
2025-06-28 15:06:10.999 [info] > git rev-parse --git-dir --git-common-dir [16ms]
2025-06-28 15:06:10.999 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-28 15:06:10.999 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-28 15:06:11.015 [info] > git config --get commit.template [9ms]
2025-06-28 15:06:11.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-06-28 15:06:11.063 [info] > git rev-parse --show-toplevel [36ms]
2025-06-28 15:06:11.430 [info] > git status -z -uall [20ms]
2025-06-28 15:06:11.450 [info] > git rev-parse --show-toplevel [153ms]
2025-06-28 15:06:11.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [195ms]
2025-06-28 15:06:12.297 [info] > git check-ignore -v -z --stdin [131ms]
2025-06-28 15:06:12.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [669ms]
2025-06-28 15:06:12.316 [info] > git rev-parse --show-toplevel [859ms]
2025-06-28 15:06:12.537 [info] > git config --get --local branch.main.vscode-merge-base [222ms]
2025-06-28 15:06:12.538 [warning] [Git][config] git config failed: Failed to execute git
2025-06-28 15:06:12.547 [info] > git config --get commit.template [344ms]
2025-06-28 15:06:12.577 [info] > git rev-parse --show-toplevel [49ms]
2025-06-28 15:06:12.605 [info] > git reflog main --grep-reflog=branch: Created from *. [58ms]
2025-06-28 15:06:12.623 [info] > git rev-parse --show-toplevel [24ms]
2025-06-28 15:06:12.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [33ms]
2025-06-28 15:06:12.696 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [75ms]
2025-06-28 15:06:12.770 [info] > git rev-parse --show-toplevel [78ms]
2025-06-28 15:06:12.782 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [75ms]
2025-06-28 15:06:12.807 [info] > git rev-parse --show-toplevel [29ms]
2025-06-28 15:06:12.824 [info] > git status -z -uall [7ms]
2025-06-28 15:06:12.844 [info] > git config --add --local branch.main.vscode-merge-base origin/main [50ms]
2025-06-28 15:06:12.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms] (cancelled)
2025-06-28 15:06:12.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [66ms]
2025-06-28 15:06:13.270 [info] > git config --get --local branch.main.vscode-merge-base [346ms]
2025-06-28 15:06:13.278 [info] > git config --get commit.template [366ms]
2025-06-28 15:06:13.278 [info] > git rev-parse --show-toplevel [374ms]
2025-06-28 15:06:13.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [42ms]
2025-06-28 15:06:13.335 [info] > git rev-parse --show-toplevel [17ms]
2025-06-28 15:06:13.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-28 15:06:13.356 [info] > git merge-base refs/heads/main refs/remotes/origin/main [31ms]
2025-06-28 15:06:13.365 [info] > git rev-parse --show-toplevel [15ms]
2025-06-28 15:06:13.583 [info] > git diff --name-status -z --diff-filter=ADMR 5b9666563172e5a82d1e4f9b1395393f7c77e87c...refs/remotes/origin/main [220ms]
2025-06-28 15:06:13.810 [info] > git rev-parse --show-toplevel [441ms]
2025-06-28 15:06:13.812 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-28 15:06:13.928 [info] > git status -z -uall [136ms]
2025-06-28 15:06:13.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [136ms]
2025-06-28 15:06:21.560 [info] > git config --get commit.template [1ms]
2025-06-28 15:06:21.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:06:21.589 [info] > git status -z -uall [8ms]
2025-06-28 15:06:21.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:27.174 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:06:27.855 [info] > git config --get commit.template [5ms]
2025-06-28 15:06:27.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:27.865 [info] > git status -z -uall [5ms]
2025-06-28 15:06:27.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:06:30.673 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:06:33.985 [info] > git config --get commit.template [5ms]
2025-06-28 15:06:33.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:06:34.012 [info] > git status -z -uall [12ms]
2025-06-28 15:06:34.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:06:39.032 [info] > git config --get commit.template [6ms]
2025-06-28 15:06:39.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:39.051 [info] > git status -z -uall [6ms]
2025-06-28 15:06:39.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:06:44.978 [info] > git config --get commit.template [10ms]
2025-06-28 15:06:44.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:44.994 [info] > git status -z -uall [7ms]
2025-06-28 15:06:44.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:50.019 [info] > git config --get commit.template [1ms]
2025-06-28 15:06:50.032 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:06:50.050 [info] > git status -z -uall [8ms]
2025-06-28 15:06:50.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:55.094 [info] > git config --get commit.template [7ms]
2025-06-28 15:06:55.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:55.116 [info] > git status -z -uall [10ms]
2025-06-28 15:06:55.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:07:00.143 [info] > git config --get commit.template [7ms]
2025-06-28 15:07:00.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:00.159 [info] > git status -z -uall [7ms]
2025-06-28 15:07:00.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:05.184 [info] > git config --get commit.template [1ms]
2025-06-28 15:07:05.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:05.290 [info] > git status -z -uall [84ms]
2025-06-28 15:07:05.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [73ms]
2025-06-28 15:07:10.321 [info] > git config --get commit.template [10ms]
2025-06-28 15:07:10.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:10.371 [info] > git status -z -uall [23ms]
2025-06-28 15:07:10.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:07:15.397 [info] > git config --get commit.template [8ms]
2025-06-28 15:07:15.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:15.411 [info] > git status -z -uall [6ms]
2025-06-28 15:07:15.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:20.442 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:20.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:20.462 [info] > git status -z -uall [10ms]
2025-06-28 15:07:20.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:25.489 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:25.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:25.510 [info] > git status -z -uall [12ms]
2025-06-28 15:07:25.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:07:30.537 [info] > git config --get commit.template [10ms]
2025-06-28 15:07:30.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:30.556 [info] > git status -z -uall [9ms]
2025-06-28 15:07:30.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:35.580 [info] > git config --get commit.template [8ms]
2025-06-28 15:07:35.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:35.598 [info] > git status -z -uall [8ms]
2025-06-28 15:07:35.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:40.625 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:40.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:40.653 [info] > git status -z -uall [13ms]
2025-06-28 15:07:40.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:45.701 [info] > git config --get commit.template [19ms]
2025-06-28 15:07:45.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:45.751 [info] > git status -z -uall [29ms]
2025-06-28 15:07:45.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:07:50.801 [info] > git config --get commit.template [19ms]
2025-06-28 15:07:50.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:07:50.832 [info] > git status -z -uall [13ms]
2025-06-28 15:07:50.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:07:55.874 [info] > git config --get commit.template [18ms]
2025-06-28 15:07:55.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:55.903 [info] > git status -z -uall [14ms]
2025-06-28 15:07:55.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:00.937 [info] > git config --get commit.template [13ms]
2025-06-28 15:08:00.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:00.957 [info] > git status -z -uall [8ms]
2025-06-28 15:08:00.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:05.989 [info] > git config --get commit.template [11ms]
2025-06-28 15:08:05.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:06.012 [info] > git status -z -uall [9ms]
2025-06-28 15:08:06.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:11.043 [info] > git config --get commit.template [10ms]
2025-06-28 15:08:11.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:11.070 [info] > git status -z -uall [14ms]
2025-06-28 15:08:11.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:08:16.097 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:16.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:08:16.115 [info] > git status -z -uall [9ms]
2025-06-28 15:08:16.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:21.146 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:21.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:21.165 [info] > git status -z -uall [7ms]
2025-06-28 15:08:21.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:26.251 [info] > git config --get commit.template [66ms]
2025-06-28 15:08:26.284 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-06-28 15:08:26.312 [info] > git status -z -uall [15ms]
2025-06-28 15:08:26.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:08:31.357 [info] > git config --get commit.template [14ms]
2025-06-28 15:08:31.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:08:31.398 [info] > git status -z -uall [21ms]
2025-06-28 15:08:31.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [142ms]
2025-06-28 15:08:36.562 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:36.565 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:08:36.587 [info] > git status -z -uall [11ms]
2025-06-28 15:08:36.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:41.619 [info] > git config --get commit.template [14ms]
2025-06-28 15:08:41.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:41.718 [info] > git status -z -uall [89ms]
2025-06-28 15:08:41.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [82ms]
2025-06-28 15:08:46.768 [info] > git config --get commit.template [19ms]
2025-06-28 15:08:46.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:08:46.804 [info] > git status -z -uall [13ms]
2025-06-28 15:08:46.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:08:51.864 [info] > git config --get commit.template [3ms]
2025-06-28 15:08:51.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:08:51.910 [info] > git status -z -uall [14ms]
2025-06-28 15:08:51.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:08:56.937 [info] > git config --get commit.template [8ms]
2025-06-28 15:08:56.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:08:56.956 [info] > git status -z -uall [10ms]
2025-06-28 15:08:56.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:01.982 [info] > git config --get commit.template [1ms]
2025-06-28 15:09:01.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:09:02.023 [info] > git status -z -uall [14ms]
2025-06-28 15:09:02.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:07.051 [info] > git config --get commit.template [7ms]
2025-06-28 15:09:07.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:07.071 [info] > git status -z -uall [8ms]
2025-06-28 15:09:07.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:12.113 [info] > git config --get commit.template [18ms]
2025-06-28 15:09:12.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:09:12.157 [info] > git status -z -uall [21ms]
2025-06-28 15:09:12.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:17.252 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:17.253 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:17.280 [info] > git status -z -uall [15ms]
2025-06-28 15:09:17.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:22.318 [info] > git config --get commit.template [14ms]
2025-06-28 15:09:22.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:09:22.357 [info] > git status -z -uall [21ms]
2025-06-28 15:09:22.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:09:27.397 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:27.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:27.422 [info] > git status -z -uall [11ms]
2025-06-28 15:09:27.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:32.454 [info] > git config --get commit.template [11ms]
2025-06-28 15:09:32.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:09:32.479 [info] > git status -z -uall [12ms]
2025-06-28 15:09:32.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:37.516 [info] > git config --get commit.template [15ms]
2025-06-28 15:09:37.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:37.536 [info] > git status -z -uall [10ms]
2025-06-28 15:09:37.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:09:42.569 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:42.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:42.601 [info] > git status -z -uall [20ms]
2025-06-28 15:09:42.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:47.627 [info] > git config --get commit.template [4ms]
2025-06-28 15:09:47.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:09:47.682 [info] > git status -z -uall [14ms]
2025-06-28 15:09:47.684 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:52.719 [info] > git config --get commit.template [14ms]
2025-06-28 15:09:52.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:09:52.748 [info] > git status -z -uall [12ms]
2025-06-28 15:09:52.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:57.779 [info] > git config --get commit.template [11ms]
2025-06-28 15:09:57.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:57.799 [info] > git status -z -uall [10ms]
2025-06-28 15:09:57.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:02.830 [info] > git config --get commit.template [11ms]
2025-06-28 15:10:02.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:02.858 [info] > git status -z -uall [14ms]
2025-06-28 15:10:02.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:10:07.887 [info] > git config --get commit.template [8ms]
2025-06-28 15:10:07.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:07.915 [info] > git status -z -uall [14ms]
2025-06-28 15:10:07.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:10:12.953 [info] > git config --get commit.template [14ms]
2025-06-28 15:10:12.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:12.986 [info] > git status -z -uall [15ms]
2025-06-28 15:10:12.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:18.016 [info] > git config --get commit.template [11ms]
2025-06-28 15:10:18.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:18.037 [info] > git status -z -uall [10ms]
2025-06-28 15:10:18.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:23.065 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:23.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:23.089 [info] > git status -z -uall [11ms]
2025-06-28 15:10:23.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:10:28.119 [info] > git config --get commit.template [10ms]
2025-06-28 15:10:28.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:28.138 [info] > git status -z -uall [9ms]
2025-06-28 15:10:28.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:10:33.166 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:33.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:33.187 [info] > git status -z -uall [10ms]
2025-06-28 15:10:33.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:38.247 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:38.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:38.275 [info] > git status -z -uall [13ms]
2025-06-28 15:10:38.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:10:43.930 [info] > git config --get commit.template [12ms]
2025-06-28 15:10:43.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:43.950 [info] > git status -z -uall [9ms]
2025-06-28 15:10:43.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:48.979 [info] > git config --get commit.template [10ms]
2025-06-28 15:10:48.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:48.999 [info] > git status -z -uall [7ms]
2025-06-28 15:10:49.000 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:54.032 [info] > git config --get commit.template [12ms]
2025-06-28 15:10:54.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:54.052 [info] > git status -z -uall [9ms]
2025-06-28 15:10:54.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:59.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-28 15:10:59.105 [info] > git config --get commit.template [33ms]
2025-06-28 15:10:59.129 [info] > git status -z -uall [11ms]
2025-06-28 15:10:59.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:11:04.157 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:04.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:04.180 [info] > git status -z -uall [11ms]
2025-06-28 15:11:04.181 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:09.210 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:09.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:09.233 [info] > git status -z -uall [11ms]
2025-06-28 15:11:09.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:14.280 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:14.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:14.309 [info] > git status -z -uall [15ms]
2025-06-28 15:11:14.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:11:19.340 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:19.341 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:19.359 [info] > git status -z -uall [8ms]
2025-06-28 15:11:19.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:11:24.388 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:24.390 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:24.410 [info] > git status -z -uall [9ms]
2025-06-28 15:11:24.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:29.443 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:29.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:29.469 [info] > git status -z -uall [13ms]
2025-06-28 15:11:29.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:34.501 [info] > git config --get commit.template [13ms]
2025-06-28 15:11:34.502 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:34.522 [info] > git status -z -uall [9ms]
2025-06-28 15:11:34.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:39.557 [info] > git config --get commit.template [13ms]
2025-06-28 15:11:39.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:11:39.586 [info] > git status -z -uall [14ms]
2025-06-28 15:11:39.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:44.614 [info] > git config --get commit.template [9ms]
2025-06-28 15:11:44.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:44.631 [info] > git status -z -uall [8ms]
2025-06-28 15:11:44.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:11:49.665 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:49.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:49.687 [info] > git status -z -uall [11ms]
2025-06-28 15:11:49.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:54.724 [info] > git config --get commit.template [15ms]
2025-06-28 15:11:54.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:54.747 [info] > git status -z -uall [9ms]
2025-06-28 15:11:54.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:59.775 [info] > git config --get commit.template [1ms]
2025-06-28 15:11:59.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:59.816 [info] > git status -z -uall [15ms]
2025-06-28 15:11:59.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:04.846 [info] > git config --get commit.template [4ms]
2025-06-28 15:12:04.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:04.886 [info] > git status -z -uall [11ms]
2025-06-28 15:12:04.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:09.905 [info] > git config --get commit.template [2ms]
2025-06-28 15:12:09.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:09.941 [info] > git status -z -uall [10ms]
2025-06-28 15:12:09.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:14.968 [info] > git config --get commit.template [2ms]
2025-06-28 15:12:14.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:15.007 [info] > git status -z -uall [9ms]
2025-06-28 15:12:15.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:20.037 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:20.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:20.052 [info] > git status -z -uall [7ms]
2025-06-28 15:12:20.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:25.082 [info] > git config --get commit.template [12ms]
2025-06-28 15:12:25.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:25.100 [info] > git status -z -uall [10ms]
2025-06-28 15:12:25.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:30.129 [info] > git config --get commit.template [8ms]
2025-06-28 15:12:30.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:12:30.150 [info] > git status -z -uall [12ms]
2025-06-28 15:12:30.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:35.178 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:35.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:35.259 [info] > git status -z -uall [66ms]
2025-06-28 15:12:35.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-28 15:12:40.291 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:40.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:40.311 [info] > git status -z -uall [8ms]
2025-06-28 15:12:40.312 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:45.342 [info] > git config --get commit.template [12ms]
2025-06-28 15:12:45.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:12:45.367 [info] > git status -z -uall [11ms]
2025-06-28 15:12:45.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:50.431 [info] > git config --get commit.template [26ms]
2025-06-28 15:12:50.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:12:50.501 [info] > git status -z -uall [30ms]
2025-06-28 15:12:50.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:12:55.539 [info] > git config --get commit.template [16ms]
2025-06-28 15:12:55.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:55.570 [info] > git status -z -uall [16ms]
2025-06-28 15:12:55.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:00.602 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:00.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:00.625 [info] > git status -z -uall [13ms]
2025-06-28 15:13:00.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:05.656 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:05.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:05.672 [info] > git status -z -uall [7ms]
2025-06-28 15:13:05.673 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:13:10.703 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:10.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:10.721 [info] > git status -z -uall [7ms]
2025-06-28 15:13:10.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:15.749 [info] > git config --get commit.template [9ms]
2025-06-28 15:13:15.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:15.768 [info] > git status -z -uall [10ms]
2025-06-28 15:13:15.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:20.794 [info] > git config --get commit.template [8ms]
2025-06-28 15:13:20.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:20.812 [info] > git status -z -uall [7ms]
2025-06-28 15:13:20.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:25.839 [info] > git config --get commit.template [10ms]
2025-06-28 15:13:25.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:25.857 [info] > git status -z -uall [7ms]
2025-06-28 15:13:25.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:30.905 [info] > git config --get commit.template [21ms]
2025-06-28 15:13:30.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:30.947 [info] > git status -z -uall [24ms]
2025-06-28 15:13:30.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:36.006 [info] > git config --get commit.template [32ms]
2025-06-28 15:13:36.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:13:36.050 [info] > git status -z -uall [21ms]
2025-06-28 15:13:36.050 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:41.082 [info] > git config --get commit.template [15ms]
2025-06-28 15:13:41.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:13:41.129 [info] > git status -z -uall [23ms]
2025-06-28 15:13:41.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:46.171 [info] > git config --get commit.template [20ms]
2025-06-28 15:13:46.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:46.194 [info] > git status -z -uall [9ms]
2025-06-28 15:13:46.194 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:13:51.223 [info] > git config --get commit.template [9ms]
2025-06-28 15:13:51.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:51.243 [info] > git status -z -uall [9ms]
2025-06-28 15:13:51.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:56.279 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:56.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:56.306 [info] > git status -z -uall [9ms]
2025-06-28 15:13:56.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:01.335 [info] > git config --get commit.template [10ms]
2025-06-28 15:14:01.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:01.354 [info] > git status -z -uall [10ms]
2025-06-28 15:14:01.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:14:06.386 [info] > git config --get commit.template [12ms]
2025-06-28 15:14:06.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:14:06.404 [info] > git status -z -uall [9ms]
2025-06-28 15:14:06.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:11.445 [info] > git config --get commit.template [20ms]
2025-06-28 15:14:11.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-28 15:14:11.504 [info] > git status -z -uall [33ms]
2025-06-28 15:14:11.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:14:16.548 [info] > git config --get commit.template [13ms]
2025-06-28 15:14:16.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:16.577 [info] > git status -z -uall [16ms]
2025-06-28 15:14:16.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:14:21.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:14:21.616 [info] > git config --get commit.template [14ms]
2025-06-28 15:14:21.634 [info] > git status -z -uall [7ms]
2025-06-28 15:14:21.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:26.717 [info] > git config --get commit.template [62ms]
2025-06-28 15:14:26.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-06-28 15:14:26.764 [info] > git status -z -uall [11ms]
2025-06-28 15:14:26.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:31.789 [info] > git config --get commit.template [8ms]
2025-06-28 15:14:31.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:31.806 [info] > git status -z -uall [8ms]
2025-06-28 15:14:31.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:36.828 [info] > git config --get commit.template [1ms]
2025-06-28 15:14:36.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:36.862 [info] > git status -z -uall [9ms]
2025-06-28 15:14:36.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:41.887 [info] > git config --get commit.template [9ms]
2025-06-28 15:14:41.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:41.909 [info] > git status -z -uall [10ms]
2025-06-28 15:14:41.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:46.936 [info] > git config --get commit.template [9ms]
2025-06-28 15:14:46.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:46.956 [info] > git status -z -uall [9ms]
2025-06-28 15:14:46.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:51.992 [info] > git config --get commit.template [12ms]
2025-06-28 15:14:51.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:52.012 [info] > git status -z -uall [10ms]
2025-06-28 15:14:52.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:14:57.042 [info] > git config --get commit.template [10ms]
2025-06-28 15:14:57.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:57.060 [info] > git status -z -uall [8ms]
2025-06-28 15:14:57.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:02.090 [info] > git config --get commit.template [13ms]
2025-06-28 15:15:02.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:02.115 [info] > git status -z -uall [8ms]
2025-06-28 15:15:02.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:07.139 [info] > git config --get commit.template [7ms]
2025-06-28 15:15:07.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:07.156 [info] > git status -z -uall [9ms]
2025-06-28 15:15:07.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:12.189 [info] > git config --get commit.template [13ms]
2025-06-28 15:15:12.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:12.208 [info] > git status -z -uall [9ms]
2025-06-28 15:15:12.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:15:17.284 [info] > git config --get commit.template [53ms]
2025-06-28 15:15:17.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:17.320 [info] > git status -z -uall [13ms]
2025-06-28 15:15:17.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:15:22.350 [info] > git config --get commit.template [11ms]
2025-06-28 15:15:22.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:22.371 [info] > git status -z -uall [10ms]
2025-06-28 15:15:22.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:27.399 [info] > git config --get commit.template [3ms]
2025-06-28 15:15:27.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:27.444 [info] > git status -z -uall [12ms]
2025-06-28 15:15:27.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:15:32.475 [info] > git config --get commit.template [14ms]
2025-06-28 15:15:32.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:32.533 [info] > git status -z -uall [41ms]
2025-06-28 15:15:32.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:15:37.581 [info] > git config --get commit.template [10ms]
2025-06-28 15:15:37.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:37.601 [info] > git status -z -uall [9ms]
2025-06-28 15:15:37.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:42.718 [info] > git config --get commit.template [99ms]
2025-06-28 15:15:42.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:42.757 [info] > git status -z -uall [12ms]
2025-06-28 15:15:42.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:47.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:15:47.806 [info] > git config --get commit.template [20ms]
2025-06-28 15:15:47.839 [info] > git status -z -uall [16ms]
2025-06-28 15:15:47.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:15:52.874 [info] > git config --get commit.template [11ms]
2025-06-28 15:15:52.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:52.895 [info] > git status -z -uall [10ms]
2025-06-28 15:15:52.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:58.092 [info] > git config --get commit.template [25ms]
2025-06-28 15:15:58.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:58.135 [info] > git status -z -uall [21ms]
2025-06-28 15:15:58.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:16:03.170 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:03.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:03.190 [info] > git status -z -uall [10ms]
2025-06-28 15:16:03.191 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:08.218 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:08.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:16:08.293 [info] > git status -z -uall [61ms]
2025-06-28 15:16:08.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-28 15:16:13.341 [info] > git config --get commit.template [13ms]
2025-06-28 15:16:13.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:16:13.405 [info] > git status -z -uall [33ms]
2025-06-28 15:16:13.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:16:28.204 [info] > git config --get commit.template [117ms]
2025-06-28 15:16:28.220 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:28.246 [info] > git status -z -uall [11ms]
2025-06-28 15:16:28.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:33.280 [info] > git config --get commit.template [16ms]
2025-06-28 15:16:33.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:33.304 [info] > git status -z -uall [9ms]
2025-06-28 15:16:33.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:16:43.186 [info] > git config --get commit.template [92ms]
2025-06-28 15:16:43.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:43.218 [info] > git status -z -uall [9ms]
2025-06-28 15:16:43.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-28 15:16:48.262 [info] > git config --get commit.template [12ms]
2025-06-28 15:16:48.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:48.279 [info] > git status -z -uall [8ms]
2025-06-28 15:16:48.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:53.320 [info] > git config --get commit.template [11ms]
2025-06-28 15:16:53.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:53.344 [info] > git status -z -uall [11ms]
2025-06-28 15:16:53.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:16:54.252 [info] > git show --textconv :backend/app/init_simple.py [15ms]
2025-06-28 15:16:54.254 [info] > git ls-files --stage -- backend/app/init_simple.py [2ms]
2025-06-28 15:16:54.267 [info] > git cat-file -s f3b2727bf467d3cfe8a97f082aa2cd528dcf8e66 [4ms]
2025-06-28 15:16:58.371 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:58.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:16:58.400 [info] > git status -z -uall [16ms]
2025-06-28 15:16:58.402 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:03.436 [info] > git config --get commit.template [13ms]
2025-06-28 15:17:03.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:03.456 [info] > git status -z -uall [9ms]
2025-06-28 15:17:03.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:08.487 [info] > git config --get commit.template [10ms]
2025-06-28 15:17:08.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:08.507 [info] > git status -z -uall [9ms]
2025-06-28 15:17:08.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:13.538 [info] > git config --get commit.template [14ms]
2025-06-28 15:17:13.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:13.568 [info] > git status -z -uall [13ms]
2025-06-28 15:17:13.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:18.622 [info] > git config --get commit.template [16ms]
2025-06-28 15:17:18.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:18.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:18.656 [info] > git status -z -uall [20ms]
2025-06-28 15:17:23.686 [info] > git config --get commit.template [12ms]
2025-06-28 15:17:23.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:23.706 [info] > git status -z -uall [11ms]
2025-06-28 15:17:23.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:28.732 [info] > git config --get commit.template [1ms]
2025-06-28 15:17:28.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:28.857 [info] > git status -z -uall [7ms]
2025-06-28 15:17:28.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:33.888 [info] > git config --get commit.template [13ms]
2025-06-28 15:17:33.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:33.907 [info] > git status -z -uall [9ms]
2025-06-28 15:17:33.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
