2025-06-28 15:06:10.999 [info] [main] Log level: Info
2025-06-28 15:06:10.999 [info] [main] Validating found git in: "git"
2025-06-28 15:06:10.999 [info] [main] Using git "2.47.2" from "git"
2025-06-28 15:06:10.999 [info] [Model][doInitialScan] Initial repository scan started
2025-06-28 15:06:10.999 [info] > git rev-parse --show-toplevel [9ms]
2025-06-28 15:06:10.999 [info] > git rev-parse --git-dir --git-common-dir [16ms]
2025-06-28 15:06:10.999 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-28 15:06:10.999 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-28 15:06:11.015 [info] > git config --get commit.template [9ms]
2025-06-28 15:06:11.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-06-28 15:06:11.063 [info] > git rev-parse --show-toplevel [36ms]
2025-06-28 15:06:11.430 [info] > git status -z -uall [20ms]
2025-06-28 15:06:11.450 [info] > git rev-parse --show-toplevel [153ms]
2025-06-28 15:06:11.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [195ms]
2025-06-28 15:06:12.297 [info] > git check-ignore -v -z --stdin [131ms]
2025-06-28 15:06:12.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [669ms]
2025-06-28 15:06:12.316 [info] > git rev-parse --show-toplevel [859ms]
2025-06-28 15:06:12.537 [info] > git config --get --local branch.main.vscode-merge-base [222ms]
2025-06-28 15:06:12.538 [warning] [Git][config] git config failed: Failed to execute git
2025-06-28 15:06:12.547 [info] > git config --get commit.template [344ms]
2025-06-28 15:06:12.577 [info] > git rev-parse --show-toplevel [49ms]
2025-06-28 15:06:12.605 [info] > git reflog main --grep-reflog=branch: Created from *. [58ms]
2025-06-28 15:06:12.623 [info] > git rev-parse --show-toplevel [24ms]
2025-06-28 15:06:12.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [33ms]
2025-06-28 15:06:12.696 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [75ms]
2025-06-28 15:06:12.770 [info] > git rev-parse --show-toplevel [78ms]
2025-06-28 15:06:12.782 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [75ms]
2025-06-28 15:06:12.807 [info] > git rev-parse --show-toplevel [29ms]
2025-06-28 15:06:12.824 [info] > git status -z -uall [7ms]
2025-06-28 15:06:12.844 [info] > git config --add --local branch.main.vscode-merge-base origin/main [50ms]
2025-06-28 15:06:12.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms] (cancelled)
2025-06-28 15:06:12.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [66ms]
2025-06-28 15:06:13.270 [info] > git config --get --local branch.main.vscode-merge-base [346ms]
2025-06-28 15:06:13.278 [info] > git config --get commit.template [366ms]
2025-06-28 15:06:13.278 [info] > git rev-parse --show-toplevel [374ms]
2025-06-28 15:06:13.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [42ms]
2025-06-28 15:06:13.335 [info] > git rev-parse --show-toplevel [17ms]
2025-06-28 15:06:13.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-28 15:06:13.356 [info] > git merge-base refs/heads/main refs/remotes/origin/main [31ms]
2025-06-28 15:06:13.365 [info] > git rev-parse --show-toplevel [15ms]
2025-06-28 15:06:13.583 [info] > git diff --name-status -z --diff-filter=ADMR 5b9666563172e5a82d1e4f9b1395393f7c77e87c...refs/remotes/origin/main [220ms]
2025-06-28 15:06:13.810 [info] > git rev-parse --show-toplevel [441ms]
2025-06-28 15:06:13.812 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-28 15:06:13.928 [info] > git status -z -uall [136ms]
2025-06-28 15:06:13.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [136ms]
2025-06-28 15:06:21.560 [info] > git config --get commit.template [1ms]
2025-06-28 15:06:21.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:06:21.589 [info] > git status -z -uall [8ms]
2025-06-28 15:06:21.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:27.174 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:06:27.855 [info] > git config --get commit.template [5ms]
2025-06-28 15:06:27.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:27.865 [info] > git status -z -uall [5ms]
2025-06-28 15:06:27.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:06:30.673 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:06:33.985 [info] > git config --get commit.template [5ms]
2025-06-28 15:06:33.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:06:34.012 [info] > git status -z -uall [12ms]
2025-06-28 15:06:34.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:06:39.032 [info] > git config --get commit.template [6ms]
2025-06-28 15:06:39.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:39.051 [info] > git status -z -uall [6ms]
2025-06-28 15:06:39.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:06:44.978 [info] > git config --get commit.template [10ms]
2025-06-28 15:06:44.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:44.994 [info] > git status -z -uall [7ms]
2025-06-28 15:06:44.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:50.019 [info] > git config --get commit.template [1ms]
2025-06-28 15:06:50.032 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:06:50.050 [info] > git status -z -uall [8ms]
2025-06-28 15:06:50.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:06:55.094 [info] > git config --get commit.template [7ms]
2025-06-28 15:06:55.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:06:55.116 [info] > git status -z -uall [10ms]
2025-06-28 15:06:55.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:07:00.143 [info] > git config --get commit.template [7ms]
2025-06-28 15:07:00.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:00.159 [info] > git status -z -uall [7ms]
2025-06-28 15:07:00.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:05.184 [info] > git config --get commit.template [1ms]
2025-06-28 15:07:05.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:05.290 [info] > git status -z -uall [84ms]
2025-06-28 15:07:05.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [73ms]
2025-06-28 15:07:10.321 [info] > git config --get commit.template [10ms]
2025-06-28 15:07:10.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:10.371 [info] > git status -z -uall [23ms]
2025-06-28 15:07:10.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:07:15.397 [info] > git config --get commit.template [8ms]
2025-06-28 15:07:15.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:15.411 [info] > git status -z -uall [6ms]
2025-06-28 15:07:15.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:20.442 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:20.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:20.462 [info] > git status -z -uall [10ms]
2025-06-28 15:07:20.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:25.489 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:25.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:25.510 [info] > git status -z -uall [12ms]
2025-06-28 15:07:25.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:07:30.537 [info] > git config --get commit.template [10ms]
2025-06-28 15:07:30.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:30.556 [info] > git status -z -uall [9ms]
2025-06-28 15:07:30.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:35.580 [info] > git config --get commit.template [8ms]
2025-06-28 15:07:35.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:35.598 [info] > git status -z -uall [8ms]
2025-06-28 15:07:35.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:40.625 [info] > git config --get commit.template [9ms]
2025-06-28 15:07:40.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:07:40.653 [info] > git status -z -uall [13ms]
2025-06-28 15:07:40.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:07:45.701 [info] > git config --get commit.template [19ms]
2025-06-28 15:07:45.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:45.751 [info] > git status -z -uall [29ms]
2025-06-28 15:07:45.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:07:50.801 [info] > git config --get commit.template [19ms]
2025-06-28 15:07:50.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:07:50.832 [info] > git status -z -uall [13ms]
2025-06-28 15:07:50.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:07:55.874 [info] > git config --get commit.template [18ms]
2025-06-28 15:07:55.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:07:55.903 [info] > git status -z -uall [14ms]
2025-06-28 15:07:55.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:00.937 [info] > git config --get commit.template [13ms]
2025-06-28 15:08:00.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:00.957 [info] > git status -z -uall [8ms]
2025-06-28 15:08:00.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:05.989 [info] > git config --get commit.template [11ms]
2025-06-28 15:08:05.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:06.012 [info] > git status -z -uall [9ms]
2025-06-28 15:08:06.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:11.043 [info] > git config --get commit.template [10ms]
2025-06-28 15:08:11.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:11.070 [info] > git status -z -uall [14ms]
2025-06-28 15:08:11.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:08:16.097 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:16.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:08:16.115 [info] > git status -z -uall [9ms]
2025-06-28 15:08:16.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:21.146 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:21.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:21.165 [info] > git status -z -uall [7ms]
2025-06-28 15:08:21.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:26.251 [info] > git config --get commit.template [66ms]
2025-06-28 15:08:26.284 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-06-28 15:08:26.312 [info] > git status -z -uall [15ms]
2025-06-28 15:08:26.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:08:31.357 [info] > git config --get commit.template [14ms]
2025-06-28 15:08:31.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:08:31.398 [info] > git status -z -uall [21ms]
2025-06-28 15:08:31.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [142ms]
2025-06-28 15:08:36.562 [info] > git config --get commit.template [9ms]
2025-06-28 15:08:36.565 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:08:36.587 [info] > git status -z -uall [11ms]
2025-06-28 15:08:36.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:08:41.619 [info] > git config --get commit.template [14ms]
2025-06-28 15:08:41.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:08:41.718 [info] > git status -z -uall [89ms]
2025-06-28 15:08:41.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [82ms]
2025-06-28 15:08:46.768 [info] > git config --get commit.template [19ms]
2025-06-28 15:08:46.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:08:46.804 [info] > git status -z -uall [13ms]
2025-06-28 15:08:46.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:08:51.864 [info] > git config --get commit.template [3ms]
2025-06-28 15:08:51.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:08:51.910 [info] > git status -z -uall [14ms]
2025-06-28 15:08:51.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:08:56.937 [info] > git config --get commit.template [8ms]
2025-06-28 15:08:56.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:08:56.956 [info] > git status -z -uall [10ms]
2025-06-28 15:08:56.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:01.982 [info] > git config --get commit.template [1ms]
2025-06-28 15:09:01.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:09:02.023 [info] > git status -z -uall [14ms]
2025-06-28 15:09:02.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:07.051 [info] > git config --get commit.template [7ms]
2025-06-28 15:09:07.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:07.071 [info] > git status -z -uall [8ms]
2025-06-28 15:09:07.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:12.113 [info] > git config --get commit.template [18ms]
2025-06-28 15:09:12.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:09:12.157 [info] > git status -z -uall [21ms]
2025-06-28 15:09:12.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:17.252 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:17.253 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:17.280 [info] > git status -z -uall [15ms]
2025-06-28 15:09:17.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:22.318 [info] > git config --get commit.template [14ms]
2025-06-28 15:09:22.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:09:22.357 [info] > git status -z -uall [21ms]
2025-06-28 15:09:22.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:09:27.397 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:27.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:27.422 [info] > git status -z -uall [11ms]
2025-06-28 15:09:27.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:32.454 [info] > git config --get commit.template [11ms]
2025-06-28 15:09:32.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:09:32.479 [info] > git status -z -uall [12ms]
2025-06-28 15:09:32.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:09:37.516 [info] > git config --get commit.template [15ms]
2025-06-28 15:09:37.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:37.536 [info] > git status -z -uall [10ms]
2025-06-28 15:09:37.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:09:42.569 [info] > git config --get commit.template [12ms]
2025-06-28 15:09:42.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:42.601 [info] > git status -z -uall [20ms]
2025-06-28 15:09:42.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:47.627 [info] > git config --get commit.template [4ms]
2025-06-28 15:09:47.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:09:47.682 [info] > git status -z -uall [14ms]
2025-06-28 15:09:47.684 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:09:52.719 [info] > git config --get commit.template [14ms]
2025-06-28 15:09:52.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:09:52.748 [info] > git status -z -uall [12ms]
2025-06-28 15:09:52.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:09:57.779 [info] > git config --get commit.template [11ms]
2025-06-28 15:09:57.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:09:57.799 [info] > git status -z -uall [10ms]
2025-06-28 15:09:57.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:02.830 [info] > git config --get commit.template [11ms]
2025-06-28 15:10:02.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:02.858 [info] > git status -z -uall [14ms]
2025-06-28 15:10:02.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:10:07.887 [info] > git config --get commit.template [8ms]
2025-06-28 15:10:07.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:07.915 [info] > git status -z -uall [14ms]
2025-06-28 15:10:07.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:10:12.953 [info] > git config --get commit.template [14ms]
2025-06-28 15:10:12.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:12.986 [info] > git status -z -uall [15ms]
2025-06-28 15:10:12.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:18.016 [info] > git config --get commit.template [11ms]
2025-06-28 15:10:18.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:18.037 [info] > git status -z -uall [10ms]
2025-06-28 15:10:18.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:23.065 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:23.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:23.089 [info] > git status -z -uall [11ms]
2025-06-28 15:10:23.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:10:28.119 [info] > git config --get commit.template [10ms]
2025-06-28 15:10:28.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:28.138 [info] > git status -z -uall [9ms]
2025-06-28 15:10:28.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:10:33.166 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:33.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:33.187 [info] > git status -z -uall [10ms]
2025-06-28 15:10:33.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:38.247 [info] > git config --get commit.template [9ms]
2025-06-28 15:10:38.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:38.275 [info] > git status -z -uall [13ms]
2025-06-28 15:10:38.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:10:43.930 [info] > git config --get commit.template [12ms]
2025-06-28 15:10:43.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:43.950 [info] > git status -z -uall [9ms]
2025-06-28 15:10:43.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:48.979 [info] > git config --get commit.template [10ms]
2025-06-28 15:10:48.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:10:48.999 [info] > git status -z -uall [7ms]
2025-06-28 15:10:49.000 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:54.032 [info] > git config --get commit.template [12ms]
2025-06-28 15:10:54.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:10:54.052 [info] > git status -z -uall [9ms]
2025-06-28 15:10:54.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:10:59.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-28 15:10:59.105 [info] > git config --get commit.template [33ms]
2025-06-28 15:10:59.129 [info] > git status -z -uall [11ms]
2025-06-28 15:10:59.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:11:04.157 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:04.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:04.180 [info] > git status -z -uall [11ms]
2025-06-28 15:11:04.181 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:09.210 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:09.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:09.233 [info] > git status -z -uall [11ms]
2025-06-28 15:11:09.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:14.280 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:14.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:14.309 [info] > git status -z -uall [15ms]
2025-06-28 15:11:14.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:11:19.340 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:19.341 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:19.359 [info] > git status -z -uall [8ms]
2025-06-28 15:11:19.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:11:24.388 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:24.390 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:24.410 [info] > git status -z -uall [9ms]
2025-06-28 15:11:24.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:29.443 [info] > git config --get commit.template [12ms]
2025-06-28 15:11:29.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:29.469 [info] > git status -z -uall [13ms]
2025-06-28 15:11:29.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:34.501 [info] > git config --get commit.template [13ms]
2025-06-28 15:11:34.502 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:34.522 [info] > git status -z -uall [9ms]
2025-06-28 15:11:34.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:39.557 [info] > git config --get commit.template [13ms]
2025-06-28 15:11:39.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:11:39.586 [info] > git status -z -uall [14ms]
2025-06-28 15:11:39.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:44.614 [info] > git config --get commit.template [9ms]
2025-06-28 15:11:44.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:44.631 [info] > git status -z -uall [8ms]
2025-06-28 15:11:44.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:11:49.665 [info] > git config --get commit.template [10ms]
2025-06-28 15:11:49.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:11:49.687 [info] > git status -z -uall [11ms]
2025-06-28 15:11:49.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:54.724 [info] > git config --get commit.template [15ms]
2025-06-28 15:11:54.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:54.747 [info] > git status -z -uall [9ms]
2025-06-28 15:11:54.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:11:59.775 [info] > git config --get commit.template [1ms]
2025-06-28 15:11:59.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:11:59.816 [info] > git status -z -uall [15ms]
2025-06-28 15:11:59.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:04.846 [info] > git config --get commit.template [4ms]
2025-06-28 15:12:04.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:04.886 [info] > git status -z -uall [11ms]
2025-06-28 15:12:04.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:09.905 [info] > git config --get commit.template [2ms]
2025-06-28 15:12:09.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:09.941 [info] > git status -z -uall [10ms]
2025-06-28 15:12:09.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:12:14.968 [info] > git config --get commit.template [2ms]
2025-06-28 15:12:14.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:15.007 [info] > git status -z -uall [9ms]
2025-06-28 15:12:15.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:20.037 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:20.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:20.052 [info] > git status -z -uall [7ms]
2025-06-28 15:12:20.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:25.082 [info] > git config --get commit.template [12ms]
2025-06-28 15:12:25.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:25.100 [info] > git status -z -uall [10ms]
2025-06-28 15:12:25.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:30.129 [info] > git config --get commit.template [8ms]
2025-06-28 15:12:30.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:12:30.150 [info] > git status -z -uall [12ms]
2025-06-28 15:12:30.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:35.178 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:35.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:35.259 [info] > git status -z -uall [66ms]
2025-06-28 15:12:35.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-28 15:12:40.291 [info] > git config --get commit.template [10ms]
2025-06-28 15:12:40.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:40.311 [info] > git status -z -uall [8ms]
2025-06-28 15:12:40.312 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:12:45.342 [info] > git config --get commit.template [12ms]
2025-06-28 15:12:45.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:12:45.367 [info] > git status -z -uall [11ms]
2025-06-28 15:12:45.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:12:50.431 [info] > git config --get commit.template [26ms]
2025-06-28 15:12:50.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:12:50.501 [info] > git status -z -uall [30ms]
2025-06-28 15:12:50.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:12:55.539 [info] > git config --get commit.template [16ms]
2025-06-28 15:12:55.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:12:55.570 [info] > git status -z -uall [16ms]
2025-06-28 15:12:55.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:00.602 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:00.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:00.625 [info] > git status -z -uall [13ms]
2025-06-28 15:13:00.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:05.656 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:05.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:05.672 [info] > git status -z -uall [7ms]
2025-06-28 15:13:05.673 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:13:10.703 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:10.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:10.721 [info] > git status -z -uall [7ms]
2025-06-28 15:13:10.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:15.749 [info] > git config --get commit.template [9ms]
2025-06-28 15:13:15.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:15.768 [info] > git status -z -uall [10ms]
2025-06-28 15:13:15.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:20.794 [info] > git config --get commit.template [8ms]
2025-06-28 15:13:20.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:20.812 [info] > git status -z -uall [7ms]
2025-06-28 15:13:20.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:25.839 [info] > git config --get commit.template [10ms]
2025-06-28 15:13:25.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:25.857 [info] > git status -z -uall [7ms]
2025-06-28 15:13:25.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:30.905 [info] > git config --get commit.template [21ms]
2025-06-28 15:13:30.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:30.947 [info] > git status -z -uall [24ms]
2025-06-28 15:13:30.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:36.006 [info] > git config --get commit.template [32ms]
2025-06-28 15:13:36.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:13:36.050 [info] > git status -z -uall [21ms]
2025-06-28 15:13:36.050 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:41.082 [info] > git config --get commit.template [15ms]
2025-06-28 15:13:41.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:13:41.129 [info] > git status -z -uall [23ms]
2025-06-28 15:13:41.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:13:46.171 [info] > git config --get commit.template [20ms]
2025-06-28 15:13:46.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:46.194 [info] > git status -z -uall [9ms]
2025-06-28 15:13:46.194 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:13:51.223 [info] > git config --get commit.template [9ms]
2025-06-28 15:13:51.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:13:51.243 [info] > git status -z -uall [9ms]
2025-06-28 15:13:51.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:13:56.279 [info] > git config --get commit.template [12ms]
2025-06-28 15:13:56.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:13:56.306 [info] > git status -z -uall [9ms]
2025-06-28 15:13:56.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:01.335 [info] > git config --get commit.template [10ms]
2025-06-28 15:14:01.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:01.354 [info] > git status -z -uall [10ms]
2025-06-28 15:14:01.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:14:06.386 [info] > git config --get commit.template [12ms]
2025-06-28 15:14:06.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:14:06.404 [info] > git status -z -uall [9ms]
2025-06-28 15:14:06.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:11.445 [info] > git config --get commit.template [20ms]
2025-06-28 15:14:11.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-28 15:14:11.504 [info] > git status -z -uall [33ms]
2025-06-28 15:14:11.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:14:16.548 [info] > git config --get commit.template [13ms]
2025-06-28 15:14:16.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:16.577 [info] > git status -z -uall [16ms]
2025-06-28 15:14:16.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:14:21.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:14:21.616 [info] > git config --get commit.template [14ms]
2025-06-28 15:14:21.634 [info] > git status -z -uall [7ms]
2025-06-28 15:14:21.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:26.717 [info] > git config --get commit.template [62ms]
2025-06-28 15:14:26.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-06-28 15:14:26.764 [info] > git status -z -uall [11ms]
2025-06-28 15:14:26.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:14:31.789 [info] > git config --get commit.template [8ms]
2025-06-28 15:14:31.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:31.806 [info] > git status -z -uall [8ms]
2025-06-28 15:14:31.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:36.828 [info] > git config --get commit.template [1ms]
2025-06-28 15:14:36.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:36.862 [info] > git status -z -uall [9ms]
2025-06-28 15:14:36.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:41.887 [info] > git config --get commit.template [9ms]
2025-06-28 15:14:41.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:41.909 [info] > git status -z -uall [10ms]
2025-06-28 15:14:41.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:46.936 [info] > git config --get commit.template [9ms]
2025-06-28 15:14:46.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:46.956 [info] > git status -z -uall [9ms]
2025-06-28 15:14:46.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:14:51.992 [info] > git config --get commit.template [12ms]
2025-06-28 15:14:51.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:52.012 [info] > git status -z -uall [10ms]
2025-06-28 15:14:52.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:14:57.042 [info] > git config --get commit.template [10ms]
2025-06-28 15:14:57.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:14:57.060 [info] > git status -z -uall [8ms]
2025-06-28 15:14:57.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:02.090 [info] > git config --get commit.template [13ms]
2025-06-28 15:15:02.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:02.115 [info] > git status -z -uall [8ms]
2025-06-28 15:15:02.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:07.139 [info] > git config --get commit.template [7ms]
2025-06-28 15:15:07.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:07.156 [info] > git status -z -uall [9ms]
2025-06-28 15:15:07.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:12.189 [info] > git config --get commit.template [13ms]
2025-06-28 15:15:12.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:12.208 [info] > git status -z -uall [9ms]
2025-06-28 15:15:12.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:15:17.284 [info] > git config --get commit.template [53ms]
2025-06-28 15:15:17.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:17.320 [info] > git status -z -uall [13ms]
2025-06-28 15:15:17.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:15:22.350 [info] > git config --get commit.template [11ms]
2025-06-28 15:15:22.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:22.371 [info] > git status -z -uall [10ms]
2025-06-28 15:15:22.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:27.399 [info] > git config --get commit.template [3ms]
2025-06-28 15:15:27.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:27.444 [info] > git status -z -uall [12ms]
2025-06-28 15:15:27.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:15:32.475 [info] > git config --get commit.template [14ms]
2025-06-28 15:15:32.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:32.533 [info] > git status -z -uall [41ms]
2025-06-28 15:15:32.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:15:37.581 [info] > git config --get commit.template [10ms]
2025-06-28 15:15:37.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:15:37.601 [info] > git status -z -uall [9ms]
2025-06-28 15:15:37.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:42.718 [info] > git config --get commit.template [99ms]
2025-06-28 15:15:42.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:42.757 [info] > git status -z -uall [12ms]
2025-06-28 15:15:42.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:47.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:15:47.806 [info] > git config --get commit.template [20ms]
2025-06-28 15:15:47.839 [info] > git status -z -uall [16ms]
2025-06-28 15:15:47.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:15:52.874 [info] > git config --get commit.template [11ms]
2025-06-28 15:15:52.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:15:52.895 [info] > git status -z -uall [10ms]
2025-06-28 15:15:52.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:15:58.092 [info] > git config --get commit.template [25ms]
2025-06-28 15:15:58.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:15:58.135 [info] > git status -z -uall [21ms]
2025-06-28 15:15:58.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:16:03.170 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:03.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:03.190 [info] > git status -z -uall [10ms]
2025-06-28 15:16:03.191 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:08.218 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:08.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:16:08.293 [info] > git status -z -uall [61ms]
2025-06-28 15:16:08.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-28 15:16:13.341 [info] > git config --get commit.template [13ms]
2025-06-28 15:16:13.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:16:13.405 [info] > git status -z -uall [33ms]
2025-06-28 15:16:13.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:16:28.204 [info] > git config --get commit.template [117ms]
2025-06-28 15:16:28.220 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:28.246 [info] > git status -z -uall [11ms]
2025-06-28 15:16:28.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:33.280 [info] > git config --get commit.template [16ms]
2025-06-28 15:16:33.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:33.304 [info] > git status -z -uall [9ms]
2025-06-28 15:16:33.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:16:43.186 [info] > git config --get commit.template [92ms]
2025-06-28 15:16:43.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:43.218 [info] > git status -z -uall [9ms]
2025-06-28 15:16:43.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-28 15:16:48.262 [info] > git config --get commit.template [12ms]
2025-06-28 15:16:48.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:48.279 [info] > git status -z -uall [8ms]
2025-06-28 15:16:48.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:16:53.320 [info] > git config --get commit.template [11ms]
2025-06-28 15:16:53.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:16:53.344 [info] > git status -z -uall [11ms]
2025-06-28 15:16:53.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:16:54.252 [info] > git show --textconv :backend/app/init_simple.py [15ms]
2025-06-28 15:16:54.254 [info] > git ls-files --stage -- backend/app/init_simple.py [2ms]
2025-06-28 15:16:54.267 [info] > git cat-file -s f3b2727bf467d3cfe8a97f082aa2cd528dcf8e66 [4ms]
2025-06-28 15:16:58.371 [info] > git config --get commit.template [10ms]
2025-06-28 15:16:58.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:16:58.400 [info] > git status -z -uall [16ms]
2025-06-28 15:16:58.402 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:03.436 [info] > git config --get commit.template [13ms]
2025-06-28 15:17:03.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:03.456 [info] > git status -z -uall [9ms]
2025-06-28 15:17:03.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:08.487 [info] > git config --get commit.template [10ms]
2025-06-28 15:17:08.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:08.507 [info] > git status -z -uall [9ms]
2025-06-28 15:17:08.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:13.538 [info] > git config --get commit.template [14ms]
2025-06-28 15:17:13.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:13.568 [info] > git status -z -uall [13ms]
2025-06-28 15:17:13.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:18.622 [info] > git config --get commit.template [16ms]
2025-06-28 15:17:18.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:18.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:18.656 [info] > git status -z -uall [20ms]
2025-06-28 15:17:23.686 [info] > git config --get commit.template [12ms]
2025-06-28 15:17:23.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:23.706 [info] > git status -z -uall [11ms]
2025-06-28 15:17:23.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:28.732 [info] > git config --get commit.template [1ms]
2025-06-28 15:17:28.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:28.857 [info] > git status -z -uall [7ms]
2025-06-28 15:17:28.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:33.888 [info] > git config --get commit.template [13ms]
2025-06-28 15:17:33.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:33.907 [info] > git status -z -uall [9ms]
2025-06-28 15:17:33.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:38.939 [info] > git config --get commit.template [13ms]
2025-06-28 15:17:38.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:38.970 [info] > git status -z -uall [14ms]
2025-06-28 15:17:38.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:17:44.070 [info] > git config --get commit.template [12ms]
2025-06-28 15:17:44.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:17:44.092 [info] > git status -z -uall [9ms]
2025-06-28 15:17:44.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:46.467 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-28 15:17:49.118 [info] > git config --get commit.template [2ms]
2025-06-28 15:17:49.135 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:49.162 [info] > git status -z -uall [15ms]
2025-06-28 15:17:49.164 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:17:49.215 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:17:50.183 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:17:54.238 [info] > git config --get commit.template [56ms]
2025-06-28 15:17:54.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:17:54.274 [info] > git status -z -uall [12ms]
2025-06-28 15:17:54.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:17:58.009 [info] > git show --textconv :backend/app/core/database.py [12ms]
2025-06-28 15:17:58.010 [info] > git ls-files --stage -- backend/app/core/database.py [2ms]
2025-06-28 15:17:58.021 [info] > git cat-file -s 00392dac69d71a775998a0f50f1f9d5a143263bf [1ms]
2025-06-28 15:18:04.981 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:18:07.125 [info] > git config --get commit.template [3ms]
2025-06-28 15:18:07.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:07.172 [info] > git status -z -uall [20ms]
2025-06-28 15:18:07.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:18:12.209 [info] > git config --get commit.template [11ms]
2025-06-28 15:18:12.210 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:12.244 [info] > git status -z -uall [19ms]
2025-06-28 15:18:12.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:18:17.283 [info] > git config --get commit.template [17ms]
2025-06-28 15:18:17.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:17.318 [info] > git status -z -uall [14ms]
2025-06-28 15:18:17.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:18:22.351 [info] > git config --get commit.template [12ms]
2025-06-28 15:18:22.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:22.380 [info] > git status -z -uall [11ms]
2025-06-28 15:18:22.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:18:27.434 [info] > git config --get commit.template [21ms]
2025-06-28 15:18:27.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:27.481 [info] > git status -z -uall [19ms]
2025-06-28 15:18:27.482 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:18:32.514 [info] > git config --get commit.template [9ms]
2025-06-28 15:18:32.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:32.538 [info] > git status -z -uall [13ms]
2025-06-28 15:18:32.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:18:37.571 [info] > git config --get commit.template [14ms]
2025-06-28 15:18:37.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:37.596 [info] > git status -z -uall [13ms]
2025-06-28 15:18:37.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:18:42.625 [info] > git config --get commit.template [11ms]
2025-06-28 15:18:42.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:18:42.650 [info] > git status -z -uall [11ms]
2025-06-28 15:18:42.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:18:47.679 [info] > git config --get commit.template [10ms]
2025-06-28 15:18:47.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:47.699 [info] > git status -z -uall [10ms]
2025-06-28 15:18:47.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:18:52.735 [info] > git config --get commit.template [15ms]
2025-06-28 15:18:52.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:52.757 [info] > git status -z -uall [9ms]
2025-06-28 15:18:52.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:18:57.788 [info] > git config --get commit.template [11ms]
2025-06-28 15:18:57.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:18:57.807 [info] > git status -z -uall [8ms]
2025-06-28 15:18:57.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:19:00.401 [info] > git blame --root --incremental 5b9666563172e5a82d1e4f9b1395393f7c77e87c -- backend/app/core/database.py [13ms]
2025-06-28 15:19:02.860 [info] > git config --get commit.template [3ms]
2025-06-28 15:19:02.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:19:02.906 [info] > git status -z -uall [18ms]
2025-06-28 15:19:02.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:19:07.938 [info] > git config --get commit.template [11ms]
2025-06-28 15:19:07.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:19:07.962 [info] > git status -z -uall [10ms]
2025-06-28 15:19:07.963 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:19:12.992 [info] > git config --get commit.template [12ms]
2025-06-28 15:19:12.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:19:13.010 [info] > git status -z -uall [8ms]
2025-06-28 15:19:13.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:19:18.037 [info] > git config --get commit.template [10ms]
2025-06-28 15:19:18.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:19:18.060 [info] > git status -z -uall [10ms]
2025-06-28 15:19:18.062 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:20:10.336 [info] > git config --get commit.template [3ms]
2025-06-28 15:20:10.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-28 15:20:10.399 [info] > git status -z -uall [17ms]
2025-06-28 15:20:10.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:20:15.433 [info] > git config --get commit.template [9ms]
2025-06-28 15:20:15.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:20:15.453 [info] > git status -z -uall [8ms]
2025-06-28 15:20:15.455 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:22:25.771 [info] > git config --get commit.template [7ms]
2025-06-28 15:22:25.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:22:25.822 [info] > git status -z -uall [17ms]
2025-06-28 15:22:25.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:22:26.566 [info] > git show --textconv :backend/.env [11ms]
2025-06-28 15:22:26.576 [info] > git ls-files --stage -- backend/.env [11ms]
2025-06-28 15:22:26.584 [info] > git hash-object -t tree /dev/null [8ms]
2025-06-28 15:22:26.585 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:22:26.586 [info] > git hash-object -t tree /dev/null [2ms]
2025-06-28 15:22:26.586 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:22:30.852 [info] > git config --get commit.template [10ms]
2025-06-28 15:22:30.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:22:30.870 [info] > git status -z -uall [8ms]
2025-06-28 15:22:30.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:22:35.897 [info] > git config --get commit.template [7ms]
2025-06-28 15:22:35.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:22:35.918 [info] > git status -z -uall [8ms]
2025-06-28 15:22:35.919 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:22:40.952 [info] > git config --get commit.template [13ms]
2025-06-28 15:22:40.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:22:40.986 [info] > git status -z -uall [15ms]
2025-06-28 15:22:40.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:22:46.033 [info] > git config --get commit.template [23ms]
2025-06-28 15:22:46.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:22:46.083 [info] > git status -z -uall [26ms]
2025-06-28 15:22:46.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:22:51.119 [info] > git config --get commit.template [13ms]
2025-06-28 15:22:51.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:22:51.148 [info] > git status -z -uall [16ms]
2025-06-28 15:22:51.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:22:56.194 [info] > git config --get commit.template [18ms]
2025-06-28 15:22:56.195 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:22:56.225 [info] > git status -z -uall [15ms]
2025-06-28 15:22:56.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:23:01.262 [info] > git config --get commit.template [13ms]
2025-06-28 15:23:01.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:23:01.358 [info] > git status -z -uall [80ms]
2025-06-28 15:23:01.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [68ms]
2025-06-28 15:23:06.396 [info] > git config --get commit.template [15ms]
2025-06-28 15:23:06.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-28 15:23:06.442 [info] > git status -z -uall [19ms]
2025-06-28 15:23:06.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:23:11.481 [info] > git config --get commit.template [12ms]
2025-06-28 15:23:11.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:23:11.515 [info] > git status -z -uall [15ms]
2025-06-28 15:23:11.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:23:16.548 [info] > git config --get commit.template [13ms]
2025-06-28 15:23:16.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:23:16.570 [info] > git status -z -uall [9ms]
2025-06-28 15:23:16.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:23:21.600 [info] > git config --get commit.template [8ms]
2025-06-28 15:23:21.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:21.620 [info] > git status -z -uall [10ms]
2025-06-28 15:23:21.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:23:26.654 [info] > git config --get commit.template [11ms]
2025-06-28 15:23:26.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:26.679 [info] > git status -z -uall [10ms]
2025-06-28 15:23:26.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:23:31.792 [info] > git config --get commit.template [91ms]
2025-06-28 15:23:31.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:23:31.828 [info] > git status -z -uall [10ms]
2025-06-28 15:23:31.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:23:36.860 [info] > git config --get commit.template [13ms]
2025-06-28 15:23:36.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:36.880 [info] > git status -z -uall [10ms]
2025-06-28 15:23:36.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:23:41.916 [info] > git config --get commit.template [14ms]
2025-06-28 15:23:41.916 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:41.939 [info] > git status -z -uall [12ms]
2025-06-28 15:23:41.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:23:46.967 [info] > git config --get commit.template [10ms]
2025-06-28 15:23:46.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:46.989 [info] > git status -z -uall [13ms]
2025-06-28 15:23:46.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:23:49.156 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:23:52.027 [info] > git config --get commit.template [15ms]
2025-06-28 15:23:52.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:23:52.056 [info] > git status -z -uall [18ms]
2025-06-28 15:23:52.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:23:57.197 [info] > git config --get commit.template [20ms]
2025-06-28 15:23:57.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-28 15:23:57.222 [info] > git status -z -uall [10ms]
2025-06-28 15:23:57.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:24:02.342 [info] > git config --get commit.template [2ms]
2025-06-28 15:24:02.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:24:02.392 [info] > git status -z -uall [18ms]
2025-06-28 15:24:02.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:24:07.424 [info] > git config --get commit.template [9ms]
2025-06-28 15:24:07.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:07.442 [info] > git status -z -uall [8ms]
2025-06-28 15:24:07.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:24:12.476 [info] > git config --get commit.template [15ms]
2025-06-28 15:24:12.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:24:12.504 [info] > git status -z -uall [12ms]
2025-06-28 15:24:12.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:24:17.538 [info] > git config --get commit.template [13ms]
2025-06-28 15:24:17.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:17.648 [info] > git status -z -uall [93ms]
2025-06-28 15:24:17.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [81ms]
2025-06-28 15:24:22.675 [info] > git config --get commit.template [8ms]
2025-06-28 15:24:22.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:24:22.699 [info] > git status -z -uall [16ms]
2025-06-28 15:24:22.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:24:24.559 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:24:27.731 [info] > git config --get commit.template [12ms]
2025-06-28 15:24:27.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:27.758 [info] > git status -z -uall [11ms]
2025-06-28 15:24:27.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:24:32.851 [info] > git config --get commit.template [68ms]
2025-06-28 15:24:32.867 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:32.902 [info] > git status -z -uall [18ms]
2025-06-28 15:24:32.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:24:37.935 [info] > git config --get commit.template [15ms]
2025-06-28 15:24:37.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:24:37.967 [info] > git status -z -uall [14ms]
2025-06-28 15:24:37.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:24:43.010 [info] > git config --get commit.template [24ms]
2025-06-28 15:24:43.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:24:43.041 [info] > git status -z -uall [14ms]
2025-06-28 15:24:43.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:24:48.088 [info] > git config --get commit.template [14ms]
2025-06-28 15:24:48.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:48.117 [info] > git status -z -uall [14ms]
2025-06-28 15:24:48.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:24:53.142 [info] > git config --get commit.template [7ms]
2025-06-28 15:24:53.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:24:53.168 [info] > git status -z -uall [14ms]
2025-06-28 15:24:53.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:24:58.207 [info] > git config --get commit.template [23ms]
2025-06-28 15:24:58.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:24:58.245 [info] > git status -z -uall [16ms]
2025-06-28 15:24:58.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:03.278 [info] > git config --get commit.template [13ms]
2025-06-28 15:25:03.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:25:03.307 [info] > git status -z -uall [15ms]
2025-06-28 15:25:03.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:25:08.342 [info] > git config --get commit.template [14ms]
2025-06-28 15:25:08.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:25:08.362 [info] > git status -z -uall [9ms]
2025-06-28 15:25:08.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:25:13.391 [info] > git config --get commit.template [9ms]
2025-06-28 15:25:13.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:25:13.428 [info] > git status -z -uall [19ms]
2025-06-28 15:25:13.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:18.461 [info] > git config --get commit.template [12ms]
2025-06-28 15:25:18.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:25:18.490 [info] > git status -z -uall [11ms]
2025-06-28 15:25:18.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:23.530 [info] > git config --get commit.template [9ms]
2025-06-28 15:25:23.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:25:23.545 [info] > git status -z -uall [8ms]
2025-06-28 15:25:23.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:25:28.577 [info] > git config --get commit.template [11ms]
2025-06-28 15:25:28.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:25:28.602 [info] > git status -z -uall [12ms]
2025-06-28 15:25:28.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:33.638 [info] > git config --get commit.template [14ms]
2025-06-28 15:25:33.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:25:33.653 [info] > git status -z -uall [7ms]
2025-06-28 15:25:33.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:38.690 [info] > git config --get commit.template [2ms]
2025-06-28 15:25:38.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:25:38.757 [info] > git status -z -uall [21ms]
2025-06-28 15:25:38.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:43.802 [info] > git config --get commit.template [19ms]
2025-06-28 15:25:43.805 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-28 15:25:43.826 [info] > git status -z -uall [11ms]
2025-06-28 15:25:43.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:48.868 [info] > git config --get commit.template [5ms]
2025-06-28 15:25:48.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-28 15:25:48.932 [info] > git status -z -uall [17ms]
2025-06-28 15:25:48.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:25:53.959 [info] > git config --get commit.template [7ms]
2025-06-28 15:25:53.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:25:53.979 [info] > git status -z -uall [7ms]
2025-06-28 15:25:53.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:25:59.014 [info] > git config --get commit.template [14ms]
2025-06-28 15:25:59.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:25:59.033 [info] > git status -z -uall [8ms]
2025-06-28 15:25:59.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:26:04.060 [info] > git config --get commit.template [7ms]
2025-06-28 15:26:04.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:26:04.077 [info] > git status -z -uall [8ms]
2025-06-28 15:26:04.078 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:26:09.117 [info] > git config --get commit.template [17ms]
2025-06-28 15:26:09.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:09.142 [info] > git status -z -uall [11ms]
2025-06-28 15:26:09.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:26:14.176 [info] > git config --get commit.template [16ms]
2025-06-28 15:26:14.177 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:14.206 [info] > git status -z -uall [16ms]
2025-06-28 15:26:14.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:19.244 [info] > git config --get commit.template [17ms]
2025-06-28 15:26:19.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:19.276 [info] > git status -z -uall [16ms]
2025-06-28 15:26:19.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:24.315 [info] > git config --get commit.template [14ms]
2025-06-28 15:26:24.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:24.344 [info] > git status -z -uall [13ms]
2025-06-28 15:26:24.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:29.376 [info] > git config --get commit.template [11ms]
2025-06-28 15:26:29.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:29.403 [info] > git status -z -uall [15ms]
2025-06-28 15:26:29.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:34.442 [info] > git config --get commit.template [18ms]
2025-06-28 15:26:34.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:34.469 [info] > git status -z -uall [13ms]
2025-06-28 15:26:34.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:26:39.499 [info] > git config --get commit.template [10ms]
2025-06-28 15:26:39.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:39.516 [info] > git status -z -uall [7ms]
2025-06-28 15:26:39.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:26:44.549 [info] > git config --get commit.template [15ms]
2025-06-28 15:26:44.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:44.566 [info] > git status -z -uall [6ms]
2025-06-28 15:26:44.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:49.600 [info] > git config --get commit.template [12ms]
2025-06-28 15:26:49.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:49.638 [info] > git status -z -uall [21ms]
2025-06-28 15:26:49.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:26:54.675 [info] > git config --get commit.template [12ms]
2025-06-28 15:26:54.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:26:54.695 [info] > git status -z -uall [8ms]
2025-06-28 15:26:54.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:26:59.717 [info] > git config --get commit.template [7ms]
2025-06-28 15:26:59.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:26:59.740 [info] > git status -z -uall [9ms]
2025-06-28 15:26:59.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:04.773 [info] > git config --get commit.template [14ms]
2025-06-28 15:27:04.774 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:04.799 [info] > git status -z -uall [13ms]
2025-06-28 15:27:04.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:27:09.828 [info] > git config --get commit.template [11ms]
2025-06-28 15:27:09.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:09.845 [info] > git status -z -uall [7ms]
2025-06-28 15:27:09.847 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:14.911 [info] > git config --get commit.template [10ms]
2025-06-28 15:27:14.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:14.935 [info] > git status -z -uall [14ms]
2025-06-28 15:27:14.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:27:19.969 [info] > git config --get commit.template [13ms]
2025-06-28 15:27:19.970 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:19.987 [info] > git status -z -uall [7ms]
2025-06-28 15:27:19.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:25.040 [info] > git config --get commit.template [18ms]
2025-06-28 15:27:25.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:27:25.073 [info] > git status -z -uall [14ms]
2025-06-28 15:27:25.073 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:30.116 [info] > git config --get commit.template [14ms]
2025-06-28 15:27:30.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:30.135 [info] > git status -z -uall [9ms]
2025-06-28 15:27:30.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:35.172 [info] > git config --get commit.template [16ms]
2025-06-28 15:27:35.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:35.199 [info] > git status -z -uall [13ms]
2025-06-28 15:27:35.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:27:40.227 [info] > git config --get commit.template [10ms]
2025-06-28 15:27:40.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:27:40.244 [info] > git status -z -uall [8ms]
2025-06-28 15:27:40.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:27:45.275 [info] > git config --get commit.template [13ms]
2025-06-28 15:27:45.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:45.313 [info] > git status -z -uall [19ms]
2025-06-28 15:27:45.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:27:50.365 [info] > git config --get commit.template [21ms]
2025-06-28 15:27:50.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:27:50.418 [info] > git status -z -uall [30ms]
2025-06-28 15:27:50.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:27:55.454 [info] > git config --get commit.template [11ms]
2025-06-28 15:27:55.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:27:55.475 [info] > git status -z -uall [12ms]
2025-06-28 15:27:55.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:28:00.510 [info] > git config --get commit.template [11ms]
2025-06-28 15:28:00.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:28:00.529 [info] > git status -z -uall [9ms]
2025-06-28 15:28:00.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:05.583 [info] > git config --get commit.template [25ms]
2025-06-28 15:28:05.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:28:05.637 [info] > git status -z -uall [22ms]
2025-06-28 15:28:05.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:28:10.669 [info] > git config --get commit.template [9ms]
2025-06-28 15:28:10.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:28:10.692 [info] > git status -z -uall [14ms]
2025-06-28 15:28:10.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:15.723 [info] > git config --get commit.template [9ms]
2025-06-28 15:28:15.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:28:15.738 [info] > git status -z -uall [8ms]
2025-06-28 15:28:15.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:20.770 [info] > git config --get commit.template [13ms]
2025-06-28 15:28:20.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:28:20.803 [info] > git status -z -uall [16ms]
2025-06-28 15:28:20.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:25.835 [info] > git config --get commit.template [12ms]
2025-06-28 15:28:25.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:28:25.861 [info] > git status -z -uall [12ms]
2025-06-28 15:28:25.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:30.889 [info] > git config --get commit.template [0ms]
2025-06-28 15:28:30.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:28:30.933 [info] > git status -z -uall [10ms]
2025-06-28 15:28:30.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:28:35.967 [info] > git config --get commit.template [11ms]
2025-06-28 15:28:35.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:28:35.985 [info] > git status -z -uall [7ms]
2025-06-28 15:28:35.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:41.015 [info] > git config --get commit.template [9ms]
2025-06-28 15:28:41.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:28:41.033 [info] > git status -z -uall [9ms]
2025-06-28 15:28:41.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:28:46.077 [info] > git config --get commit.template [14ms]
2025-06-28 15:28:46.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-28 15:28:46.124 [info] > git status -z -uall [19ms]
2025-06-28 15:28:46.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:28:51.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:28:51.166 [info] > git config --get commit.template [20ms]
2025-06-28 15:28:51.187 [info] > git status -z -uall [9ms]
2025-06-28 15:28:51.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:28:56.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:28:56.235 [info] > git config --get commit.template [18ms]
2025-06-28 15:28:56.262 [info] > git status -z -uall [14ms]
2025-06-28 15:28:56.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:29:01.295 [info] > git config --get commit.template [13ms]
2025-06-28 15:29:01.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:01.312 [info] > git status -z -uall [7ms]
2025-06-28 15:29:01.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:06.341 [info] > git config --get commit.template [6ms]
2025-06-28 15:29:06.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:29:06.402 [info] > git status -z -uall [26ms]
2025-06-28 15:29:06.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:11.911 [info] > git config --get commit.template [14ms]
2025-06-28 15:29:11.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:11.940 [info] > git status -z -uall [16ms]
2025-06-28 15:29:11.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:29:16.971 [info] > git config --get commit.template [12ms]
2025-06-28 15:29:16.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:16.999 [info] > git status -z -uall [14ms]
2025-06-28 15:29:17.002 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:29:22.034 [info] > git config --get commit.template [13ms]
2025-06-28 15:29:22.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:22.058 [info] > git status -z -uall [13ms]
2025-06-28 15:29:22.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:27.090 [info] > git config --get commit.template [13ms]
2025-06-28 15:29:27.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:27.123 [info] > git status -z -uall [15ms]
2025-06-28 15:29:27.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:32.156 [info] > git config --get commit.template [14ms]
2025-06-28 15:29:32.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:29:32.182 [info] > git status -z -uall [10ms]
2025-06-28 15:29:32.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:37.201 [info] > git config --get commit.template [2ms]
2025-06-28 15:29:37.214 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:37.239 [info] > git status -z -uall [11ms]
2025-06-28 15:29:37.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:29:42.267 [info] > git config --get commit.template [8ms]
2025-06-28 15:29:42.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:29:42.287 [info] > git status -z -uall [12ms]
2025-06-28 15:29:42.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:47.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:29:47.321 [info] > git config --get commit.template [14ms]
2025-06-28 15:29:47.349 [info] > git status -z -uall [10ms]
2025-06-28 15:29:47.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:52.379 [info] > git config --get commit.template [11ms]
2025-06-28 15:29:52.380 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:52.395 [info] > git status -z -uall [7ms]
2025-06-28 15:29:52.396 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:29:57.433 [info] > git config --get commit.template [16ms]
2025-06-28 15:29:57.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:29:57.463 [info] > git status -z -uall [14ms]
2025-06-28 15:29:57.465 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:30:02.497 [info] > git config --get commit.template [13ms]
2025-06-28 15:30:02.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:30:02.514 [info] > git status -z -uall [6ms]
2025-06-28 15:30:02.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:30:07.551 [info] > git config --get commit.template [14ms]
2025-06-28 15:30:07.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:07.575 [info] > git status -z -uall [12ms]
2025-06-28 15:30:07.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:30:12.604 [info] > git config --get commit.template [3ms]
2025-06-28 15:30:12.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:12.642 [info] > git status -z -uall [12ms]
2025-06-28 15:30:12.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:30:17.677 [info] > git config --get commit.template [14ms]
2025-06-28 15:30:17.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:17.701 [info] > git status -z -uall [10ms]
2025-06-28 15:30:17.703 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:30:22.745 [info] > git config --get commit.template [16ms]
2025-06-28 15:30:22.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:22.781 [info] > git status -z -uall [21ms]
2025-06-28 15:30:22.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:30:27.810 [info] > git config --get commit.template [3ms]
2025-06-28 15:30:27.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:27.866 [info] > git status -z -uall [11ms]
2025-06-28 15:30:27.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:30:32.996 [info] > git config --get commit.template [3ms]
2025-06-28 15:30:33.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:33.049 [info] > git status -z -uall [16ms]
2025-06-28 15:30:33.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:30:38.077 [info] > git config --get commit.template [2ms]
2025-06-28 15:30:38.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:30:38.134 [info] > git status -z -uall [12ms]
2025-06-28 15:30:38.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:30:43.159 [info] > git config --get commit.template [0ms]
2025-06-28 15:30:43.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:30:43.199 [info] > git status -z -uall [10ms]
2025-06-28 15:30:43.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:30:48.236 [info] > git config --get commit.template [16ms]
2025-06-28 15:30:48.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:48.260 [info] > git status -z -uall [9ms]
2025-06-28 15:30:48.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:30:53.299 [info] > git config --get commit.template [12ms]
2025-06-28 15:30:53.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:30:53.318 [info] > git status -z -uall [7ms]
2025-06-28 15:30:53.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:30:58.347 [info] > git config --get commit.template [13ms]
2025-06-28 15:30:58.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:30:58.377 [info] > git status -z -uall [14ms]
2025-06-28 15:30:58.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:31:03.409 [info] > git config --get commit.template [11ms]
2025-06-28 15:31:03.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:03.435 [info] > git status -z -uall [10ms]
2025-06-28 15:31:03.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:31:08.464 [info] > git config --get commit.template [9ms]
2025-06-28 15:31:08.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:08.496 [info] > git status -z -uall [18ms]
2025-06-28 15:31:08.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:31:13.535 [info] > git config --get commit.template [13ms]
2025-06-28 15:31:13.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:13.564 [info] > git status -z -uall [14ms]
2025-06-28 15:31:13.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:31:18.603 [info] > git config --get commit.template [18ms]
2025-06-28 15:31:18.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:31:18.641 [info] > git status -z -uall [21ms]
2025-06-28 15:31:18.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:31:23.674 [info] > git config --get commit.template [2ms]
2025-06-28 15:31:23.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:23.724 [info] > git status -z -uall [14ms]
2025-06-28 15:31:23.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:31:28.762 [info] > git config --get commit.template [14ms]
2025-06-28 15:31:28.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:31:28.797 [info] > git status -z -uall [16ms]
2025-06-28 15:31:28.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:31:33.822 [info] > git config --get commit.template [1ms]
2025-06-28 15:31:33.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:33.870 [info] > git status -z -uall [17ms]
2025-06-28 15:31:33.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:31:38.925 [info] > git config --get commit.template [28ms]
2025-06-28 15:31:38.928 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-28 15:31:39.036 [info] > git status -z -uall [87ms]
2025-06-28 15:31:39.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [76ms]
2025-06-28 15:31:40.535 [info] > git blame --root --incremental 5b9666563172e5a82d1e4f9b1395393f7c77e87c -- backend/.env [5ms]
2025-06-28 15:31:40.535 [info] fatal: no such path backend/.env in 5b9666563172e5a82d1e4f9b1395393f7c77e87c
2025-06-28 15:31:44.073 [info] > git config --get commit.template [15ms]
2025-06-28 15:31:44.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:44.100 [info] > git status -z -uall [12ms]
2025-06-28 15:31:44.103 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:31:49.130 [info] > git config --get commit.template [8ms]
2025-06-28 15:31:49.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:31:49.158 [info] > git status -z -uall [14ms]
2025-06-28 15:31:49.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:31:54.244 [info] > git config --get commit.template [17ms]
2025-06-28 15:31:54.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:31:54.270 [info] > git status -z -uall [12ms]
2025-06-28 15:31:54.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:31:59.304 [info] > git config --get commit.template [13ms]
2025-06-28 15:31:59.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:31:59.331 [info] > git status -z -uall [15ms]
2025-06-28 15:31:59.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:04.362 [info] > git config --get commit.template [13ms]
2025-06-28 15:32:04.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:32:04.393 [info] > git status -z -uall [16ms]
2025-06-28 15:32:04.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:32:09.429 [info] > git config --get commit.template [16ms]
2025-06-28 15:32:09.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:32:09.455 [info] > git status -z -uall [10ms]
2025-06-28 15:32:09.456 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:14.487 [info] > git config --get commit.template [11ms]
2025-06-28 15:32:14.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:32:14.510 [info] > git status -z -uall [10ms]
2025-06-28 15:32:14.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:19.544 [info] > git config --get commit.template [13ms]
2025-06-28 15:32:19.546 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:32:19.562 [info] > git status -z -uall [8ms]
2025-06-28 15:32:19.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:24.594 [info] > git config --get commit.template [13ms]
2025-06-28 15:32:24.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:32:24.622 [info] > git status -z -uall [13ms]
2025-06-28 15:32:24.623 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:29.663 [info] > git config --get commit.template [20ms]
2025-06-28 15:32:29.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:32:29.702 [info] > git status -z -uall [18ms]
2025-06-28 15:32:29.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:32:34.734 [info] > git config --get commit.template [11ms]
2025-06-28 15:32:34.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:32:34.757 [info] > git status -z -uall [11ms]
2025-06-28 15:32:34.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:32:42.265 [info] > git config --get commit.template [26ms]
2025-06-28 15:32:42.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:32:42.305 [info] > git status -z -uall [16ms]
2025-06-28 15:32:42.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:32:47.375 [info] > git config --get commit.template [33ms]
2025-06-28 15:32:47.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:32:47.421 [info] > git status -z -uall [23ms]
2025-06-28 15:32:47.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:32:52.481 [info] > git config --get commit.template [22ms]
2025-06-28 15:32:52.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:32:52.573 [info] > git status -z -uall [30ms]
2025-06-28 15:32:52.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:32:57.622 [info] > git config --get commit.template [14ms]
2025-06-28 15:32:57.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:32:57.663 [info] > git status -z -uall [20ms]
2025-06-28 15:32:57.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:33:02.709 [info] > git config --get commit.template [17ms]
2025-06-28 15:33:02.711 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:33:02.740 [info] > git status -z -uall [17ms]
2025-06-28 15:33:02.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:33:07.778 [info] > git config --get commit.template [14ms]
2025-06-28 15:33:07.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:33:07.801 [info] > git status -z -uall [10ms]
2025-06-28 15:33:07.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:33:12.851 [info] > git config --get commit.template [24ms]
2025-06-28 15:33:12.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:33:12.894 [info] > git status -z -uall [19ms]
2025-06-28 15:33:12.898 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:33:17.931 [info] > git config --get commit.template [14ms]
2025-06-28 15:33:17.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:33:17.967 [info] > git status -z -uall [19ms]
2025-06-28 15:33:17.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:33:23.261 [info] > git config --get commit.template [12ms]
2025-06-28 15:33:23.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:33:23.288 [info] > git status -z -uall [12ms]
2025-06-28 15:33:23.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:33:28.310 [info] > git config --get commit.template [2ms]
2025-06-28 15:33:28.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:33:28.349 [info] > git status -z -uall [15ms]
2025-06-28 15:33:28.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:33:33.394 [info] > git config --get commit.template [4ms]
2025-06-28 15:33:33.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:33:33.493 [info] > git status -z -uall [28ms]
2025-06-28 15:33:33.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:33:40.275 [info] > git config --get commit.template [3ms]
2025-06-28 15:33:40.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-28 15:33:40.336 [info] > git status -z -uall [19ms]
2025-06-28 15:33:40.337 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:33:45.377 [info] > git config --get commit.template [19ms]
2025-06-28 15:33:45.380 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:33:45.433 [info] > git status -z -uall [32ms]
2025-06-28 15:33:45.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-28 15:33:55.302 [info] > git config --get commit.template [15ms]
2025-06-28 15:33:55.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:33:55.338 [info] > git status -z -uall [18ms]
2025-06-28 15:33:55.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:34:00.379 [info] > git config --get commit.template [19ms]
2025-06-28 15:34:00.380 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:00.403 [info] > git status -z -uall [10ms]
2025-06-28 15:34:00.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:34:05.446 [info] > git config --get commit.template [16ms]
2025-06-28 15:34:05.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:05.504 [info] > git status -z -uall [38ms]
2025-06-28 15:34:05.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:34:10.548 [info] > git config --get commit.template [14ms]
2025-06-28 15:34:10.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:10.569 [info] > git status -z -uall [12ms]
2025-06-28 15:34:10.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:34:15.614 [info] > git config --get commit.template [11ms]
2025-06-28 15:34:15.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:15.657 [info] > git status -z -uall [16ms]
2025-06-28 15:34:15.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:34:20.691 [info] > git config --get commit.template [13ms]
2025-06-28 15:34:20.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:20.717 [info] > git status -z -uall [8ms]
2025-06-28 15:34:20.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:34:25.768 [info] > git config --get commit.template [20ms]
2025-06-28 15:34:25.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:25.810 [info] > git status -z -uall [24ms]
2025-06-28 15:34:25.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:34:30.844 [info] > git config --get commit.template [11ms]
2025-06-28 15:34:30.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:30.872 [info] > git status -z -uall [14ms]
2025-06-28 15:34:30.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:34:35.910 [info] > git config --get commit.template [14ms]
2025-06-28 15:34:35.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:35.931 [info] > git status -z -uall [9ms]
2025-06-28 15:34:35.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:34:37.843 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:34:40.977 [info] > git config --get commit.template [11ms]
2025-06-28 15:34:40.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:41.014 [info] > git status -z -uall [17ms]
2025-06-28 15:34:41.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:34:46.053 [info] > git config --get commit.template [17ms]
2025-06-28 15:34:46.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:46.084 [info] > git status -z -uall [14ms]
2025-06-28 15:34:46.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:34:51.118 [info] > git config --get commit.template [11ms]
2025-06-28 15:34:51.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:51.149 [info] > git status -z -uall [15ms]
2025-06-28 15:34:51.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:34:56.192 [info] > git config --get commit.template [14ms]
2025-06-28 15:34:56.194 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:34:56.218 [info] > git status -z -uall [11ms]
2025-06-28 15:34:56.220 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:35:01.262 [info] > git config --get commit.template [17ms]
2025-06-28 15:35:01.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:35:01.304 [info] > git status -z -uall [26ms]
2025-06-28 15:35:01.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-28 15:35:02.994 [info] > git show --textconv :backend/.env.example [35ms]
2025-06-28 15:35:02.997 [info] > git ls-files --stage -- backend/.env.example [6ms]
2025-06-28 15:35:03.019 [info] > git cat-file -s 225c5fefc28abb0dc0711170d55a9782420d8441 [3ms]
2025-06-28 15:35:06.349 [info] > git config --get commit.template [3ms]
2025-06-28 15:35:06.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:35:06.430 [info] > git status -z -uall [13ms]
2025-06-28 15:35:06.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:35:06.649 [info] > git show --textconv :backend/.env [23ms]
2025-06-28 15:35:06.649 [info] > git ls-files --stage -- backend/.env [2ms]
2025-06-28 15:35:06.650 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:35:06.650 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:35:08.113 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:35:10.283 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-28 15:35:11.480 [info] > git config --get commit.template [15ms]
2025-06-28 15:35:11.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:11.514 [info] > git status -z -uall [16ms]
2025-06-28 15:35:11.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:35:16.535 [info] > git config --get commit.template [1ms]
2025-06-28 15:35:16.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:16.572 [info] > git status -z -uall [14ms]
2025-06-28 15:35:16.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:35:21.832 [info] > git config --get commit.template [2ms]
2025-06-28 15:35:21.867 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-28 15:35:21.912 [info] > git status -z -uall [21ms]
2025-06-28 15:35:21.913 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:35:26.949 [info] > git config --get commit.template [15ms]
2025-06-28 15:35:26.950 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:26.976 [info] > git status -z -uall [11ms]
2025-06-28 15:35:26.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:35:32.008 [info] > git config --get commit.template [11ms]
2025-06-28 15:35:32.009 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:32.037 [info] > git status -z -uall [14ms]
2025-06-28 15:35:32.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:35:37.272 [info] > git config --get commit.template [208ms]
2025-06-28 15:35:37.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:37.365 [info] > git status -z -uall [28ms]
2025-06-28 15:35:37.368 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:35:42.680 [info] > git config --get commit.template [249ms]
2025-06-28 15:35:42.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-28 15:35:42.730 [info] > git status -z -uall [17ms]
2025-06-28 15:35:42.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:35:47.771 [info] > git config --get commit.template [16ms]
2025-06-28 15:35:47.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:47.837 [info] > git status -z -uall [35ms]
2025-06-28 15:35:47.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:35:55.918 [info] > git config --get commit.template [24ms]
2025-06-28 15:35:55.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:35:55.980 [info] > git status -z -uall [32ms]
2025-06-28 15:35:55.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:36:01.015 [info] > git config --get commit.template [13ms]
2025-06-28 15:36:01.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:01.039 [info] > git status -z -uall [13ms]
2025-06-28 15:36:01.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:36:06.076 [info] > git config --get commit.template [15ms]
2025-06-28 15:36:06.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:06.104 [info] > git status -z -uall [11ms]
2025-06-28 15:36:06.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:36:11.136 [info] > git config --get commit.template [12ms]
2025-06-28 15:36:11.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:11.276 [info] > git status -z -uall [131ms]
2025-06-28 15:36:11.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [233ms]
2025-06-28 15:36:16.420 [info] > git config --get commit.template [13ms]
2025-06-28 15:36:16.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:36:16.438 [info] > git status -z -uall [8ms]
2025-06-28 15:36:16.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:36:21.467 [info] > git config --get commit.template [9ms]
2025-06-28 15:36:21.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:36:21.499 [info] > git status -z -uall [17ms]
2025-06-28 15:36:21.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:36:26.525 [info] > git config --get commit.template [8ms]
2025-06-28 15:36:26.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:26.543 [info] > git status -z -uall [11ms]
2025-06-28 15:36:26.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:36:31.624 [info] > git config --get commit.template [11ms]
2025-06-28 15:36:31.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:31.648 [info] > git status -z -uall [16ms]
2025-06-28 15:36:31.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:36:36.677 [info] > git config --get commit.template [11ms]
2025-06-28 15:36:36.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:36.698 [info] > git status -z -uall [11ms]
2025-06-28 15:36:36.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:36:41.738 [info] > git config --get commit.template [14ms]
2025-06-28 15:36:41.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:36:41.768 [info] > git status -z -uall [12ms]
2025-06-28 15:36:41.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:36:46.826 [info] > git config --get commit.template [26ms]
2025-06-28 15:36:46.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:36:46.858 [info] > git status -z -uall [9ms]
2025-06-28 15:36:46.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:36:51.900 [info] > git config --get commit.template [17ms]
2025-06-28 15:36:51.902 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:36:51.931 [info] > git status -z -uall [14ms]
2025-06-28 15:36:51.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:36:56.969 [info] > git config --get commit.template [18ms]
2025-06-28 15:36:56.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:36:57.004 [info] > git status -z -uall [19ms]
2025-06-28 15:36:57.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:37:02.033 [info] > git config --get commit.template [10ms]
2025-06-28 15:37:02.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:37:02.052 [info] > git status -z -uall [10ms]
2025-06-28 15:37:02.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:37:07.093 [info] > git config --get commit.template [18ms]
2025-06-28 15:37:07.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:37:07.135 [info] > git status -z -uall [22ms]
2025-06-28 15:37:07.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:37:12.159 [info] > git config --get commit.template [3ms]
2025-06-28 15:37:12.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:37:12.215 [info] > git status -z -uall [20ms]
2025-06-28 15:37:12.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:00.446 [info] > git config --get commit.template [54ms]
2025-06-28 15:42:00.461 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:00.491 [info] > git status -z -uall [18ms]
2025-06-28 15:42:00.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:42:05.526 [info] > git config --get commit.template [12ms]
2025-06-28 15:42:05.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:05.552 [info] > git status -z -uall [12ms]
2025-06-28 15:42:05.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:10.587 [info] > git config --get commit.template [14ms]
2025-06-28 15:42:10.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:10.631 [info] > git status -z -uall [19ms]
2025-06-28 15:42:10.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:15.660 [info] > git config --get commit.template [11ms]
2025-06-28 15:42:15.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:15.689 [info] > git status -z -uall [15ms]
2025-06-28 15:42:15.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:20.718 [info] > git config --get commit.template [10ms]
2025-06-28 15:42:20.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:20.749 [info] > git status -z -uall [17ms]
2025-06-28 15:42:20.751 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:42:25.786 [info] > git config --get commit.template [15ms]
2025-06-28 15:42:25.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:25.820 [info] > git status -z -uall [18ms]
2025-06-28 15:42:25.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:30.851 [info] > git config --get commit.template [9ms]
2025-06-28 15:42:30.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:30.876 [info] > git status -z -uall [12ms]
2025-06-28 15:42:30.877 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:35.911 [info] > git config --get commit.template [11ms]
2025-06-28 15:42:35.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:35.933 [info] > git status -z -uall [9ms]
2025-06-28 15:42:35.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:42:40.972 [info] > git config --get commit.template [13ms]
2025-06-28 15:42:40.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:40.993 [info] > git status -z -uall [9ms]
2025-06-28 15:42:40.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:46.023 [info] > git config --get commit.template [9ms]
2025-06-28 15:42:46.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:46.054 [info] > git status -z -uall [13ms]
2025-06-28 15:42:46.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:42:51.092 [info] > git config --get commit.template [16ms]
2025-06-28 15:42:51.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:51.120 [info] > git status -z -uall [14ms]
2025-06-28 15:42:51.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:42:56.146 [info] > git config --get commit.template [8ms]
2025-06-28 15:42:56.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:42:56.173 [info] > git status -z -uall [13ms]
2025-06-28 15:42:56.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:01.205 [info] > git config --get commit.template [10ms]
2025-06-28 15:43:01.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:43:01.226 [info] > git status -z -uall [12ms]
2025-06-28 15:43:01.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:06.259 [info] > git config --get commit.template [14ms]
2025-06-28 15:43:06.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:06.289 [info] > git status -z -uall [16ms]
2025-06-28 15:43:06.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:43:11.325 [info] > git config --get commit.template [13ms]
2025-06-28 15:43:11.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:43:11.356 [info] > git status -z -uall [13ms]
2025-06-28 15:43:11.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:16.390 [info] > git config --get commit.template [9ms]
2025-06-28 15:43:16.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:16.417 [info] > git status -z -uall [11ms]
2025-06-28 15:43:16.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:21.508 [info] > git config --get commit.template [9ms]
2025-06-28 15:43:21.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:21.537 [info] > git status -z -uall [15ms]
2025-06-28 15:43:21.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:43:26.569 [info] > git config --get commit.template [14ms]
2025-06-28 15:43:26.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:26.597 [info] > git status -z -uall [16ms]
2025-06-28 15:43:26.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:31.657 [info] > git config --get commit.template [25ms]
2025-06-28 15:43:31.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:43:31.701 [info] > git status -z -uall [25ms]
2025-06-28 15:43:31.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:43:36.772 [info] > git config --get commit.template [10ms]
2025-06-28 15:43:36.773 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:43:36.790 [info] > git status -z -uall [8ms]
2025-06-28 15:43:36.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:41.813 [info] > git config --get commit.template [5ms]
2025-06-28 15:43:41.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:43:41.845 [info] > git status -z -uall [13ms]
2025-06-28 15:43:41.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:46.877 [info] > git config --get commit.template [13ms]
2025-06-28 15:43:46.878 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:46.900 [info] > git status -z -uall [12ms]
2025-06-28 15:43:46.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:43:51.934 [info] > git config --get commit.template [13ms]
2025-06-28 15:43:51.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:43:51.962 [info] > git status -z -uall [12ms]
2025-06-28 15:43:51.964 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:43:56.995 [info] > git config --get commit.template [12ms]
2025-06-28 15:43:56.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:43:57.032 [info] > git status -z -uall [15ms]
2025-06-28 15:43:57.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:01.990 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:44:02.063 [info] > git config --get commit.template [12ms]
2025-06-28 15:44:02.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:02.093 [info] > git status -z -uall [15ms]
2025-06-28 15:44:02.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:44:07.124 [info] > git config --get commit.template [13ms]
2025-06-28 15:44:07.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:07.149 [info] > git status -z -uall [11ms]
2025-06-28 15:44:07.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:12.182 [info] > git config --get commit.template [13ms]
2025-06-28 15:44:12.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:44:12.214 [info] > git status -z -uall [18ms]
2025-06-28 15:44:12.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:17.245 [info] > git config --get commit.template [0ms]
2025-06-28 15:44:17.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:44:17.272 [info] > git status -z -uall [7ms]
2025-06-28 15:44:17.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:22.365 [info] > git config --get commit.template [25ms]
2025-06-28 15:44:22.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-28 15:44:22.440 [info] > git status -z -uall [27ms]
2025-06-28 15:44:22.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 15:44:27.554 [info] > git config --get commit.template [23ms]
2025-06-28 15:44:27.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:44:27.596 [info] > git status -z -uall [28ms]
2025-06-28 15:44:27.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:44:32.642 [info] > git config --get commit.template [14ms]
2025-06-28 15:44:32.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:32.678 [info] > git status -z -uall [20ms]
2025-06-28 15:44:32.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:37.729 [info] > git config --get commit.template [26ms]
2025-06-28 15:44:37.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:37.762 [info] > git status -z -uall [12ms]
2025-06-28 15:44:37.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:42.795 [info] > git config --get commit.template [10ms]
2025-06-28 15:44:42.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:44:42.815 [info] > git status -z -uall [9ms]
2025-06-28 15:44:42.817 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:47.853 [info] > git config --get commit.template [16ms]
2025-06-28 15:44:47.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:47.891 [info] > git status -z -uall [12ms]
2025-06-28 15:44:47.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:44:52.932 [info] > git config --get commit.template [17ms]
2025-06-28 15:44:52.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:52.969 [info] > git status -z -uall [21ms]
2025-06-28 15:44:52.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:44:58.669 [info] > git config --get commit.template [2ms]
2025-06-28 15:44:58.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:44:58.718 [info] > git status -z -uall [18ms]
2025-06-28 15:44:58.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:45:03.744 [info] > git config --get commit.template [3ms]
2025-06-28 15:45:03.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:45:03.805 [info] > git status -z -uall [22ms]
2025-06-28 15:45:03.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:45:08.852 [info] > git config --get commit.template [9ms]
2025-06-28 15:45:08.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:45:08.873 [info] > git status -z -uall [8ms]
2025-06-28 15:45:08.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:45:13.910 [info] > git config --get commit.template [16ms]
2025-06-28 15:45:13.913 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:45:13.955 [info] > git status -z -uall [21ms]
2025-06-28 15:45:13.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:45:18.989 [info] > git config --get commit.template [13ms]
2025-06-28 15:45:18.990 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:45:19.015 [info] > git status -z -uall [12ms]
2025-06-28 15:45:19.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:45:24.058 [info] > git config --get commit.template [17ms]
2025-06-28 15:45:24.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:45:24.103 [info] > git status -z -uall [22ms]
2025-06-28 15:45:24.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:45:29.148 [info] > git config --get commit.template [18ms]
2025-06-28 15:45:29.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:45:29.175 [info] > git status -z -uall [14ms]
2025-06-28 15:45:29.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:45:34.234 [info] > git config --get commit.template [14ms]
2025-06-28 15:45:34.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:45:34.262 [info] > git status -z -uall [13ms]
2025-06-28 15:45:34.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:00.930 [info] > git config --get commit.template [14ms]
2025-06-28 15:49:00.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:49:00.971 [info] > git status -z -uall [21ms]
2025-06-28 15:49:00.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:06.005 [info] > git config --get commit.template [13ms]
2025-06-28 15:49:06.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:49:06.098 [info] > git status -z -uall [77ms]
2025-06-28 15:49:06.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [65ms]
2025-06-28 15:49:06.895 [info] > git show --textconv :backend/.env [12ms]
2025-06-28 15:49:06.896 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:49:06.896 [info] > git ls-files --stage -- backend/.env [1ms]
2025-06-28 15:49:06.897 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:49:10.360 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:49:11.130 [info] > git config --get commit.template [10ms]
2025-06-28 15:49:11.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:49:11.149 [info] > git status -z -uall [9ms]
2025-06-28 15:49:11.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:49:11.808 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-28 15:49:14.319 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:49:15.753 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:49:16.184 [info] > git config --get commit.template [15ms]
2025-06-28 15:49:16.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:49:16.213 [info] > git status -z -uall [14ms]
2025-06-28 15:49:16.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:16.898 [info] > git show --textconv :frontend/src/views/admin/ProjectCreate.vue [15ms]
2025-06-28 15:49:16.899 [info] > git ls-files --stage -- frontend/src/views/admin/ProjectCreate.vue [4ms]
2025-06-28 15:49:16.914 [info] > git cat-file -s 889f358a2b4642927c86e35f7f45a3f8ca1f326e [1ms]
2025-06-28 15:49:21.251 [info] > git config --get commit.template [13ms]
2025-06-28 15:49:21.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:49:21.279 [info] > git status -z -uall [15ms]
2025-06-28 15:49:21.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:49:26.314 [info] > git config --get commit.template [13ms]
2025-06-28 15:49:26.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:49:26.341 [info] > git status -z -uall [14ms]
2025-06-28 15:49:26.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:31.377 [info] > git config --get commit.template [15ms]
2025-06-28 15:49:31.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:49:31.414 [info] > git status -z -uall [22ms]
2025-06-28 15:49:31.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:36.454 [info] > git config --get commit.template [17ms]
2025-06-28 15:49:36.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:49:36.476 [info] > git status -z -uall [8ms]
2025-06-28 15:49:36.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:49:41.510 [info] > git config --get commit.template [12ms]
2025-06-28 15:49:41.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:49:41.533 [info] > git status -z -uall [11ms]
2025-06-28 15:49:41.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:50:38.669 [info] > git config --get commit.template [8ms]
2025-06-28 15:50:38.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:50:38.686 [info] > git status -z -uall [8ms]
2025-06-28 15:50:38.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:50:42.172 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:50:43.719 [info] > git config --get commit.template [14ms]
2025-06-28 15:50:43.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:50:43.744 [info] > git status -z -uall [14ms]
2025-06-28 15:50:43.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:50:48.775 [info] > git config --get commit.template [13ms]
2025-06-28 15:50:48.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:50:48.801 [info] > git status -z -uall [13ms]
2025-06-28 15:50:48.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:50:53.836 [info] > git config --get commit.template [14ms]
2025-06-28 15:50:53.837 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:50:53.854 [info] > git status -z -uall [7ms]
2025-06-28 15:50:53.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:50:56.153 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-28 15:50:56.912 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:50:58.895 [info] > git config --get commit.template [15ms]
2025-06-28 15:50:58.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:50:58.925 [info] > git status -z -uall [14ms]
2025-06-28 15:50:58.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:51:03.959 [info] > git config --get commit.template [13ms]
2025-06-28 15:51:03.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:51:03.986 [info] > git status -z -uall [13ms]
2025-06-28 15:51:03.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:51:09.011 [info] > git config --get commit.template [7ms]
2025-06-28 15:51:09.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:51:09.041 [info] > git status -z -uall [15ms]
2025-06-28 15:51:09.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:51:14.062 [info] > git config --get commit.template [0ms]
2025-06-28 15:51:14.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:51:14.113 [info] > git status -z -uall [16ms]
2025-06-28 15:51:14.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:51:19.108 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-28 15:51:19.141 [info] > git show --textconv :tests/backend/test-ai-integration.md [18ms]
2025-06-28 15:51:19.141 [info] > git ls-files --stage -- tests/backend/test-ai-integration.md [2ms]
2025-06-28 15:51:19.142 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/tests/backend/test-ai-integration.md.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftests%2Fbackend%2Ftest-ai-integration.md%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:51:19.142 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/tests/backend/test-ai-integration.md.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Ftests%2Fbackend%2Ftest-ai-integration.md%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:51:19.301 [info] > git config --get commit.template [106ms]
2025-06-28 15:51:19.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:51:19.419 [info] > git status -z -uall [66ms]
2025-06-28 15:51:19.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-28 15:51:24.461 [info] > git config --get commit.template [13ms]
2025-06-28 15:51:24.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:51:24.491 [info] > git status -z -uall [19ms]
2025-06-28 15:51:24.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:51:29.537 [info] > git config --get commit.template [17ms]
2025-06-28 15:51:29.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:51:29.560 [info] > git status -z -uall [14ms]
2025-06-28 15:51:29.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:51:34.583 [info] > git config --get commit.template [1ms]
2025-06-28 15:51:34.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:51:34.634 [info] > git status -z -uall [20ms]
2025-06-28 15:51:34.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:51:39.665 [info] > git config --get commit.template [10ms]
2025-06-28 15:51:39.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:51:39.699 [info] > git status -z -uall [20ms]
2025-06-28 15:51:39.701 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:51:44.739 [info] > git config --get commit.template [15ms]
2025-06-28 15:51:44.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:51:44.772 [info] > git status -z -uall [15ms]
2025-06-28 15:51:44.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:51:49.806 [info] > git config --get commit.template [5ms]
2025-06-28 15:51:49.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:51:49.896 [info] > git status -z -uall [22ms]
2025-06-28 15:51:49.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:51:54.943 [info] > git config --get commit.template [21ms]
2025-06-28 15:51:54.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:51:54.960 [info] > git status -z -uall [7ms]
2025-06-28 15:51:54.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:52:00.012 [info] > git config --get commit.template [22ms]
2025-06-28 15:52:00.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:00.038 [info] > git status -z -uall [13ms]
2025-06-28 15:52:00.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:52:05.068 [info] > git config --get commit.template [8ms]
2025-06-28 15:52:05.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:52:05.106 [info] > git status -z -uall [21ms]
2025-06-28 15:52:05.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:52:10.150 [info] > git config --get commit.template [21ms]
2025-06-28 15:52:10.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:10.188 [info] > git status -z -uall [24ms]
2025-06-28 15:52:10.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:52:15.223 [info] > git config --get commit.template [12ms]
2025-06-28 15:52:15.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:15.240 [info] > git status -z -uall [6ms]
2025-06-28 15:52:15.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:52:20.273 [info] > git config --get commit.template [13ms]
2025-06-28 15:52:20.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:52:20.309 [info] > git status -z -uall [10ms]
2025-06-28 15:52:20.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:52:25.354 [info] > git config --get commit.template [17ms]
2025-06-28 15:52:25.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:52:25.401 [info] > git status -z -uall [26ms]
2025-06-28 15:52:25.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 15:52:30.450 [info] > git config --get commit.template [19ms]
2025-06-28 15:52:30.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:30.487 [info] > git status -z -uall [22ms]
2025-06-28 15:52:30.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:52:35.549 [info] > git config --get commit.template [26ms]
2025-06-28 15:52:35.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:52:35.602 [info] > git status -z -uall [23ms]
2025-06-28 15:52:35.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:52:40.651 [info] > git config --get commit.template [15ms]
2025-06-28 15:52:40.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:40.672 [info] > git status -z -uall [11ms]
2025-06-28 15:52:40.673 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 15:52:45.817 [info] > git config --get commit.template [105ms]
2025-06-28 15:52:45.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:52:45.890 [info] > git status -z -uall [24ms]
2025-06-28 15:52:45.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 15:52:51.050 [info] > git config --get commit.template [51ms]
2025-06-28 15:52:51.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 15:52:51.144 [info] > git status -z -uall [53ms]
2025-06-28 15:52:51.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:52:56.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:52:56.183 [info] > git config --get commit.template [19ms]
2025-06-28 15:52:56.223 [info] > git status -z -uall [18ms]
2025-06-28 15:52:56.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:53:01.254 [info] > git config --get commit.template [9ms]
2025-06-28 15:53:01.255 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 15:53:01.272 [info] > git status -z -uall [10ms]
2025-06-28 15:53:01.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:53:06.318 [info] > git config --get commit.template [18ms]
2025-06-28 15:53:06.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:53:06.358 [info] > git status -z -uall [16ms]
2025-06-28 15:53:06.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:53:11.403 [info] > git config --get commit.template [15ms]
2025-06-28 15:53:11.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 15:53:11.432 [info] > git status -z -uall [14ms]
2025-06-28 15:53:11.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:55:38.992 [info] > git config --get commit.template [0ms]
2025-06-28 15:55:39.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:55:39.050 [info] > git status -z -uall [18ms]
2025-06-28 15:55:39.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-28 15:55:44.115 [info] > git config --get commit.template [20ms]
2025-06-28 15:55:44.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:55:44.160 [info] > git status -z -uall [21ms]
2025-06-28 15:55:44.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 15:59:15.348 [info] > git config --get commit.template [0ms]
2025-06-28 15:59:15.366 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:15.393 [info] > git status -z -uall [19ms]
2025-06-28 15:59:15.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 15:59:18.795 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:59:20.357 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-28 15:59:31.954 [info] > git config --get commit.template [10ms]
2025-06-28 15:59:31.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:31.987 [info] > git status -z -uall [14ms]
2025-06-28 15:59:31.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:59:37.013 [info] > git config --get commit.template [8ms]
2025-06-28 15:59:37.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:37.037 [info] > git status -z -uall [16ms]
2025-06-28 15:59:37.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:59:43.958 [info] > git config --get commit.template [8ms]
2025-06-28 15:59:43.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:43.976 [info] > git status -z -uall [8ms]
2025-06-28 15:59:43.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:59:49.009 [info] > git config --get commit.template [12ms]
2025-06-28 15:59:49.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:49.041 [info] > git status -z -uall [15ms]
2025-06-28 15:59:49.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:59:50.661 [info] > git show --textconv :.env [12ms]
2025-06-28 15:59:50.662 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:59:50.663 [info] > git ls-files --stage -- .env [3ms]
2025-06-28 15:59:50.663 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 15:59:54.081 [info] > git config --get commit.template [19ms]
2025-06-28 15:59:54.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-28 15:59:54.114 [info] > git status -z -uall [16ms]
2025-06-28 15:59:54.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 15:59:59.141 [info] > git config --get commit.template [8ms]
2025-06-28 15:59:59.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 15:59:59.156 [info] > git status -z -uall [7ms]
2025-06-28 15:59:59.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:00:09.414 [info] > git config --get commit.template [22ms]
2025-06-28 16:00:09.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 16:00:09.470 [info] > git status -z -uall [32ms]
2025-06-28 16:00:09.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:00:17.040 [info] > git config --get commit.template [10ms]
2025-06-28 16:00:17.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 16:00:17.160 [info] > git status -z -uall [111ms]
2025-06-28 16:00:17.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [98ms]
2025-06-28 16:00:17.474 [info] > git show --textconv :backend/.env [10ms]
2025-06-28 16:00:17.474 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 16:00:17.475 [info] > git ls-files --stage -- backend/.env [2ms]
2025-06-28 16:00:17.475 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/backend/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fbackend%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-28 16:01:16.612 [info] > git config --get commit.template [0ms]
2025-06-28 16:01:16.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:01:16.663 [info] > git status -z -uall [16ms]
2025-06-28 16:01:16.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 16:01:21.711 [info] > git config --get commit.template [23ms]
2025-06-28 16:01:21.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 16:01:21.749 [info] > git status -z -uall [9ms]
2025-06-28 16:01:21.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:01:26.775 [info] > git config --get commit.template [7ms]
2025-06-28 16:01:26.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 16:01:26.813 [info] > git status -z -uall [15ms]
2025-06-28 16:01:26.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:02:37.092 [info] > git config --get commit.template [22ms]
2025-06-28 16:02:37.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:02:37.143 [info] > git status -z -uall [36ms]
2025-06-28 16:02:37.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:02:42.191 [info] > git config --get commit.template [17ms]
2025-06-28 16:02:42.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-28 16:02:42.251 [info] > git status -z -uall [24ms]
2025-06-28 16:02:42.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 16:03:16.884 [info] > git config --get commit.template [7ms]
2025-06-28 16:03:16.885 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:03:16.902 [info] > git status -z -uall [11ms]
2025-06-28 16:03:16.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:03:21.931 [info] > git config --get commit.template [10ms]
2025-06-28 16:03:21.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:03:21.960 [info] > git status -z -uall [17ms]
2025-06-28 16:03:21.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:03:38.611 [info] > git config --get commit.template [26ms]
2025-06-28 16:03:38.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 16:03:38.646 [info] > git status -z -uall [21ms]
2025-06-28 16:03:38.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:03:43.673 [info] > git config --get commit.template [9ms]
2025-06-28 16:03:43.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:03:43.712 [info] > git status -z -uall [16ms]
2025-06-28 16:03:43.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:03:48.738 [info] > git config --get commit.template [4ms]
2025-06-28 16:03:48.774 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:03:48.817 [info] > git status -z -uall [16ms]
2025-06-28 16:03:48.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:03:53.858 [info] > git config --get commit.template [16ms]
2025-06-28 16:03:53.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:03:53.893 [info] > git status -z -uall [19ms]
2025-06-28 16:03:53.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 16:03:58.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-28 16:03:58.928 [info] > git config --get commit.template [13ms]
2025-06-28 16:03:58.959 [info] > git status -z -uall [15ms]
2025-06-28 16:03:58.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:04:03.990 [info] > git config --get commit.template [12ms]
2025-06-28 16:04:03.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:04:04.007 [info] > git status -z -uall [6ms]
2025-06-28 16:04:04.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:04:09.036 [info] > git config --get commit.template [8ms]
2025-06-28 16:04:09.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 16:04:09.056 [info] > git status -z -uall [12ms]
2025-06-28 16:04:09.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:10:23.377 [info] > git config --get commit.template [25ms]
2025-06-28 16:10:23.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 16:10:23.476 [info] > git status -z -uall [32ms]
2025-06-28 16:10:23.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-28 16:10:28.552 [info] > git config --get commit.template [31ms]
2025-06-28 16:10:28.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:10:28.595 [info] > git status -z -uall [27ms]
2025-06-28 16:10:28.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-28 16:10:33.635 [info] > git config --get commit.template [0ms]
2025-06-28 16:10:33.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:10:33.677 [info] > git status -z -uall [17ms]
2025-06-28 16:10:33.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:10:38.722 [info] > git config --get commit.template [19ms]
2025-06-28 16:10:38.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:10:38.746 [info] > git status -z -uall [11ms]
2025-06-28 16:10:38.747 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:10:43.776 [info] > git config --get commit.template [11ms]
2025-06-28 16:10:43.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:10:43.798 [info] > git status -z -uall [9ms]
2025-06-28 16:10:43.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 16:10:48.843 [info] > git config --get commit.template [18ms]
2025-06-28 16:10:48.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:10:48.883 [info] > git status -z -uall [21ms]
2025-06-28 16:10:48.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-28 16:10:54.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [112ms]
2025-06-28 16:10:54.045 [info] > git config --get commit.template [127ms]
2025-06-28 16:10:54.070 [info] > git status -z -uall [11ms]
2025-06-28 16:10:54.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:10:59.106 [info] > git config --get commit.template [14ms]
2025-06-28 16:10:59.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:10:59.140 [info] > git status -z -uall [18ms]
2025-06-28 16:10:59.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:11:04.175 [info] > git config --get commit.template [13ms]
2025-06-28 16:11:04.177 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:04.199 [info] > git status -z -uall [12ms]
2025-06-28 16:11:04.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:11:09.237 [info] > git config --get commit.template [15ms]
2025-06-28 16:11:09.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:09.385 [info] > git status -z -uall [135ms]
2025-06-28 16:11:09.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [125ms]
2025-06-28 16:11:14.421 [info] > git config --get commit.template [12ms]
2025-06-28 16:11:14.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:11:14.460 [info] > git status -z -uall [14ms]
2025-06-28 16:11:14.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:11:19.502 [info] > git config --get commit.template [19ms]
2025-06-28 16:11:19.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:19.539 [info] > git status -z -uall [15ms]
2025-06-28 16:11:19.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:11:24.572 [info] > git config --get commit.template [13ms]
2025-06-28 16:11:24.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:24.612 [info] > git status -z -uall [20ms]
2025-06-28 16:11:24.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:11:29.669 [info] > git config --get commit.template [32ms]
2025-06-28 16:11:29.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 16:11:29.727 [info] > git status -z -uall [25ms]
2025-06-28 16:11:29.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:11:34.783 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-28 16:11:34.783 [info] > git config --get commit.template [17ms]
2025-06-28 16:11:34.802 [info] > git status -z -uall [12ms]
2025-06-28 16:11:34.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:11:39.840 [info] > git config --get commit.template [18ms]
2025-06-28 16:11:39.842 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:39.868 [info] > git status -z -uall [12ms]
2025-06-28 16:11:39.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 16:11:44.914 [info] > git config --get commit.template [21ms]
2025-06-28 16:11:44.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:11:44.953 [info] > git status -z -uall [16ms]
2025-06-28 16:11:44.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-28 16:11:50.004 [info] > git config --get commit.template [22ms]
2025-06-28 16:11:50.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:11:50.049 [info] > git status -z -uall [22ms]
2025-06-28 16:11:50.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:15:52.913 [info] > git config --get commit.template [11ms]
2025-06-28 16:15:52.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:15:52.935 [info] > git status -z -uall [8ms]
2025-06-28 16:15:52.937 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:15:57.973 [info] > git config --get commit.template [15ms]
2025-06-28 16:15:57.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-28 16:15:58.006 [info] > git status -z -uall [14ms]
2025-06-28 16:15:58.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 16:16:03.083 [info] > git config --get commit.template [19ms]
2025-06-28 16:16:03.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-28 16:16:03.123 [info] > git status -z -uall [17ms]
2025-06-28 16:16:03.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:16:08.157 [info] > git config --get commit.template [13ms]
2025-06-28 16:16:08.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:16:08.185 [info] > git status -z -uall [12ms]
2025-06-28 16:16:08.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-28 16:16:33.048 [info] > git config --get commit.template [14ms]
2025-06-28 16:16:33.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:16:33.086 [info] > git status -z -uall [22ms]
2025-06-28 16:16:33.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-28 16:16:38.121 [info] > git config --get commit.template [13ms]
2025-06-28 16:16:38.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:16:38.144 [info] > git status -z -uall [9ms]
2025-06-28 16:16:38.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:16:43.185 [info] > git config --get commit.template [15ms]
2025-06-28 16:16:43.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:16:43.223 [info] > git status -z -uall [19ms]
2025-06-28 16:16:43.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:16:48.255 [info] > git config --get commit.template [13ms]
2025-06-28 16:16:48.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:16:48.289 [info] > git status -z -uall [15ms]
2025-06-28 16:16:48.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:16:53.323 [info] > git config --get commit.template [13ms]
2025-06-28 16:16:53.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:16:53.361 [info] > git status -z -uall [18ms]
2025-06-28 16:16:53.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-28 16:16:58.402 [info] > git config --get commit.template [13ms]
2025-06-28 16:16:58.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:16:58.427 [info] > git status -z -uall [13ms]
2025-06-28 16:16:58.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:17:03.466 [info] > git config --get commit.template [16ms]
2025-06-28 16:17:03.467 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:17:03.502 [info] > git status -z -uall [17ms]
2025-06-28 16:17:03.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-28 16:17:08.562 [info] > git config --get commit.template [32ms]
2025-06-28 16:17:08.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:17:08.611 [info] > git status -z -uall [27ms]
2025-06-28 16:17:08.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:17:13.666 [info] > git config --get commit.template [21ms]
2025-06-28 16:17:13.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:17:13.706 [info] > git status -z -uall [22ms]
2025-06-28 16:17:13.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:17:18.740 [info] > git config --get commit.template [12ms]
2025-06-28 16:17:18.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-28 16:17:18.763 [info] > git status -z -uall [9ms]
2025-06-28 16:17:18.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-28 16:17:23.799 [info] > git config --get commit.template [15ms]
2025-06-28 16:17:23.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-28 16:17:23.838 [info] > git status -z -uall [15ms]
2025-06-28 16:17:23.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
