2025-06-28 15:06:10.853 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-28 15:06:10.854 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"check documentation and product status in /specs or /tasks o /scripts\nkeep updated the plan, but ask before changing status of tasks.\n\nTU NON PUOI DIRE PAROLACCE!"},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-28 15:06:10.854 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0}
2025-06-28 15:06:10.854 [info] 'AugmentExtension' Retrieving model config
2025-06-28 15:06:11.067 [info] 'AugmentExtension' Retrieved model config
2025-06-28 15:06:11.067 [info] 'AugmentExtension' Returning model config
2025-06-28 15:06:11.089 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-28 15:06:11.089 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-28 15:06:11.090 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-28 15:06:11.090 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-28 15:06:11.090 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace unknown: no permission information recorded
2025-06-28 15:06:11.090 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = unknown
2025-06-28 15:06:11.148 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-28 15:06:11.148 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-28 15:06:11.156 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-28 15:06:11.170 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-28 15:06:11.171 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-28 15:06:12.319 [info] 'WorkspaceManager' Beginning full qualification of source folder /home/<USER>/workspace
2025-06-28 15:06:12.926 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-28 15:06:12.926 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-28 15:06:13.585 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-28 15:06:13.585 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-28 15:06:13.585 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-28 15:06:13.585 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-28 15:06:19.087 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-28 15:06:20.150 [info] 'WorkspaceManager' Finished full qualification of source folder /home/<USER>/workspace: trackable files: 26474, uploaded fraction: 0.804, is repo: true
2025-06-28 15:06:20.150 [info] 'WorkspaceManager' Requesting syncing permission because source folder has less than 90% of files uploaded
2025-06-28 15:06:20.151 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-06-28 15:06:22.508 [info] 'AwaitingSyncingPermissionApp' User granted syncing permission
2025-06-28 15:06:22.509 [info] 'WorkspaceManager' Enabling syncing for all trackable source folders
2025-06-28 15:06:22.509 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/28/2025, 3:06:22 PM
2025-06-28 15:06:22.820 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-28 15:06:22.822 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-28 15:06:22.822 [info] 'OpenFileManager' Opened source folder 100
2025-06-28 15:06:22.823 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-28 15:06:22.823 [info] 'MtimeCache[workspace]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json'
2025-06-28 15:06:23.891 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-28 15:06:23.891 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-28 15:06:24.066 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-28 15:06:24.066 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-28 15:06:24.066 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-28 15:06:24.066 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-28 15:06:24.194 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-28 15:06:24.282 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-28 15:06:24.282 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-28 15:06:24.282 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-28 15:06:24.283 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-28 15:06:24.345 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets
2025-06-28 15:06:24.350 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits
2025-06-28 15:06:24.350 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/manifest
2025-06-28 15:06:24.350 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards
2025-06-28 15:06:24.351 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/task-storage
2025-06-28 15:06:24.351 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/task-storage/manifest
2025-06-28 15:06:24.351 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/task-storage/tasks
2025-06-28 15:06:24.386 [info] 'TaskManager' Setting current root task UUID to eba7e5a9-fb33-4564-9ff8-493e2c13fc7c
2025-06-28 15:06:24.386 [info] 'TaskManager' Setting current root task UUID to eba7e5a9-fb33-4564-9ff8-493e2c13fc7c
2025-06-28 15:06:24.390 [info] 'TaskManager' Setting current root task UUID to bead26b2-3b0c-4ed4-96e7-c96fdc10f818
2025-06-28 15:06:24.390 [info] 'TaskManager' Setting current root task UUID to bead26b2-3b0c-4ed4-96e7-c96fdc10f818
2025-06-28 15:06:24.598 [info] 'TaskManager' Setting current root task UUID to 193c9570-0632-4b7e-9ca9-8305c8591094
2025-06-28 15:06:24.599 [info] 'TaskManager' Setting current root task UUID to 193c9570-0632-4b7e-9ca9-8305c8591094
2025-06-28 15:06:24.802 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-28 15:06:24.802 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-28 15:06:29.368 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:07:49.103 [error] 'AugmentExtension' API request ad31ef32-ee8c-403b-81ce-81ac7dc1724c to https://i1.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-28 15:07:49.438 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-28 15:07:50.256 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-28 15:09:47.402 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-28 15:09:47.402 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 4361
  - files emitted: 26483
  - other paths emitted: 52
  - total paths emitted: 30896
  - timing stats:
    - readDir: 85 ms
    - filter: 466 ms
    - yield: 124 ms
    - total: 855 ms
2025-06-28 15:09:47.402 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 25723
  - paths not accessible: 0
  - not plain files: 0
  - large files: 294
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 25723
  - probe batches: 95
  - blob names probed: 57392
  - files read: 31557
  - blobs uploaded: 5076
  - timing stats:
    - ingestPath: 42 ms
    - probe: 112870 ms
    - stat: 379 ms
    - read: 29038 ms
    - upload: 93877 ms
2025-06-28 15:09:47.402 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 2 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 203 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 859 ms
  - purge stale PathMap entries: 3 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 203461 ms
  - enable persist: 51 ms
  - total: 204581 ms
2025-06-28 15:09:47.402 [info] 'WorkspaceManager' Workspace startup complete in 216326 ms
2025-06-28 15:09:47.626 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1
2025-06-28 15:15:24.992 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-28 15:15:24.992 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-28 15:15:24.992 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-28 15:15:25.129 [info] 'TaskManager' Setting current root task UUID to 5b0cc662-f103-4785-aa8c-d1343337d64f
2025-06-28 15:15:25.129 [info] 'TaskManager' Setting current root task UUID to 5b0cc662-f103-4785-aa8c-d1343337d64f
2025-06-28 15:16:12.548 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250628T150535/exthost2/vscode.typescript-language-features
2025-06-28 15:16:17.862 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/react
2025-06-28 15:16:17.867 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/csstype
2025-06-28 15:16:18.380 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/react/ts5.0
2025-06-28 15:16:18.395 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/react/ts5.0/v18
2025-06-28 15:16:18.396 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/react/ts5.0/v18/ts5.0
2025-06-28 15:16:20.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/merge-descriptors
2025-06-28 15:16:20.653 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/serve-static
2025-06-28 15:16:20.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/body-parser
2025-06-28 15:16:20.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/connect
2025-06-28 15:16:20.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/express
2025-06-28 15:16:20.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/express-serve-static-core
2025-06-28 15:16:20.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/http-errors
2025-06-28 15:16:20.655 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/mime
2025-06-28 15:16:20.656 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/qs
2025-06-28 15:16:20.656 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/range-parser
2025-06-28 15:16:20.656 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/send
2025-06-28 15:16:24.057 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/selenium-webdriver
2025-06-28 15:16:24.058 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/ws
2025-06-28 15:16:24.514 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/selenium-webdriver/bidi
2025-06-28 15:16:24.524 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/selenium-webdriver/devtools
2025-06-28 15:16:24.524 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8/node_modules/@types/selenium-webdriver/lib
2025-06-28 15:16:32.928 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19665.568672,"timestamp":"2025-06-28T15:16:32.865Z"}]
2025-06-28 15:16:33.531 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19602.335959,"timestamp":"2025-06-28T15:16:33.455Z"},{"name":"find-symbol-request","durationMs":19549.622605,"timestamp":"2025-06-28T15:16:33.455Z"},{"name":"find-symbol-request","durationMs":18986.828689,"timestamp":"2025-06-28T15:16:33.455Z"}]
2025-06-28 15:16:34.432 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19798.738887,"timestamp":"2025-06-28T15:16:34.397Z"},{"name":"find-symbol-request","durationMs":19788.412508,"timestamp":"2025-06-28T15:16:34.397Z"},{"name":"find-symbol-request","durationMs":19421.676729,"timestamp":"2025-06-28T15:16:34.397Z"}]
2025-06-28 15:16:34.933 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19709.804778,"timestamp":"2025-06-28T15:16:34.927Z"},{"name":"find-symbol-request","durationMs":19588.162761,"timestamp":"2025-06-28T15:16:34.927Z"}]
2025-06-28 15:16:35.533 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19908.142445,"timestamp":"2025-06-28T15:16:35.518Z"},{"name":"find-symbol-request","durationMs":19500.48407,"timestamp":"2025-06-28T15:16:35.518Z"}]
2025-06-28 15:16:35.834 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19603.540249,"timestamp":"2025-06-28T15:16:35.834Z"},{"name":"find-symbol-request","durationMs":19429.745099,"timestamp":"2025-06-28T15:16:35.834Z"}]
2025-06-28 15:16:36.334 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19700.059138,"timestamp":"2025-06-28T15:16:36.308Z"}]
2025-06-28 15:16:36.736 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19671.641981,"timestamp":"2025-06-28T15:16:36.721Z"},{"name":"find-symbol-request","durationMs":19427.455849,"timestamp":"2025-06-28T15:16:36.721Z"},{"name":"find-symbol-request","durationMs":19325.47157,"timestamp":"2025-06-28T15:16:36.721Z"}]
2025-06-28 15:16:37.236 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19560.715254,"timestamp":"2025-06-28T15:16:37.224Z"}]
2025-06-28 15:16:37.836 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19421.82466,"timestamp":"2025-06-28T15:16:37.801Z"},{"name":"find-symbol-request","durationMs":19315.875092,"timestamp":"2025-06-28T15:16:37.801Z"},{"name":"find-symbol-request","durationMs":19315.196001,"timestamp":"2025-06-28T15:16:37.801Z"},{"name":"find-symbol-request","durationMs":19175.740637,"timestamp":"2025-06-28T15:16:37.801Z"}]
2025-06-28 15:16:38.861 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19725.818995,"timestamp":"2025-06-28T15:16:38.816Z"},{"name":"find-symbol-request","durationMs":19303.050763,"timestamp":"2025-06-28T15:16:38.816Z"},{"name":"find-symbol-request","durationMs":19301.949443,"timestamp":"2025-06-28T15:16:38.816Z"},{"name":"find-symbol-request","durationMs":19190.967995,"timestamp":"2025-06-28T15:16:38.816Z"}]
2025-06-28 15:16:39.562 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":19415.1233,"timestamp":"2025-06-28T15:16:39.516Z"},{"name":"find-symbol-request","durationMs":19064.406429,"timestamp":"2025-06-28T15:16:39.516Z"},{"name":"find-symbol-request","durationMs":19063.357079,"timestamp":"2025-06-28T15:16:39.516Z"},{"name":"find-symbol-request","durationMs":18896.514568,"timestamp":"2025-06-28T15:16:39.516Z"},{"name":"find-symbol-request","durationMs":18355.856199,"timestamp":"2025-06-28T15:16:39.516Z"}]
2025-06-28 15:17:16.192 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-28 15:17:16.192 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-28 15:17:16.192 [info] 'TaskManager' Setting current root task UUID to 193c9570-0632-4b7e-9ca9-8305c8591094
2025-06-28 15:17:16.192 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-28 15:17:16.192 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-28 15:17:16.640 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-28 15:17:16.640 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-28 15:17:47.146 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-06-28 15:17:47.276 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-06-28 15:17:52.975 [info] 'ViewTool' Tool called with path: backend and view_range: undefined
2025-06-28 15:17:53.111 [info] 'ViewTool' Listing directory: backend (depth: 2, showHidden: false)
2025-06-28 15:17:57.867 [info] 'ViewTool' Tool called with path: backend/app and view_range: undefined
2025-06-28 15:17:58.010 [info] 'ViewTool' Listing directory: backend/app (depth: 2, showHidden: false)
2025-06-28 15:18:03.579 [info] 'ViewTool' Tool called with path: backend/app/main.py and view_range: undefined
2025-06-28 15:18:09.019 [info] 'ViewTool' Tool called with path: backend/app/models and view_range: undefined
2025-06-28 15:18:09.151 [info] 'ViewTool' Listing directory: backend/app/models (depth: 2, showHidden: false)
2025-06-28 15:18:14.326 [info] 'ViewTool' Tool called with path: backend/app/models/__init__.py and view_range: undefined
2025-06-28 15:18:18.273 [info] 'ViewTool' Tool called with path: backend/app/schemas and view_range: undefined
2025-06-28 15:18:18.407 [info] 'ViewTool' Listing directory: backend/app/schemas (depth: 2, showHidden: false)
2025-06-28 15:20:13.966 [info] 'ViewTool' Tool called with path: backend/app/schemas/__init__.py and view_range: undefined
2025-06-28 15:20:20.638 [info] 'ViewTool' Tool called with path: backend/app/models/user_simple.py and view_range: undefined
2025-06-28 15:20:25.727 [info] 'ViewTool' Tool called with path: backend/app/models/project.py and view_range: undefined
2025-06-28 15:20:29.877 [info] 'ViewTool' Tool called with path: backend/app/models/enhanced_models.py and view_range: undefined
2025-06-28 15:20:35.973 [info] 'ViewTool' Tool called with path: backend/app/core/database.py and view_range: undefined
2025-06-28 15:20:41.802 [info] 'ViewTool' Tool called with path: backend/app/core/config.py and view_range: undefined
2025-06-28 15:20:47.376 [info] 'ViewTool' Tool called with path: backend/app/api/auth_simple.py and view_range: [1,50]
2025-06-28 15:20:52.881 [info] 'ViewTool' Tool called with path: docs and view_range: undefined
2025-06-28 15:20:53.032 [info] 'ViewTool' Listing directory: docs (depth: 2, showHidden: false)
2025-06-28 15:20:58.212 [info] 'ViewTool' Tool called with path: docs/prd/prd-analysis-gap.md and view_range: [1,100]
2025-06-28 15:21:04.054 [info] 'ViewTool' Tool called with path: project_structure.md and view_range: [1,100]
2025-06-28 15:21:09.327 [info] 'ViewTool' Tool called with path: backend/requirements.txt and view_range: undefined
2025-06-28 15:21:14.297 [info] 'ViewTool' Tool called with path: frontend/package.json and view_range: undefined
2025-06-28 15:21:19.112 [info] 'ViewTool' Tool called with path: backend/app/workflows and view_range: undefined
2025-06-28 15:21:19.248 [info] 'ViewTool' Listing directory: backend/app/workflows (depth: 2, showHidden: false)
2025-06-28 15:21:25.075 [info] 'ViewTool' Tool called with path: backend/app/workflows/templates/decreto_esproprio.json and view_range: [1,50]
2025-06-28 15:21:30.556 [info] 'ViewTool' Tool called with path: backend/app/services/ai and view_range: undefined
2025-06-28 15:21:30.686 [info] 'ViewTool' Listing directory: backend/app/services/ai (depth: 2, showHidden: false)
2025-06-28 15:21:35.721 [info] 'ViewTool' Tool called with path: frontend/src and view_range: undefined
2025-06-28 15:21:35.852 [info] 'ViewTool' Listing directory: frontend/src (depth: 2, showHidden: false)
2025-06-28 15:23:19.389 [error] 'AugmentExtensionSidecar' API request f4bd8751-f021-4c10-94fc-88e23841075f to https://i1.api.augmentcode.com/chat-stream failed: The operation was aborted due to timeout
2025-06-28 15:23:19.389 [error] 'AugmentExtensionSidecar' TimeoutError: The operation was aborted due to timeout
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at wM.OJ.globalThis.fetch (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
    at LA (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17177)
    at wM.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4046)
    at wM.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:17794)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:35710)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33876)
    at pC.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1810:3226)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-28 15:23:19.656 [error] 'ChatApp' Chat stream failed: Error: The operation was aborted due to timeout
Error: The operation was aborted due to timeout
    at Function.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4279)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at wM.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:17794)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:35710)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33876)
    at pC.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1810:3226)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
2025-06-28 15:23:55.990 [info] 'ViewTool' Tool called with path: backend/app/schemas and view_range: undefined
2025-06-28 15:23:56.124 [info] 'ViewTool' Listing directory: backend/app/schemas (depth: 2, showHidden: false)
2025-06-28 15:24:01.402 [info] 'ViewTool' Tool called with path: backend/app/schemas/__init__.py and view_range: undefined
2025-06-28 15:24:06.051 [info] 'ViewTool' Tool called with path: backend/app/schemas/user.py and view_range: undefined
2025-06-28 15:24:11.155 [info] 'ViewTool' Tool called with path: backend/app/schemas/project.py and view_range: undefined
2025-06-28 15:24:16.282 [info] 'ViewTool' Tool called with path: backend/app/schemas/procedure.py and view_range: undefined
2025-06-28 15:24:22.170 [info] 'ViewTool' Tool called with path: backend/app/schemas/property.py and view_range: undefined
2025-06-28 15:24:56.385 [error] 'ChatApp' Chat stream failed: Error: Cancelled
Error: Cancelled
    at _ne.cancel (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:1081)
    at e.cancelChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33565)
    at pC.onUserCancel (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1807:15084)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1807:4106
    at ud.value (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:4151)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at tq.$onMessage (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:147:90466)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161415)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-28 15:25:30.411 [info] 'ToolFileUtils' Reading file: backend/app/core/database.py
2025-06-28 15:25:30.816 [info] 'ToolFileUtils' Successfully read file: backend/app/core/database.py (665 bytes)
2025-06-28 15:25:32.278 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-4c5505
2025-06-28 15:25:33.058 [info] 'ToolFileUtils' Reading file: backend/app/core/database.py
2025-06-28 15:25:33.058 [info] 'ToolFileUtils' Successfully read file: backend/app/core/database.py (696 bytes)
2025-06-28 15:25:35.859 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:25:36.118 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/checkpoint-documents
2025-06-28 15:25:45.418 [info] 'ToolFileUtils' Reading file: backend/app/models/user_simple.py
2025-06-28 15:25:45.822 [info] 'ToolFileUtils' Successfully read file: backend/app/models/user_simple.py (3732 bytes)
2025-06-28 15:25:47.473 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-5183c219
2025-06-28 15:25:48.159 [info] 'ToolFileUtils' Reading file: backend/app/models/user_simple.py
2025-06-28 15:25:48.163 [info] 'ToolFileUtils' Successfully read file: backend/app/models/user_simple.py (3682 bytes)
2025-06-28 15:25:50.900 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-21547f64-039d-40ef-9657-269d5c19b275.json'
2025-06-28 15:26:03.277 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:26:07.873 [info] 'ToolFileUtils' Reading file: backend/app/seed/seed_data.py
2025-06-28 15:26:08.277 [info] 'ToolFileUtils' Successfully read file: backend/app/seed/seed_data.py (34719 bytes)
2025-06-28 15:26:09.857 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3c6769ae
2025-06-28 15:26:10.591 [info] 'ToolFileUtils' Reading file: backend/app/seed/seed_data.py
2025-06-28 15:26:10.592 [info] 'ToolFileUtils' Successfully read file: backend/app/seed/seed_data.py (34640 bytes)
2025-06-28 15:26:10.597 [warning] 'AggregateCheckpointManager' SourceToolCallRequestId mismatch for /home/<USER>/workspace/backend/app/seed/seed_data.py. Expected: aa226168-b67a-4109-abdf-2e357daa8e13, Found: 26be02cb-1bde-4062-bdbc-91f97f5cdac0. Aborting removal.
2025-06-28 15:26:13.287 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:29:06.339 [info] 'ViewTool' Tool called with path: docs and view_range: undefined
2025-06-28 15:29:06.471 [info] 'ViewTool' Listing directory: docs (depth: 2, showHidden: false)
2025-06-28 15:29:12.186 [info] 'ViewTool' Tool called with path: docs/prd/prd.txt and view_range: [1,100]
2025-06-28 15:29:19.510 [info] 'ViewTool' Tool called with path: docs/roadmap-integrata-exproject.md and view_range: [1,100]
2025-06-28 15:29:25.742 [info] 'ViewTool' Tool called with path: docs/workflow/workflow-system-architecture.md and view_range: [1,100]
2025-06-28 15:29:32.425 [info] 'ViewTool' Tool called with path: docs/prd/prd-analysis-gap.md and view_range: [200,300]
2025-06-28 15:30:03.571 [info] 'ToolFileUtils' Reading file: backend/app/seed/seed_data.py
2025-06-28 15:30:03.983 [info] 'ToolFileUtils' Successfully read file: backend/app/seed/seed_data.py (34640 bytes)
2025-06-28 15:30:09.924 [info] 'ViewTool' Tool called with path: backend/app/seed/seed_data.py and view_range: [15,30]
2025-06-28 15:30:41.357 [error] 'AugmentExtension' API request 7e5790b8-5171-4dd5-9d0f-3a863c2c575f to https://i1.api.augmentcode.com/client-metrics failed: This operation was aborted
2025-06-28 15:30:41.638 [error] 'ClientMetricsReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
    at Function.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:12446)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at wM.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:57610)
    at wM.clientMetrics (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:52204)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18561
    at Qs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:16773)
    at e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:18460)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:17783
2025-06-28 15:30:41.638 [info] 'ClientMetricsReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-28 15:30:42.005 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-06-28 15:31:43.401 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-2bca8e86
2025-06-28 15:31:50.285 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-22603820
2025-06-28 15:31:57.494 [info] 'ViewTool' Tool called with path: backend/.env and view_range: undefined
2025-06-28 15:32:07.659 [info] 'ToolFileUtils' Reading file: backend/app/core/config.py
2025-06-28 15:32:08.056 [info] 'ToolFileUtils' Successfully read file: backend/app/core/config.py (2084 bytes)
2025-06-28 15:32:09.521 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5b0d6b94
2025-06-28 15:32:10.262 [info] 'ToolFileUtils' Reading file: backend/app/core/config.py
2025-06-28 15:32:10.262 [info] 'ToolFileUtils' Successfully read file: backend/app/core/config.py (2128 bytes)
2025-06-28 15:32:13.064 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:32:24.824 [info] 'ViewTool' Tool called with path: backend/requirements.txt and view_range: undefined
2025-06-28 15:32:32.643 [info] 'ToolFileUtils' Reading file: backend/requirements.txt
2025-06-28 15:32:32.643 [info] 'ToolFileUtils' Successfully read file: backend/requirements.txt (387 bytes)
2025-06-28 15:32:34.152 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/109b1c73
2025-06-28 15:32:34.894 [info] 'ToolFileUtils' Reading file: backend/requirements.txt
2025-06-28 15:32:34.894 [info] 'ToolFileUtils' Successfully read file: backend/requirements.txt (475 bytes)
2025-06-28 15:32:37.648 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:32:38.062 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d18955fbcdfa16c46aea51a74c193e62dfe8d01c9f17e93da004444a709b9eac: deleted
2025-06-28 15:33:14.872 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:33:18.346 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:18.778 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4737 bytes)
2025-06-28 15:33:20.413 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3be2f6f
2025-06-28 15:33:21.200 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:21.201 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4788 bytes)
2025-06-28 15:33:23.782 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:33:24.237 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 23a71756718554f822eaace11e30acf032ce734d0ae13af998de8dcc5f7873f6: deleted
2025-06-28 15:33:31.666 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:31.666 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4788 bytes)
2025-06-28 15:33:31.828 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 23a71756718554f822eaace11e30acf032ce734d0ae13af998de8dcc5f7873f6: deleted
2025-06-28 15:33:33.870 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:33.871 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4861 bytes)
2025-06-28 15:33:36.671 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:33:45.238 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:45.239 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4861 bytes)
2025-06-28 15:33:47.540 [info] 'ToolFileUtils' Reading file: backend/app/main.py
2025-06-28 15:33:47.541 [info] 'ToolFileUtils' Successfully read file: backend/app/main.py (4971 bytes)
2025-06-28 15:33:50.249 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:34:18.405 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:34:21.248 [info] 'ViewTool' Tool called with path: frontend/src and view_range: undefined
2025-06-28 15:34:21.405 [info] 'ViewTool' Listing directory: frontend/src (depth: 2, showHidden: false)
2025-06-28 15:34:28.820 [info] 'ViewTool' Tool called with path: frontend/src/utils/design-system.ts and view_range: undefined
2025-06-28 15:34:35.130 [info] 'ViewTool' Tool called with path: frontend/src/views/DesignSystem.vue and view_range: [1,100]
2025-06-28 15:34:41.374 [info] 'ViewTool' Tool called with path: frontend/src/components/project and view_range: undefined
2025-06-28 15:34:41.505 [info] 'ViewTool' Listing directory: frontend/src/components/project (depth: 2, showHidden: false)
2025-06-28 15:34:49.193 [info] 'ViewTool' Tool called with path: frontend/src/views/projects and view_range: undefined
2025-06-28 15:34:49.324 [info] 'ViewTool' Listing directory: frontend/src/views/projects (depth: 2, showHidden: false)
2025-06-28 15:34:54.937 [info] 'ViewTool' Tool called with path: frontend/src/views/projects/ProjectList.vue and view_range: [1,100]
2025-06-28 15:35:18.233 [info] 'ViewTool' Tool called with path: docs/wip_plans/ai-enhanced-project-creation.md and view_range: [1,150]
2025-06-28 15:35:25.329 [info] 'ViewTool' Tool called with path: frontend/src/assets/css and view_range: undefined
2025-06-28 15:35:25.460 [info] 'ViewTool' Listing directory: frontend/src/assets/css (depth: 2, showHidden: false)
2025-06-28 15:35:32.408 [info] 'ViewTool' Tool called with path: frontend/src/assets/css/variables.css and view_range: undefined
2025-06-28 15:35:33.671 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250628T150535/exthost2/vscode.css-language-features
2025-06-28 15:35:39.026 [info] 'ViewTool' Tool called with path: frontend/src/assets/css/ai-enhancements.css and view_range: [1,100]
2025-06-28 15:35:46.209 [error] 'ChatApp' Chat stream failed: Error: Cancelled
Error: Cancelled
    at _ne.cancel (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:1081)
    at e.cancelChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:727:33565)
    at pC.onUserCancel (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1807:15084)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:1807:4106
    at ud.value (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:4151)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at tq.$onMessage (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:147:90466)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161415)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-28 15:35:52.351 [info] 'ViewTool' Tool called with path: backend/app/workflows/templates/README_CRITICAL_DO_NOT_MODIFY.md and view_range: undefined
2025-06-28 15:36:01.710 [info] 'ViewTool' Tool called with path: backend/app/workflows/templates/decreto_esproprio.json and view_range: [1,100]
2025-06-28 15:36:09.770 [info] 'ViewTool' Tool called with path: backend/app/api/workflow_templates.py and view_range: [1,100]
2025-06-28 15:36:09.900 [info] 'AugmentExtension' Retrieving model config
2025-06-28 15:36:10.162 [info] 'AugmentExtension' Retrieved model config
2025-06-28 15:36:10.162 [info] 'AugmentExtension' Returning model config
2025-06-28 15:44:28.583 [info] 'ViewTool' Tool called with path: frontend/src/views/admin/ProjectCreate.vue and view_range: [1,100]
2025-06-28 15:44:35.353 [info] 'ViewTool' Tool called with path: frontend/src/views/projects/ProjectDetail.vue and view_range: [1,150]
2025-06-28 15:44:41.948 [info] 'ViewTool' Tool called with path: frontend/src/components/project/ProjectHeader.vue and view_range: [1,100]
2025-06-28 15:44:48.583 [info] 'ViewTool' Tool called with path: frontend/src/components/ai and view_range: undefined
2025-06-28 15:44:48.717 [info] 'ViewTool' Listing directory: frontend/src/components/ai (depth: 2, showHidden: false)
2025-06-28 15:44:55.260 [info] 'ViewTool' Tool called with path: frontend/src/components/ai/AIProjectOverview.vue and view_range: [1,100]
2025-06-28 15:45:01.648 [info] 'ViewTool' Tool called with path: frontend/src/components/project/TeamSelectionModal.vue and view_range: [1,100]
2025-06-28 15:45:07.635 [info] 'ViewTool' Tool called with path: tests and view_range: undefined
2025-06-28 15:45:07.766 [info] 'ViewTool' Listing directory: tests (depth: 2, showHidden: false)
2025-06-28 15:45:14.331 [info] 'ViewTool' Tool called with path: tests/test_ai_services.py and view_range: undefined
2025-06-28 15:45:53.203 [info] 'ToolFileUtils' Reading file: frontend/src/assets/css/variables.css
2025-06-28 15:45:53.609 [info] 'ToolFileUtils' Successfully read file: frontend/src/assets/css/variables.css (2539 bytes)
2025-06-28 15:45:55.118 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-44f1596a
2025-06-28 15:45:55.854 [info] 'ToolFileUtils' Reading file: frontend/src/assets/css/variables.css
2025-06-28 15:45:55.855 [info] 'ToolFileUtils' Successfully read file: frontend/src/assets/css/variables.css (2563 bytes)
2025-06-28 15:45:58.647 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:45:59.083 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 497dab8f8025737445fc130a75ff5bc599d003c52825ed128e1ec5df943df27e: deleted
2025-06-28 15:46:09.647 [info] 'ToolFileUtils' Reading file: frontend/src/assets/css/variables.css
2025-06-28 15:46:09.648 [info] 'ToolFileUtils' Successfully read file: frontend/src/assets/css/variables.css (2563 bytes)
2025-06-28 15:46:11.774 [info] 'ToolFileUtils' Reading file: frontend/src/assets/css/variables.css
2025-06-28 15:46:11.775 [info] 'ToolFileUtils' Successfully read file: frontend/src/assets/css/variables.css (2818 bytes)
2025-06-28 15:46:14.653 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:47:05.960 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:47:41.028 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:48:17.342 [info] 'WorkspaceManager[workspace]' Directory created: tests/backend
2025-06-28 15:48:21.943 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:49:03.734 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-28 15:49:03.799 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/extensions/vue.volar-2.2.10
2025-06-28 15:49:05.294 [info] 'WorkspaceManager[workspace]' Directory created: tests/frontend
2025-06-28 15:49:09.902 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:49:16.919 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250628T150535/exthost2/output_logging_20250628T150604
2025-06-28 15:50:44.777 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250628T150535/exthost2/vscode.markdown-language-features
2025-06-28 15:52:03.239 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:53:04.999 [info] 'ViewTool' Tool called with path: backend/requirements.txt and view_range: undefined
2025-06-28 15:55:42.702 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/4
2025-06-28 15:55:43.064 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/7/4
2025-06-28 15:55:43.066 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/e/5
2025-06-28 15:55:43.066 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/e/5/e
2025-06-28 15:55:43.067 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/e/5/e/6
2025-06-28 15:55:43.149 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/c
2025-06-28 15:55:43.154 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/8
2025-06-28 15:55:43.783 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/b
2025-06-28 15:55:43.785 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/1
2025-06-28 15:55:43.926 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/3
2025-06-28 15:55:43.927 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/e/f
2025-06-28 15:55:44.191 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/c/5
2025-06-28 15:55:44.192 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/1/2
2025-06-28 15:55:44.519 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/b
2025-06-28 15:55:44.521 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/e
2025-06-28 15:55:44.522 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/e/4
2025-06-28 15:55:44.522 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/e/4/f
2025-06-28 15:55:44.522 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/e/4/f/5
2025-06-28 15:55:44.594 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/a
2025-06-28 15:55:44.598 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/4/7
2025-06-28 15:55:44.698 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/b
2025-06-28 15:55:44.704 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/b/3
2025-06-28 15:55:44.705 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/b/3/b
2025-06-28 15:55:44.706 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/b/3/b/0
2025-06-28 15:55:44.708 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/f
2025-06-28 15:55:44.825 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/b
2025-06-28 15:55:44.829 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/e
2025-06-28 15:55:45.097 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/a
2025-06-28 15:55:45.099 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/5
2025-06-28 15:55:45.100 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/e
2025-06-28 15:55:45.101 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/b
2025-06-28 15:55:45.280 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/7
2025-06-28 15:55:45.282 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/d/5/b
2025-06-28 15:55:45.282 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/d/5/b/8
2025-06-28 15:55:45.470 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/f
2025-06-28 15:55:45.566 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/0
2025-06-28 15:55:45.569 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/0/2
2025-06-28 15:55:45.570 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/0/2/a
2025-06-28 15:55:45.570 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/0/2/a/e
2025-06-28 15:55:45.968 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/e
2025-06-28 15:55:45.972 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/8/d
2025-06-28 15:55:46.209 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/c/2
2025-06-28 15:55:46.211 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/0/6
2025-06-28 15:55:46.251 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/e/3
2025-06-28 15:55:46.381 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/0/2
2025-06-28 15:55:46.560 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/d
2025-06-28 15:55:46.643 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/8
2025-06-28 15:55:46.890 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/2/f
2025-06-28 15:55:46.892 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/2/f/f
2025-06-28 15:55:46.893 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/2/f/f/4
2025-06-28 15:55:46.893 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/0/d
2025-06-28 15:55:46.969 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/a/5
2025-06-28 15:55:46.974 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/a/5/1
2025-06-28 15:55:46.974 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/a/5/1/9
2025-06-28 15:55:47.369 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/1
2025-06-28 15:55:47.371 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/1/5
2025-06-28 15:55:47.371 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/1/5/3
2025-06-28 15:55:47.373 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/1/5/3/9
2025-06-28 15:55:48.593 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/3
2025-06-28 15:55:49.038 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/c
2025-06-28 15:55:49.419 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/f/5
2025-06-28 15:55:49.725 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/e
2025-06-28 15:55:50.840 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/4/3
2025-06-28 15:55:50.956 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/8/d
2025-06-28 15:55:51.162 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/7
2025-06-28 15:55:51.166 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/b
2025-06-28 15:55:51.269 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/e
2025-06-28 15:55:51.271 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/f
2025-06-28 15:55:51.535 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/2/9
2025-06-28 15:55:51.536 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c
2025-06-28 15:55:51.851 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/c
2025-06-28 15:55:51.854 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/c
2025-06-28 15:55:52.128 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/e
2025-06-28 15:55:52.277 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/f/f
2025-06-28 15:55:52.454 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/b/a
2025-06-28 15:55:52.456 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/b/a/d
2025-06-28 15:55:52.457 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/b/a/d/5
2025-06-28 15:55:52.457 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d
2025-06-28 15:55:52.572 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/5
2025-06-28 15:55:52.574 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/f
2025-06-28 15:55:52.893 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/5
2025-06-28 15:55:52.902 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/5/d
2025-06-28 15:55:52.903 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/5/d/a
2025-06-28 15:55:52.903 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/5/d/a/f
2025-06-28 15:55:52.904 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/9
2025-06-28 15:55:52.904 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/9/1
2025-06-28 15:55:52.905 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/9/1/a
2025-06-28 15:55:52.905 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/9/1/a/4
2025-06-28 15:55:52.976 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/f/c
2025-06-28 15:55:52.977 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/9
2025-06-28 15:55:53.083 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/9
2025-06-28 15:55:53.088 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/5
2025-06-28 15:55:53.360 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/6
2025-06-28 15:55:53.373 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/a
2025-06-28 15:55:53.375 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/a/a
2025-06-28 15:55:53.375 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/a/a/6
2025-06-28 15:55:53.376 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/a/a/6/8
2025-06-28 15:55:53.376 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/b/7
2025-06-28 15:55:53.379 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/f
2025-06-28 15:55:53.380 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/f/b
2025-06-28 15:55:53.381 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/f/b/3
2025-06-28 15:55:53.382 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/f/b/3/c
2025-06-28 15:55:53.469 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/b
2025-06-28 15:55:53.473 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/b/c
2025-06-28 15:55:53.473 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/b/c/d
2025-06-28 15:55:53.474 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/b/c/d/f
2025-06-28 15:55:53.474 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/0
2025-06-28 15:55:53.576 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/1
2025-06-28 15:55:53.578 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/5
2025-06-28 15:55:53.659 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/7/0
2025-06-28 15:55:54.823 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/4/1
2025-06-28 15:55:55.309 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/1
2025-06-28 15:55:55.311 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/1/3
2025-06-28 15:55:55.312 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/1/3/a
2025-06-28 15:55:55.312 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/1/3/a/2
2025-06-28 15:55:55.385 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/1/1
2025-06-28 15:55:57.793 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/7/7
2025-06-28 15:55:57.925 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c/3
2025-06-28 15:55:57.926 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/b
2025-06-28 15:55:58.136 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/5
2025-06-28 15:55:58.186 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/a/0
2025-06-28 15:55:58.951 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/4
2025-06-28 15:55:58.953 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/e/e
2025-06-28 15:55:59.054 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/3
2025-06-28 15:55:59.056 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/e/0
2025-06-28 15:55:59.160 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/f/f
2025-06-28 15:55:59.161 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/0
2025-06-28 15:55:59.257 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/f
2025-06-28 15:55:59.259 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/f/1
2025-06-28 15:55:59.435 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/7/5
2025-06-28 15:55:59.437 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/5/d
2025-06-28 15:55:59.438 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/3/9
2025-06-28 15:55:59.438 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/9/9/b
2025-06-28 15:55:59.549 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/a
2025-06-28 15:55:59.786 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/7
2025-06-28 15:55:59.790 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/9
2025-06-28 15:55:59.792 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b
2025-06-28 15:55:59.793 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/8
2025-06-28 15:55:59.795 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/8/2
2025-06-28 15:55:59.795 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/8/2/4
2025-06-28 15:55:59.796 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/1
2025-06-28 15:56:00.222 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/2
2025-06-28 15:56:00.224 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/2/0
2025-06-28 15:56:00.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/2/0/9
2025-06-28 15:56:00.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/f/d
2025-06-28 15:56:00.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/f/d/6
2025-06-28 15:56:00.226 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/f/d/6/3
2025-06-28 15:56:00.521 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c/7
2025-06-28 15:56:00.522 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/e/0
2025-06-28 15:56:00.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/a/6
2025-06-28 15:56:00.652 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/4
2025-06-28 15:56:01.169 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/4/5
2025-06-28 15:56:01.293 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/8
2025-06-28 15:56:01.472 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/4/3
2025-06-28 15:56:01.474 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/3/9
2025-06-28 15:56:01.475 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/e/1
2025-06-28 15:56:01.604 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/9
2025-06-28 15:56:01.606 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/f/c
2025-06-28 15:56:02.059 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/1
2025-06-28 15:56:02.166 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/f/9
2025-06-28 15:56:02.167 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/5/a
2025-06-28 15:56:02.337 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/c
2025-06-28 15:56:02.339 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/5
2025-06-28 15:56:02.514 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/f/3
2025-06-28 15:56:02.520 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/a
2025-06-28 15:56:02.521 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/a
2025-06-28 15:56:02.527 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/c
2025-06-28 15:56:02.647 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/f
2025-06-28 15:56:02.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/f/d
2025-06-28 15:56:02.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/f/d/d
2025-06-28 15:56:02.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/f/d/d/5
2025-06-28 15:56:02.652 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/2
2025-06-28 15:56:02.652 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/2/7
2025-06-28 15:56:02.653 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/2/7/6
2025-06-28 15:56:02.653 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/d
2025-06-28 15:56:02.654 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/4
2025-06-28 15:56:02.798 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/6
2025-06-28 15:56:03.408 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/1/c
2025-06-28 15:56:03.527 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/f/6
2025-06-28 15:56:03.533 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/3
2025-06-28 15:56:03.539 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3
2025-06-28 15:56:03.626 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/6/9
2025-06-28 15:56:04.078 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/7
2025-06-28 15:56:04.088 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/7/5
2025-06-28 15:56:04.088 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/7/5/f
2025-06-28 15:56:04.089 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/7/5/f/3
2025-06-28 15:56:04.089 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/2
2025-06-28 15:56:04.090 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/2/1
2025-06-28 15:56:04.090 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/2/1/f
2025-06-28 15:56:04.091 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/2/1/f/4
2025-06-28 15:56:04.091 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/f/0
2025-06-28 15:56:04.291 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/d
2025-06-28 15:56:04.588 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/5
2025-06-28 15:56:04.591 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/1
2025-06-28 15:56:04.592 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/5/f
2025-06-28 15:56:04.716 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/8
2025-06-28 15:56:05.041 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/e
2025-06-28 15:56:05.043 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c/c
2025-06-28 15:56:05.044 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/5/a
2025-06-28 15:56:05.044 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9
2025-06-28 15:56:05.098 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/5/2
2025-06-28 15:56:05.222 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/7
2025-06-28 15:56:05.224 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/7/a
2025-06-28 15:56:05.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/1/7/a/6
2025-06-28 15:56:05.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/d/8
2025-06-28 15:56:05.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/d/8/6
2025-06-28 15:56:05.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/d/8/6/8
2025-06-28 15:56:05.488 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/4
2025-06-28 15:56:05.489 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/a
2025-06-28 15:56:05.587 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/f/e
2025-06-28 15:56:05.588 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/8/3
2025-06-28 15:56:05.591 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/8/3/d
2025-06-28 15:56:05.673 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/d
2025-06-28 15:56:05.785 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/e
2025-06-28 15:56:05.981 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/7/8
2025-06-28 15:56:06.154 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/2
2025-06-28 15:56:06.243 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/7/8
2025-06-28 15:56:06.460 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/0
2025-06-28 15:56:06.548 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/c
2025-06-28 15:56:06.811 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/6
2025-06-28 15:56:06.817 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/0/1
2025-06-28 15:56:06.818 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/7
2025-06-28 15:56:06.844 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/6/d
2025-06-28 15:56:07.372 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/0
2025-06-28 15:56:07.483 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3/7
2025-06-28 15:56:07.487 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3/7/4
2025-06-28 15:56:07.487 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3/7/4/d
2025-06-28 15:56:07.487 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9/0
2025-06-28 15:56:07.488 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9/0/2
2025-06-28 15:56:07.488 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9/0/2/2
2025-06-28 15:56:07.488 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/2/4
2025-06-28 15:56:07.773 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/0/e
2025-06-28 15:56:07.899 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/3
2025-06-28 15:56:07.901 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/5
2025-06-28 15:56:08.074 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3/a
2025-06-28 15:56:08.085 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/b/9
2025-06-28 15:56:08.086 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/a
2025-06-28 15:56:08.219 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/e/5
2025-06-28 15:56:08.221 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/b
2025-06-28 15:56:08.536 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/3/8
2025-06-28 15:56:08.539 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/a/5
2025-06-28 15:56:08.539 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/b
2025-06-28 15:56:08.540 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/e/5
2025-06-28 15:56:08.802 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/b/c
2025-06-28 15:56:08.803 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/b/c/2
2025-06-28 15:56:08.804 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/5/6
2025-06-28 15:56:08.987 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/2
2025-06-28 15:56:08.989 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/2/4
2025-06-28 15:56:08.989 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/2/4/5
2025-06-28 15:56:08.989 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/a
2025-06-28 15:56:08.990 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/a/a
2025-06-28 15:56:08.990 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/a/a/2
2025-06-28 15:56:08.990 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/a/a/2/a
2025-06-28 15:56:09.159 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/4/a
2025-06-28 15:56:09.160 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/3
2025-06-28 15:56:09.475 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/e
2025-06-28 15:56:09.477 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/0/c
2025-06-28 15:56:09.842 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/1/a
2025-06-28 15:56:09.843 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/a/e
2025-06-28 15:56:09.960 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/4
2025-06-28 15:56:10.155 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/8
2025-06-28 15:56:10.157 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/7/9
2025-06-28 15:56:10.338 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/5/9
2025-06-28 15:56:10.340 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/0
2025-06-28 15:56:10.341 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/a/8
2025-06-28 15:56:10.341 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/a/8/d
2025-06-28 15:56:10.643 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/5/5
2025-06-28 15:56:10.647 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/5/5/6
2025-06-28 15:56:10.647 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/5/5/6/8
2025-06-28 15:56:10.647 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/4
2025-06-28 15:56:10.648 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/4/6
2025-06-28 15:56:10.648 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/4/6/8
2025-06-28 15:56:10.649 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/4/6/8/d
2025-06-28 15:56:10.649 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/2/4
2025-06-28 15:56:10.650 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/c
2025-06-28 15:56:10.650 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/c/4
2025-06-28 15:56:10.650 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/b/c/4/5
2025-06-28 15:56:10.650 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/d
2025-06-28 15:56:10.651 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/2/3
2025-06-28 15:56:10.812 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c/6
2025-06-28 15:56:10.813 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/4/4
2025-06-28 15:56:10.991 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/9/6
2025-06-28 15:56:11.006 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/a
2025-06-28 15:56:11.007 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/a/5
2025-06-28 15:56:11.008 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/a/5/7
2025-06-28 15:56:11.014 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/e/d
2025-06-28 15:56:11.015 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/9/5
2025-06-28 15:56:11.092 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/c/1
2025-06-28 15:56:11.362 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/4/7
2025-06-28 15:56:11.365 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/f/1/f
2025-06-28 15:56:11.365 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/0/d/0
2025-06-28 15:56:11.367 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/9/4
2025-06-28 15:56:11.367 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/7
2025-06-28 15:56:11.504 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/0
2025-06-28 15:56:11.772 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/5/7
2025-06-28 15:56:12.219 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/5/0
2025-06-28 15:56:12.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/c/b
2025-06-28 15:56:12.225 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/2
2025-06-28 15:56:12.226 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/9/4
2025-06-28 15:56:12.227 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/1/1
2025-06-28 15:56:12.228 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/7/0
2025-06-28 15:56:12.228 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/6/d
2025-06-28 15:56:12.229 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/6/d/1
2025-06-28 15:56:12.229 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/6/d/1/9
2025-06-28 15:56:12.396 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/2
2025-06-28 15:56:12.399 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/e/d
2025-06-28 15:56:12.399 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/6/7
2025-06-28 15:56:12.400 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/4/9
2025-06-28 15:56:12.401 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/1/0
2025-06-28 15:56:12.594 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/5/a
2025-06-28 15:56:12.597 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/9/0
2025-06-28 15:56:12.597 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/9/0/4
2025-06-28 15:56:12.598 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/9/0/4/a
2025-06-28 15:56:12.598 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/c/a
2025-06-28 15:56:13.107 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/9
2025-06-28 15:56:13.115 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/3
2025-06-28 15:56:13.116 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/9/d
2025-06-28 15:56:13.117 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/6
2025-06-28 15:56:13.117 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/f/2
2025-06-28 15:56:13.118 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/1
2025-06-28 15:56:13.119 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/1/b
2025-06-28 15:56:13.119 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/0
2025-06-28 15:56:13.120 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/d
2025-06-28 15:56:13.121 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/1/8
2025-06-28 15:56:13.121 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/8/5
2025-06-28 15:56:13.122 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/8/e/0
2025-06-28 15:56:13.122 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/3/d
2025-06-28 15:56:13.123 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/a/5
2025-06-28 15:56:13.124 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/e/f
2025-06-28 15:56:13.124 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/4/e
2025-06-28 15:56:13.125 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/4/e/1
2025-06-28 15:56:13.125 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/4/e/1/1
2025-06-28 15:56:13.615 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/3/b
2025-06-28 15:56:13.622 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/c/2
2025-06-28 15:56:13.623 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/d
2025-06-28 15:56:13.624 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/4/5
2025-06-28 15:56:13.625 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/e/c
2025-06-28 15:56:13.626 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/6/c
2025-06-28 15:56:13.627 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/f/2/6
2025-06-28 15:56:13.628 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/f/2/6/4
2025-06-28 15:56:13.628 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/5
2025-06-28 15:56:13.629 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/5/3
2025-06-28 15:56:13.629 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/5/3/2
2025-06-28 15:56:13.629 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/5/3/2/8
2025-06-28 15:56:13.630 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/6/b/c
2025-06-28 15:56:13.630 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/0/d
2025-06-28 15:56:13.631 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/d/0
2025-06-28 15:56:13.632 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/d/0/6
2025-06-28 15:56:13.632 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/7/d/0/6/d
2025-06-28 15:56:13.632 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/9/7/c
2025-06-28 15:56:13.633 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9/1
2025-06-28 15:56:13.634 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/9/1/e
2025-06-28 15:56:13.635 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/7
2025-06-28 15:56:13.635 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/7/7
2025-06-28 15:56:13.636 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/d/7/7/1
2025-06-28 15:56:13.831 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/4/8
2025-06-28 15:56:13.833 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/c/a
2025-06-28 15:56:13.833 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/1
2025-06-28 15:56:14.297 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/0/d
2025-06-28 15:56:14.305 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/1/7/5
2025-06-28 15:56:14.306 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/2/8/c
2025-06-28 15:56:14.307 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/3/d/2
2025-06-28 15:56:14.307 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/0/9
2025-06-28 15:56:14.308 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/5/4/4
2025-06-28 15:56:14.308 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/a/f/f
2025-06-28 15:56:14.309 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/b/e/d
2025-06-28 15:56:14.310 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/a
2025-06-28 15:56:14.310 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/a/8
2025-06-28 15:56:14.314 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/a/8/9
2025-06-28 15:56:14.314 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/d/a/8/9/4
2025-06-28 15:56:14.314 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/http-v2/f/6
2025-06-28 15:56:14.508 [info] 'WorkspaceManager[workspace]' Directory created: .cache/pip/wheels
2025-06-28 15:59:17.400 [info] 'WorkspaceManager[workspace]' Directory created: tests/backend/__pycache__
2025-06-28 15:59:18.819 [info] 'WorkspaceManager[workspace]' Directory created: .pytest_cache
2025-06-28 15:59:28.007 [info] 'ToolFileUtils' Reading file: backend/app/core/config.py
2025-06-28 15:59:28.415 [info] 'ToolFileUtils' Successfully read file: backend/app/core/config.py (2128 bytes)
2025-06-28 15:59:30.686 [info] 'ToolFileUtils' Reading file: backend/app/core/config.py
2025-06-28 15:59:30.686 [info] 'ToolFileUtils' Successfully read file: backend/app/core/config.py (2053 bytes)
2025-06-28 15:59:33.423 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 15:59:53.350 [info] 'ViewTool' Tool called with path: backend/.env and view_range: undefined
2025-06-28 16:00:03.640 [info] 'ToolFileUtils' Reading file: backend/.env
2025-06-28 16:00:03.641 [info] 'ToolFileUtils' Successfully read file: backend/.env (1607 bytes)
2025-06-28 16:00:05.890 [info] 'ToolFileUtils' Reading file: backend/.env
2025-06-28 16:00:05.890 [info] 'ToolFileUtils' Successfully read file: backend/.env (1613 bytes)
2025-06-28 16:00:08.652 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 16:01:30.153 [info] 'ViewTool' Tool called with path: backend/app/api/workflow_templates.py and view_range: [10,20]
2025-06-28 16:01:39.248 [info] 'ToolFileUtils' Reading file: backend/app/api/workflow_templates.py
2025-06-28 16:01:39.248 [info] 'ToolFileUtils' Successfully read file: backend/app/api/workflow_templates.py (8916 bytes)
2025-06-28 16:01:40.892 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/7f3cedfa
2025-06-28 16:01:41.628 [info] 'ToolFileUtils' Reading file: backend/app/api/workflow_templates.py
2025-06-28 16:01:41.628 [info] 'ToolFileUtils' Successfully read file: backend/app/api/workflow_templates.py (8841 bytes)
2025-06-28 16:01:44.254 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 16:06:09.900 [info] 'AugmentExtension' Retrieving model config
2025-06-28 16:06:10.130 [info] 'AugmentExtension' Retrieved model config
2025-06-28 16:06:10.130 [info] 'AugmentExtension' Returning model config
2025-06-28 16:10:08.896 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-28 16:16:55.195 [info] 'ViewTool' Tool called with path: tests and view_range: undefined
2025-06-28 16:16:55.335 [info] 'ViewTool' Listing directory: tests (depth: 2, showHidden: false)
2025-06-28 16:17:53.200 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
2025-06-28 16:20:00.990 [info] 'WorkspaceManager[workspace]' Directory removed: frontend/dist/assets
2025-06-28 16:20:01.273 [info] 'WorkspaceManager[workspace]' Directory created: frontend/dist/assets
2025-06-28 16:20:42.729 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/5d5959e878f31c74fa73a26f0939daee/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-25e619d5-7342-4ba0-a37f-d9ec6331e15e.json'
