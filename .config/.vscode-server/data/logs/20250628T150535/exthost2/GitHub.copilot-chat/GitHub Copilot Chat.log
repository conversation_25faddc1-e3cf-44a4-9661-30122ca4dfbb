2025-06-28 15:06:10.985 [info] Can't use the Electron fetcher in this environment.
2025-06-28 15:06:10.985 [info] Using the Node fetch fetcher.
2025-06-28 15:06:10.985 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-28 15:06:10.985 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-28 15:06:10.985 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-28 15:06:10.985 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-28 15:06:14.424 [info] Logged in as daniele-sabetta
2025-06-28 15:06:14.737 [warning] Invalid copilot token: missing token: 403 
2025-06-28 15:06:14.739 [error] Error: Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta.
    at l3._authShowWarnings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:58724)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at l3.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:57690)
    at jF.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:3690)
    at y1.waitForChatEnabled (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:5828)
    at y1.run (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:5712)
    at x6.askToUpgradeAuthPermissions (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:5370): Failed to get copilot token
2025-06-28 15:06:14.739 [error] GitHub Copilot could not connect to server. Extension activation failed: "Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta."
2025-06-28 15:06:14.739 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-06-28 15:06:14.739 [error] Error: Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta.
    at l3._authShowWarnings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:58724)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at l3.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:57690)
    at jF.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:3690)
    at HB._getAuthSession (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:4237)
    at Object.n [as task] (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:751)
    at $p._processQueue (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:475:24577)
2025-06-28 15:06:14.739 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-06-28 15:06:14.739 [error] Error: Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta.
    at l3._authShowWarnings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:58724)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at l3.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:57690)
    at jF.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:3690)
    at HB._getAuthSession (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:4237)
    at r (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:3730)
    at HB._registerEmbeddings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:4199)
    at async Promise.all (index 1)
    at async Promise.allSettled (index 1)
    at _T.waitForActivationBlockers (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:855)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:1666
    at TKe (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:1446)
    at Wb.n (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:120:13382)
    at Wb.m (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:120:13345)
    at Wb.l (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:120:12801)
2025-06-28 15:06:14.739 [info] activationBlocker from 'languageModelAccess' took for 3693ms
2025-06-28 15:16:15.173 [info] TypeScript server plugin activated.
2025-06-28 15:16:15.189 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-28 15:21:16.427 [info] Logged in as daniele-sabetta
2025-06-28 15:21:16.720 [warning] Invalid copilot token: missing token: 403 
2025-06-28 15:21:16.721 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-06-28 15:21:16.721 [error] Error: Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta.
    at l3._authShowWarnings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:58724)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at l3.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:57690)
    at jF.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:3690)
    at HB._getAuthSession (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:4237)
    at Object.n [as task] (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:751)
    at $p._processQueue (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:475:24577)
2025-06-28 15:36:18.354 [info] Logged in as daniele-sabetta
2025-06-28 15:36:18.654 [warning] Invalid copilot token: missing token: 403 
2025-06-28 15:36:18.655 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-06-28 15:36:18.655 [error] Error: Thank you for using GitHub Copilot. Your subscription has ended. You are currently logged in as daniele-sabetta.
    at l3._authShowWarnings (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:58724)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at l3.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:485:57690)
    at jF.getCopilotToken (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:443:3690)
    at HB._getAuthSession (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:4237)
    at Object.n [as task] (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:482:751)
    at $p._processQueue (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.3/dist/extension.js:475:24577)
